package env

import (
	"log"
	"testing"

	"dt-common/logger"
	"dt-common/redisc"
	"redis-agent/config"
	"redis-agent/library/errc"
)

// 单元测试Mock初始化
func Mock(t *testing.T, configPath ...string) {
	// 初始化配置
	err := config.Init(configPath...)
	if err != nil {
		log.Panicf("Failed to init config, error=(%v)", err)
	}

	// 初始化 Logger
	var logConfig logger.Config
	err = config.Get("logger", &logConfig)
	if err != nil {
		log.Panicf("Failed to init logger, error=(%v)", err)
	}
	logger.Init(&logConfig)

	// MOCK redisClient
	var redisConfig redisc.Config
	err = config.Get("redis", &logConfig)
	if err != nil {
		log.Panicf("Failed to init logger, error=(%v)", err)
	}
	redisConfig.Mock = true
	redisc.Init(&redisConfig)

	// 自定义错误码
	errc.Init()
}
