package env

import (
	"context"
	"log"
	"strings"
	"testing"

	"github.com/jarcoal/httpmock"

	"dt-common/ent"
	"dt-common/ent/cluster"
	"dt-common/ent/stage"
	"dt-common/ent/task"
	"dt-common/logger"
	"dt-common/mysql"
	"dt-common/user-center/flowc"
	"redis-xweb/config"
	"redis-xweb/library/billing"
	"redis-xweb/library/errc"
	"redis-xweb/library/monitor"
	"redis-xweb/library/ras"
)

func MockResponse() {}

// 单元测试Mock初始化
func Mock(t *testing.T, configPath ...string) {
	// 初始化配置
	err := config.Init(configPath...)
	if err != nil {
		log.Panicf("failed to init config, error=(%v)", err)
	}

	// 初始化 Logger
	var logConfig logger.Config
	err = config.Get("logger", &logConfig)
	if err != nil {
		log.Panicf("failed to init logger, error=(%v)", err)
	}
	logger.Init(&logConfig)

	// 初始化 监控配置
	var monitorConfig monitor.Config
	config.Get("monitor", &monitorConfig)
	if err != nil {
		log.Panicf("failed to init monitor lib, error=(%v)", err)
	}
	monitor.Init(&monitorConfig)

	// 初始化自定义错误
	errc.Init()

	// 初始化 billing
	var billingConfig billing.Config
	config.Get("billing", &billingConfig)
	logger.Debug("billing config: %+v", billingConfig)
	if err != nil {
		log.Panicf("failed to init billing sdk, error=(%v)", err)
	}
	billing.Init(&billingConfig)

	// 初始化 flow-center
	var flowConfig flowc.Config
	config.Get("flow_center", &flowConfig)
	if err != nil {
		log.Panicf("failed to init flow center, error=(%v)", err)
	}
	flowc.Init(&flowConfig)

	// 初始化 ras
	var rasConfig ras.Config
	config.Get("ras", &rasConfig)
	logger.Debug("ras config: %+v", rasConfig)
	if err != nil {
		log.Panicf("failed to init ras sdk, error=(%v)", err)
	}
	ras.Init(&rasConfig)

	// MOCK DB
	mysql.MockInit(t)

	// MOCK HTTP
	httpmock.Activate()
}

// MOCK DB Data Cluster
func MockCluster(name string) *ent.Cluster {
	db, err := mysql.Database()
	if err != nil {
		log.Panicf("failed to get mock mysql client, error=(%v)", err)
	}

	c, err := db.Cluster.Query().Where(cluster.NameEQ(name)).First(context.Background())
	if err != nil {
		c, err = db.Cluster.Create().
			SetName(name).SetAlias(name).SetType("enterprise").SetDocker(0).
			SetArea("public").SetProductLine("siod-kafka").SetSubsystem(strings.ReplaceAll(name, "_", "-")).
			SetStorageSize(10).SetShardNum(2).SetMaxmemoryPolicy("novication").
			SetProxyNum(4).SetSmart("smart.group." + strings.ReplaceAll(name, "_", "-") + "-router.siod-redis").
			SetPort(8001).SetPassword("123").SetRedisVersion("4.0").SetProxyVersion("a326").
			SetLevel(1).SetDepartment("系统运维部").SetDepartmentID(1000000037).SetOwner("jiayiming_dxm").
			SetFeishuID("oc_965fe40ad53c0e06cf0f7b795ccacfe1").Save(context.Background())
		if err != nil {
			log.Panicf("failed to get mock mysql client, error=(%v)", err)
		}
	}

	return c
}

// MOCK DB Data Task with a Stage
func MockTaskWithStage(objC *ent.Cluster) (*ent.Task, *ent.Stage) {
	db, err := mysql.Database()
	if err != nil {
		log.Panicf("failed to get mock mysql client, error=(%v)", err)
	}

	objT, err := db.Task.Query().Where(task.ClusterID(objC.ID)).First(context.Background())
	if err != nil {
		objT = db.Task.Create().
			SetClusterID(objC.ID).SetClusterName(objC.Name).
			SetName("test").SetApplicant("tester").SetType("test").SetDescription("test").
			SaveX(context.Background())
	}

	objS := MockStage(objT)

	return objT, objS
}

// MOCK DB Data Stage
func MockStage(objT *ent.Task) *ent.Stage {
	db, err := mysql.Database()
	if err != nil {
		log.Panicf("failed to get mock mysql client, error=(%v)", err)
	}

	seq, err := db.Stage.Query().Where(stage.TaskID(objT.ID)).Count(context.Background())
	if err != nil {
		log.Panicf("failed to count stage, error=(%v)", err)
	}

	objS, err := db.Stage.Create().
		SetTask(objT).SetClusterName(objT.ClusterName).
		SetName("test").SetType("test").SetSequence(seq + 1).SetParameter("{}").
		Save(context.Background())
	if err != nil {
		log.Panicf("failed to mock stage, error=(%v)", err)
	}

	return objS
}
