package env

import (
	"log"

	"dt-common/apptree"
	"dt-common/bnsgroup"
	"dt-common/logger"
	"dt-common/mysql"
	"dt-common/noah"
	"dt-common/user-center/authc"
	"dt-common/user-center/flowc"
	"redis-xweb/config"
	"redis-xweb/library/billing"
	"redis-xweb/library/errc"
	"redis-xweb/library/monitor"
	"redis-xweb/library/ras"
)

func Init(configPath ...string) {
	// 初始化配置
	err := config.Init(configPath...)
	if err != nil {
		log.Panicf("failed to init config, error=(%v)", err)
	}

	// 初始化 Logger
	var logConfig logger.Config
	err = config.Get("logger", &logConfig)
	if err != nil {
		log.Panicf("failed to init logger, error=(%v)", err)
	}
	logger.Init(&logConfig)

	// 初始化 db
	var dbConfig mysql.Config
	err = config.Get("db", &dbConfig)
	// dbConfig.AutoMigrate = true // 测试环境创建库表用，线上要保持关闭状态
	if err != nil {
		log.Panicf("failed to init mysql, error=(%v)", err)
	}
	mysql.Init(&dbConfig)

	// 初始化 noah
	var noahConfig noah.Config
	config.Get("noah", &noahConfig)
	if err != nil {
		log.Panicf("failed to init noah sdk, error=(%v)", err)
	}
	noah.Init(&noahConfig)

	// 初始化 sre-noah
	var sreNoahConfig apptree.Config
	config.Get("sre_noah", &sreNoahConfig)
	if err != nil {
		log.Panicf("failed to init sa, error=(%v)", err)
	}
	apptree.Init(&sreNoahConfig)

	// 初始化bns-group
	var bnsGroupConfig bnsgroup.Config
	config.Get("bns_group", &bnsGroupConfig)
	if err != nil {
		log.Panicf("failed to init bns-group, error=(%v)", err)
	}
	bnsgroup.Init(&bnsGroupConfig)

	// 初始化 auth-center
	var authConfig authc.Config
	config.Get("auth_center", &authConfig)
	if err != nil {
		log.Panicf("failed to init auth center, error=(%v)", err)
	}
	authc.Init(&authConfig)

	// 初始化 flow-center
	var flowConfig flowc.Config
	config.Get("flow_center", &flowConfig)
	if err != nil {
		log.Panicf("failed to init flow center, error=(%v)", err)
	}
	flowc.Init(&flowConfig)

	// 初始化 billing
	var billingConfig billing.Config
	config.Get("billing", &billingConfig)
	logger.Debug("billing config: %+v", billingConfig)
	if err != nil {
		log.Panicf("failed to init billing sdk, error=(%v)", err)
	}
	billing.Init(&billingConfig)

	// 初始化 ras
	var rasConfig ras.Config
	config.Get("ras", &rasConfig)
	logger.Debug("ras config: %+v", rasConfig)
	if err != nil {
		log.Panicf("failed to init ras sdk, error=(%v)", err)
	}
	ras.Init(&rasConfig)

	// 初始化 监控配置
	var monitorConfig monitor.Config
	config.Get("monitor", &monitorConfig)
	if err != nil {
		log.Panicf("failed to init monitor lib, error=(%v)", err)
	}
	monitor.Init(&monitorConfig)

	// 初始化自定义错误
	errc.Init()
}
