package env

import (
	"log"

	"dt-common/logger"
	"dt-common/redisc"
	"redis-agent/config"
	"redis-agent/library/errc"
)

func Init(configPath ...string) {
	// 初始化配置
	err := config.Init(configPath...)
	if err != nil {
		log.Panicf("fail to init config, err: %s", err.<PERSON>rror())
	}

	// 初始化 Logger
	var logConfig logger.Config
	err = config.Get("logger", &logConfig)
	if err != nil {
		log.Panicf("fail to init logger, err: %s", err.Error())
	}
	logger.Init(&logConfig)

	// 初始化 redisClient
	var redisConfig redisc.Config
	err = config.Get("redis", &redisConfig)
	if err != nil {
		log.Panicf("Failed to init logger, error=(%v)", err)
	}
	redisc.Init(&redisConfig)

	// 自定义错误码
	errc.Init()
}
