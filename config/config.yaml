application:
  # debug:开发环境 test:测试环境 release:线上环境
  mode: test
  # 服务端口号
  port: 8433
  # 接口鉴权token
  token: dxm_test_123456

logger:
  # 日志等级 [error, warn, info, debug] 默认info
  level: debug
  # 是否打印到控制台 [true, false]
  log_console: true
  # 日志存放路径
  path: log/redis-agent.log
  # 在进行切割之前，日志文件的最大大小（以MB为单位）
  max_size: 100
  # 保留旧文件的最大天数
  max_age: 30
  # 保留旧文件的最大个数
  max_backups: 10

redis:
  autoCloseAfterSeconds: 30
  maxRetries: 3
  dialTimeout: 50
  writeTimeout: 2000
  readTimeout: 150

env:
  local_ip: 127.0.0.1
  # 进程根路径
  proxy_root: /home/<USER>/local/router_server${port}
  redis_root: /home/<USER>/local/redis${port}
  sentinel_root: /home/<USER>/local/sentinel${port}
  # 白名单路径
  proxy_whitelist: /home/<USER>/local/router_server${port}/conf
  redis_whitelist: /home/<USER>/local/redis${port}/conf
  sentinel_whitelist: /home/<USER>/local/sentinel${port}/conf
  # 控制脚本路径
  proxy_script: /home/<USER>/local/script/nutcracker_service${port}.sh
  redis_script: /home/<USER>/local/script/redis_service.sh
  sentinel_script: /home/<USER>/local/script/sentinel_service.sh

# 容器路径
# env:
#   local_ip: 127.0.0.1
#   # 进程根路径
#   proxy_root: /home/<USER>/local/router
#   redis_root: /home/<USER>/local/redis
#   sentinel_root: /home/<USER>/local/sentinel
#   # 白名单路径
#   proxy_whitelist: /home/<USER>/whitelist
#   redis_whitelist: /home/<USER>/whitelist
#   sentinel_whitelist: /home/<USER>/whitelist
#   # 控制脚本路径
#   proxy_script: /home/<USER>/local/router/nutcracker_service.sh
#   redis_script: /home/<USER>/local/redis/redis_service.sh
#   sentinel_script: /home/<USER>/local/sentinel/sentinel_service.sh
