application:
  # debug:开发环境 test:测试环境 release:线上环境
  mode: debug
  # 服务端口号
  port: 8274
  # 回调token
  token: dxm_xweb_123456
  # 前端地址
  fe_host: http://cloud-test.duxiaoman-int.com/redisService

ras:
  manager:
    url: http://************:8405
    token: dxm_manager_123456
  cmanager:
    url: http://************:8811/redis-cmanager
    token: dxm_cmanager_123456
  timeout: 5

db:
  host: ************
  port: 8846
  name: r3
  user: normal
  password: xjHD6Ph38xsDYK3A
  # 是否进入debug模式，打印所有执行的sql语句
  orm_debug: true
  # 最大连接空置
  max_idle_conns: 10
  # 设置最大连接数
  max_open_conns: 100
  single_query_timeout: 10

logger:
  # 日志等级 [error, warn, info, debug] 默认info
  level: debug
  # 是否打印到控制台 [true, false]
  log_console: false
  # 日志存放路径
  path: log/redis-xweb.log
  # 在进行切割之前，日志文件的最大大小（以MB为单位）
  max_size: 20
  # 保留旧文件的最大天数
  max_age: 30
  # 保留旧文件的最大个数
  max_backups: 25

auth_center:
  server: http://*************:8322/auth-center
  access_key: siod-sc-0ZeI4iDTF2mnJ5L9
  secret_key: S3g2LoXPOqjsyWzcGyjFsYAw5ngEf5iM

flow_center:
  server: http://cloud-test.duxiaoman-int.com/flow-center
  token: redis-xweb-token

noah:
  url: http://noahkj.dxmkj01-int.com:8050
  token: fb63f513ebf89c4040e2cb9cdf7b10f4
  requestWaitTime: 1000

sre_noah:
  url: http://sc.duxiaoman-int.com/sre-service
  token: 07a8a8bf-41a1-475e-be8c-73784569d7e0

billing:
  # url: ra-billing-test.duxiaoman-int.com
  # url: 10.32.140.215:8700
  url: 10.32.141.8:8800
  access_key: siod-sc-4OEltHYmYh7UOaH4
  secret_key: 8hMnU8LS1GiyM9Wdgza1IRyVPPInI7jW
  resource_id: 100049
  proxy_combo_code: 491488382
  redis_combo_code: 491488383
  memory_combo_code: 491488384
  sentinel_combo_code: 491488385
  old_combo_code: 100049985621

monitor:
  critical: dba_redis_blind
  major: dba_redis_blind
  warning: dba_redis_blind
  notice: dba_redis_blind
  agent_port: 8433

# 集群部署区域，科技云：基金隔笼、保险专区，小贷云：征信专区
area:
  public: 公共区
  fund: 基金隔笼
  insurance: 保险专区
  credit: 征信专区

# 报警机器人
hi:
  token: 5e2a3937c12017b25b7a42a5dacce80f
  # 报警群组ID
  defaultId: oc_965fe40ad53c0e06cf0f7b795ccacfe1
  defaultUsers:
    - jiayiming_dxm
    - huzhaoyun_dxm

bns_group:
  url: http://cloud-test.duxiaoman-int.com/bns-group
  token: JKLKJUYTGFRN
