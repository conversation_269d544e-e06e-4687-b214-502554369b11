application:
  # debug:开发环境 test:测试环境 release:线上环境
  mode: debug
  # 服务端口号
  port: 8811
  # 接口鉴权token
  token: dxm_cmanager_123456
  # 应用部署的bns
  bns: redis-cmanager.siod-kafka

redis_xweb:
  host: ************
  port: 8274
  token: dxm_xweb_123456
  timeout: 2
  maxRetryTimes: 3

redis_agent:
  token: dxm_test_123456
  port: 8433
  timeout: 5

inspection:
  memory_sync:
    interval: 300 # 内存数据同步周期
  proxy_prober:
    meta_interval: 600 # 自动屏蔽元数据更新周期
    interval: 10 # 自动屏蔽周期
    concurrency: 100 # 自动屏蔽并发数
    keep_enabled_num: 0.5 # 最小保留实例数（小于1按百分比算，否则按整数算）
  cluster_reconcile:
    interval: 30 # 间隔时间：s
    concurrency: 20 # 并发渲染集群数
    step_retry_times: 5 # 集群渲染阶段重试次数

db:
  host: ************
  port: 8846
  name: r3
  user: normal
  password: xjHD6Ph38xsDYK3A
  # 是否进入debug模式，打印所有执行的sql语句
  orm_debug: true
  # 最大连接空置时间
  max_idle_conns: 10
  # 设置最大连接数
  max_open_conns: 100
  # 设置超时时间
  single_query_timeout: 30

logger:
  # 日志等级 [error, warn, info, debug] 默认info
  level: debug
  # 是否打印到控制台 [true, false]
  log_console: false
  # 日志存放路径
  path: log/redis-cmanager.log
  # 在进行切割之前，日志文件的最大大小（以MB为单位）
  max_size: 20
  # 保留旧文件的最大天数
  max_age: 30
  # 保留旧文件的最大个数
  max_backups: 25

cloud:
  url: http://sc.duxiaoman-int.com/sre-service
  token: 07a8a8bf-41a1-475e-be8c-73784569d7e0

noah:
  url: http://noah.duxiaoman-int.com
  token: fb63f513ebf89c4040e2cb9cdf7b10f4
  requestWaitTime: 1000

redis:
  autoCloseAfterSeconds: 30
  maxRetries: 3
  dialTimeout: 50
  writeTimeout: 2000
  readTimeout: 150

# 报警机器人
hi:
  token: 5e2a3937c12017b25b7a42a5dacce80f
  defaultId: oc_965fe40ad53c0e06cf0f7b795ccacfe1
  defaultUsers:
    - jiayiming_dxm
    - huzhaoyun_dxm

billing:
  url: *************:8700
  access_key: siod-sc-4OEltHYmYh7UOaH4
  secret_key: 8hMnU8LS1GiyM9Wdgza1IRyVPPInI7jW
  resource_id: 100056 # fec资源ID

fec:
  host: http://*************:8536
  token: fec-manager_yujiazheng_dxm_4e73a1f4
  ak: siod-sc-0ZeI4iDTF2mnJ5L9
  sk: S3g2LoXPOqjsyWzcGyjFsYAw5ngEf5iM

event_center:
  host: http://*************:8899

renderer:
  image:
    redis: r.duxiaoman-int.com/siod_redis/redis-docker
    sentinel: r.duxiaoman-int.com/siod_redis/sentinel-docker
    router: r.duxiaoman-int.com/siod_redis/router-docker
    version: v1.0.0-test
  idc:
    hba: ZZJG
    hbb: ZZJG
    hbc: ZZJG
  safe_region: 测试Internal
  app:
    product_line: siod-redis
    department_id: 1000000037
    department: 系统运维部
    owner: jiayiming_dxm
