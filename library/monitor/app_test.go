package monitor

import (
	"regexp"
	"testing"

	"github.com/jarcoal/httpmock"
)

var (
	app = Proxy{
		App: &App{
			ProductName: "siod-mysql",
			AppName:     "redis-cni-test-router",
			Port:        8001,
		},
		ClusterName: "redis-migration-test6",
	}
)

func TestClearMonitors(t *testing.T) {
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct{}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, error)
	}{
		{
			name: "test1: process get failed",
			before: func() {
				httpmock.Reset()
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+/portTasks`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": []}`))
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/logtask/api/v1/products/[\w\W]+/apps/[\w\W]+/logTaskConfig`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": []}`))
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+/logCollectTasks`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": []}`))
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+/execCollectTasks`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": []}`))
			},
			wantErr: true,
		},
		{
			name: "test2: process delete failed",
			before: func() {
				httpmock.Reset()
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+/processCollectTasks`),
					httpmock.NewStringResponder(200, "{\"success\": true, \"message\": \"\", \"data\": [{\"id\":1,\"name\":\"pt\"}]}"))
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+/portTasks`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": []}`))
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/logtask/api/v1/products/[\w\W]+/apps/[\w\W]+/logTaskConfig`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": []}`))
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+/logCollectTasks`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": []}`))
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+/execCollectTasks`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": []}`))
			},
			wantErr: true,
		},
		{
			name: "test3: process delete success",
			before: func() {
				httpmock.Reset()
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+/processCollectTasks`),
					httpmock.NewStringResponder(200, "{\"success\": true, \"message\": \"\", \"data\": [{\"id\":1,\"name\":\"pt\"}]}"))
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+/portTasks`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": []}`))
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/logtask/api/v1/products/[\w\W]+/apps/[\w\W]+/logTaskConfig`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": []}`))
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+/logCollectTasks`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": []}`))
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+/execCollectTasks`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": []}`))

				httpmock.RegisterRegexpResponder("DELETE", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+/processCollectTasks`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": "{}"}`))
			},
			wantErr: false,
		},
		{
			name: "test4: port get failed",
			before: func() {
				httpmock.Reset()
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+/processCollectTasks`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": "{}"}`))
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/logtask/api/v1/products/[\w\W]+/apps/[\w\W]+/logTaskConfig`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": "{}"}`))
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+/logCollectTasks`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": "{}"}`))
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+/execCollectTasks`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": "{}"}`))
			},
			wantErr: true,
		},
		{
			name: "test5: port delete failed",
			before: func() {
				httpmock.Reset()
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+/processCollectTasks`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": []}`))
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+/portTasks`),
					httpmock.NewStringResponder(200, "{\"success\": true, \"message\": \"\", \"data\": [{\"id\":1,\"name\":\"pt\"}]}"))
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/logtask/api/v1/products/[\w\W]+/apps/[\w\W]+/logTaskConfig`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": []}`))
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+/logCollectTasks`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": []}`))
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+/execCollectTasks`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": []}`))
			},
			wantErr: true,
		},
		{
			name: "test6: port delete success",
			before: func() {
				httpmock.Reset()
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+/processCollectTasks`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": []}`))
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+/portTasks`),
					httpmock.NewStringResponder(200, "{\"success\": true, \"message\": \"\", \"data\": [{\"id\":1,\"name\":\"pt\"}]}"))
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/logtask/api/v1/products/[\w\W]+/apps/[\w\W]+/logTaskConfig`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": []}`))
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+/logCollectTasks`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": []}`))
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+/execCollectTasks`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": []}`))

				httpmock.RegisterRegexpResponder("DELETE", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+/portTasks`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": "{}"}`))
			},
			wantErr: false,
		},
		{
			name: "test7: log get failed",
			before: func() {
				httpmock.Reset()
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+/processCollectTasks`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": "{}"}`))
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+/portTasks`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": "{}"}`))
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+/logCollectTasks`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": "{}"}`))
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+/execCollectTasks`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": "{}"}`))
			},
			wantErr: true,
		},
		{
			name: "test8: log delete failed",
			before: func() {
				httpmock.Reset()
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+/processCollectTasks`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": []}`))
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+/portTasks`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": []}`))
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/logtask/api/v1/products/[\w\W]+/apps/[\w\W]+/logTaskConfig`),
					httpmock.NewStringResponder(200, "{\"success\": true, \"message\": \"\", \"data\": [{\"id\":1,\"name\":\"pt\"}]}"))
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+/logCollectTasks`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": []}`))
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+/execCollectTasks`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": []}`))
			},
			wantErr: true,
		},
		{
			name: "test9: log delete success",
			before: func() {
				httpmock.Reset()
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+/processCollectTasks`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": []}`))
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+/portTasks`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": []}`))
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/logtask/api/v1/products/[\w\W]+/apps/[\w\W]+/logTaskConfig`),
					httpmock.NewStringResponder(200, "{\"success\": true, \"message\": \"\", \"data\": [{\"id\":1,\"name\":\"pt\"}]}"))
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+/logCollectTasks`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": []}`))
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+/execCollectTasks`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": []}`))

				httpmock.RegisterRegexpResponder("DELETE", regexp.MustCompile(`://[\w\W]+/logtask/api/v1/products/[\w\W]+/apps/[\w\W]+/logTaskConfig`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": "{}"}`))
			},
			wantErr: false,
		},
		{
			name: "test10: logCollect get failed",
			before: func() {
				httpmock.Reset()
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+/processCollectTasks`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": "{}"}`))
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+/portTasks`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": "{}"}`))
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/logtask/api/v1/products/[\w\W]+/apps/[\w\W]+/logTaskConfig`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": "{}"}`))
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+/execCollectTasks`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": "{}"}`))
			},
			wantErr: true,
		},
		{
			name: "test11: logCollect delete failed",
			before: func() {
				httpmock.Reset()
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+/processCollectTasks`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": []}`))
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+/portTasks`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": []}`))
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/logtask/api/v1/products/[\w\W]+/apps/[\w\W]+/logTaskConfig`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": []}`))
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+/logCollectTasks`),
					httpmock.NewStringResponder(200, "{\"success\": true, \"message\": \"\", \"data\": [{\"id\":1,\"name\":\"pt\"}]}"))
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+/execCollectTasks`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": []}`))
			},
			wantErr: true,
		},
		{
			name: "test12: logCollect delete success",
			before: func() {
				httpmock.Reset()
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+/processCollectTasks`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": []}`))
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+/portTasks`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": []}`))
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/logtask/api/v1/products/[\w\W]+/apps/[\w\W]+/logTaskConfig`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": []}`))
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+/logCollectTasks`),
					httpmock.NewStringResponder(200, "{\"success\": true, \"message\": \"\", \"data\": [{\"id\":1,\"name\":\"pt\"}]}"))
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+/execCollectTasks`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": []}`))

				httpmock.RegisterRegexpResponder("DELETE", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+/logCollectTasks`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": "{}"}`))
			},
			wantErr: false,
		},
		{
			name: "test13: exec get failed",
			before: func() {
				httpmock.Reset()
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+/processCollectTasks`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": "{}"}`))
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+/portTasks`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": "{}"}`))
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/logtask/api/v1/products/[\w\W]+/apps/[\w\W]+/logTaskConfig`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": "{}"}`))
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+/logCollectTasks`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": "{}"}`))
			},
			wantErr: true,
		},
		{
			name: "test14: exec delete failed",
			before: func() {
				httpmock.Reset()
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+/processCollectTasks`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": []}`))
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+/portTasks`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": []}`))
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/logtask/api/v1/products/[\w\W]+/apps/[\w\W]+/logTaskConfig`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": []}`))
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+/logCollectTasks`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": []}`))
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+/execCollectTasks`),
					httpmock.NewStringResponder(200, "{\"success\": true, \"message\": \"\", \"data\": [{\"id\":1,\"name\":\"pt\"}]}"))
			},
			wantErr: true,
		},
		{
			name: "test15: exec delete success",
			before: func() {
				httpmock.Reset()
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+/processCollectTasks`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": []}`))
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+/portTasks`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": []}`))
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/logtask/api/v1/products/[\w\W]+/apps/[\w\W]+/logTaskConfig`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": []}`))
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+/logCollectTasks`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": []}`))
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+/execCollectTasks`),
					httpmock.NewStringResponder(200, "{\"success\": true, \"message\": \"\", \"data\": [{\"id\":1,\"name\":\"pt\"}]}"))

				httpmock.RegisterRegexpResponder("DELETE", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+/execCollectTasks`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": "{}"}`))
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := app.ClearMonitors()
			if (err != nil) != tt.wantErr {
				t.Errorf("app.ClearMonitors() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, err)
			}
		})
	}
}

func TestClearAlarms(t *testing.T) {
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct{}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, error)
	}{
		{
			name:    "test1: getPolicyList failed",
			wantErr: true,
		},
		{
			name: "test2: delete failed",
			before: func() {
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+/policies`),
					httpmock.NewStringResponder(200, "{\"success\":true,\"message\":\"\",\"data\":[{\"id\":1,\"name\":\"test\"}]}"))
			},
			wantErr: true,
		},
		{
			name: "test3: success",
			before: func() {
				httpmock.RegisterRegexpResponder("DELETE", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+/policies`),
					httpmock.NewStringResponder(200, "{\"success\":true,\"message\":\"\",\"data\":{}}"))
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := proxy.ClearAlarms()
			if (err != nil) != tt.wantErr {
				t.Errorf("proxy.ClearAlarms() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, err)
			}
		})
	}
}
