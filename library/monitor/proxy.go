package monitor

import (
	"fmt"
	"strconv"
)

// Proxy模块监控管理
type Proxy struct {
	*App
	ClusterName string
}

// 监控任务管理
func (o *Proxy) Monitors(opt string) error {
	o.logCollectRexpMap = map[string]*map[string]string{
		"proxy": {
			"epoll_wait_fail":        "(epoll wait .* timeout)",
			"read_data_fail":         "(recv on sd.failed:)",
			"write_data_fail":        "(sendv on sd .* failed)",
			"unsupported_command":    "(parsed unsupported command)",
			"key_len_exceed":         "(greater than or equal to maximum)",
			"fd_exceed":              "(with max fds)",
			"client_exceed":          "(client.*exceed limit )",
			"server_reply_error":     "(with unknown type)",
			"reject_slave":           "(reject slave)",
			"sentinel_disconnection": "(force to close sentinel connection)",
			"forbidden":              "(ip .* is forbidden. not in whitelist)",
			"malloc_fail":            "(malloc .* failed)",
			"success_switch":         "(success switch)",
			"switch_failed":          "(switch failed)",
			"cmd_graylist":           "(recv a cmd that is not whitelisted)",
			"Too_many_open_files":    "(Too many open files)",
			"slow":                   "(SLOW_LOG_1)",
			"whitelist_proc_exit":    "(Bns whitelist exited unexpectly)",
		},
		"whitelist": {
			"bns_not_exist":   "(won't enter PROTECT MODE)",
			"protect_flag_ON": "(protect_flag is ON)",
			"less_IPs":        "(We get less IPs in the whitelist now so we postpone)",
		},
	}

	// 8个监控任务
	tasks := [][]string{
		{"process", "proxy", "/home/<USER>/local/router/bin/nutcracker", "proxy进程监控", "10"},
		{"process", "agent", "/home/<USER>/redis-agent/cmd/redis-agent", "redis-agent进程监控", "10"},

		{"port", fmt.Sprintf("proxy_%d", o.Port), strconv.Itoa(o.Port), "端口监控", "10"},
		{"port", fmt.Sprintf("agent_%d", agentPort), strconv.Itoa(agentPort), "redis-agent端口监控", "10"},

		{"logCollect", "proxy", "/home/<USER>/local/router/log/nutcracker.log", "进程日志采集", "10", "proxy"},
		{"logCollect", "whitelist", "/home/<USER>/local/router/log/whitelist.log", "白名单日志采集", "60", "whitelist"}, // 白名单60s一周期，避免当出现bns_not_exist时不断报警

		{"exec", "proxy", "/home/<USER>/redis_monitor/proxy_status.py", "运行数据采集", "10"},
		{"exec", "core", "/home/<USER>/redis_monitor/out_core.sh", "out core监控", "10"},
	}

	switch opt {
	case "add":
		return o.addMonitors(&tasks)
	case "del":
		return o.delMonitors(&tasks)
	default:
		return fmt.Errorf("opt should be one of add or del")
	}
}

// 添加报警策略
// Critical需要立即处理，马上就死或已经死了
// Major需要尽快处理，不处理有可能会死
// Warning需要关注风险，短期不处理问题不大
// Notice通知到即可
func (o *Proxy) Alarms(opt string) error {
	alarms := [][]string{
		// ** 容器监控 **//
		{"HOST", "critical", "machine_down", "machine_down", "STATUS_PING{_statistic='avg'}==0", "3", "3"},
		{"HOST", "critical", "machine_mem_req_100", "machine_mem_req_100", "MEM_USED_PERCENT{_statistic='avg'}>=100", "3", "3"},

		// ** 进程监控 **//
		{"INSTANCE", "critical", "cpu_usage_80", "cpu_usage_80", "process.proxy.cpu_usage>0.8", "3", "3"},
		{"INSTANCE", "major", "cpu_usage_60", "cpu_usage_60", "process.proxy.cpu_usage>0.6", "30", "30"},

		// ** 端口监控 **//
		{"INSTANCE", "critical", fmt.Sprintf("proxy_%d_dead", o.Port), fmt.Sprintf("proxy_%d_dead", o.Port), fmt.Sprintf("port.proxy_%d.status!='ok'", o.Port)},
		{"INSTANCE", "critical", fmt.Sprintf("agent_%d_dead", agentPort), fmt.Sprintf("agent_%d_dead", agentPort), fmt.Sprintf("port.agent_%d.status!='ok'", agentPort)},

		// ** 日志监控 **//
		{"INSTANCE", "critical", "malloc_fail", "malloc_fail", "log.proxy.malloc_fail{_statistic='count'}>0"},
		{"INSTANCE", "critical", "server_reply_error", "server_reply_error", "log.proxy.server_reply_error{_statistic='count'}>0"},
		{"INSTANCE", "critical", "switch_failed", "switch_failed", "log.proxy.switch_failed{_statistic='count'}>0"},
		{"INSTANCE", "critical", "protect_flag_ON", "protect_flag_ON", "log.whitelist.protect_flag_ON{_statistic='count'}>0"},
		{"INSTANCE", "major", "sentinel_disconnection", "sentinel_disconnection", "log.proxy.sentinel_disconnection{_statistic='count'}>0"},
		{"INSTANCE", "major", "epoll_fail", "epoll_fail", "log.proxy.epoll_wait_fail{_statistic='count'}>0", "2", "2"},
		{"INSTANCE", "warning", "whitelist_proc_exit", "whitelist_proc_exit", "log.proxy.whitelist_proc_exit{_statistic='count'}>0"},
		{"INSTANCE", "warning", "ip_forbidden", "ip_forbidden", "log.proxy.forbidden{_statistic='count'}>10"},
		{"INSTANCE", "warning", "eject_slave", "eject_slave", "log.proxy.reject_slave{_statistic='count'}>0"},
		{"INSTANCE", "warning", "slow", "slow", "log.proxy.slow{_statistic='count'}>=10", "3", "3"},
		{"INSTANCE", "warning", "bns_not_exist", "bns_not_exist", "log.whitelist.bns_not_exist{_statistic='count'}>0"},
		{"INSTANCE", "notice", "success_switch", "success_switch", "log.proxy.success_switch{_statistic='count'}>0"},

		// ** 自定义监控 **//
		{"INSTANCE", "critical", "out_core", "out_core", "exec.core.core_num{_statistic='avg'}==1"},
		{"INSTANCE", "critical", "connections_8000", "connections_8000", "exec.proxy.client_connections{_statistic='avg'}>=8000"},
		{"INSTANCE", "major", "sentinel_diff", "sentinel_diff", "exec.proxy.proxy_sentinel_master_same==1"},
		
	}

	switch opt {
	case "add":
		return o.addAlarms(&alarms)
	case "del":
		return o.delAlarms(&alarms)
	default:
		return fmt.Errorf("opt should be one of add or del")
	}
}
