package monitor

import (
	"dt-common/noah"
)

type M interface {
	BNS() string
	GetPort() int
	RunMessage() string
	Monitors(string) error
	Alarms(string) error
	ClearMonitors(...string) error
	ClearAlarms(...string) error
}

var (
	levelActionMap = map[noah.PolicyLevel]string{} // 报警动作与报警级别映射map
	agentPort      int
)

type Config struct {
	Critical  string `yaml:"critical"`
	Major     string `yaml:"major"`
	Warning   string `yaml:"warning"`
	Notice    string `yaml:"notice"`
	AgentPort int    `yaml:"agent_port"`
}

// 配置初始化
func Init(cfg *Config) {
	levelActionMap[noah.PolicyLevelCritical] = cfg.Critical
	levelActionMap[noah.PolicyLevelMajor] = cfg.Major
	levelActionMap[noah.PolicyLevelWarning] = cfg.Warning
	levelActionMap[noah.PolicyLevelNotice] = cfg.Notice

	agentPort = cfg.AgentPort
}
