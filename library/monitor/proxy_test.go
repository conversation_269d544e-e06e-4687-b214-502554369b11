package monitor

import (
	"regexp"
	"testing"

	"github.com/jarcoal/httpmock"
)

var (
	proxy = Proxy{
		App: &App{
			ProductName: "siod-mysql",
			AppName:     "redis-cni-test-router",
			Port:        8001,
		},
		ClusterName: "redis-migration-test6",
	}
)

func TestProxyMonitor(t *testing.T) {
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		opt string
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, error)
	}{
		{
			name: "test1: add monitor",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+Tasks`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": "{}"}`))
			},
			args: args{
				opt: "add",
			},
			wantErr: false,
		},
		{
			name: "test2: del monitor",
			before: func() {
				httpmock.RegisterRegexpResponder("DELETE", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+Tasks`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": "{}"}`))
			},
			args: args{
				opt: "del",
			},
			wantErr: false,
		},
		{
			name: "test3: switch default",
			args: args{
				opt: "aaa",
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := proxy.Monitors(tt.args.opt)
			if (err != nil) != tt.wantErr {
				t.Errorf("proxy.Monitors() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, err)
			}
		})
	}
}

func TestProxyAlarms(t *testing.T) {
	httpmock.Activate()
defer httpmock.DeactivateAndReset()

	type args struct {
		opt string
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, error)
	}{
		{
			name: "test1: add Alarms",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+/policies`),
					httpmock.NewStringResponder(200, "{\"success\":true,\"message\":\"\",\"data\":{\"id\":1}}"))
			},
			args: args{
				opt: "add",
			},
			wantErr: false,
		},
		{
			name: "test2: del Alarms",
			before: func() {
				httpmock.RegisterRegexpResponder("DELETE", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+/policies`),
					httpmock.NewStringResponder(200, "{\"success\":true,\"message\":\"\",\"data\":{\"id\":1}}"))
			},
			args: args{
				opt: "del",
			},
			wantErr: false,
		},
		{
			name: "test3: switch default",
			args: args{
				opt: "aaa",
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := proxy.Alarms(tt.args.opt)
			if (err != nil) != tt.wantErr {
				t.Errorf("proxy.Alarmss() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, err)
			}
		})
	}
}
