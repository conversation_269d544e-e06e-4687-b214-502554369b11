package monitor

import (
	"context"
	"fmt"
	"strconv"
	"strings"

	"golang.org/x/sync/errgroup"

	"dt-common/noah"
)

// 额外策略参数
type DimensionParams struct {
	Type       string
	Dimensions *[]noah.PolicyDimension
}

type Service string

const (
	SERVICE_PROXY    Service = "router"
	SERVICE_REDIS    Service = "redis"
	SERVICE_SENTINEL Service = "sentinel"

	// 物理网络容器监控脚本
	DOCKER_PROC     string = "/home/<USER>/redis_monitor/docker_proc "
	DOCKER_RUN      string = "/home/<USER>/redis_monitor/docker_run "
	DOCKER_RESOURCE string = "/home/<USER>/redis_monitor/docker_container.py"
)

type App struct {
	ProductName string
	AppName     string
	Port        int
	message     string

	logCollectRexpMap map[string]*map[string]string // 单维度日志监控字符串匹配规则，一个key就是一个日志监控任务
	dimensions        map[string]*DimensionParams   // 维度，1、生效范围：仅PolicyType为"INSTANCE"时有效 2、多维度日志
}

// 获取当前的BNS
func (a *App) BNS() string {
	return a.AppName + "." + a.ProductName
}

// 获取端口
func (a *App) GetPort() int {
	return a.Port
}

// 获取执行日志
func (a *App) RunMessage() string {
	return strings.TrimRight(a.message, "\n")
}

// 设置单维度日志匹配项
func (a *App) SetLogCollectRexp(name string, m *map[string]string) {
	if a.logCollectRexpMap == nil {
		a.logCollectRexpMap = map[string]*map[string]string{}
	}
	a.logCollectRexpMap[name] = m
}

// 设置维度
func (a *App) SetDimension(name string, dimension *DimensionParams) error {
	if a.dimensions == nil {
		a.dimensions = map[string]*DimensionParams{}
	}

	if _, exist := a.dimensions[name]; exist {
		return fmt.Errorf("dimension %s alreay exists", name)
	}

	a.dimensions[name] = dimension

	return nil
}

// =====================================================
//                    监 控 采 集
// =====================================================

// 组装单纬度日志监控Filter参数
// matchMetricMap map[指标名]匹配字符串
func (*App) getLogCollectTaskFilter(matchMetricMap *map[string]string) []*noah.LogCollectTaskFilter {
	result := make([]*noah.LogCollectTaskFilter, len(*matchMetricMap))
	counter := 0
	for metricName, matchStr := range *matchMetricMap {
		result[counter] = &noah.LogCollectTaskFilter{
			MatchStr: matchStr,
			Items: []*noah.LogCollectTaskMetric{
				{
					MetricName: metricName,
					Value:      "",
				},
			},
		}
		counter += 1
	}
	return result
}

// 添加监控采集任务，Task格式：[0]类型 [1]任务名 [2]目标 [3]备注 [4]采集周期 [5]日志MapKey
// 重复添加跳过
func (a *App) addMonitors(tasks *[][]string) error {
	ctx := context.Background()
	g, _ := errgroup.WithContext(ctx)

	a.message = "Add tasks...\n"
	for _, task := range *tasks {
		var module, name, content, comment, cycleStr, logKey string
		switch len(task) {
		case 6:
			logKey = task[5]
			fallthrough
		case 5:
			module, name, content, comment, cycleStr = task[0], task[1], task[2], task[3], task[4]
		default:
			return fmt.Errorf("wrong number of monitor task, task=%v", task)
		}

		cycle, err := strconv.Atoi(cycleStr)
		if err != nil {
			return err
		}

		g.Go(func() error {
			var err error
			switch module {
			case "process":
				err = noah.CreateProcessTask(&noah.ProcessTask{
					ProductName: a.ProductName,
					AppName:     a.AppName,
					Name:        name,
					Target:      content,
					Comment:     comment,
				})
			case "port":
				var port int
				port, err = strconv.Atoi(content)
				if err != nil {
					err = fmt.Errorf("port is not a number")
				} else {
					err = noah.CreatePortTask(&noah.PortTask{
						ProductName: a.ProductName,
						AppName:     a.AppName,
						Name:        name,
						Port:        port,
						Comment:     comment,
					})
				}
			// case "log":
			// 	err = noah.CreateLogTask(&noah.LogTask{
			// 		ProductName: a.ProductName,
			// 		AppName:     a.AppName,
			// 		Name:        name,
			// 		Alias:       name,
			// 		Target:      content,
			// 		Cycle:       cycle,
			// 		Params:      a.logParamsMap[logMathcMapKey].LogTaskParams,
			// 		Example:     []string{a.logParamsMap[logMathcMapKey].Example},
			// 		Comment:     comment,
			// 	})
			case "logCollect":
				if _, exist := a.logCollectRexpMap[logKey]; !exist {
					err = fmt.Errorf("logCollect task missing logKey, taskName=%s", name)
				} else {
					err = noah.CreateLogCollectTask(&noah.LogCollectTask{
						ProductName: a.ProductName,
						AppName:     a.AppName,
						Name:        name,
						Target:      content,
						Cycle:       cycle,
						Params: &noah.LogCollectTaskParams{
							SendDefaultValue: true,
							Filters:          a.getLogCollectTaskFilter(a.logCollectRexpMap[logKey]),
						},
						Comment: comment,
					})
				}
			case "exec":
				err = noah.CreateExecTask(&noah.ExecTask{
					ProductName: a.ProductName,
					AppName:     a.AppName,
					Name:        name,
					Method:      noah.ExecTaskMethodScript,
					Target:      content,
					Comment:     comment,
				})
			default:
				err = fmt.Errorf("wrong module of monitor task, module=%s", module)
			}

			if err == nil {
				a.message += fmt.Sprintf("(%s) succeed to create %s task %s\n", a.BNS(), module, name)
			} else if strings.Contains(err.Error(), "exists") || strings.Contains(err.Error(), "存在") {
				a.message += fmt.Sprintf("(%s) %s task %s alreay exists, skip\n", a.BNS(), module, name)
			} else {
				a.message += fmt.Sprintf("(%s) failed to create %s task %s, error=(%v)\n", a.BNS(), module, name, err)
				return err
			}
			return nil
		})
	}

	return g.Wait()
}

// 删除监控采集任务，Task格式：[0]类型 [1]任务名
func (a *App) delMonitors(tasks *[][]string) error {
	ctx := context.Background()
	g, _ := errgroup.WithContext(ctx)

	a.message = "Delete tasks...\n"
	for _, task := range *tasks {
		if len(task) < 2 {
			return fmt.Errorf("wrong number of monitor task, task=%v", task)
		}

		module, name := task[0], task[1]
		g.Go(func() error {
			var err error
			switch module {
			case "process":
				err = noah.DeleteProcessTask(a.ProductName, a.AppName, name)
			case "port":
				err = noah.DeletePortTask(a.ProductName, a.AppName, name)
			// case "log":
			// 	err = noah.DeleteLogTask(a.ProductName, a.AppName, name)
			case "logCollect":
				err = noah.DeleteLogCollectTask(a.ProductName, a.AppName, name)
			case "exec":
				err = noah.DeleteExecTask(a.ProductName, a.AppName, name)
			default:
				err = fmt.Errorf("wrong module of monitor task, module=%s", module)
			}

			if err == nil {
				a.message += fmt.Sprintf("(%s) succeed to delete %s task %s\n", a.BNS(), module, name)
			} else if strings.Contains(err.Error(), "exists") || strings.Contains(err.Error(), "存在") {
				a.message += fmt.Sprintf("(%s) %s task %s not exists, skip\n", a.BNS(), module, name)
			} else {
				a.message += fmt.Sprintf("(%s) failed to delete %s task %s, error=(%v)\n", a.BNS(), module, name, err)
				return err
			}
			return nil
		})
	}

	return g.Wait()
}

// 清理采集任务
func (a *App) ClearMonitors(saveTasks ...string) error {
	filter := map[string]int{}
	for _, task := range saveTasks {
		filter[task] = 1
	}

	a.message = "Clear tasks...\n"
	g, _ := errgroup.WithContext(context.Background())
	// 进程监控
	g.Go(func() (err error) {
		processTasks, err := noah.GetProcessTaskList(&noah.MonitorTaskSearchParams{
			ProductName: a.ProductName,
			AppName:     a.AppName,
		})
		if err != nil {
			a.message += fmt.Sprintf("(%s) failed to get process task\n", a.BNS())
			return err
		}
		for _, task := range *processTasks {
			// 过滤任务
			if _, ok := filter[fmt.Sprintf("process#%s", task.Name)]; ok {
				a.message += fmt.Sprintf("(%s) skip process task %s\n", a.BNS(), task.Name)
				continue
			}
			err = noah.DeleteProcessTask(a.ProductName, a.AppName, task.Name)
			if err == nil {
				a.message += fmt.Sprintf("(%s) succeed to delete process task %s\n", a.BNS(), task.Name)
			} else if strings.Contains(err.Error(), "exists") || strings.Contains(err.Error(), "存在") {
				a.message += fmt.Sprintf("(%s) process task %s not exists, skip\n", a.BNS(), task.Name)
			} else {
				a.message += fmt.Sprintf("(%s) failed to delete process task %s, error=(%v)\n", a.BNS(), task.Name, err)
				return err
			}
		}
		return nil
	})
	// 端口监控
	g.Go(func() (err error) {
		portTasks, err := noah.GetPortTaskList(&noah.MonitorTaskSearchParams{
			ProductName: a.ProductName,
			AppName:     a.AppName,
		})
		if err != nil {
			a.message += fmt.Sprintf("(%s) failed to get port task\n", a.BNS())
			return err
		}
		for _, task := range *portTasks {
			// 过滤任务
			if _, ok := filter[fmt.Sprintf("port#%s", task.Name)]; ok {
				a.message += fmt.Sprintf("(%s) skip port task %s\n", a.BNS(), task.Name)
				continue
			}

			err = noah.DeletePortTask(a.ProductName, a.AppName, task.Name)
			if err == nil {
				a.message += fmt.Sprintf("(%s) succeed to delete port task %s\n", a.BNS(), task.Name)
			} else if strings.Contains(err.Error(), "exists") || strings.Contains(err.Error(), "存在") {
				a.message += fmt.Sprintf("(%s) port task %s not exists, skip\n", a.BNS(), task.Name)
			} else {
				a.message += fmt.Sprintf("(%s) failed to delete port task %s, error=(%v)\n", a.BNS(), task.Name, err)
				return err
			}
		}
		return nil
	})
	// // 日志(多维度)监控
	// g.Go(func() (err error) {
	// 	logTasks, err := noah.GetLogTaskList(&noah.MonitorTaskSearchParams{
	// 		ProductName: a.ProductName,
	// 		AppName:     a.AppName,
	// 	})
	// 	if err != nil {
	// 		a.message += fmt.Sprintf("(%s) failed to get log task\n", a.BNS())
	// 		return err
	// 	}
	// 	for _, task := range *logTasks {
	// 		// 过滤任务
	// 		if _, ok := filter[fmt.Sprintf("log#%s", task.Name)]; ok {
	// 			a.message += fmt.Sprintf("(%s) skip log task %s\n", a.BNS(), task.Name)
	// 			continue
	// 		}

	// 		err = noah.DeleteLogTask(a.ProductName, a.AppName, task.Name)
	// 		if err == nil {
	// 			a.message += fmt.Sprintf("(%s) succeed to delete log task %s\n", a.BNS(), task.Name)
	// 		} else if strings.Contains(err.Error(), "exists") || strings.Contains(err.Error(), "存在") {
	// 			a.message += fmt.Sprintf("(%s) log task %s not exists, skip\n", a.BNS(), task.Name)
	// 		} else {
	// 			a.message += fmt.Sprintf("(%s) failed to delete log task %s, error=(%v)\n", a.BNS(), task.Name, err)
	// 			return err
	// 		}
	// 	}
	// 	return nil
	// })
	// 日志(单维度)
	g.Go(func() (err error) {
		logCollectTasks, err := noah.GetLogCollectTaskList(&noah.MonitorTaskSearchParams{
			ProductName: a.ProductName,
			AppName:     a.AppName,
		})
		if err != nil {
			a.message += fmt.Sprintf("(%s) failed to get logCollect task\n", a.BNS())
			return err
		}
		for _, task := range *logCollectTasks {
			// 过滤任务
			if _, ok := filter[fmt.Sprintf("logCollect#%s", task.Name)]; ok {
				a.message += fmt.Sprintf("(%s) skip logCollect task %s\n", a.BNS(), task.Name)
				continue
			}

			err = noah.DeleteLogCollectTask(a.ProductName, a.AppName, task.Name)
			if err == nil {
				a.message += fmt.Sprintf("(%s) succeed to delete logCollect task %s\n", a.BNS(), task.Name)
			} else if strings.Contains(err.Error(), "exists") || strings.Contains(err.Error(), "存在") {
				a.message += fmt.Sprintf("(%s) logCollect task %s not exists, skip\n", a.BNS(), task.Name)
			} else {
				a.message += fmt.Sprintf("(%s) failed to delete logCollect task %s, error=(%v)\n", a.BNS(), task.Name, err)
				return err
			}
		}
		return nil
	})
	// 自定义
	g.Go(func() (err error) {
		execTasks, err := noah.GetExecTaskList(&noah.MonitorTaskSearchParams{
			ProductName: a.ProductName,
			AppName:     a.AppName,
		})
		if err != nil {
			a.message += fmt.Sprintf("(%s) failed to get exec task\n", a.BNS())
			return err
		}
		for _, task := range *execTasks {
			// 过滤任务
			if _, ok := filter[fmt.Sprintf("exec#%s", task.Name)]; ok {
				a.message += fmt.Sprintf("(%s) skip exec task %s\n", a.BNS(), task.Name)
				continue
			}

			err = noah.DeleteExecTask(a.ProductName, a.AppName, task.Name)
			if err == nil {
				a.message += fmt.Sprintf("(%s) succeed to delete exec task %s\n", a.BNS(), task.Name)
			} else if strings.Contains(err.Error(), "exists") || strings.Contains(err.Error(), "存在") {
				a.message += fmt.Sprintf("(%s) exec task %s not exists, skip\n", a.BNS(), task.Name)
			} else {
				a.message += fmt.Sprintf("(%s) failed to delete exec task %s, error=(%v)\n", a.BNS(), task.Name, err)
				return err
			}
		}
		return nil
	})

	return g.Wait()
}

// =====================================================
//                    报 警 策 略
// =====================================================

// 创建级别为Critical的报警策略，filter默认为1个周期发生1次即报警，可通过额外传参的方式修改filter，告警方式为电话、短信、如流
// 报警策略不重复发送
func (a *App) critical(name string, alias string, formula string, policyType noah.PolicyType, instanceType string, filter ...int) error {
	var filterMax, filterTotal int32
	if len(filter) != 0 {
		filterTotal = int32(filter[0])
		filterMax = int32(filter[1])
	} else {
		filterTotal = 1
		filterMax = 1
	}
	p := noah.Policy{
		ProductName: a.ProductName,
		AppName:     a.AppName,
		Name:        name,
		Alias:       alias,
		PolicyType:  policyType,
		Level:       noah.PolicyLevelCritical,
		MonitoringObject: &noah.MonitoringObject{
			Type:  noah.MonitoringTypeApp,
			Names: []string{fmt.Sprintf("%s.%s", a.AppName, a.ProductName)},
		},
		IncidentActions: []string{levelActionMap[noah.PolicyLevelCritical]},
		Filter: &noah.PolicyFilter{
			Total: filterTotal,
			Max:   filterMax,
		},
		PolicyMode:  0,
		FormulaV2:   formula,
		MergeWindow: -1,
		NotifyRepeat: &noah.NotifyRepeat{
			Interval: -1,
			Max:      1,
		},
	}

	// 生效范围，只在PolicyType为INSTANCE时生效
	if policyType == noah.PolicyTypeInstance && instanceType != "" {
		p.InstanceDimensionType = a.dimensions[instanceType].Type
		p.InstanceDimensions = a.dimensions[instanceType].Dimensions
	}

	err := noah.CreatePolicy(&p)
	return err
}

// 创建级别为Major的报警策略，filter默认为1个周期发生1次即报警，可通过额外传参的方式修改filter，告警方式为短信、如流
// 报警策略不重复发送
func (a *App) major(name string, alias string, formula string, policyType noah.PolicyType, instanceType string, filter ...int) error {
	var filterMax, filterTotal int32
	if len(filter) != 0 {
		filterTotal = int32(filter[0])
		filterMax = int32(filter[1])
	} else {
		filterTotal = 1
		filterMax = 1
	}
	p := noah.Policy{
		ProductName: a.ProductName,
		AppName:     a.AppName,
		Name:        name,
		Alias:       alias,
		PolicyType:  policyType,
		Level:       noah.PolicyLevelMajor,
		MonitoringObject: &noah.MonitoringObject{
			Type:  noah.MonitoringTypeApp,
			Names: []string{fmt.Sprintf("%s.%s", a.AppName, a.ProductName)},
		},
		IncidentActions: []string{levelActionMap[noah.PolicyLevelMajor]},
		Filter: &noah.PolicyFilter{
			Total: filterTotal,
			Max:   filterMax,
		},
		PolicyMode:  0,
		FormulaV2:   formula,
		MergeWindow: -1,
		NotifyRepeat: &noah.NotifyRepeat{
			Max:      1,
			Interval: -1,
		},
	}

	// 生效范围，只在PolicyType为INSTANCE时生效
	if policyType == noah.PolicyTypeInstance && instanceType != "" {
		p.InstanceDimensionType = a.dimensions[instanceType].Type
		p.InstanceDimensions = a.dimensions[instanceType].Dimensions
	}

	err := noah.CreatePolicy(&p)
	return err
}

// 创建级别为Warning的报警策略，filter默认为1个周期发生1次即报警，可通过额外传参的方式修改filter，告警方式为如流
// 报警策略不重复发送
func (a *App) warning(name string, alias string, formula string, policyType noah.PolicyType, instanceType string, filter ...int) error {
	var filterMax, filterTotal int32
	if len(filter) != 0 {
		filterTotal = int32(filter[0])
		filterMax = int32(filter[1])
	} else {
		filterTotal = 1
		filterMax = 1
	}

	p := noah.Policy{
		ProductName: a.ProductName,
		AppName:     a.AppName,
		Name:        name,
		Alias:       alias,
		PolicyType:  policyType,
		Level:       noah.PolicyLevelWarning,
		MonitoringObject: &noah.MonitoringObject{
			Type:  noah.MonitoringTypeApp,
			Names: []string{fmt.Sprintf("%s.%s", a.AppName, a.ProductName)},
		},
		IncidentActions: []string{levelActionMap[noah.PolicyLevelWarning]},
		Filter: &noah.PolicyFilter{
			Total: filterTotal,
			Max:   filterMax,
		},
		PolicyMode:  0,
		FormulaV2:   formula,
		MergeWindow: -1,
		NotifyRepeat: &noah.NotifyRepeat{
			Max:      1,
			Interval: -1,
		},
	}

	// 生效范围，只在PolicyType为INSTANCE时生效
	if policyType == noah.PolicyTypeInstance && instanceType != "" {
		p.InstanceDimensionType = a.dimensions[instanceType].Type
		p.InstanceDimensions = a.dimensions[instanceType].Dimensions
	}

	err := noah.CreatePolicy(&p)
	return err
}

// 创建级别为 Notice 的报警策略，默认为1个周期发生1次即报警，可通过额外传参的方式修改 filter
// 报警策略不重复发送
func (a *App) notice(name string, alias string, formula string, policyType noah.PolicyType, instanceType string, filter ...int) error {
	var filterMax, filterTotal int32
	if len(filter) != 0 {
		filterTotal = int32(filter[0])
		filterMax = int32(filter[1])
	} else {
		filterTotal = 1
		filterMax = 1
	}

	p := noah.Policy{
		ProductName: a.ProductName,
		AppName:     a.AppName,
		Name:        name,
		Alias:       alias,
		PolicyType:  policyType,
		Level:       noah.PolicyLevelNotice,
		MonitoringObject: &noah.MonitoringObject{
			Type:  noah.MonitoringTypeApp,
			Names: []string{fmt.Sprintf("%s.%s", a.AppName, a.ProductName)},
		},
		IncidentActions: []string{levelActionMap[noah.PolicyLevelNotice]},
		Filter: &noah.PolicyFilter{
			Total: filterTotal,
			Max:   filterMax,
		},
		PolicyMode:  0,
		FormulaV2:   formula,
		MergeWindow: -1,
		NotifyRepeat: &noah.NotifyRepeat{
			Max:      1,
			Interval: -1,
		},
	}

	// 生效范围，只在PolicyType为INSTANCE时生效
	if policyType == noah.PolicyTypeInstance && instanceType != "" {
		p.InstanceDimensionType = a.dimensions[instanceType].Type
		p.InstanceDimensions = a.dimensions[instanceType].Dimensions
	}

	err := noah.CreatePolicy(&p)
	return err
}

// filter: total个周期中发生max则报警
// 添加报警策略，数组规则：
// 固定5个元素 policyType, level, name, alias, formula
// - 可变3个  policyType, level, name, alias, formula, instanceType
// - 可变3个  policyType, level, name, alias, formula, filterTotal, filterMax
// - 可变3个  policyType, level, name, alias, formula, instanceType, filterTotal, filterMax
func (a *App) addAlarms(alarms *[][]string) error {
	ctx := context.Background()
	g, _ := errgroup.WithContext(ctx)

	a.message = "Add alarms...\n"
	for _, alarm := range *alarms {
		// 初始化
		var policyType noah.PolicyType
		var level, name, alias, formula, instanceType string
		filter := []int{1, 1}

		switch len(alarm) {
		case 8:
			filter[1], _ = strconv.Atoi(alarm[7])
			filter[0], _ = strconv.Atoi(alarm[6])
			instanceType = alarm[5]
		case 7:
			filter[0], _ = strconv.Atoi(alarm[5])
			filter[1], _ = strconv.Atoi(alarm[6])
		case 6:
			instanceType = alarm[5]
		case 5:
		default:
			return fmt.Errorf("wrong number of alarm policy, alarm=%v", alarm)
		}
		policyType, level, name, alias, formula = noah.PolicyType(alarm[0]), alarm[1], alarm[2], alarm[3], alarm[4]

		g.Go(func() error {
			var err error
			switch level {
			case "critical":
				err = a.critical(name, alias, formula, policyType, instanceType, filter...)
			case "major":
				err = a.major(name, alias, formula, policyType, instanceType, filter...)
			case "warning":
				err = a.warning(name, alias, formula, policyType, instanceType, filter...)
			case "notice":
				err = a.notice(name, alias, formula, policyType, instanceType, filter...)
			default:
				err = fmt.Errorf("alarm metadata error, wrong alarm level: %s", level)
			}
			if err == nil {
				a.message += fmt.Sprintf("(%s) succeed to create %s alarm %s\n", a.BNS(), level, name)
			} else if strings.Contains(err.Error(), "exists") || strings.Contains(err.Error(), "存在") {
				a.message += fmt.Sprintf("(%s) %s alarm %s alreay exists, skip\n", a.BNS(), level, name)
			} else {
				a.message += fmt.Sprintf("(%s) failed to create %s alarm %s, error=(%v)\n", a.BNS(), level, name, err)
				return err
			}
			return nil
		})
	}

	return g.Wait()
}

// 删除报警策略
func (a *App) delAlarms(alarms *[][]string) error {
	ctx := context.Background()
	g, _ := errgroup.WithContext(ctx)

	a.message = "Delete alarms...\n"
	for _, alarm := range *alarms {
		if len(alarm) < 3 {
			return fmt.Errorf("wrong number of alarm policy, alarm=%v", alarm)
		}
		level, name := alarm[1], alarm[2]
		g.Go(func() error {
			err := noah.DeletePolicy(a.ProductName, a.AppName, name)
			if err == nil {
				a.message += fmt.Sprintf("(%s) succeed to delete %s alarm %s\n", a.BNS(), level, name)
			} else if strings.Contains(err.Error(), "exists") || strings.Contains(err.Error(), "存在") {
				a.message += fmt.Sprintf("(%s) %s alarm %s not exists, skip\n", a.BNS(), level, name)
			} else {
				a.message += fmt.Sprintf("(%s) failed to delete %s alarm %s, error=(%v)\n", a.BNS(), level, name, err)
				return err
			}
			return nil
		})
	}

	return g.Wait()
}

// 清理监控策略
func (a *App) ClearAlarms(savePolicies ...string) (err error) {
	filter := map[string]int{}
	for _, policyName := range savePolicies {
		filter[policyName] = 1
	}

	ctx := context.Background()
	g, _ := errgroup.WithContext(ctx)

	a.message = "Clear alarms...\n"
	page := 1
	policies := &[]noah.Policy{}
	for page > 0 {
		for _, item := range *policies {
			// 过滤策略
			if _, ok := filter[item.Name]; ok {
				a.message += fmt.Sprintf("(%s) skip alarm %s\n", a.BNS(), item.Name)
				continue
			}

			name, level := item.Name, item.Level
			g.Go(func() error {
				err = noah.DeletePolicy(a.ProductName, a.AppName, name)
				if err == nil {
					a.message += fmt.Sprintf("(%s) succeed to delete %s alarm %s\n", a.BNS(), level, name)
				} else if strings.Contains(err.Error(), "exists") || strings.Contains(err.Error(), "存在") {
					a.message += fmt.Sprintf("(%s) %s alarm %s not exists, skip\n", a.BNS(), level, name)
				} else {
					a.message += fmt.Sprintf("(%s) failed to delete %s alarm %s, error=(%v)\n", a.BNS(), level, name, err)
					return err
				}
				return nil
			})
		}

		if page == 1 || len(*policies) == 200 {
			// 初始化参数
			policies, err = noah.GetPolicyList(&noah.PolicySearchParams{
				ProductName: a.ProductName,
				AppName:     a.AppName,
				PageNumber:  page,
				PageSize:    200,
			})
			if err != nil {
				return err
			}
			page += 1
		} else {
			page = 0
		}
	}

	return g.Wait()
}
