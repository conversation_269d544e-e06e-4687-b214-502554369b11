package monitor

import (
	"testing"
)

func TestInit(t *testing.T) {
	// env.Mock(t, "../../../config/config.yaml")

	type args struct {
		cfg *Config
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test1: check params",
			args: args{
				cfg: &Config{},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			Init(tt.args.cfg)
			// if (err != nil) != tt.wantErr {
			// 	t.<PERSON>rf("Init() error = %v, wantErr %v", err, tt.wantErr)
			// }
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}
