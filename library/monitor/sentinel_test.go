package monitor

import (
	"regexp"
	"testing"

	"github.com/jarcoal/httpmock"
)

var (
	sentinel = Sentinel{
		App: &App{
			ProductName: "siod-redis",
			AppName:     "r3-test-sentinel",
			Port:        9002,
		},
	}
)

func TestSentinelMonitor(t *testing.T) {
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		opt string
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, error)
	}{
		{
			name: "test1: add monitor",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+Tasks`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": "{}"}`))
			},
			args: args{
				opt: "add",
			},
			wantErr: false,
		},
		{
			name: "test2: del monitor",
			before: func() {
				httpmock.RegisterRegexpResponder("DELETE", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+Tasks`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": "{}"}`))
			},
			args: args{
				opt: "del",
			},
			wantErr: false,
		},
		{
			name: "test3: switch default",
			args: args{
				opt: "aaa",
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := sentinel.Monitors(tt.args.opt)
			if (err != nil) != tt.wantErr {
				t.Errorf("sentinel.Monitors() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, err)
			}
		})
	}
}

func TestSentinelAlarms(t *testing.T) {
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		opt string
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, error)
	}{
		{
			name: "test1: add Alarms",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+/policies`),
					httpmock.NewStringResponder(200, "{\"success\":true,\"message\":\"\",\"data\":{\"id\":1}}"))
			},
			args: args{
				opt: "add",
			},
			wantErr: false,
		},
		{
			name: "test2: del Alarms",
			before: func() {
				httpmock.RegisterRegexpResponder("DELETE", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+/policies`),
					httpmock.NewStringResponder(200, "{\"success\":true,\"message\":\"\",\"data\":{\"id\":1}}"))
			},
			args: args{
				opt: "del",
			},
			wantErr: false,
		},
		{
			name: "test3: switch default",
			args: args{
				opt: "aaa",
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := sentinel.Alarms(tt.args.opt)
			if (err != nil) != tt.wantErr {
				t.Errorf("sentinel.Alarmss() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, err)
			}
		})
	}
}

func TestSentinelClean(t *testing.T) {
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		section string
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, error)
	}{
		{
			name: "test1: del monitor failed",
			args: args{
				section: "monitor",
			},
			wantErr: true,
		},
		{
			name: "test2: del monitor success",
			before: func() {
				httpmock.RegisterRegexpResponder("DELETE", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+Tasks`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": "{}"}`))
			},
			args: args{
				section: "monitor",
			},
			wantErr: false,
		},
		{
			name: "test2: del alarm failed",
			args: args{
				section: "alarm",
			},
			wantErr: true,
		},
		{
			name: "test4: del alarm success",
			before: func() {
				httpmock.RegisterRegexpResponder("DELETE", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+/policies`),
					httpmock.NewStringResponder(200, "{\"success\":true,\"message\":\"\",\"data\":{\"id\":1}}"))
			},
			args: args{
				section: "alarm",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := sentinel.Clean(tt.args.section)
			if (err != nil) != tt.wantErr {
				t.Errorf("sentinel.Clean() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, err)
			}
		})
	}
}
