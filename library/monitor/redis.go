package monitor

import (
	"fmt"
	"strconv"
)

// Redis模块监控管理
type Redis struct {
	*App
	ClusterName string
}

// 添加
func (o *Redis) Monitors(opt string) error {
	o.logCollectRexpMap = map[string]*map[string]string{
		"redis": {
			"aof_take_too_long":          "(fsync is taking too long)",
			"aof_rewrite_failed":         "(Can't rewrite append only file in background)",
			"bgsave_failed":              "(Can't save in background)",
			"psync_failed":               "(Unable to send PSYNC to master)",
			"master_link_slave_timeout":  "(Timeout connecting to the MASTER)",
			"kick_timeout_slave":         "(Disconnecting timedout slave)",
			"rdb_load_failed":            "(Fatal error loading the DB)",
			"aof_load_failed":            "(can't open the append log file for reading )",
			"ip_forbidden":               "(client's ip is not in the whitelist)",
			"config_rewrite":             "(CONFIG REWRITE)",
			"wf_num":                     "(#)",
			"reach_output_buffer_limits": "(overcoming)",
			"whitelist_proc_exit":        "(Bns whitelist exited unexpectly)",
		},
		"whitelist": {
			"bns_not_exist":   "(won't enter PROTECT MODE)",
			"protect_flag_ON": "(protect_flag is ON)",
			"bns_error":       "(The BNS name cannot be analyzed)",
		},
	}

	tasks := [][]string{
		{"process", "redis", "/home/<USER>/local/redis/bin/redis-server", "redis进程监控", "10"},
		{"process", "agent", "/home/<USER>/redis-agent/cmd/redis-agent", "redis-agent进程监控", "10"},

		{"port", fmt.Sprintf("redis_%d", o.Port), strconv.Itoa(o.Port), "端口监控", "10"},
		{"port", fmt.Sprintf("agent_%d", agentPort), strconv.Itoa(agentPort), "redis-agent端口监控", "10"},

		{"logCollect", "redis", "/home/<USER>/local/redis/log/redis.log", "进程日志采集", "10", "redis"},
		{"logCollect", "whitelist", "/home/<USER>/local/redis/log/whitelist.log", "白名单日志采集", "60", "whitelist"}, // 白名单60s一周期，避免当出现bns_not_exist时不断报警

		{"exec", "redis", "/home/<USER>/redis_monitor/redis_status.py", "运行数据采集", "10"},
		{"exec", "core", "/home/<USER>/redis_monitor/out_core.sh", "out core监控", "10"},
	}

	switch opt {
	case "add":
		return o.addMonitors(&tasks)
	case "del":
		return o.delMonitors(&tasks)
	default:
		return fmt.Errorf("opt should be one of add or del")
	}
}

// 添加报警策略
// Critical需要立即处理，马上就死或已经死了，Major需要尽快处理，不处理有可能会死，Warning需要关注风险，短期不处理问题不大，Notice收到即可
func (o *Redis) Alarms(opt string) error {
	alarms := [][]string{
		// ** 容器监控 **//
		{"HOST", "critical", "machine_down", "machine_down", "STATUS_PING{_statistic='avg'}==0", "3", "3"},
		{"HOST", "critical", "machine_mem_req_100", "machine_mem_req_100", "MEM_USED_PERCENT{_statistic='avg'}>=100", "3", "3"},

		// ** 进程监控 **//
		{"INSTANCE", "critical", "cpu_usage_90", "cpu_usage_90", "process.redis.cpu_usage>0.9", "1", "1"},
		{"INSTANCE", "critical", "cpu_usage_70", "cpu_usage_70", "process.redis.cpu_usage>0.7", "10", "5"},
		{"INSTANCE", "major", "cpu_usage_50", "cpu_usage_50", "process.redis.cpu_usage>0.5", "20", "10"},

		// ** 端口监控 **//
		{"INSTANCE", "critical", fmt.Sprintf("redis_%d_dead", o.Port), fmt.Sprintf("redis_%d_dead", o.Port), fmt.Sprintf("port.redis_%d.status!='ok'", o.Port)},
		{"INSTANCE", "critical", fmt.Sprintf("agent_%d_dead", agentPort), fmt.Sprintf("agent_%d_dead", agentPort), fmt.Sprintf("port.agent_%d.status!='ok'", agentPort)},

		// ** 日志监控 **//
		{"INSTANCE", "critical", "overcoming_limit", "overcoming_limit", "log.redis.reach_output_buffer_limits{_statistic='count'}>0"},
		{"INSTANCE", "major", "bns_error", "bns_error", "log.whitelist.bns_error{_statistic='count'}>0"},
		{"INSTANCE", "major", "bns_not_exist", "bns_not_exist", "log.whitelist.bns_not_exist{_statistic='count'}>0"},
		{"INSTANCE", "major", "aof_load_failed", "aof_load_failed", "log.redis.aof_load_failed{_statistic='count'}>0"},
		{"INSTANCE", "major", "aof_rewrite_fail", "aof_rewrite_fail", "log.redis.aof_rewrite_failed{_statistic='count'}>0"},
		{"INSTANCE", "major", "rdb_load_failed", "rdb_load_failed", "log.redis.rdb_load_failed{_statistic='count'}>0"},
		{"INSTANCE", "major", "bgsave_failed", "bgsave_failed", "log.redis.bgsave_failed{_statistic='count'}>0"},
		{"INSTANCE", "major", "psync_failed", "psync_failed", "log.redis.psync_failed{_statistic='count'}>0"},
		{"INSTANCE", "warning", "aof_take_too_long", "aof_take_too_long", "log.redis.aof_take_too_long{_statistic='count'}>0"},
		{"INSTANCE", "warning", "master_link_slave_timeout", "master_link_slave_timeout", "log.redis.master_link_slave_timeout{_statistic='count'}>0"},
		{"INSTANCE", "warning", "whitelist_proc_exit", "whitelist_proc_exit", "log.redis.whitelist_proc_exit{_statistic='count'}>0"},
		{"INSTANCE", "warning", "wf_num", "wf_num", "log.redis.wf_num{_statistic='count'}>0"},
		{"INSTANCE", "warning", "ip_forbidden", "ip_forbidden", "log.redis.ip_forbidden{_statistic='count'}>0"},
		{"INSTANCE", "warning", "protect_flag_ON", "protect_flag_ON", "log.whitelist.protect_flag_ON{_statistic='count'}>0"},

		// ** 自定义监控 **//
		{"INSTANCE", "critical", "out_core", "out_core", "exec.core.core_num{_statistic='avg'}==1"},
		{"INSTANCE", "critical", "maxmemory_usage_100", "maxmemory_usage_100", "exec.redis.used_memory{_statistic='avg'}/exec.redis.maxmemory{_statistic='avg'}>=1"},
		{"INSTANCE", "critical", "maxmemory_usage_95", "maxmemory_usage_95", "exec.redis.used_memory{_statistic='avg'}/exec.redis.maxmemory{_statistic='avg'}>=0.95"},
		{"INSTANCE", "critical", "maxmemory_usage_85", "maxmemory_usage_85", "exec.redis.used_memory{_statistic='avg'}/exec.redis.maxmemory{_statistic='avg'}>=0.85", "3", "3"},
		{"INSTANCE", "warning", "maxmemory_usage_75", "maxmemory_usage_75", "exec.redis.used_memory{_statistic='avg'}/exec.redis.maxmemory{_statistic='avg'}>=0.75", "10", "10"},
		{"INSTANCE", "warning", "slow", "slow", "exec.redis.role=='master'&&exec.redis.slow{_statistic='avg'}>10", "10", "5"},
		{"INSTANCE", "warning", "has_no_slaves", "has_no_slaves", "exec.redis.role=='master'&&exec.redis.connected_slaves==0", "3", "3"},
		{"INSTANCE", "warning", "has_too_many_slaves", "has_too_many_slaves", "exec.redis.role=='master'&&exec.redis.connected_slaves>1", "10", "10"},
		{"INSTANCE", "warning", "aof_write_error", "aof_write_error", "exec.redis.aof_last_bgrewrite_status!='ok' || exec.redis.aof_last_write_status!='ok'"},
		{"INSTANCE", "warning", "master_aof", "master_aof", "exec.redis.aof_enabled==1&&exec.redis.role=='master'"},
		{"INSTANCE", "warning", "slave_notaof", "slave_notaof", "exec.redis.aof_enabled==0&&exec.redis.role=='slave'"},
		{"INSTANCE", "warning", "mem_fragmentation_ratio", "mem_fragmentation_ratio", "(exec.redis.used_memory{_statistic='avg'}/exec.redis.maxmemory{_statistic='avg'}>=0.8)&&exec.redis.mem_fragmentation_ratio>1.5", "30", "30"},
	}
	switch opt {
	case "add":
		return o.addAlarms(&alarms)
	case "del":
		return o.delAlarms(&alarms)
	default:
		return fmt.Errorf("opt should be one of add or del")
	}
}
