package cli

import (
	"strconv"

	"dt-common/logger"
	"dt-common/redisc"
)

const (
	SENTINEL    = "sentinel"
	MONITOR     = "monitor"
	REMOVE      = "remove"
	RESET       = "reset"
	NO          = "no"
	ONE         = "one"
	MASTER      = "master"
	SET         = "set"
	REPLICATION = "replication"
	PONG        = "PONG"
)

// ========================================
//				   redis
// ========================================

// MakeMaster slave of no one 方式提升主库
// 注意有被sentinel改回的可能，正常需要通过sentinel failover的方式切主库
func MakeMaster(ip string, port int) error {
	cli, err := redisc.Client(ip, port)
	if err != nil {
		logger.Error("failed to init redis cli, ip=%s, port=%d, error=(%v)", ip, port, err)
		return err
	}
	defer cli.Close()

	ctx, cancel := redisc.WriteTimeout()
	err = cli.SlaveOf(ctx, NO, ONE).Err()
	cancel()
	if err != nil {
		logger.Error("failed to exec [slaveof no one], ip=%s, port=%d, error=(%v)", ip, port, err)
		return err
	}
	logger.Debug("succeed to exec [slaveof no one], ip=%s, port=%d", ip, port)

	return nil
}

// MakeSlave 将实例挂载到master，进行主从复制
func MakeSlave(masterIP string, masterPort int, slaveIp string, slavePort int) error {
	cli, err := redisc.Client(slaveIp, slavePort)
	if err != nil {
		logger.Error("failed to init redis cli, ip=%s, port=%d, error=(%v)", slaveIp, slavePort, err)
		return err
	}
	defer cli.Close()

	ctx, cancel := redisc.WriteTimeout()
	err = cli.SlaveOf(ctx, masterIP, strconv.Itoa(masterPort)).Err()
	cancel()
	if err != nil {
		logger.Error("failed to exec [slaveof %s %d], ip=%s, port=%d, error=(%v)", masterIP, masterPort, slaveIp, slavePort, err)
		return err
	}
	logger.Debug("succeed to exec [slaveof %s %d], ip=%s, port=%d", masterIP, masterPort, slaveIp, slavePort)

	return nil
}
