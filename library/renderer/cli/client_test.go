package cli

import (
	"errors"
	"testing"

	"dt-common/redisc"
	"redis-cmanager/env"
)

// ========================================
//				   redis
// ========================================

// 单测：MakeMaster
func TestMakeMaster(t *testing.T) {
	env.<PERSON><PERSON>(t, "../../../config/config.yaml")

	type args struct {
		ip   string
		port int
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(t *testing.T)
	}{
		{
			name: "return err",
			before: func() {
				redisc.Mock().ExpectSlaveOf("no", "one").SetErr(errors.New("test"))
			},
			args: args{
				ip:   "************",
				port: 7001,
			},
			wantErr: true,
		},
		{
			name: "success",
			before: func() {
				redisc.Mock().ExpectSlaveOf("no", "one").SetVal("ok")
			},
			args: args{
				ip:   "************",
				port: 7001,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}

			err := MakeMaster(tt.args.ip, tt.args.port)
			if (err != nil) != tt.wantErr {
				t.Errorf("MakeMaster error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

// 单测：MakeSlave
func TestMakeSlave(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")

	type args struct {
		masterIP   string
		masterPort int
		slaveIP    string
		slavePort  int
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(t *testing.T)
	}{
		{
			name: "return err",
			before: func() {
				redisc.Mock().ExpectSlaveOf("************", "7001").SetErr(errors.New("test"))
			},
			args: args{
				masterIP:   "************",
				masterPort: 7001,
				slaveIP:    "************",
				slavePort:  7001,
			},
			wantErr: true,
		},
		{
			name: "success",
			before: func() {
				redisc.Mock().ExpectSlaveOf("************", "7001").SetVal("ok")
			},
			args: args{
				masterIP:   "************",
				masterPort: 7001,
				slaveIP:    "************",
				slavePort:  7001,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}

			err := MakeSlave(tt.args.masterIP, tt.args.masterPort, tt.args.slaveIP, tt.args.slavePort)
			if (err != nil) != tt.wantErr {
				t.Errorf("MakeSlave error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}
