package common

import (
	"crypto/rand"
	"fmt"
	"math/big"
)

// RandomString 生成指定长度的随机字符串
func randomString() string {
	const letters = "abcdefghijklmnopqrstuvwxyz0123456789"

	ret := make([]byte, RANDOMSTRINGLEN)
	for i := 0; i < RANDOMSTRINGLEN; i++ {
		b, err := rand.Int(rand.Reader, big.NewInt(int64(len(letters))))
		if err != nil {
			return ""
		}
		ret[i] = letters[b.Int64()]
	}
	return string(ret)
}

// GetRedisName 获取redis名
// index为分片编号
func GetRedisPodName(clusterAlias string, index int) string {
	return fmt.Sprintf("%s-%s-%d-%s", PREFIX_REDIS_POD, clusterAlias, index, randomString())
}

// GetRedisNameInLabel 获取redis名，label中使用
func GetRedisLabelName(clusterAlias string) string {
	return fmt.Sprintf("%s-%s", PREFIX_REDIS_POD, clusterAlias)
}

// GetSentinelName 获取sentinel名，label中使用
func GetSentinelPodName(clusterAlias string) string {
	return fmt.Sprintf("%s-%s-%s", PREFIX_SENTINEL_POD, clusterAlias, randomString())
}

// GetSentinelNameInLabel 获取sentinel名，label中使用
func GetSentinelLabelName(clusterAlias string) string {
	return fmt.Sprintf("%s-%s", PREFIX_SENTINEL_POD, clusterAlias)
}

// GetRouterName 获取router名
func GetRouterPodName(clusterAlias string) string {
	return fmt.Sprintf("%s-%s-%s", PREFIX_ROUTER_POD, clusterAlias, randomString())
}

// GetRouterNameInLabel 获取router名，label中使用
func GetRouterLabelName(clusterAlias string) string {
	return fmt.Sprintf("%s-%s", PREFIX_ROUTER_POD, clusterAlias)
}

// GetRedisAppName 获取redis app名
func GetRedisAppName(clusterAlias string) string {
	return fmt.Sprintf("%s%s", clusterAlias, SUFFIX_REDIS_APP)
}

// GetSentinelAppName 获取sentinel app名
func GetSentinelAppName(clusterAlias string) string {
	return fmt.Sprintf("%s%s", clusterAlias, SUFFIX_SENTINEL_APP)
}

// GetRouterAppName 获取router app名
func GetRouterAppName(clusterAlias string) string {
	return fmt.Sprintf("%s%s", clusterAlias, SUFFIX_ROUTER_APP)
}

// GetRedisAppName 获取redis app别名
// func GetRedisAppAliasName(clusterAlias string) string {
// 	return fmt.Sprintf("%s%s", clusterAlias, SUFFIX_REDIS_APP)
// }

// GetSentinelAppName 获取sentinel app别名
// func GetSentinelAppAliasName(clusterAlias string) string {
// 	return fmt.Sprintf("%s%s", clusterAlias, SUFFIX_SENTINEL_APP)
// }

// GetRouterAppName 获取router app别名
// func GetRouterAppAliasName(clusterAlias string) string {
// 	return fmt.Sprintf("%s%s", clusterAlias, SUFFIX_ROUTER_APP)
// }
