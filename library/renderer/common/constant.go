package common

import "time"

// ====================================
// 				  渲染
// ====================================

const (
	CLUSTER_NAME_LENGTH_LIMIT = 21

	COMPONENT_REDIS    = "redis"
	COMPONENT_SENTINEL = "sentinel"
	COMPONENT_ROUTER   = "router"

	DEFAULT_REDIS_PORT    = 7000
	DEFAULT_SENTINEL_PORT = 9001
	DEFAULT_ROUTER_PORT   = 8001
	DEFAULT_ROUTER_SPORT  = 9001

	DEFAULT_CLIENT_CONNECTIONS = 10000
	DEFAULT_ROUTER_NUMBER      = 2

	DIAL_TIMEOUT = 2 * time.Second
)

// ====================================
// 		     App & Instance
// ====================================

const (
	// BNS命名 - 后缀
	SUFFIX_REDIS_APP    = "-redis"
	SUFFIX_SENTINEL_APP = "-sentinel"
	SUFFIX_ROUTER_APP   = "-router"

	INSTANCE_RUN_USER           = "redis"
	INSTANCE_DEPLOY_REDIS       = "/home/<USER>/local/redis"
	INSTANCE_DEPLOY_SENTINEL    = "/home/<USER>/local/sentinel"
	INSTANCE_DEPLOY_ROUTER      = "/home/<USER>/local/router"
	INSTANCE_DEPLOY_REDIS_AGENT = "/home/<USER>/redis-agent"

	// bns tag deploy:docker
	INSTANCE_TAG_KEY_DEPLOY   = "deploy"
	INSTANCE_TAG_VALUE_DEPLOY = "docker"

	INSTANCE_TAG_KEY_REGION         = "idc"
	INSTANCE_TAG_KEY_REGION_SERVICE = "service"
)

// ====================================
// 				  POD
// ====================================

const (
	//默认组件镜像，随着项目调试会发生变化，以下镜像是结合redis-operator项目需求修改的镜像，后续还会修改(冒号后面是镜像的tag标签，用于版本迭代)
	//镜像仓库地址：http://r.duxiaoman-int.com/harbor/projects
	//user="admin"
	//password="harbor@DXM123"

	DEFAULT_REDIS_IMAGE    = "r.duxiaoman-int.com/siod_redis/redis-docker"
	DEFAULT_SENTINEL_IMAGE = "r.duxiaoman-int.com/siod_redis/router-docker"
	DEFAULT_ROUTER_IMAGE   = "r.duxiaoman-int.com/siod_redis/sentinel-docker"
	DEFAULT_IMAGE_VERSION  = "v20240102"

	// POD标签
	LABEL_IDC       = "dxm-redis/idc"       // 标识机房属性node标签
	LABEL_COMPONENT = "dxm-redis/component" // 标识机器可部署的redis组件，value等于redis时可部署redis和router，等于sentinel时可部署sentinel
	LABEL_NAME      = "dxm-redis/name"      //
	LABEL_INDEX     = "dxm-redis/index"     //
	LABEL_TAINT     = "dxm-redis/taint"     // 标识是否为污点POD，污点POD不会被纳入到渲染逻辑中

	// POD命名 - 前缀
	PREFIX_REDIS_POD    = "r"
	PREFIX_SENTINEL_POD = "s"
	PREFIX_ROUTER_POD   = "p"

	// 随机字符串长度
	RANDOMSTRINGLEN = 2
)

// ====================================
// 				 白名单
// ====================================

const (
	WHITELIST     = "whitelist"
	PRIVILEGE_R   = "r"   // redis/router白名单只读权限标记
	PRIVILEGE_RW  = "rw"  // redis/router白名单读写权限标记
	PRIVILEGE_X   = "x"   // sentinel白名单权限标记
	PRIVILEGE_RWX = "rwx" // router白名单读写权限标记

	DEFAULT_WHITEIP      = "127.0.0.1"   // host
	DEFAULT_HOST_WHITEIP = "***********" // pod
)
