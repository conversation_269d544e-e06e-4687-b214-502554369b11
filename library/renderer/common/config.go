package common

var CFG = Config{}

type ImageConfig struct {
	Redis    string `yaml:"redis"`
	Sentinel string `yaml:"sentinel"`
	Router   string `yaml:"router"`
	Version  string `yaml:"version"`
}

type Application struct {
	Port  int    `yaml:"port"`
	BNS   string `yaml:"bns"`
	Token string `yaml:"token"`
}

type Config struct {
	Image       *ImageConfig      `yaml:"image"`
	IDC         map[string]string `yaml:"idc"`
	App         *AppConfig        `yaml:"app"`
	SafeRegion  string            `yaml:"safe_region"`
	Application *Application      `yaml:"application"`
}

// bns配置
type AppConfig struct {
	ProductLine         string `yaml:"product_line"`
	Owner               string `yaml:"owner"`
	DepartmentID        int    `yaml:"department_id"`
	Department          string `yaml:"department"`
}

func Init(conf *Config) {
	CFG = *conf
}
