package common

import (
	"strings"
	"testing"
)

func TestGetRedisName(t *testing.T) {
	type args struct {
		alias string
		index int
	}
	tests := []struct {
		name   string
		args   args
		want   string
		expect func(*testing.T, string)
	}{
		{
			name: "test1",
			args: args{
				alias: "hu",
				index: -1,
			},
			expect: func(t *testing.T, s string) {
				if !strings.HasPrefix(s, "r-hu-") && len(s) != 7 {
					t.<PERSON>("GetRedisName() = %v, want %v", s, "r-hu")
				}
			},
		},
		{
			name: "test2",
			args: args{
				alias: "hu",
				index: 1,
			},
			expect: func(t *testing.T, s string) {
				if !strings.HasPrefix(s, "r-hu-1-") && len(s) != 9 {
					t.<PERSON><PERSON><PERSON>("GetRedisName() = %v, want %v", s, "r-hu")
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := GetRedisPodName(tt.args.alias, tt.args.index)
			// if len(got) != len(tt.want) {
			// 	t.Errorf("GetRedisName() = %v, want %v", got, tt.want)
			// }
			if tt.expect != nil {
				tt.expect(t, got)
			}
		})
	}
}

func TestGetSentinelName(t *testing.T) {
	type args struct {
		alias string
	}
	tests := []struct {
		name   string
		args   args
		want   string
		expect func(*testing.T, string)
	}{
		{
			name: "test1",
			args: args{
				alias: "hu",
			},
			want: "s-hu-xx",
			expect: func(t *testing.T, s string) {
				if !strings.HasPrefix(s, "s-hu-") && len(s) != 7 {
					t.Errorf("GetSentinelName() = %v, want %v", s, "r-hu")
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := GetSentinelPodName(tt.args.alias)
			// if  got != tt.want {
			// 	t.Errorf("GetSentinelName() = %v, want %v", got, tt.want)
			// }
			if tt.expect != nil {
				tt.expect(t, got)
			}
		})
	}
}

func TestGetRouterName(t *testing.T) {
	type args struct {
		alias string
	}
	tests := []struct {
		name   string
		args   args
		want   string
		expect func(*testing.T, string)
	}{
		{
			name: "test1",
			args: args{
				alias: "hu",
			},
			want: "p-hu-xx",
			expect: func(t *testing.T, s string) {
				if !strings.HasPrefix(s, "p-hu-") && len(s) != 7 {
					t.Errorf("GetRouterName() = %v, want %v", s, "r-hu")
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := GetRouterPodName(tt.args.alias)
			// if  len(got) != len(tt.want) {
			// 	t.Errorf("GetRouterName() = %v, want %v", got, tt.want)
			// }
			if tt.expect != nil {
				tt.expect(t, got)
			}
		})
	}
}
