package renderer

import (
	"context"
	"errors"
	"fmt"
	"log"
	"reflect"
	"regexp"
	"testing"
	"time"

	"github.com/jarcoal/httpmock"

	"dt-common/mysql"
	"dt-common/omodel"
	"dt-common/redisc"
	"redis-cmanager/env"
	"redis-cmanager/library/fec"
	"redis-cmanager/library/renderer/common"
)

func TestRender(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")

	type args struct {
		deployment *Cluster
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, *Cluster)
	}{
		{
			name: "unit test",
			args: args{
				deployment: &Cluster{
					InspectionResult: &InspectionResult{
						State:  INSPECTION_STATE_TEST,
						ErrMsg: "test",
					},
				},
			},
			wantErr: true,
		},
		{
			name: "locked",
			before: func() {
				renderLock.Store("render_test", time.Now())
			},
			args: args{
				deployment: &Cluster{
					Name:             "render_test",
					InspectionResult: &InspectionResult{State: ""},
				},
			},
			wantErr: false,
			expect: func(t *testing.T, d *Cluster) {
				if d.InspectionResult.State != INSPECTION_STATE_INIT {
					t.Errorf("expect InspectionResult State empty, but got %s", d.InspectionResult.State)
				}
				if _, isLoaded := renderLock.Load("render_test"); !isLoaded {
					t.Errorf("expect lock, but got nil")
					return
				}
				renderLock.Delete("render_test")
			},
		},
		{
			name: "failed",
			before: func() {
				for i := 0; i < TOTAL_STEP; i++ {
					stepMap[i] = &Step{Name: fmt.Sprintf("test%d", i), Func: func(c *Cluster, ctx *Context) error { return nil }}
				}
				stepMap[2] = &Step{Name: "test2", Func: func(c *Cluster, ctx *Context) error { return fmt.Errorf("test") }}
			},
			args: args{
				deployment: &Cluster{
					InspectionResult: &InspectionResult{State: ""},
				},
			},
			wantErr: true,
			expect: func(t *testing.T, d *Cluster) {
				if d.InspectionResult.State != INSPECTION_STATE_ERROR {
					t.Errorf("expect InspectionResult.State error, but got %s", d.InspectionResult.State)
				}
				if d.InspectionResult.StepProgress[2] != STEP_ABNORMAL {
					t.Errorf("expect InspectionResult.StepProgress[2] 2, but got %d", d.InspectionResult.StepProgress[2])
				}
			},
		},
		{
			name: "success",
			before: func() {
				for i := 0; i < TOTAL_STEP; i++ {
					stepMap[i] = &Step{Name: fmt.Sprintf("test%d", i), Func: func(c *Cluster, ctx *Context) error { return nil }}
				}
			},
			args: args{
				deployment: &Cluster{
					InspectionResult: &InspectionResult{State: ""},
				},
			},
			wantErr: false,
			expect: func(t *testing.T, d *Cluster) {
				if d.InspectionResult.State != INSPECTION_STATE_NORMAL {
					t.Errorf("expect InspectionResult.State error, but got %s", d.InspectionResult.State)
				}
				if d.InspectionResult.StepProgress[2] != STEP_COMPLETED {
					t.Errorf("expect InspectionResult.StepProgress[2] 1, but got %d", d.InspectionResult.StepProgress[2])
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := Render(tt.args.deployment)
			if (err != nil) != tt.wantErr {
				t.Errorf("Render() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, tt.args.deployment)
			}
		})
	}
}

// =======================================
// 		STEP0 参数校验
// =======================================

func Test_noDuplicateKey(t *testing.T) {
	type args struct {
		strSlice []string
	}
	tests := []struct {
		name    string
		args    args
		want    []string
		wantErr bool
	}{
		{
			name: "normal customConfig",
			args: args{
				strSlice: []string{"maxmemory-policy volatile-lru", "maxmemory 8gb", "save \"\""},
			},
			wantErr: false,
			want:    []string{"maxmemory-policy volatile-lru", "maxmemory 8gb", "save \"\""},
		},
		{
			name: "customConfig with empty str",
			args: args{
				strSlice: []string{"maxmemory-policy volatile-lru", "maxmemory 8gb", ""},
			},
			wantErr: false,
			want:    []string{"maxmemory-policy volatile-lru", "maxmemory 8gb"},
		},
		{
			name: "customConfig with same key",
			args: args{
				strSlice: []string{"maxmemory-policy volatile-lru", "maxmemory 8gb", "maxmemory 7gb"},
			},
			wantErr: true,
			want:    []string{"maxmemory-policy volatile-lru", "maxmemory 8gb"},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := noDuplicateKey(tt.args.strSlice)
			if (err != nil) != tt.wantErr {
				t.Errorf("noDuplicateKey() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("noDuplicateKey() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_validate(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")

	type args struct {
		r   *Cluster
		ctx *Context
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "名称过长, > 21",
			args: args{
				ctx: &Context{},
				r: &Cluster{
					Name: "aaaaaaaaaaaaaaaaaaaaaa",
				},
			},
			wantErr: true,
		},
		{
			name: "redis配置存在重复项",
			args: args{
				ctx: &Context{},
				r: &Cluster{
					Name: "a",
					Spec: &Spec{
						Redis: &RedisSettings{
							CustomConfig: []string{"maxmemory-policy volatile-lru", "maxmemory 8gb", "maxmemory 7gb"},
						},
					},
				},
			},
			wantErr: true,
		},
		{
			name: "redis分片数为0",
			args: args{
				ctx: &Context{},
				r: &Cluster{
					Name: "a",
					Spec: &Spec{
						Redis: &RedisSettings{
							CustomConfig: []string{"maxmemory-policy volatile-lru", "maxmemory 8gb"},
							NumOfShards:  0,
						},
						Sentinel: &SentinelSettings{},
						Router:   &RouterSettings{},
					},
				},
			},
			wantErr: true,
		},
		{
			name: "sentinel数量为偶数",
			args: args{
				ctx: &Context{},
				r: &Cluster{
					Name: "a",
					Spec: &Spec{
						Redis: &RedisSettings{
							CustomConfig: []string{"maxmemory-policy volatile-lru", "maxmemory 8gb"},
							NumOfShards:  1,
							Replicas: map[string]int{
								"hba": 0,
								"hbb": 1,
							},
						},
						Sentinel: &SentinelSettings{
							Replicas: map[string]int{
								"hba": 1,
								"hbb": 1,
								"hbc": 2,
							},
						},
						Router: &RouterSettings{},
						App:    &AppSettings{},
					},
					InspectionResult: &InspectionResult{},
				},
			},
			wantErr: true,
		},
		{
			name: "sentinel数量少于3个",
			args: args{
				ctx: &Context{},
				r: &Cluster{
					Name: "a",
					Spec: &Spec{
						Redis: &RedisSettings{
							CustomConfig: []string{"maxmemory-policy volatile-lru", "maxmemory 8gb"},
							NumOfShards:  1,
							Replicas: map[string]int{
								"hba": 0,
								"hbb": 1,
							},
						},
						Sentinel: &SentinelSettings{
							Replicas: map[string]int{
								"hba": 1,
							},
						},
						Router: &RouterSettings{},
						App:    &AppSettings{},
					},
					InspectionResult: &InspectionResult{},
				},
			},
			wantErr: true,
		},

		{
			name: "redis ProductLine为空",
			args: args{
				ctx: &Context{},
				r: &Cluster{
					Name: "a",
					Spec: &Spec{
						Redis: &RedisSettings{
							CustomConfig: []string{"maxmemory-policy volatile-lru", "maxmemory 8gb"},
							NumOfShards:  1,
							Replicas:     map[string]int{"hba": 0, "hbb": 1},
						},
						Sentinel: &SentinelSettings{
							Replicas: map[string]int{"hba": 1, "hbb": 1, "hbc": 1},
						},
						Router: &RouterSettings{},
						App:    &AppSettings{},
					},
					InspectionResult: &InspectionResult{},
				},
			},
			wantErr: true,
		},
		{
			name: "redis Subsystems为空",
			args: args{
				ctx: &Context{},
				r: &Cluster{
					Name: "a",
					Spec: &Spec{
						Redis: &RedisSettings{
							CustomConfig: []string{"maxmemory-policy volatile-lru", "maxmemory 8gb"},
							NumOfShards:  1,
							Replicas:     map[string]int{"hba": 0, "hbb": 1},
						},
						Sentinel: &SentinelSettings{
							Replicas: map[string]int{"hba": 1, "hbb": 1, "hbc": 1},
						},
						Router: &RouterSettings{},
						App: &AppSettings{
							ProductLine: "test",
						},
					},
					InspectionResult: &InspectionResult{},
				},
			},
			wantErr: true,
		},
		{
			name: "redis SentinelProductLine为空",
			args: args{
				ctx: &Context{},
				r: &Cluster{
					Name: "a",
					Spec: &Spec{
						Redis: &RedisSettings{
							CustomConfig: []string{"maxmemory-policy volatile-lru", "maxmemory 8gb"},
							NumOfShards:  1,
							Replicas:     map[string]int{"hba": 0, "hbb": 1},
						},
						Sentinel: &SentinelSettings{
							Replicas: map[string]int{"hba": 1, "hbb": 1, "hbc": 1},
						},
						Router: &RouterSettings{},
						App: &AppSettings{
							ProductLine: "test",
							Subsystem:   "test",
						},
					},
					InspectionResult: &InspectionResult{},
				},
			},
			wantErr: true,
		},
		{
			name: "redis AppPrefix为空",
			args: args{
				ctx: &Context{},
				r: &Cluster{
					Name: "a",
					Spec: &Spec{
						Redis: &RedisSettings{
							CustomConfig: []string{"maxmemory-policy volatile-lru", "maxmemory 8gb"},
							NumOfShards:  1,
							Replicas:     map[string]int{"hba": 0, "hbb": 1},
						},
						Sentinel: &SentinelSettings{
							Replicas: map[string]int{"hba": 1, "hbb": 1, "hbc": 1},
						},
						Router: &RouterSettings{},
						App: &AppSettings{
							ProductLine:       "test",
							Subsystem:         "test",
							SentinelSubsystem: "test",
						},
					},
					InspectionResult: &InspectionResult{},
				},
			},
			wantErr: true,
		},
		{
			name: "redis AppPrefixAlias为空",
			args: args{
				ctx: &Context{},
				r: &Cluster{
					Name: "a",
					Spec: &Spec{
						Redis: &RedisSettings{
							CustomConfig: []string{"maxmemory-policy volatile-lru", "maxmemory 8gb"},
							NumOfShards:  1,
							Replicas:     map[string]int{"hba": 0, "hbb": 1},
						},
						Sentinel: &SentinelSettings{
							Replicas: map[string]int{"hba": 1, "hbb": 1, "hbc": 1},
						},
						Router: &RouterSettings{},
						App: &AppSettings{
							ProductLine:       "test",
							Subsystem:         "test",
							SentinelSubsystem: "test",
							AppPrefix:         "test",
						},
					},
					InspectionResult: &InspectionResult{},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := validate(tt.args.r, tt.args.ctx); (err != nil) != tt.wantErr {
				t.Errorf("validate() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

// ========================================
//	               BNS保持
// ========================================

func Test_createApps(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")

	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		deployment *Cluster
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
	}{
		{
			name: "not init",
			args: args{
				deployment: &Cluster{
					Status: DEPLOYMENT_STATUS_NORMAL,
				},
			},
			wantErr: false,
		},
		{
			name: "create all",
			before: func() {
				httpmock.RegisterResponder("GET", "http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-kafka/subSystems/r3-hu-test/apps/r3-hu-test-redis",
					httpmock.NewStringResponder(200, `{"success": true, "message": "OK", "data": {"name": "0.r3-test-router.siod-kafka"}}`),
				)
				httpmock.RegisterResponder("PUT", "http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-kafka/subSystems/r3-hu-test/apps/r3-hu-test-redis/healthCheckPolicy?checkType=1&port=6379",
					httpmock.NewStringResponder(200, `{"success": true, "message": "OK", "data": {"name": "0.r3-test-router.siod-kafka"}}`),
				)

				httpmock.RegisterResponder("GET", "http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-kafka/subSystems/r3-hu-test/apps/r3-hu-test-sentinel",
					httpmock.NewStringResponder(200, `{"success": true, "message": "OK", "data": {"name": "0.r3-test-router.siod-kafka"}}`),
				)
				httpmock.RegisterResponder("PUT", "http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-kafka/subSystems/r3-hu-test/apps/r3-hu-test-sentinel/healthCheckPolicy?checkType=1&port=9000",
					httpmock.NewStringResponder(200, `{"success": true, "message": "OK", "data": {"name": "0.r3-test-router.siod-kafka"}}`),
				)

				httpmock.RegisterResponder("GET", "http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-kafka/subSystems/r3-hu-test/apps/r3-hu-test-router",
					httpmock.NewStringResponder(200, `{"success": true, "message": "OK", "data": {"name": "0.r3-test-router.siod-kafka"}}`),
				)
				httpmock.RegisterResponder("PUT", "http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-kafka/subSystems/r3-hu-test/apps/r3-hu-test-router/healthCheckPolicy?checkType=1&port=8000",
					httpmock.NewStringResponder(200, `{"success": true, "message": "OK", "data": {"name": "0.r3-test-router.siod-kafka"}}`),
				)
			},
			args: args{
				deployment: &Cluster{
					Name:      "r3_hu_test",
					Alias:     "r3-hu-test",
					EnabledAZ: []string{"hbb", "hbb"},
					Spec: &Spec{
						Redis: &RedisSettings{
							Image:        "image",
							ImageVersion: "1",
							Replicas: map[string]int{
								"hbb": 2,
							},
							NumOfShards: 2,
							Port:        6379,
						},
						Sentinel: &SentinelSettings{
							Port: 9000,
						},
						Router: &RouterSettings{
							Port: 8000,
						},
						App: &AppSettings{
							ProductLine:       "siod-kafka",
							AppPrefix:         "r3-hu-test",
							AppPrefixAlias:    "r3-hu-test",
							Subsystem:         "r3-hu-test",
							SentinelSubsystem: "r3-hu-test",
							DepartmentName:    "系统运维部",
							DepartmentId:      **********,
							RDOwner:           "jiayiming_dxm",
							OPOwner:           "jiayiming_dxm",
						},
					},
					Status: DEPLOYMENT_STATUS_INIT,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}

			err := createApps(tt.args.deployment, nil)
			if (err != nil) != tt.wantErr {
				t.Errorf("createApps() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

// =======================================
// 		STEP 2&10 前置：实例数量检查
// =======================================

func Test_isNewRedisNeeded(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")
	objC := env.MockCluster("apply_test")
	db, _ := mysql.Database()

	type args struct {
		deployment *Cluster
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, bool)
	}{
		{
			name: "shard less than expected",
			args: args{
				deployment: &Cluster{
					Name:      "apply_test",
					EnabledAZ: []string{"hbb"},
					Spec: &Spec{
						Redis: &RedisSettings{
							Replicas:    map[string]int{"hba": 1, "hbb": 1},
							NumOfShards: 2,
							Port:        7000,
						},
					},
				},
			},
			wantErr: false,
			expect: func(t *testing.T, needApplyPod bool) {
				if !needApplyPod {
					t.Errorf("expect not pass but got true")
				}
			},
		},
		{
			name: "0 replica in idc hba",
			before: func() {
				db.Redis.Delete().Exec(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("apply-test-redis.siod-redis").SetName("apply_test-server1").SetIP("**********").SetPort(7000).SetIdc("hbb").SetRole("master").ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("apply-test-redis.siod-redis").SetName("apply_test-server2").SetIP("**********").SetPort(7000).SetIdc("hbb").SetRole("master").ExecX(context.Background())
			},
			args: args{
				deployment: &Cluster{
					Name:      "apply_test",
					EnabledAZ: []string{"hba", "hbb"},
					Spec: &Spec{
						Redis: &RedisSettings{
							Replicas:    map[string]int{"hba": 0, "hbb": 1},
							NumOfShards: 2,
							Port:        7000,
						},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "idc less than expected",
			before: func() {
				db.Redis.Delete().Exec(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("apply-test-redis.siod-redis").SetName("apply_test-server1").SetIP("**********").SetPort(7000).SetIdc("hbb").SetRole("slave").ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("apply-test-redis.siod-redis").SetName("apply_test-server2").SetIP("**********").SetPort(7000).SetIdc("hbb").SetRole("slave").ExecX(context.Background())
			},
			args: args{
				deployment: &Cluster{
					Name:      "apply_test",
					EnabledAZ: []string{"hba", "hbb"},
					Spec: &Spec{
						Redis: &RedisSettings{
							Replicas:    map[string]int{"hba": 1, "hbb": 1},
							NumOfShards: 2,
							Port:        7000,
						},
					},
				},
			},
			wantErr: false,
			expect: func(t *testing.T, b bool) {
				if !b {
					t.Errorf("expect not pass but got true")
				}
			},
		},
		{
			name: "replica less than expected",
			before: func() {
				db.Redis.Delete().Exec(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("apply-test-redis.siod-redis").SetName("apply_test-server1").SetIP("**********").SetPort(7000).SetIdc("hbb").SetRole("slave").ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("apply-test-redis.siod-redis").SetName("apply_test-server2").SetIP("**********").SetPort(7000).SetIdc("hbb").SetRole("slave").ExecX(context.Background())
			},
			args: args{
				deployment: &Cluster{
					Name:      "apply_test",
					EnabledAZ: []string{"hbb"},
					Spec: &Spec{
						Redis: &RedisSettings{
							Replicas:    map[string]int{"hba": 1, "hbb": 2},
							NumOfShards: 2,
							Port:        7000,
						},
					},
				},
			},
			wantErr: false,
			expect: func(t *testing.T, b bool) {
				if !b {
					t.Errorf("expect not pass but got true")
				}
			},
		},
		{
			name: "more than expected",
			before: func() {
				db.Redis.Delete().Exec(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("apply-test-redis.siod-redis").SetName("apply_test-server1").SetIP("**********").SetPort(7000).SetIdc("hbb").SetRole("slave").ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("apply-test-redis.siod-redis").SetName("apply_test-server2").SetIP("**********").SetPort(7000).SetIdc("hbb").SetRole("slave").ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("apply-test-redis.siod-redis").SetName("apply_test-server2").SetIP("**********").SetPort(7000).SetIdc("hbb").SetRole("slave").ExecX(context.Background())
			},
			args: args{
				deployment: &Cluster{
					Name:      "apply_test",
					EnabledAZ: []string{"hbb"},
					Spec: &Spec{
						Redis: &RedisSettings{
							Replicas:    map[string]int{"hba": 1, "hbb": 1},
							NumOfShards: 2,
							Port:        7000,
						},
					},
				},
			},
			wantErr: true,
		},
		{
			name: "idc more than expected",
			before: func() {
				db.Redis.Delete().Exec(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("apply-test-redis.siod-redis").SetName("apply_test-server1").SetIP("**********").SetPort(7000).SetIdc("hba").SetRole("master").ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("apply-test-redis.siod-redis").SetName("apply_test-server1").SetIP("**********").SetPort(7000).SetIdc("hbb").SetRole("slave").ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("apply-test-redis.siod-redis").SetName("apply_test-server2").SetIP("**********").SetPort(7000).SetIdc("hba").SetRole("master").ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("apply-test-redis.siod-redis").SetName("apply_test-server2").SetIP("**********").SetPort(7000).SetIdc("hbb").SetRole("slave").ExecX(context.Background())
			},
			args: args{
				deployment: &Cluster{
					Name:      "apply_test",
					EnabledAZ: []string{"hbb"},
					Spec: &Spec{
						Redis: &RedisSettings{
							Replicas:    map[string]int{"hba": 1, "hbb": 1},
							NumOfShards: 2,
							Port:        7000,
						},
					},
				},
			},
			wantErr: true,
		},
		{
			name: "success",
			before: func() {
				db.Redis.Delete().Exec(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("apply-test-redis.siod-redis").SetName("apply_test-server1").SetIP("**********").SetPort(7000).SetIdc("hba").SetRole("master").ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("apply-test-redis.siod-redis").SetName("apply_test-server1").SetIP("**********").SetPort(7000).SetIdc("hbb").SetRole("slave").ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("apply-test-redis.siod-redis").SetName("apply_test-server2").SetIP("**********").SetPort(7000).SetIdc("hba").SetRole("master").ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("apply-test-redis.siod-redis").SetName("apply_test-server2").SetIP("**********").SetPort(7000).SetIdc("hbb").SetRole("slave").ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("apply-test-redis.siod-redis").SetName("apply_test-server2").SetIP("**********").SetPort(7000).SetIdc("hbb").SetRole("slave").SetTaint(true).ExecX(context.Background())

			},
			args: args{
				deployment: &Cluster{
					Name:      "apply_test",
					EnabledAZ: []string{"hba", "hbb"},
					Spec: &Spec{
						Redis: &RedisSettings{
							Replicas:    map[string]int{"hba": 1, "hbb": 1},
							NumOfShards: 2,
							Port:        7000,
						},
					},
				},
			},
			wantErr: false,
			expect: func(t *testing.T, b bool) {
				if b {
					t.Errorf("expect pass but got false")
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			passed, err := isNewRedisNeeded(tt.args.deployment, &Context{})
			if (err != nil) != tt.wantErr {
				t.Errorf("isNewRedisNeeded() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, passed)
			}
		})
	}
}

func Test_isNewSentinelNeeded(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")
	objC := env.MockCluster("apply_test")
	db, _ := mysql.Database()

	type args struct {
		deployment *Cluster
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, bool)
	}{
		{
			name: "idc less than expected",
			before: func() {
				db.Sentinel.Delete().Exec(context.Background())
				db.Sentinel.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("apply-test-sentinel.siod-redis").SetIP("**********").SetPort(9001).SetIdc("hbb").ExecX(context.Background())
			},
			args: args{
				deployment: &Cluster{
					Name:      "apply_test",
					EnabledAZ: []string{"hba", "hbb"},
					Spec: &Spec{
						Sentinel: &SentinelSettings{
							Replicas: map[string]int{"hba": 2, "hbb": 1},
							Port:     9001,
						},
					},
				},
			},
			wantErr: false,
			expect: func(t *testing.T, b bool) {
				if !b {
					t.Errorf("expect not pass but got true")
				}
			},
		},
		{
			name: "replica less than expected",
			before: func() {
				db.Sentinel.Delete().Exec(context.Background())
				db.Sentinel.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("apply-test-sentinel.siod-redis").SetIP("**********").SetPort(9001).SetIdc("hba").ExecX(context.Background())
				db.Sentinel.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("apply-test-sentinel.siod-redis").SetIP("**********").SetPort(9001).SetIdc("hbb").ExecX(context.Background())
			},
			args: args{
				deployment: &Cluster{
					Name:      "apply_test",
					EnabledAZ: []string{"hba", "hbb"},
					Spec: &Spec{
						Sentinel: &SentinelSettings{
							Replicas: map[string]int{"hba": 2, "hbb": 1},
							Port:     9001,
						},
					},
				},
			},
			wantErr: false,
			expect: func(t *testing.T, b bool) {
				if !b {
					t.Errorf("expect not pass but got true")
				}
			},
		},
		{
			name: "more than expected",
			before: func() {
				db.Sentinel.Delete().Exec(context.Background())
				db.Sentinel.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("apply-test-sentinel.siod-redis").SetIP("**********").SetPort(9001).SetIdc("hbb").ExecX(context.Background())
				db.Sentinel.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("apply-test-sentinel.siod-redis").SetIP("**********").SetPort(9001).SetIdc("hbb").ExecX(context.Background())
			},
			args: args{
				deployment: &Cluster{
					Name:      "apply_test",
					EnabledAZ: []string{"hbb"},
					Spec: &Spec{
						Sentinel: &SentinelSettings{
							Replicas: map[string]int{"hba": 2, "hbb": 1},
							Port:     9001,
						},
					},
				},
			},
			wantErr: true,
		},
		{
			name: "idc more than expected",
			before: func() {
				db.Sentinel.Delete().Exec(context.Background())
				db.Sentinel.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("apply-test-sentinel.siod-redis").SetIP("**********").SetPort(9001).SetIdc("hba").ExecX(context.Background())
				db.Sentinel.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("apply-test-sentinel.siod-redis").SetIP("**********").SetPort(9001).SetIdc("hbb").ExecX(context.Background())
			},
			args: args{
				deployment: &Cluster{
					Name:      "apply_test",
					EnabledAZ: []string{"hbb"},
					Spec: &Spec{
						Sentinel: &SentinelSettings{
							Replicas: map[string]int{"hba": 2, "hbb": 1},
							Port:     9001,
						},
					},
				},
			},
			wantErr: true,
		},
		{
			name: "success",
			before: func() {
				db.Sentinel.Delete().Exec(context.Background())
				db.Sentinel.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("apply-test-sentinel.siod-redis").SetIP("**********").SetPort(9001).SetIdc("hba").ExecX(context.Background())
				db.Sentinel.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("apply-test-sentinel.siod-redis").SetIP("**********").SetPort(9001).SetIdc("hba").ExecX(context.Background())
				db.Sentinel.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("apply-test-sentinel.siod-redis").SetIP("**********").SetPort(9001).SetIdc("hbb").ExecX(context.Background())
				db.Sentinel.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("apply-test-sentinel.siod-redis").SetIP("**********").SetPort(9001).SetIdc("hbb").SetTaint(true).ExecX(context.Background())

			},
			args: args{
				deployment: &Cluster{
					Name:      "apply_test",
					EnabledAZ: []string{"hba", "hbb"},
					Spec: &Spec{
						Sentinel: &SentinelSettings{
							Replicas: map[string]int{"hba": 2, "hbb": 1},
							Port:     9001,
						},
					},
				},
			},
			wantErr: false,
			expect: func(t *testing.T, b bool) {
				if b {
					t.Errorf("expect pass but got false")
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			passed, err := isNewSentinelNeeded(tt.args.deployment, &Context{})
			if (err != nil) != tt.wantErr {
				t.Errorf("isNewSentinelNeeded() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, passed)
			}
		})
	}
}

func Test_isNewRouterNeeded(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")
	objC := env.MockCluster("apply_test")
	db, _ := mysql.Database()

	type args struct {
		deployment *Cluster
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, bool)
	}{
		{
			name: "idc less than expected",
			before: func() {
				db.Proxy.Delete().Exec(context.Background())
				db.Proxy.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("apply-test-router.siod-redis").SetIP("**********").SetPort(8001).SetIdc("hbb").ExecX(context.Background())
			},
			args: args{
				deployment: &Cluster{
					Name:      "apply_test",
					EnabledAZ: []string{"hba", "hbb"},
					Spec: &Spec{
						Router: &RouterSettings{
							Replicas: map[string]int{"hba": 2, "hbb": 1},
							Port:     9001,
						},
					},
				},
			},
			wantErr: false,
			expect: func(t *testing.T, b bool) {
				if !b {
					t.Errorf("expect not pass but got true")
				}
			},
		},
		{
			name: "replica less than expected",
			before: func() {
				db.Proxy.Delete().Exec(context.Background())
				db.Proxy.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("apply-test-router.siod-redis").SetIP("**********").SetPort(8001).SetIdc("hba").ExecX(context.Background())
				db.Proxy.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("apply-test-router.siod-redis").SetIP("**********").SetPort(8001).SetIdc("hbb").ExecX(context.Background())
			},
			args: args{
				deployment: &Cluster{
					Name:      "apply_test",
					EnabledAZ: []string{"hba", "hbb"},
					Spec: &Spec{
						Router: &RouterSettings{
							Replicas: map[string]int{"hba": 2, "hbb": 1},
							Port:     9001,
						},
					},
				},
			},
			wantErr: false,
			expect: func(t *testing.T, b bool) {
				if !b {
					t.Errorf("expect not pass but got true")
				}
			},
		},
		{
			name: "more than expected",
			before: func() {
				db.Proxy.Delete().Exec(context.Background())
				db.Proxy.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("apply-test-router.siod-redis").SetIP("**********").SetPort(8001).SetIdc("hbb").ExecX(context.Background())
				db.Proxy.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("apply-test-router.siod-redis").SetIP("**********").SetPort(8001).SetIdc("hbb").ExecX(context.Background())
			},
			args: args{
				deployment: &Cluster{
					Name:      "apply_test",
					EnabledAZ: []string{"hbb"},
					Spec: &Spec{
						Router: &RouterSettings{
							Replicas: map[string]int{"hba": 2, "hbb": 1},
							Port:     9001,
						},
					},
				},
			},
			wantErr: true,
		},
		{
			name: "idc more than expected",
			before: func() {
				db.Proxy.Delete().Exec(context.Background())
				db.Proxy.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("apply-test-router.siod-redis").SetIP("**********").SetPort(8001).SetIdc("hba").ExecX(context.Background())
				db.Proxy.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("apply-test-router.siod-redis").SetIP("**********").SetPort(8001).SetIdc("hbb").ExecX(context.Background())
			},
			args: args{
				deployment: &Cluster{
					Name:      "apply_test",
					EnabledAZ: []string{"hbb"},
					Spec: &Spec{
						Router: &RouterSettings{
							Replicas: map[string]int{"hba": 2, "hbb": 1},
							Port:     9001,
						},
					},
				},
			},
			wantErr: true,
		},
		{
			name: "success",
			before: func() {
				db.Proxy.Delete().Exec(context.Background())
				db.Proxy.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("apply-test-router.siod-redis").SetIP("**********").SetPort(8001).SetIdc("hba").ExecX(context.Background())
				db.Proxy.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("apply-test-router.siod-redis").SetIP("**********").SetPort(8001).SetIdc("hba").ExecX(context.Background())
				db.Proxy.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("apply-test-router.siod-redis").SetIP("**********").SetPort(8001).SetIdc("hbb").ExecX(context.Background())
				db.Proxy.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("apply-test-router.siod-redis").SetIP("**********").SetPort(8001).SetIdc("hbb").SetTaint(true).ExecX(context.Background())
			},
			args: args{
				deployment: &Cluster{
					Name:      "apply_test",
					EnabledAZ: []string{"hba", "hbb"},
					Spec: &Spec{
						Router: &RouterSettings{
							Replicas: map[string]int{"hba": 2, "hbb": 1},
							Port:     9001,
						},
					},
				},
			},
			wantErr: false,
			expect: func(t *testing.T, b bool) {
				if b {
					t.Errorf("expect pass but got false")
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			passed, err := isNewRouterNeeded(tt.args.deployment, &Context{})
			if (err != nil) != tt.wantErr {
				t.Errorf("isNewRouterNeeded() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, passed)
			}
		})
	}
}

// =======================================
// 		STEP 2&10 POD申请
// =======================================

// 申请Pod
func Test_createRedisPod(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")
	common.CFG = common.Config{
		App: &common.AppConfig{
			ProductLine: "siod-kafka",
		},
		Application: &common.Application{
			Port:  6379,
			BNS:   "redis-cmanager.siod-kafka",
			Token: "123",
		},
	}

	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		deployment *Cluster
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "list pod failed",
			args: args{
				deployment: &Cluster{
					Alias: "r3-rd-test",
				},
			},
			wantErr: true,
		},
		{
			name: "has no index label",
			before: func() {
				httpmock.RegisterResponder("POST", "http://*************:8536/fec-manager/api/v1/fec/listPod",
					httpmock.NewStringResponder(200, "{\"errno\": 0,\"errmsg\": \"\",\"data\": [{\"id\": \"\",\"podName\": \"r-r3-rd-test-0-1bj0uqn4\",\"productName\": \"siod-kafka\",\"appName\": \"r3-rd-test-redis\",\"podIp\": \"************\",\"idc\": \"BDDX\",\"comboDesc\": \"\",\"safeRegion\": \"test_internal\",\"owner\": \"jiayiming_dxm\",\"applicant\": \"\",\"status\": \"Running\",\"labels\": {\"avoid_delete_by_accident\": \"true\",\"dxm-redis/component\": \"redis\",\"dxm-redis/name\": \"r-r3-rd-test\",\"fec2/app_name\": \"r3-rd-test-redis\",\"fec2/bnsMount\": \"false\",\"fec2/budget_execution_id\": \"\",\"fec2/budget_number\": \"\",\"fec2/buget-checked\": \"pass\",\"fec2/cni-safe-region\": \"test_internal\",\"fec2/combo_code\": \"567756428\",\"fec2/device-id\": \"fec-PMOYhZDwP\",\"fec2/disable-inject-sidecar\": \"false\",\"fec2/fec-id\": \"fid-XxqWgKwoo\",\"fec2/idc\": \"BDDX\",\"fec2/over_budget\": \"true\",\"fec2/owner\": \"jiayiming_dxm\",\"fec2/product_name\": \"siod-kafka\",\"fec2/retain\": \"true\",\"fec2/saas\": \"redis\",\"fec2/service_level\": \"\",\"fec2/version\": \"v0.0.2\",\"fec2/white\": \"false\"},\"hostname\": \"r-r3-rd-test-0-1bj0uqn4.redis.fec.kj01.bddx.dxm-int.com\",\"scheduleLimit\": null,\"containerInfo\": [{\"containerName\": \"r-r3-rd-test-0\",\"imageName\": \"r.duxiaoman-int.com/siod_redis/redis-docker\",\"imageVersion\": \"v2024072402\",\"imagePullPolicy\": \"IfNotPresent\",\"cpu\": 1,\"mem\": 1,\"port\": null,\"entryCommand\": \"\",\"execArgs\": null,\"envArgs\": {\"CLOUD_TYPE\": \"kj\",\"CONTAINER_NAME\": \"r-r3-rd-test-0\",\"CONTAINER_TYPE\": \"main\",\"DEVICE_TYPE\": \"pod\",\"FECID\": \"fid-XxqWgKwoo\",\"FEC_OPERATOR_URL\": \"https://************:11443/fec2\",\"HAWKING_ENV\": \"container\",\"INIT_NOAH_USER\": \"redis\",\"MAX_MEMORY_POLICY\": \"volatile-lru\",\"NODE_IP\": \"\",\"POD_IP\": \"\",\"POD_NAME\": \"\",\"REDIS_PORT\": \"7000\"},\"diskApplication\": 0,\"mountType\": \"\",\"volumeMounts\": null,\"status\": \"Running\",\"privileged\": false}]}]}"))
			},
			args: args{
				deployment: &Cluster{
					Name:  "r3_rd_test",
					Alias: "r3-rd-test",
				},
			},
			wantErr: true,
		},
		{
			name: "has no idc label",
			before: func() {
				httpmock.RegisterResponder("POST", "http://*************:8536/fec-manager/api/v1/fec/listPod",
					httpmock.NewStringResponder(200, "{\"errno\": 0,\"errmsg\": \"\",\"data\": [{\"id\": \"\",\"podName\": \"r-r3-rd-test-0-1bj0uqn4\",\"productName\": \"siod-kafka\",\"appName\": \"r3-rd-test-redis\",\"podIp\": \"************\",\"idc\": \"BDDX\",\"comboDesc\": \"\",\"safeRegion\": \"test_internal\",\"owner\": \"jiayiming_dxm\",\"applicant\": \"\",\"status\": \"Running\",\"labels\": {\"avoid_delete_by_accident\": \"true\",\"dxm-redis/component\": \"redis\",\"dxm-redis/index\": \"0\",\"dxm-redis/name\": \"r-r3-rd-test\",\"fec2/app_name\": \"r3-rd-test-redis\",\"fec2/bnsMount\": \"false\",\"fec2/budget_execution_id\": \"\",\"fec2/budget_number\": \"\",\"fec2/buget-checked\": \"pass\",\"fec2/cni-safe-region\": \"test_internal\",\"fec2/combo_code\": \"567756428\",\"fec2/device-id\": \"fec-PMOYhZDwP\",\"fec2/disable-inject-sidecar\": \"false\",\"fec2/fec-id\": \"fid-XxqWgKwoo\",\"fec2/idc\": \"BDDX\",\"fec2/over_budget\": \"true\",\"fec2/owner\": \"jiayiming_dxm\",\"fec2/product_name\": \"siod-kafka\",\"fec2/retain\": \"true\",\"fec2/saas\": \"redis\",\"fec2/service_level\": \"\",\"fec2/version\": \"v0.0.2\",\"fec2/white\": \"false\"},\"hostname\": \"r-r3-rd-test-0-1bj0uqn4.redis.fec.kj01.bddx.dxm-int.com\",\"scheduleLimit\": null,\"containerInfo\": [{\"containerName\": \"r-r3-rd-test-0\",\"imageName\": \"r.duxiaoman-int.com/siod_redis/redis-docker\",\"imageVersion\": \"v2024072402\",\"imagePullPolicy\": \"IfNotPresent\",\"cpu\": 1,\"mem\": 1,\"port\": null,\"entryCommand\": \"\",\"execArgs\": null,\"envArgs\": {\"CLOUD_TYPE\": \"kj\",\"CONTAINER_NAME\": \"r-r3-rd-test-0\",\"CONTAINER_TYPE\": \"main\",\"DEVICE_TYPE\": \"pod\",\"FECID\": \"fid-XxqWgKwoo\",\"FEC_OPERATOR_URL\": \"https://************:11443/fec2\",\"HAWKING_ENV\": \"container\",\"INIT_NOAH_USER\": \"redis\",\"MAX_MEMORY_POLICY\": \"volatile-lru\",\"NODE_IP\": \"\",\"POD_IP\": \"\",\"POD_NAME\": \"\",\"REDIS_PORT\": \"7000\"},\"diskApplication\": 0,\"mountType\": \"\",\"volumeMounts\": null,\"status\": \"Running\",\"privileged\": false}]}]}"))
			},
			args: args{
				deployment: &Cluster{
					Name:  "r3_rd_test",
					Alias: "r3-rd-test",
				},
			},
			wantErr: true,
		},
		{
			name: "excessive pod",
			before: func() {
				httpmock.RegisterResponder("POST", "http://*************:8536/fec-manager/api/v1/fec/listPod",
					httpmock.NewStringResponder(200, "{\"errno\": 0,\"errmsg\": \"\",\"data\": [{\"id\": \"\",\"podName\": \"r-r3-rd-test-0-1bj0uqn4\",\"productName\": \"siod-kafka\",\"appName\": \"r3-rd-test-redis\",\"podIp\": \"************\",\"idc\": \"BDDX\",\"comboDesc\": \"\",\"safeRegion\": \"test_internal\",\"owner\": \"jiayiming_dxm\",\"applicant\": \"\",\"status\": \"Running\",\"labels\": {\"avoid_delete_by_accident\": \"true\",\"dxm-redis/component\": \"redis\",\"dxm-redis/index\": \"0\",\"dxm-redis/idc\":\"hbb\",\"dxm-redis/name\": \"r-r3-rd-test\",\"fec2/app_name\": \"r3-rd-test-redis\",\"fec2/bnsMount\": \"false\",\"fec2/budget_execution_id\": \"\",\"fec2/budget_number\": \"\",\"fec2/buget-checked\": \"pass\",\"fec2/cni-safe-region\": \"test_internal\",\"fec2/combo_code\": \"567756428\",\"fec2/device-id\": \"fec-PMOYhZDwP\",\"fec2/disable-inject-sidecar\": \"false\",\"fec2/fec-id\": \"fid-XxqWgKwoo\",\"fec2/idc\": \"BDDX\",\"fec2/over_budget\": \"true\",\"fec2/owner\": \"jiayiming_dxm\",\"fec2/product_name\": \"siod-kafka\",\"fec2/retain\": \"true\",\"fec2/saas\": \"redis\",\"fec2/service_level\": \"\",\"fec2/version\": \"v0.0.2\",\"fec2/white\": \"false\"},\"hostname\": \"r-r3-rd-test-0-1bj0uqn4.redis.fec.kj01.bddx.dxm-int.com\",\"scheduleLimit\": null,\"containerInfo\": [{\"containerName\": \"r-r3-rd-test-0\",\"imageName\": \"r.duxiaoman-int.com/siod_redis/redis-docker\",\"imageVersion\": \"v2024072402\",\"imagePullPolicy\": \"IfNotPresent\",\"cpu\": 1,\"mem\": 1,\"port\": null,\"entryCommand\": \"\",\"execArgs\": null,\"envArgs\": {\"CLOUD_TYPE\": \"kj\",\"CONTAINER_NAME\": \"r-r3-rd-test-0\",\"CONTAINER_TYPE\": \"main\",\"DEVICE_TYPE\": \"pod\",\"FECID\": \"fid-XxqWgKwoo\",\"FEC_OPERATOR_URL\": \"https://************:11443/fec2\",\"HAWKING_ENV\": \"container\",\"INIT_NOAH_USER\": \"redis\",\"MAX_MEMORY_POLICY\": \"volatile-lru\",\"NODE_IP\": \"\",\"POD_IP\": \"\",\"POD_NAME\": \"\",\"REDIS_PORT\": \"7000\"},\"diskApplication\": 0,\"mountType\": \"\",\"volumeMounts\": null,\"status\": \"Running\",\"privileged\": false}]},{\"id\": \"\",\"podName\": \"r-r3-rd-test-0-1bj0uqn4\",\"productName\": \"siod-kafka\",\"appName\": \"r3-rd-test-redis\",\"podIp\": \"************\",\"idc\": \"BDDX\",\"comboDesc\": \"\",\"safeRegion\": \"test_internal\",\"owner\": \"jiayiming_dxm\",\"applicant\": \"\",\"status\": \"Running\",\"labels\": {\"avoid_delete_by_accident\": \"true\",\"dxm-redis/component\": \"redis\",\"dxm-redis/index\": \"0\",\"dxm-redis/idc\":\"hbb\",\"dxm-redis/name\": \"r-r3-rd-test\",\"fec2/app_name\": \"r3-rd-test-redis\",\"fec2/bnsMount\": \"false\",\"fec2/budget_execution_id\": \"\",\"fec2/budget_number\": \"\",\"fec2/buget-checked\": \"pass\",\"fec2/cni-safe-region\": \"test_internal\",\"fec2/combo_code\": \"567756428\",\"fec2/device-id\": \"fec-PMOYhZDwP\",\"fec2/disable-inject-sidecar\": \"false\",\"fec2/fec-id\": \"fid-XxqWgKwoo\",\"fec2/idc\": \"BDDX\",\"fec2/over_budget\": \"true\",\"fec2/owner\": \"jiayiming_dxm\",\"fec2/product_name\": \"siod-kafka\",\"fec2/retain\": \"true\",\"fec2/saas\": \"redis\",\"fec2/service_level\": \"\",\"fec2/version\": \"v0.0.2\",\"fec2/white\": \"false\"},\"hostname\": \"r-r3-rd-test-0-1bj0uqn4.redis.fec.kj01.bddx.dxm-int.com\",\"scheduleLimit\": null,\"containerInfo\": [{\"containerName\": \"r-r3-rd-test-0\",\"imageName\": \"r.duxiaoman-int.com/siod_redis/redis-docker\",\"imageVersion\": \"v2024072402\",\"imagePullPolicy\": \"IfNotPresent\",\"cpu\": 1,\"mem\": 1,\"port\": null,\"entryCommand\": \"\",\"execArgs\": null,\"envArgs\": {\"CLOUD_TYPE\": \"kj\",\"CONTAINER_NAME\": \"r-r3-rd-test-0\",\"CONTAINER_TYPE\": \"main\",\"DEVICE_TYPE\": \"pod\",\"FECID\": \"fid-XxqWgKwoo\",\"FEC_OPERATOR_URL\": \"https://************:11443/fec2\",\"HAWKING_ENV\": \"container\",\"INIT_NOAH_USER\": \"redis\",\"MAX_MEMORY_POLICY\": \"volatile-lru\",\"NODE_IP\": \"\",\"POD_IP\": \"\",\"POD_NAME\": \"\",\"REDIS_PORT\": \"7000\"},\"diskApplication\": 0,\"mountType\": \"\",\"volumeMounts\": null,\"status\": \"Running\",\"privileged\": false}]}]}"))
			},
			args: args{
				deployment: &Cluster{
					Name:      "r3_rd_test",
					Alias:     "r3-rd-test",
					EnabledAZ: []string{"hbb"},
					Spec: &Spec{
						Redis: &RedisSettings{
							Replicas:    map[string]int{"hbb": 1},
							NumOfShards: 1,
							Port:        7000,
						},
						App: &AppSettings{
							ProductLine: "siod-kafka",
							AppPrefix:   "test3",
						},
					},
				},
			},
			wantErr: true,
		},
		{
			name: "ApplyPod failed",
			before: func() {
				httpmock.RegisterResponder("POST", "http://*************:8536/fec-manager/api/v1/fec/listPod",
					httpmock.NewStringResponder(200, "{\"errno\": 0,\"errmsg\": \"\",\"data\": [{\"id\": \"\",\"podName\": \"r-r3-rd-test-0-1bj0uqn4\",\"productName\": \"siod-kafka\",\"appName\": \"r3-rd-test-redis\",\"podIp\": \"************\",\"idc\": \"BDDX\",\"comboDesc\": \"\",\"safeRegion\": \"test_internal\",\"owner\": \"jiayiming_dxm\",\"applicant\": \"\",\"status\": \"Running\",\"labels\": {\"avoid_delete_by_accident\": \"true\",\"dxm-redis/component\": \"redis\",\"dxm-redis/index\": \"0\",\"dxm-redis/idc\":\"hbb\",\"dxm-redis/name\": \"r-r3-rd-test\",\"fec2/app_name\": \"r3-rd-test-redis\",\"fec2/bnsMount\": \"false\",\"fec2/budget_execution_id\": \"\",\"fec2/budget_number\": \"\",\"fec2/buget-checked\": \"pass\",\"fec2/cni-safe-region\": \"test_internal\",\"fec2/combo_code\": \"567756428\",\"fec2/device-id\": \"fec-PMOYhZDwP\",\"fec2/disable-inject-sidecar\": \"false\",\"fec2/fec-id\": \"fid-XxqWgKwoo\",\"fec2/idc\": \"BDDX\",\"fec2/over_budget\": \"true\",\"fec2/owner\": \"jiayiming_dxm\",\"fec2/product_name\": \"siod-kafka\",\"fec2/retain\": \"true\",\"fec2/saas\": \"redis\",\"fec2/service_level\": \"\",\"fec2/version\": \"v0.0.2\",\"fec2/white\": \"false\"},\"hostname\": \"r-r3-rd-test-0-1bj0uqn4.redis.fec.kj01.bddx.dxm-int.com\",\"scheduleLimit\": null,\"containerInfo\": [{\"containerName\": \"r-r3-rd-test-0\",\"imageName\": \"r.duxiaoman-int.com/siod_redis/redis-docker\",\"imageVersion\": \"v2024072402\",\"imagePullPolicy\": \"IfNotPresent\",\"cpu\": 1,\"mem\": 1,\"port\": null,\"entryCommand\": \"\",\"execArgs\": null,\"envArgs\": {\"CLOUD_TYPE\": \"kj\",\"CONTAINER_NAME\": \"r-r3-rd-test-0\",\"CONTAINER_TYPE\": \"main\",\"DEVICE_TYPE\": \"pod\",\"FECID\": \"fid-XxqWgKwoo\",\"FEC_OPERATOR_URL\": \"https://************:11443/fec2\",\"HAWKING_ENV\": \"container\",\"INIT_NOAH_USER\": \"redis\",\"MAX_MEMORY_POLICY\": \"volatile-lru\",\"NODE_IP\": \"\",\"POD_IP\": \"\",\"POD_NAME\": \"\",\"REDIS_PORT\": \"7000\"},\"diskApplication\": 0,\"mountType\": \"\",\"volumeMounts\": null,\"status\": \"Running\",\"privileged\": false}]}]}"))
				httpmock.RegisterResponder("POST", "http://*************:8700/v2/api/billing/budget/getResourceCombos",
					httpmock.NewStringResponder(200, `{"errno":0,"errmsg":"","data":[{"id":37,"packageId":"BBC-K8KT1Pu2","parentResourceId":"r-W89zIydp","name":"均衡类-BBC1","desc":"均衡类-BBC1-2023【CPU:1核 内存:4G 数据盘:50G  】","resourceId":100056,"isAble":2,"creator":"yangpengyi_dxm","createdAt":"2022-12-10T13:59:50+08:00","updatedAt":"2023-12-07T10:41:33+08:00","unitInfo":"[{\"key\":\"cpu\",\"unit\":\"核\",\"value\":\"1\"},{\"key\":\"内存\",\"unit\":\"G\",\"value\":\"4\"},{\"key\":\"磁盘\",\"unit\":\"G\",\"value\":\"50\"}]","priceType":1,"price":68.56,"priceNoTax":0,"code":100056199037,"abilites":0,"dynamicComboId":"","idc":"","cost_price":0,"machineNums":0,"resourceName":"BBC"}]}`))
			},
			args: args{
				deployment: &Cluster{
					Name:      "r3_rd_test",
					Alias:     "r3-rd-test",
					EnabledAZ: []string{"hbb"},
					Spec: &Spec{
						Redis: &RedisSettings{
							Image:        "r.duxiaoman-int.com/siod_redis/redis-docker",
							ImageVersion: "v2024072402",
							Replicas:     map[string]int{"hbb": 1},
							NumOfShards:  2,
							Resource: &Resource{
								CPU: 1000,
								Mem: 4096,
							},
							Port: 7000,
						},
						App: &AppSettings{
							ProductLine: "siod-kafka",
							AppPrefix:   "test3",
						},
					},
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}

			err := createRedisPod(tt.args.deployment)
			if (err != nil) != tt.wantErr {
				t.Errorf("createRedisPod() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_createSentinelPod(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")
	common.CFG = common.Config{
		App: &common.AppConfig{
			ProductLine: "siod-kafka",
		},
		Application: &common.Application{
			Port:  6379,
			BNS:   "redis-cmanager.siod-kafka",
			Token: "123",
		},
	}

	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		deployment *Cluster
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "list pod failed",
			args: args{
				deployment: &Cluster{
					Alias: "r3-rd-test",
				},
			},
			wantErr: true,
		},
		{
			name: "has no idc label",
			before: func() {
				httpmock.RegisterResponder("POST", "http://*************:8536/fec-manager/api/v1/fec/listPod",
					httpmock.NewStringResponder(200, "{\"errno\": 0,\"errmsg\": \"\",\"data\": [{\"id\": \"\",\"podName\": \"r-r3-rd-test-0-1bj0uqn4\",\"productName\": \"siod-kafka\",\"appName\": \"r3-rd-test-redis\",\"podIp\": \"************\",\"idc\": \"BDDX\",\"comboDesc\": \"\",\"safeRegion\": \"test_internal\",\"owner\": \"jiayiming_dxm\",\"applicant\": \"\",\"status\": \"Running\",\"labels\": {\"avoid_delete_by_accident\": \"true\",\"dxm-redis/component\": \"redis\",\"dxm-redis/index\": \"0\",\"dxm-redis/name\": \"r-r3-rd-test\",\"fec2/app_name\": \"r3-rd-test-redis\",\"fec2/bnsMount\": \"false\",\"fec2/budget_execution_id\": \"\",\"fec2/budget_number\": \"\",\"fec2/buget-checked\": \"pass\",\"fec2/cni-safe-region\": \"test_internal\",\"fec2/combo_code\": \"567756428\",\"fec2/device-id\": \"fec-PMOYhZDwP\",\"fec2/disable-inject-sidecar\": \"false\",\"fec2/fec-id\": \"fid-XxqWgKwoo\",\"fec2/idc\": \"BDDX\",\"fec2/over_budget\": \"true\",\"fec2/owner\": \"jiayiming_dxm\",\"fec2/product_name\": \"siod-kafka\",\"fec2/retain\": \"true\",\"fec2/saas\": \"redis\",\"fec2/service_level\": \"\",\"fec2/version\": \"v0.0.2\",\"fec2/white\": \"false\"},\"hostname\": \"r-r3-rd-test-0-1bj0uqn4.redis.fec.kj01.bddx.dxm-int.com\",\"scheduleLimit\": null,\"containerInfo\": [{\"containerName\": \"r-r3-rd-test-0\",\"imageName\": \"r.duxiaoman-int.com/siod_redis/redis-docker\",\"imageVersion\": \"v2024072402\",\"imagePullPolicy\": \"IfNotPresent\",\"cpu\": 1,\"mem\": 1,\"port\": null,\"entryCommand\": \"\",\"execArgs\": null,\"envArgs\": {\"CLOUD_TYPE\": \"kj\",\"CONTAINER_NAME\": \"r-r3-rd-test-0\",\"CONTAINER_TYPE\": \"main\",\"DEVICE_TYPE\": \"pod\",\"FECID\": \"fid-XxqWgKwoo\",\"FEC_OPERATOR_URL\": \"https://************:11443/fec2\",\"HAWKING_ENV\": \"container\",\"INIT_NOAH_USER\": \"redis\",\"MAX_MEMORY_POLICY\": \"volatile-lru\",\"NODE_IP\": \"\",\"POD_IP\": \"\",\"POD_NAME\": \"\",\"REDIS_PORT\": \"7000\"},\"diskApplication\": 0,\"mountType\": \"\",\"volumeMounts\": null,\"status\": \"Running\",\"privileged\": false}]}]}"))
			},
			args: args{
				deployment: &Cluster{
					Name:  "r3_rd_test",
					Alias: "r3-rd-test",
				},
			},
			wantErr: true,
		},
		{
			name: "excessive pod",
			before: func() {
				httpmock.RegisterResponder("POST", "http://*************:8536/fec-manager/api/v1/fec/listPod",
					httpmock.NewStringResponder(200, "{\"errno\": 0,\"errmsg\": \"\",\"data\": [{\"id\": \"\",\"podName\": \"r-r3-rd-test-0-1bj0uqn4\",\"productName\": \"siod-kafka\",\"appName\": \"r3-rd-test-redis\",\"podIp\": \"************\",\"idc\": \"BDDX\",\"comboDesc\": \"\",\"safeRegion\": \"test_internal\",\"owner\": \"jiayiming_dxm\",\"applicant\": \"\",\"status\": \"Running\",\"labels\": {\"avoid_delete_by_accident\": \"true\",\"dxm-redis/component\": \"redis\",\"dxm-redis/index\": \"0\",\"dxm-redis/idc\":\"hbb\",\"dxm-redis/name\": \"r-r3-rd-test\",\"fec2/app_name\": \"r3-rd-test-redis\",\"fec2/bnsMount\": \"false\",\"fec2/budget_execution_id\": \"\",\"fec2/budget_number\": \"\",\"fec2/buget-checked\": \"pass\",\"fec2/cni-safe-region\": \"test_internal\",\"fec2/combo_code\": \"567756428\",\"fec2/device-id\": \"fec-PMOYhZDwP\",\"fec2/disable-inject-sidecar\": \"false\",\"fec2/fec-id\": \"fid-XxqWgKwoo\",\"fec2/idc\": \"BDDX\",\"fec2/over_budget\": \"true\",\"fec2/owner\": \"jiayiming_dxm\",\"fec2/product_name\": \"siod-kafka\",\"fec2/retain\": \"true\",\"fec2/saas\": \"redis\",\"fec2/service_level\": \"\",\"fec2/version\": \"v0.0.2\",\"fec2/white\": \"false\"},\"hostname\": \"r-r3-rd-test-0-1bj0uqn4.redis.fec.kj01.bddx.dxm-int.com\",\"scheduleLimit\": null,\"containerInfo\": [{\"containerName\": \"r-r3-rd-test-0\",\"imageName\": \"r.duxiaoman-int.com/siod_redis/redis-docker\",\"imageVersion\": \"v2024072402\",\"imagePullPolicy\": \"IfNotPresent\",\"cpu\": 1,\"mem\": 1,\"port\": null,\"entryCommand\": \"\",\"execArgs\": null,\"envArgs\": {\"CLOUD_TYPE\": \"kj\",\"CONTAINER_NAME\": \"r-r3-rd-test-0\",\"CONTAINER_TYPE\": \"main\",\"DEVICE_TYPE\": \"pod\",\"FECID\": \"fid-XxqWgKwoo\",\"FEC_OPERATOR_URL\": \"https://************:11443/fec2\",\"HAWKING_ENV\": \"container\",\"INIT_NOAH_USER\": \"redis\",\"MAX_MEMORY_POLICY\": \"volatile-lru\",\"NODE_IP\": \"\",\"POD_IP\": \"\",\"POD_NAME\": \"\",\"REDIS_PORT\": \"7000\"},\"diskApplication\": 0,\"mountType\": \"\",\"volumeMounts\": null,\"status\": \"Running\",\"privileged\": false}]},{\"id\": \"\",\"podName\": \"r-r3-rd-test-0-1bj0uqn4\",\"productName\": \"siod-kafka\",\"appName\": \"r3-rd-test-redis\",\"podIp\": \"************\",\"idc\": \"BDDX\",\"comboDesc\": \"\",\"safeRegion\": \"test_internal\",\"owner\": \"jiayiming_dxm\",\"applicant\": \"\",\"status\": \"Running\",\"labels\": {\"avoid_delete_by_accident\": \"true\",\"dxm-redis/component\": \"redis\",\"dxm-redis/index\": \"0\",\"dxm-redis/idc\":\"hbb\",\"dxm-redis/name\": \"r-r3-rd-test\",\"fec2/app_name\": \"r3-rd-test-redis\",\"fec2/bnsMount\": \"false\",\"fec2/budget_execution_id\": \"\",\"fec2/budget_number\": \"\",\"fec2/buget-checked\": \"pass\",\"fec2/cni-safe-region\": \"test_internal\",\"fec2/combo_code\": \"567756428\",\"fec2/device-id\": \"fec-PMOYhZDwP\",\"fec2/disable-inject-sidecar\": \"false\",\"fec2/fec-id\": \"fid-XxqWgKwoo\",\"fec2/idc\": \"BDDX\",\"fec2/over_budget\": \"true\",\"fec2/owner\": \"jiayiming_dxm\",\"fec2/product_name\": \"siod-kafka\",\"fec2/retain\": \"true\",\"fec2/saas\": \"redis\",\"fec2/service_level\": \"\",\"fec2/version\": \"v0.0.2\",\"fec2/white\": \"false\"},\"hostname\": \"r-r3-rd-test-0-1bj0uqn4.redis.fec.kj01.bddx.dxm-int.com\",\"scheduleLimit\": null,\"containerInfo\": [{\"containerName\": \"r-r3-rd-test-0\",\"imageName\": \"r.duxiaoman-int.com/siod_redis/redis-docker\",\"imageVersion\": \"v2024072402\",\"imagePullPolicy\": \"IfNotPresent\",\"cpu\": 1,\"mem\": 1,\"port\": null,\"entryCommand\": \"\",\"execArgs\": null,\"envArgs\": {\"CLOUD_TYPE\": \"kj\",\"CONTAINER_NAME\": \"r-r3-rd-test-0\",\"CONTAINER_TYPE\": \"main\",\"DEVICE_TYPE\": \"pod\",\"FECID\": \"fid-XxqWgKwoo\",\"FEC_OPERATOR_URL\": \"https://************:11443/fec2\",\"HAWKING_ENV\": \"container\",\"INIT_NOAH_USER\": \"redis\",\"MAX_MEMORY_POLICY\": \"volatile-lru\",\"NODE_IP\": \"\",\"POD_IP\": \"\",\"POD_NAME\": \"\",\"REDIS_PORT\": \"7000\"},\"diskApplication\": 0,\"mountType\": \"\",\"volumeMounts\": null,\"status\": \"Running\",\"privileged\": false}]}]}"))
			},
			args: args{
				deployment: &Cluster{
					Name:      "r3_rd_test",
					Alias:     "r3-rd-test",
					EnabledAZ: []string{"hbb"},
					Spec: &Spec{
						Sentinel: &SentinelSettings{
							Replicas: map[string]int{"hbb": 1},
							Port:     9001,
						},
						App: &AppSettings{
							ProductLine: "siod-kafka",
							AppPrefix:   "test3",
						},
					},
				},
			},
			wantErr: true,
		},
		{
			name: "ApplyPod failed",
			before: func() {
				httpmock.RegisterResponder("POST", "http://*************:8536/fec-manager/api/v1/fec/listPod",
					httpmock.NewStringResponder(200, "{\"errno\": 0,\"errmsg\": \"\",\"data\": [{\"id\": \"\",\"podName\": \"r-r3-rd-test-0-1bj0uqn4\",\"productName\": \"siod-kafka\",\"appName\": \"r3-rd-test-redis\",\"podIp\": \"************\",\"idc\": \"BDDX\",\"comboDesc\": \"\",\"safeRegion\": \"test_internal\",\"owner\": \"jiayiming_dxm\",\"applicant\": \"\",\"status\": \"Running\",\"labels\": {\"avoid_delete_by_accident\": \"true\",\"dxm-redis/component\": \"redis\",\"dxm-redis/index\": \"0\",\"dxm-redis/idc\":\"hbb\",\"dxm-redis/name\": \"r-r3-rd-test\",\"fec2/app_name\": \"r3-rd-test-redis\",\"fec2/bnsMount\": \"false\",\"fec2/budget_execution_id\": \"\",\"fec2/budget_number\": \"\",\"fec2/buget-checked\": \"pass\",\"fec2/cni-safe-region\": \"test_internal\",\"fec2/combo_code\": \"567756428\",\"fec2/device-id\": \"fec-PMOYhZDwP\",\"fec2/disable-inject-sidecar\": \"false\",\"fec2/fec-id\": \"fid-XxqWgKwoo\",\"fec2/idc\": \"BDDX\",\"fec2/over_budget\": \"true\",\"fec2/owner\": \"jiayiming_dxm\",\"fec2/product_name\": \"siod-kafka\",\"fec2/retain\": \"true\",\"fec2/saas\": \"redis\",\"fec2/service_level\": \"\",\"fec2/version\": \"v0.0.2\",\"fec2/white\": \"false\"},\"hostname\": \"r-r3-rd-test-0-1bj0uqn4.redis.fec.kj01.bddx.dxm-int.com\",\"scheduleLimit\": null,\"containerInfo\": [{\"containerName\": \"r-r3-rd-test-0\",\"imageName\": \"r.duxiaoman-int.com/siod_redis/redis-docker\",\"imageVersion\": \"v2024072402\",\"imagePullPolicy\": \"IfNotPresent\",\"cpu\": 1,\"mem\": 1,\"port\": null,\"entryCommand\": \"\",\"execArgs\": null,\"envArgs\": {\"CLOUD_TYPE\": \"kj\",\"CONTAINER_NAME\": \"r-r3-rd-test-0\",\"CONTAINER_TYPE\": \"main\",\"DEVICE_TYPE\": \"pod\",\"FECID\": \"fid-XxqWgKwoo\",\"FEC_OPERATOR_URL\": \"https://************:11443/fec2\",\"HAWKING_ENV\": \"container\",\"INIT_NOAH_USER\": \"redis\",\"MAX_MEMORY_POLICY\": \"volatile-lru\",\"NODE_IP\": \"\",\"POD_IP\": \"\",\"POD_NAME\": \"\",\"REDIS_PORT\": \"7000\"},\"diskApplication\": 0,\"mountType\": \"\",\"volumeMounts\": null,\"status\": \"Running\",\"privileged\": false}]}]}"))
				httpmock.RegisterResponder("POST", "http://*************:8700/v2/api/billing/budget/getResourceCombos",
					httpmock.NewStringResponder(200, `{"errno":0,"errmsg":"","data":[{"id":37,"packageId":"BBC-K8KT1Pu2","parentResourceId":"r-W89zIydp","name":"均衡类-BBC1","desc":"均衡类-BBC1-2023【CPU:1核 内存:4G 数据盘:50G  】","resourceId":100056,"isAble":2,"creator":"yangpengyi_dxm","createdAt":"2022-12-10T13:59:50+08:00","updatedAt":"2023-12-07T10:41:33+08:00","unitInfo":"[{\"key\":\"cpu\",\"unit\":\"核\",\"value\":\"1\"},{\"key\":\"内存\",\"unit\":\"G\",\"value\":\"4\"},{\"key\":\"磁盘\",\"unit\":\"G\",\"value\":\"50\"}]","priceType":1,"price":68.56,"priceNoTax":0,"code":100056199037,"abilites":0,"dynamicComboId":"","idc":"","cost_price":0,"machineNums":0,"resourceName":"BBC"}]}`))
			},
			args: args{
				deployment: &Cluster{
					Name:      "r3_rd_test",
					Alias:     "r3-rd-test",
					EnabledAZ: []string{"hbb"},
					Spec: &Spec{
						Sentinel: &SentinelSettings{
							Image:        "r.duxiaoman-int.com/siod_redis/redis-docker",
							ImageVersion: "v2024072402",
							Replicas:     map[string]int{"hbb": 2},
							Resource: &Resource{
								CPU: 1000,
								Mem: 4096,
							},
							Port: 9001,
						},
						App: &AppSettings{
							ProductLine: "siod-kafka",
							AppPrefix:   "test3",
						},
					},
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}

			err := createSentinelPod(tt.args.deployment)
			if (err != nil) != tt.wantErr {
				t.Errorf("createSentinelPod() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_createRouterPod(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")
	common.CFG = common.Config{
		App: &common.AppConfig{
			Owner:      "jiayiming_dxm",
			Department: "系统运维部",
		},
		Application: &common.Application{
			Port:  6379,
			BNS:   "redis-cmanager.siod-kafka",
			Token: "123",
		},
	}

	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		deployment *Cluster
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "list pod failed",
			args: args{
				deployment: &Cluster{
					Alias: "r3-rd-test",
				},
			},
			wantErr: true,
		},
		{
			name: "has no idc label",
			before: func() {
				httpmock.RegisterResponder("POST", "http://*************:8536/fec-manager/api/v1/fec/listPod",
					httpmock.NewStringResponder(200, "{\"errno\": 0,\"errmsg\": \"\",\"data\": [{\"id\": \"\",\"podName\": \"r-r3-rd-test-0-1bj0uqn4\",\"productName\": \"siod-kafka\",\"appName\": \"r3-rd-test-redis\",\"podIp\": \"************\",\"idc\": \"BDDX\",\"comboDesc\": \"\",\"safeRegion\": \"test_internal\",\"owner\": \"jiayiming_dxm\",\"applicant\": \"\",\"status\": \"Running\",\"labels\": {\"avoid_delete_by_accident\": \"true\",\"dxm-redis/component\": \"redis\",\"dxm-redis/index\": \"0\",\"dxm-redis/name\": \"r-r3-rd-test\",\"fec2/app_name\": \"r3-rd-test-redis\",\"fec2/bnsMount\": \"false\",\"fec2/budget_execution_id\": \"\",\"fec2/budget_number\": \"\",\"fec2/buget-checked\": \"pass\",\"fec2/cni-safe-region\": \"test_internal\",\"fec2/combo_code\": \"567756428\",\"fec2/device-id\": \"fec-PMOYhZDwP\",\"fec2/disable-inject-sidecar\": \"false\",\"fec2/fec-id\": \"fid-XxqWgKwoo\",\"fec2/idc\": \"BDDX\",\"fec2/over_budget\": \"true\",\"fec2/owner\": \"jiayiming_dxm\",\"fec2/product_name\": \"siod-kafka\",\"fec2/retain\": \"true\",\"fec2/saas\": \"redis\",\"fec2/service_level\": \"\",\"fec2/version\": \"v0.0.2\",\"fec2/white\": \"false\"},\"hostname\": \"r-r3-rd-test-0-1bj0uqn4.redis.fec.kj01.bddx.dxm-int.com\",\"scheduleLimit\": null,\"containerInfo\": [{\"containerName\": \"r-r3-rd-test-0\",\"imageName\": \"r.duxiaoman-int.com/siod_redis/redis-docker\",\"imageVersion\": \"v2024072402\",\"imagePullPolicy\": \"IfNotPresent\",\"cpu\": 1,\"mem\": 1,\"port\": null,\"entryCommand\": \"\",\"execArgs\": null,\"envArgs\": {\"CLOUD_TYPE\": \"kj\",\"CONTAINER_NAME\": \"r-r3-rd-test-0\",\"CONTAINER_TYPE\": \"main\",\"DEVICE_TYPE\": \"pod\",\"FECID\": \"fid-XxqWgKwoo\",\"FEC_OPERATOR_URL\": \"https://************:11443/fec2\",\"HAWKING_ENV\": \"container\",\"INIT_NOAH_USER\": \"redis\",\"MAX_MEMORY_POLICY\": \"volatile-lru\",\"NODE_IP\": \"\",\"POD_IP\": \"\",\"POD_NAME\": \"\",\"REDIS_PORT\": \"7000\"},\"diskApplication\": 0,\"mountType\": \"\",\"volumeMounts\": null,\"status\": \"Running\",\"privileged\": false}]}]}"))
			},
			args: args{
				deployment: &Cluster{
					Name:  "r3_rd_test",
					Alias: "r3-rd-test",
				},
			},
			wantErr: true,
		},
		{
			name: "excessive pod",
			before: func() {
				httpmock.RegisterResponder("POST", "http://*************:8536/fec-manager/api/v1/fec/listPod",
					httpmock.NewStringResponder(200, "{\"errno\": 0,\"errmsg\": \"\",\"data\": [{\"id\": \"\",\"podName\": \"r-r3-rd-test-0-1bj0uqn4\",\"productName\": \"siod-kafka\",\"appName\": \"r3-rd-test-redis\",\"podIp\": \"************\",\"idc\": \"BDDX\",\"comboDesc\": \"\",\"safeRegion\": \"test_internal\",\"owner\": \"jiayiming_dxm\",\"applicant\": \"\",\"status\": \"Running\",\"labels\": {\"avoid_delete_by_accident\": \"true\",\"dxm-redis/component\": \"redis\",\"dxm-redis/index\": \"0\",\"dxm-redis/idc\":\"hbb\",\"dxm-redis/name\": \"r-r3-rd-test\",\"fec2/app_name\": \"r3-rd-test-redis\",\"fec2/bnsMount\": \"false\",\"fec2/budget_execution_id\": \"\",\"fec2/budget_number\": \"\",\"fec2/buget-checked\": \"pass\",\"fec2/cni-safe-region\": \"test_internal\",\"fec2/combo_code\": \"567756428\",\"fec2/device-id\": \"fec-PMOYhZDwP\",\"fec2/disable-inject-sidecar\": \"false\",\"fec2/fec-id\": \"fid-XxqWgKwoo\",\"fec2/idc\": \"BDDX\",\"fec2/over_budget\": \"true\",\"fec2/owner\": \"jiayiming_dxm\",\"fec2/product_name\": \"siod-kafka\",\"fec2/retain\": \"true\",\"fec2/saas\": \"redis\",\"fec2/service_level\": \"\",\"fec2/version\": \"v0.0.2\",\"fec2/white\": \"false\"},\"hostname\": \"r-r3-rd-test-0-1bj0uqn4.redis.fec.kj01.bddx.dxm-int.com\",\"scheduleLimit\": null,\"containerInfo\": [{\"containerName\": \"r-r3-rd-test-0\",\"imageName\": \"r.duxiaoman-int.com/siod_redis/redis-docker\",\"imageVersion\": \"v2024072402\",\"imagePullPolicy\": \"IfNotPresent\",\"cpu\": 1,\"mem\": 1,\"port\": null,\"entryCommand\": \"\",\"execArgs\": null,\"envArgs\": {\"CLOUD_TYPE\": \"kj\",\"CONTAINER_NAME\": \"r-r3-rd-test-0\",\"CONTAINER_TYPE\": \"main\",\"DEVICE_TYPE\": \"pod\",\"FECID\": \"fid-XxqWgKwoo\",\"FEC_OPERATOR_URL\": \"https://************:11443/fec2\",\"HAWKING_ENV\": \"container\",\"INIT_NOAH_USER\": \"redis\",\"MAX_MEMORY_POLICY\": \"volatile-lru\",\"NODE_IP\": \"\",\"POD_IP\": \"\",\"POD_NAME\": \"\",\"REDIS_PORT\": \"7000\"},\"diskApplication\": 0,\"mountType\": \"\",\"volumeMounts\": null,\"status\": \"Running\",\"privileged\": false}]},{\"id\": \"\",\"podName\": \"r-r3-rd-test-0-1bj0uqn4\",\"productName\": \"siod-kafka\",\"appName\": \"r3-rd-test-redis\",\"podIp\": \"************\",\"idc\": \"BDDX\",\"comboDesc\": \"\",\"safeRegion\": \"test_internal\",\"owner\": \"jiayiming_dxm\",\"applicant\": \"\",\"status\": \"Running\",\"labels\": {\"avoid_delete_by_accident\": \"true\",\"dxm-redis/component\": \"redis\",\"dxm-redis/index\": \"0\",\"dxm-redis/idc\":\"hbb\",\"dxm-redis/name\": \"r-r3-rd-test\",\"fec2/app_name\": \"r3-rd-test-redis\",\"fec2/bnsMount\": \"false\",\"fec2/budget_execution_id\": \"\",\"fec2/budget_number\": \"\",\"fec2/buget-checked\": \"pass\",\"fec2/cni-safe-region\": \"test_internal\",\"fec2/combo_code\": \"567756428\",\"fec2/device-id\": \"fec-PMOYhZDwP\",\"fec2/disable-inject-sidecar\": \"false\",\"fec2/fec-id\": \"fid-XxqWgKwoo\",\"fec2/idc\": \"BDDX\",\"fec2/over_budget\": \"true\",\"fec2/owner\": \"jiayiming_dxm\",\"fec2/product_name\": \"siod-kafka\",\"fec2/retain\": \"true\",\"fec2/saas\": \"redis\",\"fec2/service_level\": \"\",\"fec2/version\": \"v0.0.2\",\"fec2/white\": \"false\"},\"hostname\": \"r-r3-rd-test-0-1bj0uqn4.redis.fec.kj01.bddx.dxm-int.com\",\"scheduleLimit\": null,\"containerInfo\": [{\"containerName\": \"r-r3-rd-test-0\",\"imageName\": \"r.duxiaoman-int.com/siod_redis/redis-docker\",\"imageVersion\": \"v2024072402\",\"imagePullPolicy\": \"IfNotPresent\",\"cpu\": 1,\"mem\": 1,\"port\": null,\"entryCommand\": \"\",\"execArgs\": null,\"envArgs\": {\"CLOUD_TYPE\": \"kj\",\"CONTAINER_NAME\": \"r-r3-rd-test-0\",\"CONTAINER_TYPE\": \"main\",\"DEVICE_TYPE\": \"pod\",\"FECID\": \"fid-XxqWgKwoo\",\"FEC_OPERATOR_URL\": \"https://************:11443/fec2\",\"HAWKING_ENV\": \"container\",\"INIT_NOAH_USER\": \"redis\",\"MAX_MEMORY_POLICY\": \"volatile-lru\",\"NODE_IP\": \"\",\"POD_IP\": \"\",\"POD_NAME\": \"\",\"REDIS_PORT\": \"7000\"},\"diskApplication\": 0,\"mountType\": \"\",\"volumeMounts\": null,\"status\": \"Running\",\"privileged\": false}]}]}"))
			},
			args: args{
				deployment: &Cluster{
					Name:      "r3_rd_test",
					Alias:     "r3-rd-test",
					EnabledAZ: []string{"hbb"},
					Spec: &Spec{
						Router: &RouterSettings{
							Replicas: map[string]int{"hbb": 1},
							Port:     8001,
						},
						App: &AppSettings{
							ProductLine: "siod-kafka",
							AppPrefix:   "test3",
						},
					},
				},
			},
			wantErr: true,
		},
		{
			name: "ApplyPod failed",
			before: func() {
				httpmock.RegisterResponder("POST", "http://*************:8536/fec-manager/api/v1/fec/listPod",
					httpmock.NewStringResponder(200, "{\"errno\": 0,\"errmsg\": \"\",\"data\": [{\"id\": \"\",\"podName\": \"r-r3-rd-test-0-1bj0uqn4\",\"productName\": \"siod-kafka\",\"appName\": \"r3-rd-test-redis\",\"podIp\": \"************\",\"idc\": \"BDDX\",\"comboDesc\": \"\",\"safeRegion\": \"test_internal\",\"owner\": \"jiayiming_dxm\",\"applicant\": \"\",\"status\": \"Running\",\"labels\": {\"avoid_delete_by_accident\": \"true\",\"dxm-redis/component\": \"redis\",\"dxm-redis/index\": \"0\",\"dxm-redis/idc\":\"hbb\",\"dxm-redis/name\": \"r-r3-rd-test\",\"fec2/app_name\": \"r3-rd-test-redis\",\"fec2/bnsMount\": \"false\",\"fec2/budget_execution_id\": \"\",\"fec2/budget_number\": \"\",\"fec2/buget-checked\": \"pass\",\"fec2/cni-safe-region\": \"test_internal\",\"fec2/combo_code\": \"567756428\",\"fec2/device-id\": \"fec-PMOYhZDwP\",\"fec2/disable-inject-sidecar\": \"false\",\"fec2/fec-id\": \"fid-XxqWgKwoo\",\"fec2/idc\": \"BDDX\",\"fec2/over_budget\": \"true\",\"fec2/owner\": \"jiayiming_dxm\",\"fec2/product_name\": \"siod-kafka\",\"fec2/retain\": \"true\",\"fec2/saas\": \"redis\",\"fec2/service_level\": \"\",\"fec2/version\": \"v0.0.2\",\"fec2/white\": \"false\"},\"hostname\": \"r-r3-rd-test-0-1bj0uqn4.redis.fec.kj01.bddx.dxm-int.com\",\"scheduleLimit\": null,\"containerInfo\": [{\"containerName\": \"r-r3-rd-test-0\",\"imageName\": \"r.duxiaoman-int.com/siod_redis/redis-docker\",\"imageVersion\": \"v2024072402\",\"imagePullPolicy\": \"IfNotPresent\",\"cpu\": 1,\"mem\": 1,\"port\": null,\"entryCommand\": \"\",\"execArgs\": null,\"envArgs\": {\"CLOUD_TYPE\": \"kj\",\"CONTAINER_NAME\": \"r-r3-rd-test-0\",\"CONTAINER_TYPE\": \"main\",\"DEVICE_TYPE\": \"pod\",\"FECID\": \"fid-XxqWgKwoo\",\"FEC_OPERATOR_URL\": \"https://************:11443/fec2\",\"HAWKING_ENV\": \"container\",\"INIT_NOAH_USER\": \"redis\",\"MAX_MEMORY_POLICY\": \"volatile-lru\",\"NODE_IP\": \"\",\"POD_IP\": \"\",\"POD_NAME\": \"\",\"REDIS_PORT\": \"7000\"},\"diskApplication\": 0,\"mountType\": \"\",\"volumeMounts\": null,\"status\": \"Running\",\"privileged\": false}]}]}"))
				httpmock.RegisterResponder("POST", "http://*************:8700/v2/api/billing/budget/getResourceCombos",
					httpmock.NewStringResponder(200, `{"errno":0,"errmsg":"","data":[{"id":37,"packageId":"BBC-K8KT1Pu2","parentResourceId":"r-W89zIydp","name":"均衡类-BBC1","desc":"均衡类-BBC1-2023【CPU:1核 内存:4G 数据盘:50G  】","resourceId":100056,"isAble":2,"creator":"yangpengyi_dxm","createdAt":"2022-12-10T13:59:50+08:00","updatedAt":"2023-12-07T10:41:33+08:00","unitInfo":"[{\"key\":\"cpu\",\"unit\":\"核\",\"value\":\"1\"},{\"key\":\"内存\",\"unit\":\"G\",\"value\":\"4\"},{\"key\":\"磁盘\",\"unit\":\"G\",\"value\":\"50\"}]","priceType":1,"price":68.56,"priceNoTax":0,"code":100056199037,"abilites":0,"dynamicComboId":"","idc":"","cost_price":0,"machineNums":0,"resourceName":"BBC"}]}`))
			},
			args: args{
				deployment: &Cluster{
					Name:      "r3_rd_test",
					Alias:     "r3-rd-test",
					EnabledAZ: []string{"hbb"},
					Spec: &Spec{
						Redis: &RedisSettings{
							NumOfShards: 1,
							Port:        7000,
						},
						Sentinel: &SentinelSettings{
							Port: 9001,
						},
						Router: &RouterSettings{
							Image:        "r.duxiaoman-int.com/siod_redis/redis-docker",
							ImageVersion: "v2024072402",
							Replicas:     map[string]int{"hbb": 2},
							Resource: &Resource{
								CPU: 1000,
								Mem: 4096,
							},
							Port:  8001,
							SPort: 9001,
						},
						App: &AppSettings{
							ProductLine: "siod-kafka",
							AppPrefix:   "test3",
						},
					},
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}

			err := createRouterPod(tt.args.deployment)
			if (err != nil) != tt.wantErr {
				t.Errorf("createRouterPod() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_createRedisAndSentinelPods(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")
	objC := env.MockCluster("apply_test")
	db, _ := mysql.Database()

	type args struct {
		deployment *Cluster
		ctx        *Context
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, *Context)
	}{
		{
			name: "no need apply new pod",
			before: func() {
				db.Redis.Delete().Exec(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("apply-test-redis.siod-redis").SetName("apply_test-server1").SetIP("**********").SetPort(7000).SetIdc("hbb").SetRole("slave").ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("apply-test-redis.siod-redis").SetName("apply_test-server2").SetIP("**********").SetPort(7000).SetIdc("hbb").SetRole("slave").ExecX(context.Background())

				db.Sentinel.Delete().Exec(context.Background())
				db.Sentinel.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("apply-test-sentinel.siod-redis").SetIP("**********").SetPort(9001).SetIdc("hbb").ExecX(context.Background())
			},
			args: args{
				deployment: &Cluster{
					Name:      "apply_test",
					EnabledAZ: []string{"hbb"},
					Spec: &Spec{
						Redis: &RedisSettings{
							Replicas:    map[string]int{"hbb": 1},
							Port:        7000,
							NumOfShards: 2,
						},
						Sentinel: &SentinelSettings{
							Replicas: map[string]int{"hbb": 1},
							Port:     9001,
						},
					},
				},
				ctx: &Context{},
			},
			wantErr: false,
			expect: func(t *testing.T, ctx *Context) {
				if len(ctx.RedisInstances) != 2 {
					t.Errorf("expect redis instances len 2, but got %d", len(ctx.RedisInstances))
				}
				if len(ctx.SentinelInstances) != 1 {
					t.Errorf("expect sentinel instances len 1, but got %d", len(ctx.SentinelInstances))
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := createRedisAndSentinelPods(tt.args.deployment, tt.args.ctx)
			if (err != nil) != tt.wantErr {
				t.Errorf("createRedisAndSentinelPods() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, tt.args.ctx)
			}
		})
	}
}

func Test_createRouterPods(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")
	objC := env.MockCluster("apply_test")
	db, _ := mysql.Database()

	type args struct {
		deployment *Cluster
		ctx        *Context
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, *Context)
	}{
		{
			name: "no need apply new pod",
			before: func() {
				db.Proxy.Delete().Exec(context.Background())
				db.Proxy.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("apply-test-router.siod-redis").SetIP("*********").SetPort(8001).SetIdc("hbb").ExecX(context.Background())
				db.Proxy.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("apply-test-router.siod-redis").SetIP("*********").SetPort(8001).SetIdc("hbb").ExecX(context.Background())
			},
			args: args{
				deployment: &Cluster{
					Name:      "apply_test",
					EnabledAZ: []string{"hbb"},
					Spec: &Spec{
						Router: &RouterSettings{
							Replicas: map[string]int{"hbb": 2},
							Port:     8001,
						},
					},
				},
				ctx: &Context{},
			},
			wantErr: false,
			expect: func(t *testing.T, ctx *Context) {
				if len(ctx.RouterInstances) != 2 {
					t.Errorf("expect router instances len 2, but got %d", len(ctx.RouterInstances))
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := createRouterPods(tt.args.deployment, tt.args.ctx)
			if (err != nil) != tt.wantErr {
				t.Errorf("createRouterPods() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, tt.args.ctx)
			}
		})
	}
}

// =======================================
//    STEP 3&11 后置：检查状态、挂实例、落库
// =======================================

func Test_upsertNoahInstances(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")
	common.Init(&common.Config{
		IDC: map[string]string{
			"hba": "ZZJG",
			"hbb": "ZZJG",
			"hbc": "ZZJG",
		},
	})

	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		pods        []*fec.PodInfo
		productLine string
		subsystem   string
		app         string
		deployPath  string
		port        int
		disable     bool
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "add instance",
			before: func() {
				httpmock.RegisterResponder("GET", "http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-kafka/apps/test-redis/instances?showPage=0",
					httpmock.NewStringResponder(200, `{"success": true, "message": "OK", "data": [
						{"name": "0.test-redis.siod-kafka","hostName": "test-0-az.fec.kj01.bddx.dxm-int.com","ip": "*********","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 7000},"deployInfo": {"deployPath": "/home/<USER>/local/redis"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"deploy":"docker","idc": "hba","service":"hbas"},"disable": false,"instanceType": "host","podId": null,"containerList": []},
						{"name": "1.test-redis.siod-kafka","hostName": "test-1-d4.fec.kj01.zzjg.dxm-int.com","ip": "*********","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 7000},"deployInfo": {"deployPath": "/home/<USER>/local/redis"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"deploy":"docker","idc": "hbb","service":"hbbs", "test":"jiayiming"},"disable": false,"instanceType": "host","podId": null,"containerList": []}
					]}`),
				)

				httpmock.RegisterResponder("POST", "http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-kafka/subSystems/test/apps/test-redis/instances",
					httpmock.NewStringResponder(200, `{"success": true, "message": "OK", "data": []}`),
				)
			},
			args: args{
				pods: []*fec.PodInfo{
					{
						PodName:  "test-0-az",
						HostName: "test-0-az.fec.kj01.bddx.dxm-int.com",
						PodIp:    "*********",
						Labels: map[string]string{
							"dxm-redis/idc":   "hba",
							"dxm-redis/index": "1",
						},
					},
					{
						PodName:  "test-1-d4",
						HostName: "test-1-d4.fec.kj01.zzjg.dxm-int.com",
						PodIp:    "*********",
						Labels: map[string]string{
							"dxm-redis/idc":   "hbb",
							"dxm-redis/index": "1",
						},
					},
					{
						PodName:  "test-2-4x",
						HostName: "test-2-4x.fec.kj01.bddx.dxm-int.com",
						PodIp:    "*********",
						Labels: map[string]string{
							"dxm-redis/idc":   "hba",
							"dxm-redis/index": "1",
						},
					},
				},
				productLine: "siod-kafka",
				subsystem:   "test",
				app:         "test-redis",
				deployPath:  "/home/<USER>/local/redis",
				port:        7000,
				disable:     false,
			},
			wantErr: false,
		},
		{
			name: "update instances",
			before: func() {
				httpmock.RegisterResponder("GET", "http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-kafka/apps/test-redis/instances?showPage=0",
					httpmock.NewStringResponder(200, `{"success": true, "message": "OK", "data": [
						{"name": "0.test-redis.siod-kafka","hostName": "test-0-az.fec.kj01.bddx.dxm-int.com","ip": "*********","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 7000},"deployInfo": {"deployPath": "/home/<USER>/local/redis"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"deploy":"docker","idc": "hba","service":"hbas"},"disable": false,"instanceType": "host","podId": null,"containerList": []},
						{"name": "1.test-redis.siod-kafka","hostName": "test-1-d4.fec.kj01.zzjg.dxm-int.com","ip": "*********","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 7001},"deployInfo": {"deployPath": "/home/<USER>/local/redis"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"deploy":"docker","idc": "hbb","service":"hbbs"},"disable": false,"instanceType": "host","podId": null,"containerList": []}
					]}`),
				)

				httpmock.RegisterResponder("PUT", "http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-kafka/apps/test-redis/instances/1.test-redis.siod-kafka",
					httpmock.NewStringResponder(200, `{"success": true, "message": "OK", "data": []}`),
				)
			},
			args: args{
				pods: []*fec.PodInfo{
					{
						PodName:  "test-0-az",
						HostName: "test-0-az.fec.kj01.bddx.dxm-int.com",
						PodIp:    "*********",
						Labels: map[string]string{
							"dxm-redis/idc":   "hba",
							"dxm-redis/index": "1",
						},
					},
					{
						PodName:  "test-1-d4",
						HostName: "test-1-d4.fec.kj01.zzjg.dxm-int.com",
						PodIp:    "*********",
						Labels: map[string]string{
							"dxm-redis/idc":   "hbb",
							"dxm-redis/index": "1",
						},
					},
				},
				productLine: "siod-kafka",
				subsystem:   "test",
				app:         "test-redis",
				deployPath:  "/home/<USER>/local/redis",
				port:        7000,
				disable:     false,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			if err := upsertNoahInstances(tt.args.pods, tt.args.productLine, tt.args.subsystem, tt.args.app, tt.args.deployPath, tt.args.port, tt.args.disable); (err != nil) != tt.wantErr {
				t.Errorf("upsertNoahInstances() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

// saveRedisPods
func Test_saveRedisPods(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")
	objC := env.MockCluster("r_save_test")
	common.Init(&common.Config{
		IDC: map[string]string{
			"hba": "ZZJG",
			"hbb": "ZZJG",
			"hbc": "ZZJG",
		},
	})

	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		deployment *Cluster
		ctx        *Context
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, *Context)
	}{
		{
			name: "no new pod applied",
			args: args{
				deployment: &Cluster{},
				ctx: &Context{
					ApplyNewPods: [3]bool{false, false, false},
				},
			},
			wantErr: false,
		},
		{
			name: "wait 15s and pod running",
			before: func() {
				httpmock.RegisterResponder("POST", "http://*************:8536/fec-manager/api/v1/fec/listPod",
					httpmock.NewStringResponder(200, `{"errno": 0,"errmsg": "","data": [
						{"id": "","podName": "r-test-0-14","productName": "siod-kafka","appName": "test-redis","podIp": "**********","idc": "ZZJG","comboDesc": "","safeRegion": "test_internal","owner": "jiayiming_dxm","applicant": "","status": "Init","labels": {"avoid_delete_by_accident": "true","dxm-redis/component": "redis","dxm-redis/idc": "hbb","dxm-redis/index": "0","dxm-redis/name": "test","fec2/app_name": "test-redis","fec2/bnsMount": "false","fec2/budget_execution_id": "","fec2/budget_number": "","fec2/buget-checked": "pass","fec2/cni-safe-region": "test_internal","fec2/combo_code": "567756428","fec2/device-id": "fec-PMOYhZDwP","fec2/disable-inject-sidecar": "false","fec2/fec-id": "fid-XxqWgKwoo","fec2/idc": "ZZJG","fec2/over_budget": "true","fec2/owner": "jiayiming_dxm","fec2/product_name": "siod-kafka","fec2/retain": "true","fec2/saas": "redis","fec2/service_level": "","fec2/version": "v0.0.2","fec2/white": "false"},"hostname": "r-test-0-14.redis.fec.kj01.zzjg.dxm-int.com","scheduleLimit": null,"containerInfo": [{"containerName": "r-test-0-14","imageName": "r.duxiaoman-int.com/siod_redis/redis-docker","imageVersion": "v2024072402","imagePullPolicy": "IfNotPresent","cpu": 1,"mem": 1,"port": null,"entryCommand": "","execArgs": null,"envArgs": {"CLOUD_TYPE": "kj","CONTAINER_NAME": "r-test-0-14","CONTAINER_TYPE": "main","DEVICE_TYPE": "pod","FECID": "fid-XxqWgKwoo","FEC_OPERATOR_URL": "https://************:11443/fec2","HAWKING_ENV": "container","INIT_NOAH_USER": "redis","MAX_MEMORY_POLICY": "volatile-lru","NODE_IP": "","POD_IP": "","POD_NAME": "","REDIS_PORT": "7000"},"diskApplication": 0,"mountType": "","volumeMounts": null,"status": "Running","privileged": false}]},
						{"id": "","podName": "r-test-1-ad","productName": "siod-kafka","appName": "test-redis","podIp": "**********","idc": "ZZJG","comboDesc": "","safeRegion": "test_internal","owner": "jiayiming_dxm","applicant": "","status": "Init","labels": {"avoid_delete_by_accident": "true","dxm-redis/component": "redis","dxm-redis/idc": "hbb","dxm-redis/index": "1","dxm-redis/name": "test","fec2/app_name": "test-redis","fec2/bnsMount": "false","fec2/budget_execution_id": "","fec2/budget_number": "","fec2/buget-checked": "pass","fec2/cni-safe-region": "test_internal","fec2/combo_code": "567756428","fec2/device-id": "fec-PMOYhZDwP","fec2/disable-inject-sidecar": "false","fec2/fec-id": "fid-XxqWgKwoo","fec2/idc": "ZZJG","fec2/over_budget": "true","fec2/owner": "jiayiming_dxm","fec2/product_name": "siod-kafka","fec2/retain": "true","fec2/saas": "redis","fec2/service_level": "","fec2/version": "v0.0.2","fec2/white": "false"},"hostname": "r-test-0-ad.redis.fec.kj01.zzjg.dxm-int.com","scheduleLimit": null,"containerInfo": [{"containerName": "r-test-1-ad","imageName": "r.duxiaoman-int.com/siod_redis/redis-docker","imageVersion": "v2024072402","imagePullPolicy": "IfNotPresent","cpu": 1,"mem": 1,"port": null,"entryCommand": "","execArgs": null,"envArgs": {"CLOUD_TYPE": "kj","CONTAINER_NAME": "r-test-1-ad","CONTAINER_TYPE": "main","DEVICE_TYPE": "pod","FECID": "fid-XxqWgKwoo","FEC_OPERATOR_URL": "https://************:11443/fec2","HAWKING_ENV": "container","INIT_NOAH_USER": "redis","MAX_MEMORY_POLICY": "volatile-lru","NODE_IP": "","POD_IP": "","POD_NAME": "","REDIS_PORT": "7000"},"diskApplication": 0,"mountType": "","volumeMounts": null,"status": "Running","privileged": false}]}
					]}`))

				// 10s 后设置成running
				go time.AfterFunc(5*time.Second, func() {
					httpmock.RegisterResponder("POST", "http://*************:8536/fec-manager/api/v1/fec/listPod",
						httpmock.NewStringResponder(200, `{"errno": 0,"errmsg": "","data": [
						{"id": "","podName": "r-test-0-14","productName": "siod-kafka","appName": "test-redis","podIp": "**********","idc": "ZZJG","comboDesc": "","safeRegion": "test_internal","owner": "jiayiming_dxm","applicant": "","status": "Running","labels": {"avoid_delete_by_accident": "true","dxm-redis/component": "redis","dxm-redis/idc": "hbb","dxm-redis/index": "0","dxm-redis/name": "test","fec2/app_name": "test-redis","fec2/bnsMount": "false","fec2/budget_execution_id": "","fec2/budget_number": "","fec2/buget-checked": "pass","fec2/cni-safe-region": "test_internal","fec2/combo_code": "567756428","fec2/device-id": "fec-PMOYhZDwP","fec2/disable-inject-sidecar": "false","fec2/fec-id": "fid-XxqWgKwoo","fec2/idc": "ZZJG","fec2/over_budget": "true","fec2/owner": "jiayiming_dxm","fec2/product_name": "siod-kafka","fec2/retain": "true","fec2/saas": "redis","fec2/service_level": "","fec2/version": "v0.0.2","fec2/white": "false"},"hostname": "r-test-0-14.redis.fec.kj01.zzjg.dxm-int.com","scheduleLimit": null,"containerInfo": [{"containerName": "r-test-0-14","imageName": "r.duxiaoman-int.com/siod_redis/redis-docker","imageVersion": "v2024072402","imagePullPolicy": "IfNotPresent","cpu": 1,"mem": 1,"port": null,"entryCommand": "","execArgs": null,"envArgs": {"CLOUD_TYPE": "kj","CONTAINER_NAME": "r-test-0-14","CONTAINER_TYPE": "main","DEVICE_TYPE": "pod","FECID": "fid-XxqWgKwoo","FEC_OPERATOR_URL": "https://************:11443/fec2","HAWKING_ENV": "container","INIT_NOAH_USER": "redis","MAX_MEMORY_POLICY": "volatile-lru","NODE_IP": "","POD_IP": "","POD_NAME": "","REDIS_PORT": "7000"},"diskApplication": 0,"mountType": "","volumeMounts": null,"status": "Running","privileged": false}]},
						{"id": "","podName": "r-test-1-ad","productName": "siod-kafka","appName": "test-redis","podIp": "**********","idc": "ZZJG","comboDesc": "","safeRegion": "test_internal","owner": "jiayiming_dxm","applicant": "","status": "Running","labels": {"avoid_delete_by_accident": "true","dxm-redis/component": "redis","dxm-redis/idc": "hbb","dxm-redis/index": "1","dxm-redis/name": "test","fec2/app_name": "test-redis","fec2/bnsMount": "false","fec2/budget_execution_id": "","fec2/budget_number": "","fec2/buget-checked": "pass","fec2/cni-safe-region": "test_internal","fec2/combo_code": "567756428","fec2/device-id": "fec-PMOYhZDwP","fec2/disable-inject-sidecar": "false","fec2/fec-id": "fid-XxqWgKwoo","fec2/idc": "ZZJG","fec2/over_budget": "true","fec2/owner": "jiayiming_dxm","fec2/product_name": "siod-kafka","fec2/retain": "true","fec2/saas": "redis","fec2/service_level": "","fec2/version": "v0.0.2","fec2/white": "false"},"hostname": "r-test-0-ad.redis.fec.kj01.zzjg.dxm-int.com","scheduleLimit": null,"containerInfo": [{"containerName": "r-test-1-ad","imageName": "r.duxiaoman-int.com/siod_redis/redis-docker","imageVersion": "v2024072402","imagePullPolicy": "IfNotPresent","cpu": 1,"mem": 1,"port": null,"entryCommand": "","execArgs": null,"envArgs": {"CLOUD_TYPE": "kj","CONTAINER_NAME": "r-test-1-ad","CONTAINER_TYPE": "main","DEVICE_TYPE": "pod","FECID": "fid-XxqWgKwoo","FEC_OPERATOR_URL": "https://************:11443/fec2","HAWKING_ENV": "container","INIT_NOAH_USER": "redis","MAX_MEMORY_POLICY": "volatile-lru","NODE_IP": "","POD_IP": "","POD_NAME": "","REDIS_PORT": "7000"},"diskApplication": 0,"mountType": "","volumeMounts": null,"status": "Running","privileged": false}]}
					]}`))
					httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/healthCheck`),
						httpmock.NewStringResponder(200, `{"code": "00000", "data": "ok"}`))
				})

				// upsert noah
				httpmock.RegisterResponder("GET", "http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-kafka/apps/r-save-test-redis/instances?showPage=0",
					httpmock.NewStringResponder(200, `{"success": true, "message": "OK", "data": [
						{"name": "0.test-redis.siod-kafka","hostName": "r-test-0-14.redis.fec.kj01.zzjg.dxm-int.com","ip": "**********","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 7000},"deployInfo": {"deployPath": "/home/<USER>/local/redis"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"deploy":"docker","idc": "hbb","service":"hbbs"},"disable": false,"instanceType": "host","podId": null,"containerList": []},
						{"name": "1.test-redis.siod-kafka","hostName": "r-test-0-ad.redis.fec.kj01.zzjg.dxm-int.com","ip": "**********","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 7000},"deployInfo": {"deployPath": "/home/<USER>/local/redis"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"deploy":"docker","idc": "hbb","service":"hbbs"},"disable": false,"instanceType": "host","podId": null,"containerList": []}
					]}`),
				)

				// insert database
				redisc.Mock().ExpectInfo("replication").SetVal("role:master")
				redisc.Mock().ExpectInfo("replication").SetVal("role:slave")
			},
			args: args{
				deployment: &Cluster{
					ID:    objC.ID,
					Name:  objC.Name,
					Alias: objC.Name,
					Spec: &Spec{
						Redis: &RedisSettings{
							Replicas:    map[string]int{"hbb": 1},
							NumOfShards: 2,
							Port:        7000,
						},
						App: &AppSettings{
							ProductLine: "siod-kafka",
							Subsystem:   "r-save-test",
							AppPrefix:   "r-save-test",
						},
					},
					EnabledAZ: []string{"hbb"},
				},
				ctx: &Context{
					ApplyNewPods: [3]bool{true, false, false},
				},
			},
			wantErr: false,
			expect: func(t *testing.T, ctx *Context) {
				if len(ctx.RedisInstances) != 2 {
					t.Errorf("expect redis instances len 2, but got %d", len(ctx.RedisInstances))
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := saveRedisPods(tt.args.deployment, tt.args.ctx)
			if (err != nil) != tt.wantErr {
				t.Errorf("saveRedisPods() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.expect != nil {
				tt.expect(t, tt.args.ctx)
			}
		})
	}
}

func Test_saveSentinelPods(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")
	objC := env.MockCluster("s_save_test")
	common.Init(&common.Config{
		IDC: map[string]string{
			"hba": "ZZJG",
			"hbb": "ZZJG",
			"hbc": "ZZJG",
		},
	})

	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		deployment *Cluster
		ctx        *Context
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, *Context)
	}{
		{
			name: "no new pod applied",
			args: args{
				deployment: &Cluster{},
				ctx: &Context{
					ApplyNewPods: [3]bool{false, false, false},
				},
			},
			wantErr: false,
		},
		{
			name: "wait 15s and pod running",
			before: func() {
				httpmock.RegisterResponder("POST", "http://*************:8536/fec-manager/api/v1/fec/listPod",
					httpmock.NewStringResponder(200, `{"errno": 0,"errmsg": "","data": [
						{"id": "","podName": "s-test-14","productName": "siod-kafka","appName": "test-sentinel","podIp": "*********","idc": "ZZJG","comboDesc": "","safeRegion": "test_internal","owner": "jiayiming_dxm","applicant": "","status": "Init","labels": {"avoid_delete_by_accident": "true","dxm-redis/component": "redis","dxm-redis/idc": "hbb","dxm-redis/index": "0","dxm-redis/name": "test","fec2/app_name": "test-sentinel","fec2/bnsMount": "false","fec2/budget_execution_id": "","fec2/budget_number": "","fec2/buget-checked": "pass","fec2/cni-safe-region": "test_internal","fec2/combo_code": "567756428","fec2/device-id": "fec-PMOYhZDwP","fec2/disable-inject-sidecar": "false","fec2/fec-id": "fid-XxqWgKwoo","fec2/idc": "ZZJG","fec2/over_budget": "true","fec2/owner": "jiayiming_dxm","fec2/product_name": "siod-kafka","fec2/retain": "true","fec2/saas": "redis","fec2/service_level": "","fec2/version": "v0.0.2","fec2/white": "false"},"hostname": "s-test-14.redis.fec.kj01.zzjg.dxm-int.com","scheduleLimit": null,"containerInfo": [{"containerName": "s-test-14","imageName": "r.duxiaoman-int.com/siod_redis/redis-docker","imageVersion": "v2024072402","imagePullPolicy": "IfNotPresent","cpu": 1,"mem": 1,"port": null,"entryCommand": "","execArgs": null,"envArgs": {"CLOUD_TYPE": "kj","CONTAINER_NAME": "s-test-14","CONTAINER_TYPE": "main","DEVICE_TYPE": "pod","FECID": "fid-XxqWgKwoo","FEC_OPERATOR_URL": "https://************:11443/fec2","HAWKING_ENV": "container","INIT_NOAH_USER": "redis","MAX_MEMORY_POLICY": "volatile-lru","NODE_IP": "","POD_IP": "","POD_NAME": "","REDIS_PORT": "7000"},"diskApplication": 0,"mountType": "","volumeMounts": null,"status": "Running","privileged": false}]}
					]}`))

				// 10s 后设置成running
				go time.AfterFunc(5*time.Second, func() {
					httpmock.RegisterResponder("POST", "http://*************:8536/fec-manager/api/v1/fec/listPod",
						httpmock.NewStringResponder(200, `{"errno": 0,"errmsg": "","data": [
						{"id": "","podName": "s-test-14","productName": "siod-kafka","appName": "test-sentinel","podIp": "*********","idc": "ZZJG","comboDesc": "","safeRegion": "test_internal","owner": "jiayiming_dxm","applicant": "","status": "Running","labels": {"avoid_delete_by_accident": "true","dxm-redis/component": "redis","dxm-redis/idc": "hbb","dxm-redis/index": "0","dxm-redis/name": "test","fec2/app_name": "test-sentinel","fec2/bnsMount": "false","fec2/budget_execution_id": "","fec2/budget_number": "","fec2/buget-checked": "pass","fec2/cni-safe-region": "test_internal","fec2/combo_code": "567756428","fec2/device-id": "fec-PMOYhZDwP","fec2/disable-inject-sidecar": "false","fec2/fec-id": "fid-XxqWgKwoo","fec2/idc": "ZZJG","fec2/over_budget": "true","fec2/owner": "jiayiming_dxm","fec2/product_name": "siod-kafka","fec2/retain": "true","fec2/saas": "redis","fec2/service_level": "","fec2/version": "v0.0.2","fec2/white": "false"},"hostname": "s-test-14.redis.fec.kj01.zzjg.dxm-int.com","scheduleLimit": null,"containerInfo": [{"containerName": "s-test-14","imageName": "r.duxiaoman-int.com/siod_redis/redis-docker","imageVersion": "v2024072402","imagePullPolicy": "IfNotPresent","cpu": 1,"mem": 1,"port": null,"entryCommand": "","execArgs": null,"envArgs": {"CLOUD_TYPE": "kj","CONTAINER_NAME": "s-test-14","CONTAINER_TYPE": "main","DEVICE_TYPE": "pod","FECID": "fid-XxqWgKwoo","FEC_OPERATOR_URL": "https://************:11443/fec2","HAWKING_ENV": "container","INIT_NOAH_USER": "redis","MAX_MEMORY_POLICY": "volatile-lru","NODE_IP": "","POD_IP": "","POD_NAME": "","REDIS_PORT": "7000"},"diskApplication": 0,"mountType": "","volumeMounts": null,"status": "Running","privileged": false}]}
					]}`))
					httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/healthCheck`),
						httpmock.NewStringResponder(200, `{"code": "00000", "data": "ok"}`))
				})

				// upsert noah
				httpmock.RegisterResponder("GET", "http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-kafka/apps/s-save-test-sentinel/instances?showPage=0",
					httpmock.NewStringResponder(200, `{"success": true, "message": "OK", "data": [
						{"name": "0.test-sentinel.siod-kafka","hostName": "s-test-14.redis.fec.kj01.zzjg.dxm-int.com","ip": "*********","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 9001},"deployInfo": {"deployPath": "/home/<USER>/local/sentinel"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"deploy":"docker","idc": "hbb","service":"hbbs"},"disable": false,"instanceType": "host","podId": null,"containerList": []}
					]}`),
				)
			},
			args: args{
				deployment: &Cluster{
					ID:    objC.ID,
					Name:  objC.Name,
					Alias: objC.Name,
					Spec: &Spec{
						Sentinel: &SentinelSettings{
							Replicas: map[string]int{"hbb": 1},
							Port:     9001,
						},
						App: &AppSettings{
							ProductLine: "siod-kafka",
							Subsystem:   "s-save-test",
							AppPrefix:   "s-save-test",
						},
					},
					EnabledAZ: []string{"hbb"},
				},
				ctx: &Context{
					ApplyNewPods: [3]bool{false, true, false},
				},
			},
			wantErr: false,
			expect: func(t *testing.T, ctx *Context) {
				if len(ctx.SentinelInstances) != 1 {
					t.Errorf("expect sentinel instances len 1, but got %d", len(ctx.SentinelInstances))
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := saveSentinelPods(tt.args.deployment, tt.args.ctx)
			if (err != nil) != tt.wantErr {
				t.Errorf("saveSentinelPods() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.expect != nil {
				tt.expect(t, tt.args.ctx)
			}
		})
	}
}

// saveRouterPods
func Test_saveRouterPods(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")
	objC := env.MockCluster("p_save_test")
	common.Init(&common.Config{
		IDC: map[string]string{
			"hba": "ZZJG",
			"hbb": "ZZJG",
			"hbc": "ZZJG",
		},
	})

	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		deployment *Cluster
		ctx        *Context
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, *Context)
	}{
		{
			name: "no new pod applied",
			args: args{
				deployment: &Cluster{},
				ctx: &Context{
					ApplyNewPods: [3]bool{false, false, false},
				},
			},
			wantErr: false,
		},
		{
			name: "wait 15s and pod running",
			before: func() {
				httpmock.RegisterResponder("POST", "http://*************:8536/fec-manager/api/v1/fec/listPod",
					httpmock.NewStringResponder(200, `{"errno": 0,"errmsg": "","data": [
						{"id": "","podName": "p-test-14","productName": "siod-kafka","appName": "test-router","podIp": "*********","idc": "ZZJG","comboDesc": "","safeRegion": "test_internal","owner": "jiayiming_dxm","applicant": "","status": "Init","labels": {"avoid_delete_by_accident": "true","dxm-redis/component": "redis","dxm-redis/idc": "hbb","dxm-redis/index": "0","dxm-redis/name": "test","fec2/app_name": "test-router","fec2/bnsMount": "false","fec2/budget_execution_id": "","fec2/budget_number": "","fec2/buget-checked": "pass","fec2/cni-safe-region": "test_internal","fec2/combo_code": "567756428","fec2/device-id": "fec-PMOYhZDwP","fec2/disable-inject-sidecar": "false","fec2/fec-id": "fid-XxqWgKwoo","fec2/idc": "ZZJG","fec2/over_budget": "true","fec2/owner": "jiayiming_dxm","fec2/product_name": "siod-kafka","fec2/retain": "true","fec2/saas": "redis","fec2/service_level": "","fec2/version": "v0.0.2","fec2/white": "false"},"hostname": "p-test-14.redis.fec.kj01.zzjg.dxm-int.com","scheduleLimit": null,"containerInfo": [{"containerName": "p-test-14","imageName": "r.duxiaoman-int.com/siod_redis/redis-docker","imageVersion": "v2024072402","imagePullPolicy": "IfNotPresent","cpu": 1,"mem": 1,"port": null,"entryCommand": "","execArgs": null,"envArgs": {"CLOUD_TYPE": "kj","CONTAINER_NAME": "p-test-14","CONTAINER_TYPE": "main","DEVICE_TYPE": "pod","FECID": "fid-XxqWgKwoo","FEC_OPERATOR_URL": "https://************:11443/fec2","HAWKING_ENV": "container","INIT_NOAH_USER": "redis","MAX_MEMORY_POLICY": "volatile-lru","NODE_IP": "","POD_IP": "","POD_NAME": "","REDIS_PORT": "7000"},"diskApplication": 0,"mountType": "","volumeMounts": null,"status": "Running","privileged": false}]},
						{"id": "","podName": "p-test-ad","productName": "siod-kafka","appName": "test-router","podIp": "*********","idc": "ZZJG","comboDesc": "","safeRegion": "test_internal","owner": "jiayiming_dxm","applicant": "","status": "Init","labels": {"avoid_delete_by_accident": "true","dxm-redis/component": "redis","dxm-redis/idc": "hbb","dxm-redis/index": "1","dxm-redis/name": "test","fec2/app_name": "test-router","fec2/bnsMount": "false","fec2/budget_execution_id": "","fec2/budget_number": "","fec2/buget-checked": "pass","fec2/cni-safe-region": "test_internal","fec2/combo_code": "567756428","fec2/device-id": "fec-PMOYhZDwP","fec2/disable-inject-sidecar": "false","fec2/fec-id": "fid-XxqWgKwoo","fec2/idc": "ZZJG","fec2/over_budget": "true","fec2/owner": "jiayiming_dxm","fec2/product_name": "siod-kafka","fec2/retain": "true","fec2/saas": "redis","fec2/service_level": "","fec2/version": "v0.0.2","fec2/white": "false"},"hostname": "p-test-ad.redis.fec.kj01.zzjg.dxm-int.com","scheduleLimit": null,"containerInfo": [{"containerName": "p-test-ad","imageName": "r.duxiaoman-int.com/siod_redis/redis-docker","imageVersion": "v2024072402","imagePullPolicy": "IfNotPresent","cpu": 1,"mem": 1,"port": null,"entryCommand": "","execArgs": null,"envArgs": {"CLOUD_TYPE": "kj","CONTAINER_NAME": "p-test-ad","CONTAINER_TYPE": "main","DEVICE_TYPE": "pod","FECID": "fid-XxqWgKwoo","FEC_OPERATOR_URL": "https://************:11443/fec2","HAWKING_ENV": "container","INIT_NOAH_USER": "redis","MAX_MEMORY_POLICY": "volatile-lru","NODE_IP": "","POD_IP": "","POD_NAME": "","REDIS_PORT": "7000"},"diskApplication": 0,"mountType": "","volumeMounts": null,"status": "Running","privileged": false}]}
					]}`))

				// 10s 后设置成running
				go time.AfterFunc(5*time.Second, func() {
					httpmock.RegisterResponder("POST", "http://*************:8536/fec-manager/api/v1/fec/listPod",
						httpmock.NewStringResponder(200, `{"errno": 0,"errmsg": "","data": [
						{"id": "","podName": "p-test-14","productName": "siod-kafka","appName": "test-router","podIp": "*********","idc": "ZZJG","comboDesc": "","safeRegion": "test_internal","owner": "jiayiming_dxm","applicant": "","status": "Running","labels": {"avoid_delete_by_accident": "true","dxm-redis/component": "redis","dxm-redis/idc": "hbb","dxm-redis/index": "0","dxm-redis/name": "test","fec2/app_name": "test-router","fec2/bnsMount": "false","fec2/budget_execution_id": "","fec2/budget_number": "","fec2/buget-checked": "pass","fec2/cni-safe-region": "test_internal","fec2/combo_code": "567756428","fec2/device-id": "fec-PMOYhZDwP","fec2/disable-inject-sidecar": "false","fec2/fec-id": "fid-XxqWgKwoo","fec2/idc": "ZZJG","fec2/over_budget": "true","fec2/owner": "jiayiming_dxm","fec2/product_name": "siod-kafka","fec2/retain": "true","fec2/saas": "redis","fec2/service_level": "","fec2/version": "v0.0.2","fec2/white": "false"},"hostname": "p-test-14.redis.fec.kj01.zzjg.dxm-int.com","scheduleLimit": null,"containerInfo": [{"containerName": "p-test-14","imageName": "r.duxiaoman-int.com/siod_redis/redis-docker","imageVersion": "v2024072402","imagePullPolicy": "IfNotPresent","cpu": 1,"mem": 1,"port": null,"entryCommand": "","execArgs": null,"envArgs": {"CLOUD_TYPE": "kj","CONTAINER_NAME": "p-test-14","CONTAINER_TYPE": "main","DEVICE_TYPE": "pod","FECID": "fid-XxqWgKwoo","FEC_OPERATOR_URL": "https://************:11443/fec2","HAWKING_ENV": "container","INIT_NOAH_USER": "redis","MAX_MEMORY_POLICY": "volatile-lru","NODE_IP": "","POD_IP": "","POD_NAME": "","REDIS_PORT": "7000"},"diskApplication": 0,"mountType": "","volumeMounts": null,"status": "Running","privileged": false}]},
						{"id": "","podName": "p-test-ad","productName": "siod-kafka","appName": "test-router","podIp": "*********","idc": "ZZJG","comboDesc": "","safeRegion": "test_internal","owner": "jiayiming_dxm","applicant": "","status": "Running","labels": {"avoid_delete_by_accident": "true","dxm-redis/component": "redis","dxm-redis/idc": "hbb","dxm-redis/index": "1","dxm-redis/name": "test","fec2/app_name": "test-router","fec2/bnsMount": "false","fec2/budget_execution_id": "","fec2/budget_number": "","fec2/buget-checked": "pass","fec2/cni-safe-region": "test_internal","fec2/combo_code": "567756428","fec2/device-id": "fec-PMOYhZDwP","fec2/disable-inject-sidecar": "false","fec2/fec-id": "fid-XxqWgKwoo","fec2/idc": "ZZJG","fec2/over_budget": "true","fec2/owner": "jiayiming_dxm","fec2/product_name": "siod-kafka","fec2/retain": "true","fec2/saas": "redis","fec2/service_level": "","fec2/version": "v0.0.2","fec2/white": "false"},"hostname": "p-test-ad.redis.fec.kj01.zzjg.dxm-int.com","scheduleLimit": null,"containerInfo": [{"containerName": "p-test-ad","imageName": "r.duxiaoman-int.com/siod_redis/redis-docker","imageVersion": "v2024072402","imagePullPolicy": "IfNotPresent","cpu": 1,"mem": 1,"port": null,"entryCommand": "","execArgs": null,"envArgs": {"CLOUD_TYPE": "kj","CONTAINER_NAME": "p-test-ad","CONTAINER_TYPE": "main","DEVICE_TYPE": "pod","FECID": "fid-XxqWgKwoo","FEC_OPERATOR_URL": "https://************:11443/fec2","HAWKING_ENV": "container","INIT_NOAH_USER": "redis","MAX_MEMORY_POLICY": "volatile-lru","NODE_IP": "","POD_IP": "","POD_NAME": "","REDIS_PORT": "7000"},"diskApplication": 0,"mountType": "","volumeMounts": null,"status": "Running","privileged": false}]}
					]}`))
					httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/healthCheck`),
						httpmock.NewStringResponder(200, `{"code": "00000", "data": "ok"}`))
				})

				// upsert noah
				httpmock.RegisterResponder("GET", "http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-kafka/apps/p-save-test-router/instances?showPage=0",
					httpmock.NewStringResponder(200, `{"success": true, "message": "OK", "data": [
						{"name": "0.test-router.siod-kafka","hostName": "p-test-14.redis.fec.kj01.zzjg.dxm-int.com","ip": "*********","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8001},"deployInfo": {"deployPath": "/home/<USER>/local/router"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"deploy":"docker","idc": "hbb","service":"hbbs"},"disable": false,"instanceType": "host","podId": null,"containerList": []},
						{"name": "1.test-router.siod-kafka","hostName": "p-test-ad.redis.fec.kj01.zzjg.dxm-int.com","ip": "*********","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8001},"deployInfo": {"deployPath": "/home/<USER>/local/router"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"deploy":"docker","idc": "hbb","service":"hbbs"},"disable": false,"instanceType": "host","podId": null,"containerList": []}
					]}`),
				)
			},
			args: args{
				deployment: &Cluster{
					ID:    objC.ID,
					Name:  objC.Name,
					Alias: objC.Name,
					Spec: &Spec{
						Router: &RouterSettings{
							Replicas: map[string]int{"hbb": 2},
							Port:     8001,
						},
						App: &AppSettings{
							ProductLine: "siod-kafka",
							Subsystem:   "p-save-test",
							AppPrefix:   "p-save-test",
						},
					},
					EnabledAZ: []string{"hbb"},
				},
				ctx: &Context{
					ApplyNewPods: [3]bool{false, false, true},
				},
			},
			wantErr: false,
			expect: func(t *testing.T, ctx *Context) {
				if len(ctx.RouterInstances) != 2 {
					t.Errorf("expect router instances len 2, but got %d", len(ctx.RouterInstances))
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := saveRouterPods(tt.args.deployment, tt.args.ctx)
			if (err != nil) != tt.wantErr {
				t.Errorf("saveRouterPods() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.expect != nil {
				tt.expect(t, tt.args.ctx)
			}
		})
	}
}

// =======================================
// 		 STEP 4&12 POD白名单
// =======================================

func Test_ensureWhitelist(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")
	objC := env.MockCluster("r3_test")

	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		deployment *Cluster
		component  string
		instances  []*omodel.Instance
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
	}{
		{
			name: "router whitelist bns invalid",
			before: func() {
				db, _ := mysql.Database()
				db.Whitelist.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetType("bns").SetValue("jiayiming.unit").SetPrivilege("z").Save(context.Background())
			},
			args: args{
				deployment: &Cluster{
					Name:  "r3_test",
					Alias: "r3-test",
					Spec: &Spec{
						ClusterWhitelist: []string{"r3-test-uniq.siod-redis"},
						Router: &RouterSettings{
							Port: 8005,
						},
						App: &AppSettings{
							ProductLine: "siod-kafka",
							AppPrefix:   "r3-test",
						},
					},
				},
				component: common.COMPONENT_ROUTER,
				instances: []*omodel.Instance{
					{IP: "************"},
					{IP: "************"},
				},
			},
			wantErr: true,
		},
		{
			name: "router whitelist ip invalid",
			before: func() {
				db, _ := mysql.Database()
				db.Whitelist.Delete().Exec(context.Background())
				db.Whitelist.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetType("ip").SetValue("************").SetPrivilege("a").Save(context.Background())
			},
			args: args{
				deployment: &Cluster{
					Name:  "r3_test",
					Alias: "r3-test",
					Spec: &Spec{
						ClusterWhitelist: []string{"r3-test-uniq.siod-redis"},
						Router: &RouterSettings{
							Port: 8005,
						},
						App: &AppSettings{
							ProductLine: "siod-kafka",
							AppPrefix:   "r3-test",
						},
					},
				},
				component: common.COMPONENT_ROUTER,
				instances: []*omodel.Instance{
					{IP: "************"},
					{IP: "************"},
				},
			},
			wantErr: true,
		},
		{
			name: "GetManagementWhiteListFromDB failed",
			before: func() {
				db, _ := mysql.Database()
				db.Whitelist.Delete().Exec(context.Background())
				db.Whitelist.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetType("bns").SetValue("jiayiming.unit").SetPrivilege("rw").Save(context.Background())
				db.Whitelist.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetType("bns").SetValue("jiayiming.unit-test").SetPrivilege("rw").Save(context.Background())
				db.Whitelist.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetType("ip").SetValue("************").SetPrivilege("rw").Save(context.Background())
				db.Whitelist.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetType("ip").SetValue("***********").SetPrivilege("rw").Save(context.Background())
			},
			args: args{
				deployment: &Cluster{
					Name:  "r3_test",
					Alias: "r3-test",
					Spec: &Spec{
						ClusterWhitelist: []string{"r3-test-uniq.siod-redis"},
						Router: &RouterSettings{
							Port: 8005,
						},
						App: &AppSettings{
							ProductLine: "siod-kafka",
							AppPrefix:   "r3-test",
						},
					},
				},
				component: common.COMPONENT_ROUTER,
				instances: []*omodel.Instance{
					{IP: "************"},
					{IP: "************"},
				},
			},
			wantErr: true,
		},
		{
			name: "agent UpdateWhitelist failed",
			before: func() {
				db, _ := mysql.Database()
				db.Configuration.Create().SetName("management_bns").SetValue("redis-manager.siod-redis").Exec(context.Background())
				db.Configuration.Create().SetName(omodel.CONFIG_REDIS_READONLY_BNS).SetValue("SecurityAuditEngine.hawking").Save(context.Background())
			},
			args: args{
				deployment: &Cluster{
					Name:  "r3_test",
					Alias: "r3-test",
					Spec: &Spec{
						ClusterWhitelist: []string{"r3-test-uniq.siod-redis"},
						Router: &RouterSettings{
							Port: 8005,
						},
						App: &AppSettings{
							ProductLine: "siod-kafka",
							AppPrefix:   "r3-test",
						},
					},
				},
				component: common.COMPONENT_ROUTER,
				instances: []*omodel.Instance{
					{IP: "************"},
					{IP: "************"},
				},
			},
			wantErr: true,
		},
		{
			name: "router whitelist success",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/whitelist/update`),
					httpmock.NewStringResponder(200, `{"code": "00000", "data": "mockok"}`))
			},
			args: args{
				deployment: &Cluster{
					Name:  "r3_test",
					Alias: "r3-test",
					Spec: &Spec{
						ClusterWhitelist: []string{"r3-test-uniq.siod-redis"},
						Router: &RouterSettings{
							Port: 8005,
						},
						App: &AppSettings{
							ProductLine: "siod-kafka",
							AppPrefix:   "r3-test",
						},
					},
				},
				component: common.COMPONENT_ROUTER,
				instances: []*omodel.Instance{
					{IP: "************"},
					{IP: "************"},
				},
			},
			wantErr: false,
		},
		{
			name: "redis whitelist success",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/whitelist/update`),
					httpmock.NewStringResponder(200, `{"code": "00000", "data": "mockok"}`))
			},
			args: args{
				deployment: &Cluster{
					Name:  "r3_test",
					Alias: "r3-test",
					Spec: &Spec{
						ClusterWhitelist: []string{"r3-test-uniq.siod-redis"},
						Redis:            &RedisSettings{Port: 7000},
						Sentinel:         &SentinelSettings{Port: 9001},
						Router:           &RouterSettings{Port: 8005},
						App: &AppSettings{
							ProductLine: "siod-kafka",
							AppPrefix:   "r3-test",
						},
					},
				},
				component: common.COMPONENT_REDIS,
				instances: []*omodel.Instance{
					{IP: "************"},
					{IP: "************"},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}

			err := ensureWhitelist(tt.args.deployment, tt.args.component, tt.args.instances)
			if (err != nil) != tt.wantErr {
				t.Errorf("ensureWhitelist() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_ensureRedisAndSentinelWhitelist(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")
	objC := env.MockCluster("r3_test")

	type args struct {
		deployment *Cluster
		context    *Context
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
	}{
		{
			name: "success",
			before: func() {
				db, _ := mysql.Database()
				db.Configuration.Create().SetName("management_bns").SetValue("redis-manager.siod-redis").Exec(context.Background())
				db.Whitelist.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetType("bns").SetValue("jiayiming.unit").SetPrivilege("z").Save(context.Background())
			},
			args: args{
				deployment: &Cluster{
					Name:  "r3_test",
					Alias: "r3-test",
					Spec: &Spec{
						ClusterWhitelist: []string{"r3-test-uniq.siod-redis"},
						Redis:            &RedisSettings{Port: 7000},
						Sentinel:         &SentinelSettings{Port: 9001},
						Router:           &RouterSettings{Port: 8005},
						App: &AppSettings{
							ProductLine: "siod-kafka",
							AppPrefix:   "r3-test",
						},
					},
				},
				context: &Context{
					RedisInstances:    []*omodel.Instance{},
					SentinelInstances: []*omodel.Instance{},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}

			err := ensureRedisAndSentinelWhitelist(tt.args.deployment, tt.args.context)
			if (err != nil) != tt.wantErr {
				t.Errorf("ensureRedisAndSentinelWhitelist() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

// =======================================
// 		STEP 5  Redis分片关系管理
// =======================================

func Test_findAllReplicas(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")

	var (
		masters []*omodel.Instance
		slaves  []*omodel.Instance
		flexs   []*omodel.Instance
	)

	type args struct {
		deployment *Cluster
		ip         string
		port       int
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "master",
			before: func() {
				masters = []*omodel.Instance{}
				slaves = []*omodel.Instance{}
				flexs = []*omodel.Instance{}
				redisc.Mock().ExpectInfo(redisc.REPLICATION).SetVal("role:master\nslave0:ip=*********,port=7000,state=online,offset=4473\nslave1:ip=************,port=7000,state=online,offset=4473")
			},
			args: args{
				deployment: &Cluster{
					Name: "test",
					Spec: &Spec{
						Redis: &RedisSettings{NumOfShards: 2},
						Sentinel: &SentinelSettings{
							Port:     9001,
							Replicas: map[string]int{"hba": 1, "hbb": 1, "hbc": 1},
						},
					},
					EnabledAZ: []string{"hbb"},
					Status:    DEPLOYMENT_STATUS_NORMAL,
				},
				ip:   "*********",
				port: 7000,
			},
			wantErr: false,
			expect: func(t *testing.T) {
				if len(masters) != 1 {
					t.Errorf("expected 1 master, but got %d", len(masters))
				}
				if len(slaves) != 2 {
					t.Errorf("expected 2 slaves, but got %d", len(slaves))
				}
				if len(flexs) != 0 {
					t.Errorf("expected 0 flex, but got %d", len(flexs))
				}
			},
		},
		{
			name: "slave",
			before: func() {
				masters = []*omodel.Instance{}
				slaves = []*omodel.Instance{}
				flexs = []*omodel.Instance{}
				redisc.Mock().ExpectInfo(redisc.REPLICATION).SetVal("role:slave\nmaster_host:*********\nmaster_port:7000")
				redisc.Mock().ExpectInfo(redisc.REPLICATION).SetVal("role:master\nslave0:ip=*********,port=7000,state=online,offset=4473\nslave1:ip=************,port=7000,state=online,offset=4473")
			},
			args: args{
				deployment: &Cluster{
					Name: "test",
					Spec: &Spec{
						Redis: &RedisSettings{NumOfShards: 2},
						Sentinel: &SentinelSettings{
							Port:     9001,
							Replicas: map[string]int{"hba": 1, "hbb": 1, "hbc": 1},
						},
					},
					EnabledAZ: []string{"hbb"},
					Status:    DEPLOYMENT_STATUS_NORMAL,
				},
				ip:   "*********",
				port: 7000,
			},
			wantErr: false,
			expect: func(t *testing.T) {
				if len(masters) != 1 {
					t.Errorf("expected 1 master, but got %d", len(masters))
				}
				if len(slaves) != 2 {
					t.Errorf("expected 2 slaves, but got %d", len(slaves))
				}
				if len(flexs) != 0 {
					t.Errorf("expected 0 flex, but got %d", len(flexs))
				}
			},
		},
		{
			name: "flex",
			before: func() {
				masters = []*omodel.Instance{}
				slaves = []*omodel.Instance{}
				flexs = []*omodel.Instance{}
				redisc.Mock().ExpectInfo(redisc.REPLICATION).SetVal("role:slave\nmaster_host:127.0.0.1\nmaster_port:7000")
			},
			args: args{
				deployment: &Cluster{
					Name: "test",
					Spec: &Spec{
						Redis: &RedisSettings{NumOfShards: 2},
						Sentinel: &SentinelSettings{
							Port:     9001,
							Replicas: map[string]int{"hba": 1, "hbb": 1, "hbc": 1},
						},
					},
					EnabledAZ: []string{"hbb"},
					Status:    DEPLOYMENT_STATUS_NORMAL,
				},
				ip:   "*********",
				port: 7000,
			},
			wantErr: false,
			expect: func(t *testing.T) {
				if len(masters) != 0 {
					t.Errorf("expected 0 master, but got %d", len(masters))
				}
				if len(slaves) != 0 {
					t.Errorf("expected 0 slaves, but got %d", len(slaves))
				}
				if len(flexs) != 1 {
					t.Errorf("expected 1 flex, but got %d", len(flexs))
				}
			},
		},
		{
			name: "first master then slave",
			before: func() {
				masters = []*omodel.Instance{
					{IP: "*********", Port: 7000},
				}
				slaves = []*omodel.Instance{
					{IP: "*********", Port: 7000},
					{IP: "************", Port: 7000},
				}
				flexs = []*omodel.Instance{}

				redisc.Mock().ExpectInfo(redisc.REPLICATION).SetVal("role:slave\nmaster_host:*********\nmaster_port:7000")
				redisc.Mock().ExpectInfo(redisc.REPLICATION).SetVal("role:master\nslave0:ip=*********,port=7000,state=online,offset=4473\nslave1:ip=************,port=7000,state=online,offset=4473")
			},
			args: args{
				deployment: &Cluster{
					Name: "test",
					Spec: &Spec{
						Redis: &RedisSettings{NumOfShards: 2},
						Sentinel: &SentinelSettings{
							Port:     9001,
							Replicas: map[string]int{"hba": 1, "hbb": 1, "hbc": 1},
						},
					},
					EnabledAZ: []string{"hbb"},
					Status:    DEPLOYMENT_STATUS_NORMAL,
				},
				ip:   "*********",
				port: 7000,
			},
			wantErr: false,
			expect: func(t *testing.T) {
				if len(masters) != 1 {
					t.Errorf("expected 1 master, but got %d", len(masters))
				}
				if len(slaves) != 2 {
					t.Errorf("expected 2 slaves, but got %d", len(slaves))
				}
				if len(flexs) != 0 {
					t.Errorf("expected 0 flex, but got %d", len(flexs))
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			now := time.Now()
			findAllReplicas(tt.args.ip, tt.args.port, &masters, &slaves, &flexs)
			// if (err != nil) != tt.wantErr {
			// 	t.Errorf("findAllReplicas() error = %v, wantErr %v", err, tt.wantErr)
			// }
			log.Println(time.Since(now))
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

func Test_asyncSlaveOf(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")

	type args struct {
		deployment *Cluster
		pairs      [][2]*omodel.Instance
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "slaveof 4 shard",
			before: func() {
				redisc.Mock().MatchExpectationsInOrder(false)
				redisc.Mock().ExpectSlaveOf("*********", "7000").SetVal("OK")
				redisc.Mock().ExpectSlaveOf("*********", "7000").SetVal("OK")
				redisc.Mock().ExpectSlaveOf("*********", "7000").SetVal("OK")
				redisc.Mock().ExpectSlaveOf("*********", "7000").SetVal("OK")

				redisc.Mock().ExpectInfo(redisc.REPLICATION).SetVal("role:slave\nmaster_host:*********\nmaster_port:7000\nmaster_link_status:up\nmaster_sync_in_progress:0")
				redisc.Mock().ExpectInfo(redisc.REPLICATION).SetVal("role:slave\nmaster_host:*********\nmaster_port:7000\nmaster_link_status:up\nmaster_sync_in_progress:0")
				redisc.Mock().ExpectInfo(redisc.REPLICATION).SetVal("role:slave\nmaster_host:*********\nmaster_port:7000\nmaster_link_status:up\nmaster_sync_in_progress:0")
				redisc.Mock().ExpectInfo(redisc.REPLICATION).SetVal("role:slave\nmaster_host:*********\nmaster_port:7000\nmaster_link_status:up\nmaster_sync_in_progress:0")
				redisc.Mock().ExpectSet("op_sync_check", time.Now().Format("200601021504"), 5*time.Second).SetVal("ok")
				redisc.Mock().ExpectGet("op_sync_check").SetVal(time.Now().Format("200601021504"))
				redisc.Mock().ExpectSet("op_sync_check", time.Now().Format("200601021504"), 5*time.Second).SetVal("ok")
				redisc.Mock().ExpectGet("op_sync_check").SetVal(time.Now().Format("200601021504"))
				redisc.Mock().ExpectSet("op_sync_check", time.Now().Format("200601021504"), 5*time.Second).SetVal("ok")
				redisc.Mock().ExpectGet("op_sync_check").SetVal(time.Now().Format("200601021504"))
				redisc.Mock().ExpectSet("op_sync_check", time.Now().Format("200601021504"), 5*time.Second).SetVal("ok")
				redisc.Mock().ExpectGet("op_sync_check").SetVal(time.Now().Format("200601021504"))
			},
			args: args{
				deployment: &Cluster{
					Name: "test",
					Spec: &Spec{
						Redis: &RedisSettings{NumOfShards: 4},
					},
				},
				pairs: [][2]*omodel.Instance{
					{&omodel.Instance{IP: "*********", Port: 7000}, &omodel.Instance{IP: "*********", Port: 7000}},
					{&omodel.Instance{IP: "*********", Port: 7000}, &omodel.Instance{IP: "*********", Port: 7000}},
					{&omodel.Instance{IP: "*********", Port: 7000}, &omodel.Instance{IP: "*********", Port: 7000}},
					{&omodel.Instance{IP: "*********", Port: 7000}, &omodel.Instance{IP: "*********", Port: 7000}},
				},
			},
		},
		{
			name: "slaveof 12 shard",
			before: func() {
				redisc.Mock().MatchExpectationsInOrder(false)
				redisc.Mock().ExpectSlaveOf("*********", "7000").SetVal("OK")
				redisc.Mock().ExpectSlaveOf("*********", "7000").SetVal("OK")
				redisc.Mock().ExpectSlaveOf("*********", "7000").SetVal("OK")
				redisc.Mock().ExpectSlaveOf("*********", "7000").SetVal("OK")
				redisc.Mock().ExpectSlaveOf("*********", "7000").SetVal("OK")
				redisc.Mock().ExpectSlaveOf("*********", "7000").SetVal("OK")
				redisc.Mock().ExpectSlaveOf("*********", "7000").SetVal("OK")
				redisc.Mock().ExpectSlaveOf("*********", "7000").SetVal("OK")
				redisc.Mock().ExpectSlaveOf("*********", "7000").SetVal("OK")
				redisc.Mock().ExpectSlaveOf("*********", "7000").SetVal("OK")
				redisc.Mock().ExpectSlaveOf("*********", "7000").SetVal("OK")
				redisc.Mock().ExpectSlaveOf("*********", "7000").SetVal("OK")

				redisc.Mock().ExpectInfo(redisc.REPLICATION).SetVal("role:slave\nmaster_host:*********\nmaster_port:7000\nmaster_link_status:up\nmaster_sync_in_progress:0")
				redisc.Mock().ExpectInfo(redisc.REPLICATION).SetVal("role:slave\nmaster_host:*********\nmaster_port:7000\nmaster_link_status:up\nmaster_sync_in_progress:0")
				redisc.Mock().ExpectInfo(redisc.REPLICATION).SetVal("role:slave\nmaster_host:*********\nmaster_port:7000\nmaster_link_status:up\nmaster_sync_in_progress:0")
				redisc.Mock().ExpectInfo(redisc.REPLICATION).SetVal("role:slave\nmaster_host:*********\nmaster_port:7000\nmaster_link_status:up\nmaster_sync_in_progress:0")
				redisc.Mock().ExpectInfo(redisc.REPLICATION).SetVal("role:slave\nmaster_host:*********\nmaster_port:7000\nmaster_link_status:up\nmaster_sync_in_progress:0")
				redisc.Mock().ExpectInfo(redisc.REPLICATION).SetVal("role:slave\nmaster_host:*********\nmaster_port:7000\nmaster_link_status:up\nmaster_sync_in_progress:0")
				redisc.Mock().ExpectInfo(redisc.REPLICATION).SetVal("role:slave\nmaster_host:*********\nmaster_port:7000\nmaster_link_status:up\nmaster_sync_in_progress:0")
				redisc.Mock().ExpectInfo(redisc.REPLICATION).SetVal("role:slave\nmaster_host:*********\nmaster_port:7000\nmaster_link_status:up\nmaster_sync_in_progress:0")
				redisc.Mock().ExpectInfo(redisc.REPLICATION).SetVal("role:slave\nmaster_host:*********\nmaster_port:7000\nmaster_link_status:up\nmaster_sync_in_progress:0")
				redisc.Mock().ExpectInfo(redisc.REPLICATION).SetVal("role:slave\nmaster_host:*********\nmaster_port:7000\nmaster_link_status:up\nmaster_sync_in_progress:0")
				redisc.Mock().ExpectInfo(redisc.REPLICATION).SetVal("role:slave\nmaster_host:*********\nmaster_port:7000\nmaster_link_status:up\nmaster_sync_in_progress:0")
				redisc.Mock().ExpectInfo(redisc.REPLICATION).SetVal("role:slave\nmaster_host:*********\nmaster_port:7000\nmaster_link_status:up\nmaster_sync_in_progress:0")
				redisc.Mock().ExpectSet("op_sync_check", time.Now().Format("200601021504"), 5*time.Second).SetVal("ok")
				redisc.Mock().ExpectGet("op_sync_check").SetVal(time.Now().Format("200601021504"))
				redisc.Mock().ExpectSet("op_sync_check", time.Now().Format("200601021504"), 5*time.Second).SetVal("ok")
				redisc.Mock().ExpectGet("op_sync_check").SetVal(time.Now().Format("200601021504"))
				redisc.Mock().ExpectSet("op_sync_check", time.Now().Format("200601021504"), 5*time.Second).SetVal("ok")
				redisc.Mock().ExpectGet("op_sync_check").SetVal(time.Now().Format("200601021504"))
				redisc.Mock().ExpectSet("op_sync_check", time.Now().Format("200601021504"), 5*time.Second).SetVal("ok")
				redisc.Mock().ExpectGet("op_sync_check").SetVal(time.Now().Format("200601021504"))
				redisc.Mock().ExpectSet("op_sync_check", time.Now().Format("200601021504"), 5*time.Second).SetVal("ok")
				redisc.Mock().ExpectGet("op_sync_check").SetVal(time.Now().Format("200601021504"))
				redisc.Mock().ExpectSet("op_sync_check", time.Now().Format("200601021504"), 5*time.Second).SetVal("ok")
				redisc.Mock().ExpectGet("op_sync_check").SetVal(time.Now().Format("200601021504"))
				redisc.Mock().ExpectSet("op_sync_check", time.Now().Format("200601021504"), 5*time.Second).SetVal("ok")
				redisc.Mock().ExpectGet("op_sync_check").SetVal(time.Now().Format("200601021504"))
				redisc.Mock().ExpectSet("op_sync_check", time.Now().Format("200601021504"), 5*time.Second).SetVal("ok")
				redisc.Mock().ExpectGet("op_sync_check").SetVal(time.Now().Format("200601021504"))
				redisc.Mock().ExpectSet("op_sync_check", time.Now().Format("200601021504"), 5*time.Second).SetVal("ok")
				redisc.Mock().ExpectGet("op_sync_check").SetVal(time.Now().Format("200601021504"))
				redisc.Mock().ExpectSet("op_sync_check", time.Now().Format("200601021504"), 5*time.Second).SetVal("ok")
				redisc.Mock().ExpectGet("op_sync_check").SetVal(time.Now().Format("200601021504"))
				redisc.Mock().ExpectSet("op_sync_check", time.Now().Format("200601021504"), 5*time.Second).SetVal("ok")
				redisc.Mock().ExpectGet("op_sync_check").SetVal(time.Now().Format("200601021504"))
				redisc.Mock().ExpectSet("op_sync_check", time.Now().Format("200601021504"), 5*time.Second).SetVal("ok")
				redisc.Mock().ExpectGet("op_sync_check").SetVal(time.Now().Format("200601021504"))
			},
			args: args{
				deployment: &Cluster{
					Name: "test",
					Spec: &Spec{
						Redis: &RedisSettings{NumOfShards: 12},
					},
				},
				pairs: [][2]*omodel.Instance{
					{&omodel.Instance{IP: "*********", Port: 7000}, &omodel.Instance{IP: "*********", Port: 7000}},
					{&omodel.Instance{IP: "*********", Port: 7000}, &omodel.Instance{IP: "*********", Port: 7000}},
					{&omodel.Instance{IP: "*********", Port: 7000}, &omodel.Instance{IP: "*********", Port: 7000}},
					{&omodel.Instance{IP: "*********", Port: 7000}, &omodel.Instance{IP: "*********", Port: 7000}},
					{&omodel.Instance{IP: "*********", Port: 7000}, &omodel.Instance{IP: "*********", Port: 7000}},
					{&omodel.Instance{IP: "*********", Port: 7000}, &omodel.Instance{IP: "*********", Port: 7000}},
					{&omodel.Instance{IP: "*********", Port: 7000}, &omodel.Instance{IP: "*********", Port: 7000}},
					{&omodel.Instance{IP: "*********", Port: 7000}, &omodel.Instance{IP: "*********", Port: 7000}},
					{&omodel.Instance{IP: "*********", Port: 7000}, &omodel.Instance{IP: "*********", Port: 7000}},
					{&omodel.Instance{IP: "*********", Port: 7000}, &omodel.Instance{IP: "*********", Port: 7000}},
					{&omodel.Instance{IP: "*********", Port: 7000}, &omodel.Instance{IP: "*********", Port: 7000}},
					{&omodel.Instance{IP: "*********", Port: 7000}, &omodel.Instance{IP: "*********", Port: 7000}},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			now := time.Now()
			asyncSlaveOf(tt.args.deployment, tt.args.pairs)
			// if (err != nil) != tt.wantErr {
			// 	t.Errorf("findAllReplicas() error = %v, wantErr %v", err, tt.wantErr)
			// }
			log.Println(time.Since(now))
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

func Test_checkShardMaster(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")

	type args struct {
		deployment *Cluster
		ctx        *Context
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "new deploy",
			before: func() {
				redisc.Mock().ExpectInfo(redisc.REPLICATION).SetVal("role:slave\nmaster_host:127.0.0.1\nmaster_port:7000")
				redisc.Mock().ExpectInfo(redisc.REPLICATION).SetVal("role:slave\nmaster_host:127.0.0.1\nmaster_port:7000")
				redisc.Mock().ExpectSlaveOf("no", "one").SetVal("OK")

				redisc.Mock().ExpectInfo(redisc.REPLICATION).SetVal("role:slave\nmaster_host:127.0.0.1\nmaster_port:7000")
				redisc.Mock().ExpectInfo(redisc.REPLICATION).SetVal("role:slave\nmaster_host:127.0.0.1\nmaster_port:7000")
				redisc.Mock().ExpectSlaveOf("no", "one").SetVal("OK")
			},
			args: args{
				deployment: &Cluster{
					Name: "test",
					Spec: &Spec{
						Redis: &RedisSettings{
							Port:        7000,
							Replicas:    map[string]int{"hba": 1, "hbb": 1},
							NumOfShards: 2,
						},
					},
					InspectionResult: &InspectionResult{State: INSPECTION_STATE_TEST},
					EnabledAZ:        []string{"hba", "hbb", "hbc"},
					Status:           DEPLOYMENT_STATUS_INIT,
				},
				ctx: &Context{
					RedisInstances: []*omodel.Instance{
						{Name: "test-server1", IP: "*********", Port: 7000},
						{Name: "test-server1", IP: "*********", Port: 7000},
						{Name: "test-server2", IP: "*********", Port: 7000},
						{Name: "test-server2", IP: "*********", Port: 7000},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "no master",
			before: func() {
				redisc.Mock().ExpectInfo(redisc.REPLICATION).SetVal("role:slave\nmaster_host:127.0.0.1\nmaster_port:7000")
				redisc.Mock().ExpectInfo(redisc.REPLICATION).SetVal("role:slave\nmaster_host:127.0.0.1\nmaster_port:7000")
			},
			args: args{
				deployment: &Cluster{
					Name: "test",
					Spec: &Spec{
						Redis: &RedisSettings{
							Port:        7000,
							Replicas:    map[string]int{"hba": 1, "hbb": 1},
							NumOfShards: 2,
						},
					},
					InspectionResult: &InspectionResult{State: INSPECTION_STATE_TEST},
					EnabledAZ:        []string{"hba", "hbb", "hbc"},
					Status:           DEPLOYMENT_STATUS_NORMAL,
				},
				ctx: &Context{
					RedisInstances: []*omodel.Instance{
						{Name: "test-server1", IP: "*********", Port: 7000},
						{Name: "test-server1", IP: "*********", Port: 7000},
						{Name: "test-server2", IP: "*********", Port: 7000},
						{Name: "test-server2", IP: "*********", Port: 7000},
					},
				},
			},
			wantErr: true,
		},
		{
			name: "multi master",
			before: func() {
				redisc.Mock().ExpectInfo(redisc.REPLICATION).SetVal("role:master")
				redisc.Mock().ExpectInfo(redisc.REPLICATION).SetVal("role:slave\nmaster_host:127.0.0.1\nmaster_port:7000")

				redisc.Mock().ExpectInfo(redisc.REPLICATION).SetVal("role:master")
				redisc.Mock().ExpectInfo(redisc.REPLICATION).SetVal("role:slave\nmaster_host:*********\nmaster_port:7000")
				redisc.Mock().ExpectInfo(redisc.REPLICATION).SetVal("role:master\nslave0:ip=*********,port=7000,state:online,lag=10")
			},
			args: args{
				deployment: &Cluster{
					Name: "test",
					Spec: &Spec{
						Redis: &RedisSettings{
							Port:        7000,
							Replicas:    map[string]int{"hba": 1, "hbb": 1},
							NumOfShards: 2,
						},
					},
					EnabledAZ: []string{"hba", "hbb", "hbc"},
					Status:    DEPLOYMENT_STATUS_NORMAL,
				},
				ctx: &Context{
					RedisInstances: []*omodel.Instance{
						{Name: "test-server1", IP: "*********", Port: 7000},
						{Name: "test-server1", IP: "*********", Port: 7000},
						{Name: "test-server2", IP: "*********", Port: 7000},
						{Name: "test-server2", IP: "*********", Port: 7000},
					},
				},
			},
			wantErr: true,
		},
		{
			name: "replace slave",
			before: func() {
				redisc.Mock().ExpectInfo(redisc.REPLICATION).SetVal("role:master\nslave0:ip=*********,port=7000,state:online,lag=10")
				redisc.Mock().ExpectInfo(redisc.REPLICATION).SetVal("role:slave\nmaster_host:127.0.0.1\nmaster_port:7000")

				redisc.Mock().ExpectInfo(redisc.REPLICATION).SetVal("role:master\nslave0:ip=*********,port=7000,state:online,lag=10")
				redisc.Mock().ExpectInfo(redisc.REPLICATION).SetVal("role:slave\nmaster_host:*********\nmaster_port:7000")
			},
			args: args{
				deployment: &Cluster{
					Name: "test",
					Spec: &Spec{
						Redis: &RedisSettings{
							Port:        7000,
							Replicas:    map[string]int{"hba": 1, "hbb": 1},
							NumOfShards: 2,
						},
					},
					InspectionResult: &InspectionResult{State: INSPECTION_STATE_TEST},
					EnabledAZ:        []string{"hba", "hbb", "hbc"},
					Status:           DEPLOYMENT_STATUS_NORMAL,
				},
				ctx: &Context{
					RedisInstances: []*omodel.Instance{
						{Name: "test-server1", IP: "*********", Port: 7000},
						{Name: "test-server1", IP: "*********", Port: 7000},
						{Name: "test-server2", IP: "*********", Port: 7000},
						{Name: "test-server2", IP: "*********", Port: 7000},
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := checkShardMaster(tt.args.deployment, tt.args.ctx)
			if (err != nil) != tt.wantErr {
				t.Errorf("checkShardMaster() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

// =======================================
// 		STEP 6  Redis配置管理
// =======================================

func Test_convertToBytes(t *testing.T) {
	type args struct {
		value string
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, int)
	}{
		{
			name: "not a number",
			args: args{
				value: "5368709120X",
			},
			wantErr: true,
		},
		{
			name: "not a number too",
			args: args{
				value: "53687X09120",
			},
			wantErr: true,
		},
		{
			name: "only number",
			args: args{
				value: "5368709120",
			},
			wantErr: false,
			expect: func(t *testing.T, s int) {
				if s != 5368709120 {
					t.Errorf("expect 5368709120 but got %d", s)
				}
			},
		},
		{
			name: "already B",
			args: args{
				value: "5368709120B",
			},
			wantErr: false,
			expect: func(t *testing.T, s int) {
				if s != 5368709120 {
					t.Errorf("expect 5368709120 but got %d", s)
				}
			},
		},
		{
			name: "GB to B",
			args: args{
				value: "5GB",
			},
			wantErr: false,
			expect: func(t *testing.T, s int) {
				if s != 5368709120 {
					t.Errorf("expect 5368709120 but got %d", s)
				}
			},
		},
		{
			name: "MB to B",
			args: args{
				value: "5MB",
			},
			wantErr: false,
			expect: func(t *testing.T, s int) {
				if s != 5242880 {
					t.Errorf("expect 5242880 but got %d", s)
				}
			},
		},
		{
			name: "mb to B",
			args: args{
				value: "5mb",
			},
			wantErr: false,
			expect: func(t *testing.T, s int) {
				if s != 5242880 {
					t.Errorf("expect 5242880 but got %d", s)
				}
			},
		},
		{
			name: "m to B",
			args: args{
				value: "5m",
			},
			wantErr: false,
			expect: func(t *testing.T, s int) {
				if s != 5000000 {
					t.Errorf("expect 5000000 but got %d", s)
				}
			},
		},
		{
			name: "wrong unit GBB",
			args: args{
				value: "5GBB",
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			bytes, err := convertToBytes(tt.args.value)
			if (err != nil) != tt.wantErr {
				t.Errorf("convertToBytes() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, bytes)
			}
		})
	}
}

func Test_configsToMap(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")

	type args struct {
		configs []string
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, map[string]string)
	}{
		{
			name: "empty config",
			args: args{
				configs: []string{},
			},
			wantErr: false,
			expect: func(t *testing.T, m map[string]string) {
				if len(m) != 0 {
					t.Errorf("expect empty map, but got %+v", m)
				}
			},
		},
		{
			name: "illegal config",
			args: args{
				configs: []string{"save"},
			},
			wantErr: true,
		},
		{
			name: "success",
			args: args{
				configs: []string{"save \"\"", "maxmemory 4GB"},
			},
			wantErr: false,
			expect: func(t *testing.T, m map[string]string) {
				if len(m) != 2 {
					t.Errorf("expect map with two items, but got %+v", m)
				}
				if v, ok := m["save"]; !ok || v != "" {
					t.Errorf("expect save config to be empty string, but got %s", v)
				}
				if v, ok := m["maxmemory"]; !ok || v != "4294967296" {
					t.Errorf("expect maxmemory config to be 4294967296, but got %s", v)
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			configMap, err := configsToMap(tt.args.configs)
			if (err != nil) != tt.wantErr {
				t.Errorf("configsToMap() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, configMap)
			}
		})
	}
}

func Test_redisSetDiffConfig(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")

	type args struct {
		deployment *Cluster
		ip         string
		port       int
		key        string
		val        string
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, bool)
	}{
		{
			name: "same",
			before: func() {
				redisc.Mock().ExpectConfigGet("maxmemory").SetVal([]interface{}{"maxmemory", "4294967296"})
			},
			args: args{
				deployment: &Cluster{Name: "test"},
				ip:         "*********",
				port:       7000,
				key:        "maxmemory",
				val:        "4294967296",
			},
			wantErr: false,
			expect: func(t *testing.T, b bool) {
				if b {
					t.Errorf("expect rewrite false, but got true")
				}
			},
		},
		{
			name: "diff",
			before: func() {
				redisc.Mock().ExpectConfigGet("maxmemory").SetVal([]interface{}{"maxmemory", "2147483648"})
				redisc.Mock().ExpectConfigSet("maxmemory", "4294967296").SetVal("OK")
			},
			args: args{
				deployment: &Cluster{Name: "test"},
				ip:         "*********",
				port:       7000,
				key:        "maxmemory",
				val:        "4294967296",
			},
			wantErr: false,
			expect: func(t *testing.T, b bool) {
				if !b {
					t.Errorf("expect rewrite true, but got false")
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			rewrite, err := redisSetDiffConfig(tt.args.deployment, tt.args.ip, tt.args.port, tt.args.key, tt.args.val)
			if (err != nil) != tt.wantErr {
				t.Errorf("redisSetDiffConfig() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, rewrite)
			}
		})
	}
}

func Test_ensureRedisConfigs(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")

	type args struct {
		deployment *Cluster
		ctx        *Context
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "config set maxmemory",
			before: func() {
				redisc.Mock().ExpectInfo().SetVal("used_memory:898729\nmaxmemory:0\nrole:master\naof_enabled:0")
				redisc.Mock().ExpectConfigGet("maxmemory").SetVal([]interface{}{"maxmemory", "0"})
				redisc.Mock().ExpectConfigSet("maxmemory", "4294967296").SetVal("OK")
				redisc.Mock().ExpectConfigRewrite().SetVal("OK")
			},
			args: args{
				deployment: &Cluster{
					Name: "test",
					Spec: &Spec{
						Redis: &RedisSettings{
							Port:         7000,
							CustomConfig: []string{"maxmemory 4GB"},
						},
					},
				},
				ctx: &Context{
					RedisInstances: []*omodel.Instance{
						{IP: "*********"},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "maxmemory < used_memory, skip",
			before: func() {
				redisc.Mock().ExpectInfo().SetVal("used_memory:4509715661\nmaxmemory:0\nrole:master\naof_enabled:0")
			},
			args: args{
				deployment: &Cluster{
					Name: "test",
					Spec: &Spec{
						Redis: &RedisSettings{
							Port:         7000,
							CustomConfig: []string{"maxmemory 4GB"},
						},
					},
				},
				ctx: &Context{
					RedisInstances: []*omodel.Instance{
						{IP: "*********"},
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := ensureRedisConfigs(tt.args.deployment, tt.args.ctx)
			if (err != nil) != tt.wantErr {
				t.Errorf("ensureRedisConfigs() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

// =======================================
// 		STEP 7  Sentinel管理
// =======================================

// fixSentinelMontior
func Test_fixSentinelMontior(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")

	type args struct {
		deployment   *Cluster
		ctx          *Context
		sentinelIp   string
		sentinelPort int
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, bool, map[string]*omodel.Instance)
	}{
		{
			name: "sentinel cannot access",
			before: func() {
				redisc.Mock().ExpectInfo(redisc.SENTINEL).SetErr(errors.New("some error happened"))
			},
			args: args{
				deployment: &Cluster{
					Name: "test",
					Spec: &Spec{
						Redis: &RedisSettings{NumOfShards: 2},
						Sentinel: &SentinelSettings{
							Port:     9001,
							Replicas: map[string]int{"hba": 1, "hbb": 1, "hbc": 1},
						},
					},
					EnabledAZ: []string{"hbb"},
					Status:    DEPLOYMENT_STATUS_NORMAL,
				},
				ctx: &Context{
					SentinelQuorum: 2,
					ShardReplicaMap: map[string][]*omodel.Instance{
						"test-server1": {
							{IP: "************", Port: 7030},
							{IP: "************", Port: 7030},
						},
						"test-server2": {
							{IP: "************", Port: 7031},
							{IP: "************", Port: 7031},
						},
					},
				},
				sentinelIp:   "*********",
				sentinelPort: 9001,
			},
			wantErr: false,
			expect: func(t *testing.T, alived bool, others map[string]*omodel.Instance) {
				if alived {
					t.Errorf("sentinel expected not alive, but got true")
				}
				if others != nil {
					t.Errorf("other sentinel map expected nil, but got %v", others)
				}
			},
		},
		{
			name: "alive but return error",
			before: func() {
				redisc.Mock().ExpectInfo(redisc.SENTINEL).SetVal(`
					master0:name=test-server2,status=ok,address=************:7031,slaves=1,sentinels=3
					master1:name=test-server1,,status=ok,address=************:7030,slaves=1,sentinels=3
				`)
				redisc.Mock().ExpectDo(redisc.SENTINEL, redisc.MONITOR, "test-server1", "************", 7030, 2).SetVal("OK")

				redisc.Mock().ExpectDo(redisc.SENTINEL, redisc.REMOVE, "test-server2").SetVal("OK")
				redisc.Mock().ExpectDo(redisc.SENTINEL, redisc.MONITOR, "test-server2", "************", 7031, 2).SetErr(errors.New("exception occured"))
			},
			args: args{
				deployment: &Cluster{
					Name: "test",
					Spec: &Spec{
						Redis: &RedisSettings{NumOfShards: 2},
						Sentinel: &SentinelSettings{
							Port:     9001,
							Replicas: map[string]int{"hba": 1, "hbb": 1, "hbc": 1},
						},
					},
					EnabledAZ: []string{"hbb"},
					Status:    DEPLOYMENT_STATUS_NORMAL,
				},
				ctx: &Context{
					SentinelQuorum: 2,
					ShardReplicaMap: map[string][]*omodel.Instance{
						"test-server1": {
							{IP: "************", Port: 7030},
							{IP: "************", Port: 7030},
						},
						"test-server2": {
							{IP: "************", Port: 7031},
							{IP: "************", Port: 7031},
						},
					},
				},
				sentinelIp:   "*********",
				sentinelPort: 9001,
			},
			wantErr: true,
			expect: func(t *testing.T, alived bool, others map[string]*omodel.Instance) {
				if !alived {
					t.Errorf("sentinel expected alived, but got false")
				}
				if len(others) != 0 {
					t.Errorf("other sentinel map expected empty, but got %v", others)
				}
			},
		},
		{
			name: "fix monitor",
			before: func() {
				redisc.Mock().ExpectInfo(redisc.SENTINEL).SetVal(`
					master0:name=test-server2,status=ok,address=************:7031,slaves=1,sentinels=3
					master1:name=test-server1,,status=ok,address=************:7030,slaves=1,sentinels=3
				`)
				redisc.Mock().ExpectDo(redisc.SENTINEL, redisc.MONITOR, "test-server1", "************", 7030, 2).SetVal("OK")

				redisc.Mock().ExpectDo(redisc.SENTINEL, redisc.REMOVE, "test-server2").SetVal("OK")
				redisc.Mock().ExpectDo(redisc.SENTINEL, redisc.MONITOR, "test-server2", "************", 7031, 2).SetVal("OK")

				redisc.Mock().ExpectDo(redisc.SENTINEL, redisc.REMOVE, "test-server1,").SetVal("OK")
			},
			args: args{
				deployment: &Cluster{
					Name: "test",
					Spec: &Spec{
						Redis: &RedisSettings{NumOfShards: 2},
						Sentinel: &SentinelSettings{
							Port:     9001,
							Replicas: map[string]int{"hba": 1, "hbb": 1, "hbc": 1},
						},
					},
					EnabledAZ: []string{"hbb"},
					Status:    DEPLOYMENT_STATUS_NORMAL,
				},
				ctx: &Context{
					SentinelQuorum: 2,
					ShardReplicaMap: map[string][]*omodel.Instance{
						"test-server1": {
							{IP: "************", Port: 7030},
							{IP: "************", Port: 7030},
						},
						"test-server2": {
							{IP: "************", Port: 7031},
							{IP: "************", Port: 7031},
						},
					},
				},
				sentinelIp:   "*********",
				sentinelPort: 9001,
			},
			wantErr: false,
			expect: func(t *testing.T, alived bool, others map[string]*omodel.Instance) {
				if !alived {
					t.Errorf("sentinel expected alived, but got false")
				}
				if len(others) != 0 {
					t.Errorf("other sentinel map expected empty, but got %v", others)
				}
			},
		},
		{
			name: "success",
			before: func() {
				redisc.Mock().ExpectInfo(redisc.SENTINEL).SetVal(`
					master0:name=test-server1,status=ok,address=************:7030,slaves=1,sentinels=3
					master1:name=test-server2,status=ok,address=************:7031,slaves=1,sentinels=2
				`)
				redisc.Mock().ExpectDo(redisc.SENTINEL, redisc.SENTINELS, "test-server1").SetVal([]interface{}{[]interface{}{"name", "feda981090e2334b5eaacd1923c9234969ac3d6f", "ip", "************", "port", "9107", "runid", "feda981090e2334b5eaacd1923c9234969ac3d6f", "flags", "sentinel", "link-pending-commands", "0", "link-refcount", "1", "last-ping-sent", "0", "last-ok-ping-reply", "336", "last-ping-reply", "336", "down-after-milliseconds", "30000", "last-hello-message", "1178", "voted-leader", "?", "voted-leader-epoch", "0"}})
				redisc.Mock().ExpectDo(redisc.SENTINEL, redisc.SENTINELS, "test-server2").SetVal([]interface{}{[]interface{}{"name", "feda981090e2334b5eaacd1923c9234969ac3d6d", "ip", "************", "port", "9107", "runid", "feda981090e2334b5eaacd1923c9234969ac3d6f", "flags", "sentinel", "link-pending-commands", "0", "link-refcount", "1", "last-ping-sent", "0", "last-ok-ping-reply", "336", "last-ping-reply", "336", "down-after-milliseconds", "30000", "last-hello-message", "1178", "voted-leader", "?", "voted-leader-epoch", "0"}})
			},
			args: args{
				deployment: &Cluster{
					Name: "test",
					Spec: &Spec{
						Redis: &RedisSettings{NumOfShards: 2},
						Sentinel: &SentinelSettings{
							Port:     9001,
							Replicas: map[string]int{"hba": 1, "hbb": 1, "hbc": 1},
						},
					},
					EnabledAZ: []string{"hbb"},
					Status:    DEPLOYMENT_STATUS_NORMAL,
				},
				ctx: &Context{
					SentinelQuorum: 2,
					ShardReplicaMap: map[string][]*omodel.Instance{
						"test-server1": {
							{IP: "************", Port: 7030},
							{IP: "************", Port: 7030},
						},
						"test-server2": {
							{IP: "************", Port: 7031},
							{IP: "************", Port: 7031},
						},
					},
				},
				sentinelIp:   "*********",
				sentinelPort: 9001,
			},
			wantErr: false,
			expect: func(t *testing.T, alived bool, others map[string]*omodel.Instance) {
				if !alived {
					t.Errorf("sentinel expected alived, but got false")
				}
				if _, exist := others["************:9107"]; !exist {
					t.Errorf("other sentinel expected ************:9107, but got nothing")
					return
				}
				if others["************:9107"].IP != "************" || others["************:9107"].Port != 9107 {
					t.Errorf("************:9107 value not right, got %v", others["************:9107"])
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			alived, others, err := fixSentinelMontior(tt.args.deployment, tt.args.ctx, tt.args.sentinelIp, tt.args.sentinelPort)
			if (err != nil) != tt.wantErr {
				t.Errorf("fixSentinelMontior() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, alived, others)
			}
		})
	}
}

func Test_expelSentinel(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")

	type args struct {
		deployment   *Cluster
		sentinelIp   string
		sentinelPort int
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "sentinel not exists",
			args: args{
				deployment: &Cluster{
					Name: "test",
					Spec: &Spec{
						Redis: &RedisSettings{NumOfShards: 2},
						Sentinel: &SentinelSettings{
							Port:     9001,
							Replicas: map[string]int{"hba": 1, "hbb": 1, "hbc": 1},
						},
					},
					EnabledAZ: []string{"hbb"},
					Status:    DEPLOYMENT_STATUS_NORMAL,
				},
				sentinelIp:   "*********85",
				sentinelPort: 9001,
			},
			wantErr: false,
		},
		{
			name: "sentinel remove failed",
			before: func() {
				redisc.Mock().ExpectDo(redisc.SENTINEL, redisc.REMOVE, "test-server1").SetErr(errors.New("some error happened"))
			},
			args: args{
				deployment: &Cluster{
					Name: "test",
					Spec: &Spec{
						Redis: &RedisSettings{NumOfShards: 2},
						Sentinel: &SentinelSettings{
							Port:     9001,
							Replicas: map[string]int{"hba": 1, "hbb": 1, "hbc": 1},
						},
					},
					EnabledAZ: []string{"hbb"},
					Status:    DEPLOYMENT_STATUS_NORMAL,
				},
				sentinelIp:   "*********",
				sentinelPort: 9001,
			},
			wantErr: false,
		},
		{
			name: "success",
			before: func() {
				redisc.Mock().ExpectDo(redisc.SENTINEL, redisc.REMOVE, "test-server1").SetVal("OK")
				redisc.Mock().ExpectDo(redisc.SENTINEL, redisc.REMOVE, "test-server2").SetVal("OK")
			},
			args: args{
				deployment: &Cluster{
					Name: "test",
					Spec: &Spec{
						Redis: &RedisSettings{NumOfShards: 2},
						Sentinel: &SentinelSettings{
							Port:     9001,
							Replicas: map[string]int{"hba": 1, "hbb": 1, "hbc": 1},
						},
					},
					EnabledAZ: []string{"hbb"},
					Status:    DEPLOYMENT_STATUS_NORMAL,
				},

				sentinelIp:   "*********",
				sentinelPort: 9001,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := expelSentinel(tt.args.deployment, tt.args.sentinelIp, tt.args.sentinelPort)
			if (err != nil) != tt.wantErr {
				t.Errorf("expelSentinel() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

// EnsureSentinelMonitors
func TestEnsureSentinelMonitors(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")

	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		deployment *Cluster
		ctx        *Context
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "2 phy, 1 docker, 1 down",
			before: func() {
				httpmock.RegisterResponder("GET", "http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-kafka/apps/test-sentinel/instances?showPage=0",
					httpmock.NewStringResponder(200, `{"success": true, "message": "OK", "data": [
						{"name": "0.test-sentinel.siod-kafka","hostName": "kafka0002.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 9001},"deployInfo": {"deployPath": "/home/<USER>"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hba"},"disable": true,"instanceType": "host","podId": null,"containerList": []},
						{"name": "1.test-sentinel.siod-kafka","hostName": "kafka0003.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 9001},"deployInfo": {"deployPath": "/home/<USER>"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hbb"},"disable": true,"instanceType": "host","podId": null,"containerList": []},
						{"name": "2.test-sentinel.siod-kafka","hostName": "kafka0004.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 9001},"deployInfo": {"deployPath": "/home/<USER>"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hbb"},"disable": true,"instanceType": "host","podId": null,"containerList": []},
						{"name": "3.test-sentinel.siod-kafka","hostName": "kafka0005.kj01.bddx.dxm-int.com","ip": "*********","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 9001},"deployInfo": {"deployPath": "/home/<USER>/local/sentinel"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hbb"},"disable": true,"instanceType": "host","podId": null,"containerList": []}
					]}`),
				)
				// *********
				redisc.Mock().ExpectInfo(redisc.SENTINEL).SetVal(`
					master0:name=test-server1,status=ok,address=************:7030,slaves=1,sentinels=3
					master1:name=test-server2,status=ok,address=************:7031,slaves=1,sentinels=3
				`)
				redisc.Mock().ExpectDo(redisc.SENTINEL, redisc.SENTINELS, "test-server1").SetVal([]interface{}{
					[]interface{}{"name", "feda981090e2334b5eaacd1923c9234969ac3d6f", "ip", "************", "port", "9001", "runid", "feda981090e2334b5eaacd1923c9234969ac3d6f", "flags", "sentinel", "link-pending-commands", "0", "link-refcount", "1", "last-ping-sent", "0", "last-ok-ping-reply", "336", "last-ping-reply", "336", "down-after-milliseconds", "30000", "last-hello-message", "1178", "voted-leader", "?", "voted-leader-epoch", "0"},
					[]interface{}{"name", "feda981090e2334b5eaacd1923c9234969ac3d6d", "ip", "************", "port", "9001", "runid", "feda981090e2334b5eaacd1923c9234969ac3d6f", "flags", "sentinel", "link-pending-commands", "0", "link-refcount", "1", "last-ping-sent", "0", "last-ok-ping-reply", "336", "last-ping-reply", "336", "down-after-milliseconds", "30000", "last-hello-message", "1178", "voted-leader", "?", "voted-leader-epoch", "0"}})
				redisc.Mock().ExpectDo(redisc.SENTINEL, redisc.SENTINELS, "test-server2").SetVal([]interface{}{
					[]interface{}{"name", "feda981090e2334b5eaacd1923c9234969ac3d6f", "ip", "************", "port", "9001", "runid", "feda981090e2334b5eaacd1923c9234969ac3d6f", "flags", "sentinel", "link-pending-commands", "0", "link-refcount", "1", "last-ping-sent", "0", "last-ok-ping-reply", "336", "last-ping-reply", "336", "down-after-milliseconds", "30000", "last-hello-message", "1178", "voted-leader", "?", "voted-leader-epoch", "0"},
					[]interface{}{"name", "feda981090e2334b5eaacd1923c9234969ac3d6d", "ip", "************", "port", "9001", "runid", "feda981090e2334b5eaacd1923c9234969ac3d6f", "flags", "sentinel", "link-pending-commands", "0", "link-refcount", "1", "last-ping-sent", "0", "last-ok-ping-reply", "336", "last-ping-reply", "336", "down-after-milliseconds", "30000", "last-hello-message", "1178", "voted-leader", "?", "voted-leader-epoch", "0"}})

				// ************
				redisc.Mock().ExpectInfo(redisc.SENTINEL).SetVal(`
					master0:name=test-server1,status=ok,address=************:7030,slaves=1,sentinels=5
					master1:name=test-server2,status=ok,address=************:7031,slaves=1,sentinels=5
				`)
				redisc.Mock().ExpectDo(redisc.SENTINEL, redisc.SENTINELS, "test-server1").SetVal([]interface{}{
					[]interface{}{"name", "feda981090e2334b5eaacd1923c9234969ac3d6d", "ip", "************", "port", "9001", "runid", "feda981090e2334b5eaacd1923c9234969ac3d6f", "flags", "sentinel", "link-pending-commands", "0", "link-refcount", "1", "last-ping-sent", "0", "last-ok-ping-reply", "336", "last-ping-reply", "336", "down-after-milliseconds", "30000", "last-hello-message", "1178", "voted-leader", "?", "voted-leader-epoch", "0"},
					[]interface{}{"name", "feda981090e2334b5eaacd1923c9234969ac3d6f", "ip", "************", "port", "9001", "runid", "feda981090e2334b5eaacd1923c9234969ac3d6f", "flags", "sentinel", "link-pending-commands", "0", "link-refcount", "1", "last-ping-sent", "0", "last-ok-ping-reply", "336", "last-ping-reply", "336", "down-after-milliseconds", "30000", "last-hello-message", "1178", "voted-leader", "?", "voted-leader-epoch", "0"},
					[]interface{}{"name", "feda981090e2334b5eaacd1923c9234969ac3d6d", "ip", "*********", "port", "9001", "runid", "feda981090e2334b5eaacd1923c9234969ac3d6f", "flags", "sentinel", "link-pending-commands", "0", "link-refcount", "1", "last-ping-sent", "0", "last-ok-ping-reply", "336", "last-ping-reply", "336", "down-after-milliseconds", "30000", "last-hello-message", "1178", "voted-leader", "?", "voted-leader-epoch", "0"},
					[]interface{}{"name", "feda981090e2334b5eaacd1923c9234969ac3d6f", "ip", "*********", "port", "9001", "runid", "feda981090e2334b5eaacd1923c9234969ac3d6f", "flags", "sentinel", "link-pending-commands", "0", "link-refcount", "1", "last-ping-sent", "0", "last-ok-ping-reply", "336", "last-ping-reply", "336", "down-after-milliseconds", "30000", "last-hello-message", "1178", "voted-leader", "?", "voted-leader-epoch", "0"},
				})
				redisc.Mock().ExpectDo(redisc.SENTINEL, redisc.SENTINELS, "test-server2").SetVal([]interface{}{
					[]interface{}{"name", "feda981090e2334b5eaacd1923c9234969ac3d6d", "ip", "************", "port", "9001", "runid", "feda981090e2334b5eaacd1923c9234969ac3d6f", "flags", "sentinel", "link-pending-commands", "0", "link-refcount", "1", "last-ping-sent", "0", "last-ok-ping-reply", "336", "last-ping-reply", "336", "down-after-milliseconds", "30000", "last-hello-message", "1178", "voted-leader", "?", "voted-leader-epoch", "0"},
					[]interface{}{"name", "feda981090e2334b5eaacd1923c9234969ac3d6f", "ip", "************", "port", "9001", "runid", "feda981090e2334b5eaacd1923c9234969ac3d6f", "flags", "sentinel", "link-pending-commands", "0", "link-refcount", "1", "last-ping-sent", "0", "last-ok-ping-reply", "336", "last-ping-reply", "336", "down-after-milliseconds", "30000", "last-hello-message", "1178", "voted-leader", "?", "voted-leader-epoch", "0"},
					[]interface{}{"name", "feda981090e2334b5eaacd1923c9234969ac3d6d", "ip", "*********", "port", "9001", "runid", "feda981090e2334b5eaacd1923c9234969ac3d6f", "flags", "sentinel", "link-pending-commands", "0", "link-refcount", "1", "last-ping-sent", "0", "last-ok-ping-reply", "336", "last-ping-reply", "336", "down-after-milliseconds", "30000", "last-hello-message", "1178", "voted-leader", "?", "voted-leader-epoch", "0"},
					[]interface{}{"name", "feda981090e2334b5eaacd1923c9234969ac3d6f", "ip", "*********", "port", "9001", "runid", "feda981090e2334b5eaacd1923c9234969ac3d6f", "flags", "sentinel", "link-pending-commands", "0", "link-refcount", "1", "last-ping-sent", "0", "last-ok-ping-reply", "336", "last-ping-reply", "336", "down-after-milliseconds", "30000", "last-hello-message", "1178", "voted-leader", "?", "voted-leader-epoch", "0"},
				})

				// remove *********
				redisc.Mock().ExpectDo(redisc.SENTINEL, redisc.REMOVE, "test-server1").SetVal("OK")
				redisc.Mock().ExpectDo(redisc.SENTINEL, redisc.REMOVE, "test-server2").SetVal("OK")

				// ************
				redisc.Mock().ExpectInfo(redisc.SENTINEL).SetVal(`
					master0:name=test-server1,status=ok,address=************:7030,slaves=1,sentinels=5
					master1:name=test-server2,status=ok,address=************:7031,slaves=1,sentinels=5
				`)
				redisc.Mock().ExpectDo(redisc.SENTINEL, redisc.SENTINELS, "test-server1").SetVal([]interface{}{
					[]interface{}{"name", "feda981090e2334b5eaacd1923c9234969ac3d6f", "ip", "************", "port", "9001", "runid", "feda981090e2334b5eaacd1923c9234969ac3d6f", "flags", "sentinel", "link-pending-commands", "0", "link-refcount", "1", "last-ping-sent", "0", "last-ok-ping-reply", "336", "last-ping-reply", "336", "down-after-milliseconds", "30000", "last-hello-message", "1178", "voted-leader", "?", "voted-leader-epoch", "0"},
					[]interface{}{"name", "feda981090e2334b5eaacd1923c9234969ac3d6f", "ip", "************", "port", "9001", "runid", "feda981090e2334b5eaacd1923c9234969ac3d6f", "flags", "sentinel", "link-pending-commands", "0", "link-refcount", "1", "last-ping-sent", "0", "last-ok-ping-reply", "336", "last-ping-reply", "336", "down-after-milliseconds", "30000", "last-hello-message", "1178", "voted-leader", "?", "voted-leader-epoch", "0"},
					[]interface{}{"name", "feda981090e2334b5eaacd1923c9234969ac3d6d", "ip", "*********", "port", "9001", "runid", "feda981090e2334b5eaacd1923c9234969ac3d6f", "flags", "sentinel", "link-pending-commands", "0", "link-refcount", "1", "last-ping-sent", "0", "last-ok-ping-reply", "336", "last-ping-reply", "336", "down-after-milliseconds", "30000", "last-hello-message", "1178", "voted-leader", "?", "voted-leader-epoch", "0"},
					[]interface{}{"name", "feda981090e2334b5eaacd1923c9234969ac3d6d", "ip", "*********", "port", "9001", "runid", "feda981090e2334b5eaacd1923c9234969ac3d6f", "flags", "sentinel", "link-pending-commands", "0", "link-refcount", "1", "last-ping-sent", "0", "last-ok-ping-reply", "336", "last-ping-reply", "336", "down-after-milliseconds", "30000", "last-hello-message", "1178", "voted-leader", "?", "voted-leader-epoch", "0"},
				})
				redisc.Mock().ExpectDo(redisc.SENTINEL, redisc.SENTINELS, "test-server2").SetVal([]interface{}{
					[]interface{}{"name", "feda981090e2334b5eaacd1923c9234969ac3d6f", "ip", "************", "port", "9001", "runid", "feda981090e2334b5eaacd1923c9234969ac3d6f", "flags", "sentinel", "link-pending-commands", "0", "link-refcount", "1", "last-ping-sent", "0", "last-ok-ping-reply", "336", "last-ping-reply", "336", "down-after-milliseconds", "30000", "last-hello-message", "1178", "voted-leader", "?", "voted-leader-epoch", "0"},
					[]interface{}{"name", "feda981090e2334b5eaacd1923c9234969ac3d6f", "ip", "************", "port", "9001", "runid", "feda981090e2334b5eaacd1923c9234969ac3d6f", "flags", "sentinel", "link-pending-commands", "0", "link-refcount", "1", "last-ping-sent", "0", "last-ok-ping-reply", "336", "last-ping-reply", "336", "down-after-milliseconds", "30000", "last-hello-message", "1178", "voted-leader", "?", "voted-leader-epoch", "0"},
					[]interface{}{"name", "feda981090e2334b5eaacd1923c9234969ac3d6d", "ip", "*********", "port", "9001", "runid", "feda981090e2334b5eaacd1923c9234969ac3d6f", "flags", "sentinel", "link-pending-commands", "0", "link-refcount", "1", "last-ping-sent", "0", "last-ok-ping-reply", "336", "last-ping-reply", "336", "down-after-milliseconds", "30000", "last-hello-message", "1178", "voted-leader", "?", "voted-leader-epoch", "0"},
					[]interface{}{"name", "feda981090e2334b5eaacd1923c9234969ac3d6d", "ip", "*********", "port", "9001", "runid", "feda981090e2334b5eaacd1923c9234969ac3d6f", "flags", "sentinel", "link-pending-commands", "0", "link-refcount", "1", "last-ping-sent", "0", "last-ok-ping-reply", "336", "last-ping-reply", "336", "down-after-milliseconds", "30000", "last-hello-message", "1178", "voted-leader", "?", "voted-leader-epoch", "0"},
				})
			},
			args: args{
				deployment: &Cluster{
					Name: "test",
					Spec: &Spec{
						Redis: &RedisSettings{NumOfShards: 2},
						Sentinel: &SentinelSettings{
							Port:     9001,
							Replicas: map[string]int{"hba": 1, "hbb": 1, "hbc": 1},
						},
						App: &AppSettings{
							AppPrefix:   "test",
							ProductLine: "siod-kafka",
						},
					},
					EnabledAZ: []string{"hbb"},
					Status:    DEPLOYMENT_STATUS_NORMAL,
				},
				ctx: &Context{
					SentinelQuorum: 2,
					SentinelInstances: []*omodel.Instance{
						{IP: "*********"},
					},
					ShardReplicaMap: map[string][]*omodel.Instance{
						"test-server1": {
							{IP: "************", Port: 7030},
							{IP: "************", Port: 7030},
						},
						"test-server2": {
							{IP: "************", Port: 7031},
							{IP: "************", Port: 7031},
						},
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := EnsureSentinelMonitors(tt.args.deployment, tt.args.ctx)
			if (err != nil) != tt.wantErr {
				t.Errorf("EnsureSentinelMonitors() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

// =======================================
// 		STEP 8  Sentinel数量检查
// =======================================

func TestCheckSentinelNumbers(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")

	type args struct {
		deployment *Cluster
		ctx        *Context
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "migrate deploy",
			before: func() {
				// *********
				redisc.Mock().ExpectInfo(redisc.SENTINEL).SetVal(`
					master0:name=test-server1,status=ok,address=************:7030,slaves=0,sentinels=1
					master1:name=test-server2,status=ok,address=************:7031,slaves=0,sentinels=1
				`)
				redisc.Mock().ExpectDo(redisc.SENTINEL, redisc.CKQUORUM, "test-server1").SetErr(errors.New("NOQUORUM 1 usable Sentinels. Not enough available Sentinels to reach the specified quorum for this master. Not enough available Sentinels to reach the majority and authorize a failover"))
				redisc.Mock().ExpectDo(redisc.SENTINEL, redisc.CKQUORUM, "test-server2").SetErr(errors.New("NOQUORUM 1 usable Sentinels. Not enough available Sentinels to reach the specified quorum for this master. Not enough available Sentinels to reach the majority and authorize a failover"))
			},
			args: args{
				ctx: &Context{
					SentinelQuorum: 2,
					OnlineSentinels: []*omodel.Instance{
						{IP: "*********", Port: 9001},
					},
				},
				deployment: &Cluster{
					Name: "test",
					Spec: &Spec{
						Redis: &RedisSettings{
							NumOfShards: 2,
						},
						Sentinel: &SentinelSettings{
							Port:     9001,
							Replicas: map[string]int{"hba": 1, "hbb": 1, "hbc": 1},
						},
					},
					EnabledAZ: []string{"hbb"},
					Status:    DEPLOYMENT_STATUS_INIT,
				},
			},
			wantErr: false,
		},
		{
			name: "should exec sentinel reset",
			before: func() {
				// *********
				redisc.Mock().ExpectInfo(redisc.SENTINEL).SetVal(`
					master0:name=test-server1,status=ok,address=************:7030,slaves=1,sentinels=3
					master1:name=test-server2,status=ok,address=************:7031,slaves=1,sentinels=4
				`)
				redisc.Mock().ExpectDo(redisc.SENTINEL, redisc.CKQUORUM, "test-server1").SetVal("OK")
				redisc.Mock().ExpectDo(redisc.SENTINEL, redisc.CKQUORUM, "test-server2").SetVal("OK")

				redisc.Mock().ExpectDo(redisc.SENTINEL, redisc.RESET, "test-server2").SetVal("OK")

				// *********
				redisc.Mock().ExpectInfo(redisc.SENTINEL).SetVal(`
					master0:name=test-server1,status=ok,address=************:7030,slaves=1,sentinels=3
					master1:name=test-server2,status=ok,address=************:7031,slaves=1,sentinels=3
				`)
				redisc.Mock().ExpectDo(redisc.SENTINEL, redisc.CKQUORUM, "test-server1").SetVal("OK")
				redisc.Mock().ExpectDo(redisc.SENTINEL, redisc.CKQUORUM, "test-server2").SetVal("OK")

				// *********
				redisc.Mock().ExpectInfo(redisc.SENTINEL).SetVal(`
					master0:name=test-server1,status=ok,address=************:7030,slaves=1,sentinels=3
					master1:name=test-server2,status=ok,address=************:7031,slaves=1,sentinels=3
				`)
				redisc.Mock().ExpectDo(redisc.SENTINEL, redisc.CKQUORUM, "test-server1").SetVal("OK")
				redisc.Mock().ExpectDo(redisc.SENTINEL, redisc.CKQUORUM, "test-server2").SetVal("OK")
			},
			args: args{
				ctx: &Context{
					SentinelQuorum: 2,
					OnlineSentinels: []*omodel.Instance{
						{IP: "*********", Port: 9001},
						{IP: "*********", Port: 9001},
						{IP: "*********", Port: 9001},
					},
				},
				deployment: &Cluster{
					Name: "test",
					Spec: &Spec{
						Redis: &RedisSettings{
							NumOfShards: 2,
						},
						Sentinel: &SentinelSettings{
							Port:     9001,
							Replicas: map[string]int{"hba": 1, "hbb": 1, "hbc": 1},
						},
					},
					EnabledAZ: []string{"hbb"},
					Status:    DEPLOYMENT_STATUS_INIT,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			if err := CheckSentinelNumbers(tt.args.deployment, tt.args.ctx); (err != nil) != tt.wantErr {
				t.Errorf("CheckSentinelNumbers() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

// =======================================
// 		STEP 9  Sentinel可用性检查
// =======================================

func Test_checkSentinelAvailability(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")

	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		deployment *Cluster
		ctx        *Context
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
	}{
		{
			name: "new deploy and get instances failed",
			args: args{
				ctx: &Context{},
				deployment: &Cluster{
					Spec: &Spec{
						Sentinel: &SentinelSettings{
							Port:     9001,
							Replicas: map[string]int{"hba": 1, "hbb": 1, "hbc": 1},
						},
						App: &AppSettings{
							ProductLine: "siod-kafka",
							AppPrefix:   "test",
						},
					},
					EnabledAZ: []string{"hba", "hbb", "hbc"},
					Status:    DEPLOYMENT_STATUS_INIT,
				},
			},
			wantErr: true,
		},
		{
			name: "new deploy and enabled instances failed",
			before: func() {
				httpmock.RegisterResponder("GET", "http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-redis/apps/test-sentinel/instances?showPage=0",
					httpmock.NewStringResponder(200, `{"success": true, "message": "OK", "data": [
						{"name": "0.redis-cmanager.siod-kafka","hostName": "kafka0002.kj01.bddx.dxm-int.com","ip": "*********","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hba"},"disable": true,"instanceType": "host","podId": null,"containerList": []},
						{"name": "1.redis-cmanager.siod-kafka","hostName": "kafka0000.kj01.bddx.dxm-int.com","ip": "*********","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hbb"},"disable": true,"instanceType": "host","podId": null,"containerList": []}
					]}`),
				)
			},
			args: args{
				ctx: &Context{},
				deployment: &Cluster{
					Spec: &Spec{
						Sentinel: &SentinelSettings{
							Port: 9001,
							Replicas: map[string]int{
								"hba": 1,
								"hbb": 1,
								"hbc": 1,
							},
						},
						App: &AppSettings{
							ProductLine: "siod-redis",
							AppPrefix:   "test",
						},
					},
					EnabledAZ: []string{"hba", "hbb", "hbc"},
					Status:    DEPLOYMENT_STATUS_INIT,
				},
			},
			wantErr: true,
		},
		{
			name: "enabled all instances but cannot access bns.serv",
			before: func() {
				httpmock.RegisterResponder("GET", "http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-redis/apps/test-sentinel/instances?showPage=0",
					httpmock.NewStringResponder(200, `{"success": true, "message": "OK", "data": [
						{"name": "0.redis-cmanager.siod-kafka","hostName": "kafka0002.kj01.bddx.dxm-int.com","ip": "*********","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hba"},"disable": true,"instanceType": "host","podId": null,"containerList": []},
						{"name": "1.redis-cmanager.siod-kafka","hostName": "kafka0000.kj01.bddx.dxm-int.com","ip": "*********","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hbb"},"disable": true,"instanceType": "host","podId": null,"containerList": []}
					]}`),
				)

				httpmock.RegisterResponder("POST", "http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-redis/apps/test-sentinel/instances/batchBlock",
					httpmock.NewStringResponder(200, `{"success": true, "message": "OK", "data": "OK"}`),
				)
			},
			args: args{
				ctx: &Context{},
				deployment: &Cluster{
					Spec: &Spec{
						Sentinel: &SentinelSettings{
							Port:     9001,
							Replicas: map[string]int{"hba": 1, "hbb": 1, "hbc": 1},
						},
						App: &AppSettings{
							ProductLine: "siod-redis",
							AppPrefix:   "test",
						},
					},
					EnabledAZ: []string{"hba", "hbb", "hbc"},
					Status:    DEPLOYMENT_STATUS_INIT,
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			if err := checkSentinelAvailability(tt.args.deployment, tt.args.ctx); (err != nil) != tt.wantErr {
				t.Errorf("checkSentinelAvailability() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

// =======================================
// 		STEP 13  Proxy可用性检查
// =======================================

func Test_checkRouterAvailability(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")

	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		deployment *Cluster
		ctx        *Context
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
	}{
		{
			name: "GetInstancesV2 failed",
			args: args{
				ctx: &Context{},
				deployment: &Cluster{
					Name:  "test",
					Alias: "test",
					Spec: &Spec{
						Router: &RouterSettings{
							Port: 8001,
						},
						App: &AppSettings{
							ProductLine: "siod-kafka",
							AppPrefix:   "test",
						},
					},
				},
			},
			wantErr: true,
		},
		{
			name: "enabled all instances but failed",
			before: func() {
				httpmock.RegisterResponder("GET", "http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-kafka/apps/test-router/instances?showPage=0",
					httpmock.NewStringResponder(200, `{"success": true, "message": "OK", "data": [
						{"name": "0.redis-cmanager.siod-kafka","hostName": "kafka0002.kj01.bddx.dxm-int.com","ip": "*********","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hba"},"disable": true,"instanceType": "host","podId": null,"containerList": []},
						{"name": "1.redis-cmanager.siod-kafka","hostName": "kafka0000.kj01.bddx.dxm-int.com","ip": "*********","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hbb"},"disable": true,"instanceType": "host","podId": null,"containerList": []}
					]}`),
				)
			},
			args: args{
				ctx: &Context{},
				deployment: &Cluster{
					Name:  "test",
					Alias: "test",
					Spec: &Spec{
						Router: &RouterSettings{
							Port: 8001,
							Replicas: map[string]int{
								"hbb": 2,
							},
						},
						App: &AppSettings{
							ProductLine: "siod-kafka",
							AppPrefix:   "test",
						},
					},
					EnabledAZ: []string{"hbb"},
					Status:    DEPLOYMENT_STATUS_INIT,
				},
			},
			wantErr: true,
		},
		{
			name: "enabled all instances but cannot access bns.serv",
			before: func() {
				httpmock.RegisterResponder("GET", "http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-kafka/apps/test-router/instances?showPage=0",
					httpmock.NewStringResponder(200, `{"success": true, "message": "OK", "data": [
						{"name": "0.redis-cmanager.siod-kafka","hostName": "kafka0002.kj01.bddx.dxm-int.com","ip": "*********","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hba"},"disable": true,"instanceType": "host","podId": null,"containerList": []},
						{"name": "1.redis-cmanager.siod-kafka","hostName": "kafka0000.kj01.bddx.dxm-int.com","ip": "*********","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hbb"},"disable": true,"instanceType": "host","podId": null,"containerList": []}
					]}`),
				)

				httpmock.RegisterResponder("POST", "http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-kafka/apps/test-router/instances/batchBlock",
					httpmock.NewStringResponder(200, `{"success": true, "message": "OK", "data": "OK"}`),
				)
			},
			args: args{
				ctx: &Context{},
				deployment: &Cluster{
					Name:  "test",
					Alias: "test",
					Spec: &Spec{
						Router: &RouterSettings{
							Port: 8001,
							Replicas: map[string]int{
								"hbb": 2,
							},
						},
						App: &AppSettings{
							ProductLine: "siod-kafka",
							AppPrefix:   "test",
						},
					},
					EnabledAZ: []string{"hbb"},
					Status:    DEPLOYMENT_STATUS_INIT,
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			if err := checkRouterAvailability(tt.args.deployment, tt.args.ctx); (err != nil) != tt.wantErr {
				t.Errorf("checkRouterAvailability() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

// =======================================
//		   日志、其他
// =======================================

func TestResultInHuman(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")

	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		result *InspectionResult
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, string)
	}{
		{
			name: "done",
			args: args{
				result: &InspectionResult{
					StepProgress: [14]int{1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1},
					StepErrTimes: [14]int{0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0},
					ErrMsg:       "",
				},
			},
			expect: func(t *testing.T, s string) {
				if s != "渲染阶段已全部完成" {
					t.Errorf("expect log 渲染阶段已全部完成, but got %s", s)
				}
			},
		},
		{
			name: "running",
			args: args{
				result: &InspectionResult{
					StepProgress: [14]int{1, 1, 1, 1, 1, 0},
					StepErrTimes: [14]int{0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0},
					ErrMsg:       "",
				},
			},
			expect: func(t *testing.T, s string) {
				if s != "阶段: Redis主从关系检查 运行中" {
					t.Errorf("expect log 阶段: Redis主从关系检查 运行中, but got %s", s)
				}
			},
		},
		{
			name: "running, but retry",
			args: args{
				result: &InspectionResult{
					StepProgress: [14]int{1, 1, 1, 1, 1, 0},
					StepErrTimes: [14]int{0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 0, 0, 0, 0},
					ErrMsg:       "测试",
				},
			},
			expect: func(t *testing.T, s string) {
				if s != "阶段: Redis主从关系检查 运行中, 已重试2次, 上一次失败原因: 测试" {
					t.Errorf("expect log 阶段: Redis主从关系检查 运行中, 已重试2次, 上一次失败原因: 测试, but got %s", s)
				}
			},
		},
		{
			name: "running, but retry",
			args: args{
				result: &InspectionResult{
					StepProgress: [14]int{1, 1, 1, 1, 1, 2},
					StepErrTimes: [14]int{0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0},
					ErrMsg:       "测试",
				},
			},
			expect: func(t *testing.T, s string) {
				if s != "阶段: Redis主从关系检查 执行失败, 失败原因: 测试, 剩余重试次数: 0次" {
					t.Errorf("expect log 阶段: Redis主从关系检查 执行失败, 失败原因: 测试, 剩余重试次数: 0次, but got %s", s)
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			log := ResultInHuman(tt.args.result)
			// if (err != nil) != tt.wantErr {
			// 	t.Errorf("ResultInHuman() error = %v, wantErr %v", err, tt.wantErr)
			// }
			if tt.expect != nil {
				tt.expect(t, log)
			}
		})
	}
}

func TestGetInspectionProgress(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")

	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		clusterName string
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, *InspectionResult)
	}{
		{
			name: "not running",
			args: args{
				clusterName: "public_test",
			},
			wantErr: true,
		},
		{
			name: "success",
			before: func() {
				renderLock.Store("public_test", &InspectionResult{
					StartedAt:    time.Now(),
					StepProgress: [14]int{},
					StepErrTimes: [14]int{},
					State:        INSPECTION_STATE_NORMAL,
					ErrMsg:       "",
				})
			},
			args: args{
				clusterName: "public_test",
			},
			wantErr: false,
			expect: func(t *testing.T, r *InspectionResult) {
				if r.State != INSPECTION_STATE_NORMAL {
					t.Errorf("expect state NORMAL, but got %v", r.State)
				}
			},
		},
		{
			name: "t",
			before: func() {
				result := &InspectionResult{
					StartedAt:    time.Now(),
					StepProgress: [14]int{0, 0, 1, 0, 0, 1, 0, 1, 0, 0},
					StepErrTimes: [14]int{},
					State:        INSPECTION_STATE_NORMAL,
					ErrMsg:       "",
				}
				renderLock.Store("public_test", result)
				result.StepProgress = [14]int{0, 1, 0, 0}
			},
			args: args{
				clusterName: "public_test",
			},
			wantErr: false,
			expect: func(t *testing.T, r *InspectionResult) {
				if r.State != INSPECTION_STATE_NORMAL {
					t.Errorf("expect state NORMAL, but got %v", r.State)
				}
				if r.StepProgress[1] != 1 {
					t.Errorf("expect progress[4] 1, but got %v", r.StepProgress[4])
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			result, err := GetInspectionProgress(tt.args.clusterName)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetInspectionProgress() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, result)
			}
		})
	}
}
