package renderer

import (
	"golang.org/x/sync/errgroup"

	"redis-cmanager/library/renderer/common"
	"redis-cmanager/library/renderer/lib"
)

// ===========================================
//               变更interface
// ===========================================

// UpdateMaxMemoryConfig 更新最大内存
// func UpdateMaxMemoryConfig(clusterName string, mem int) error {
// 	// todo: mem参数为使用
// 	deployment, err := GetDeploymentFromDB(clusterName)
// 	if err != nil {
// 		logger.Error("failed to get deployment from db", err)
// 	}
// 	customConfig := []string{}
// 	for _, v := range deployment.Spec.Redis.CustomConfig {
// 		if strings.Contains(v, "maxmemory ") {
// 			customConfig = append(customConfig, fmt.Sprintf("maxmemory %vgb", deployment.Spec.Redis.Resource.Mem*2/1024))
// 		} else {
// 			customConfig = append(customConfig, v)
// 		}
// 	}
// 	deployment.Spec.Redis.CustomConfig = customConfig
// 	return SaveNewDeployment(deployment)
// }

// 更新全组件白名单
func EnsureWhitelists(deployment *Cluster) error {
	g := errgroup.Group{}
	g.Go(func() error {
		redisInstances, err := lib.QueryRedisInstances(deployment.Name, true)
		if err != nil {
			return err
		}
		err = ensureWhitelist(deployment, common.COMPONENT_REDIS, redisInstances)
		if err != nil {
			return err
		}
		return nil
	})
	g.Go(func() error {
		sentinelInstances, err := lib.QuerySentinelInstances(deployment.Name, true)
		if err != nil {
			return err
		}
		err = ensureWhitelist(deployment, common.COMPONENT_SENTINEL, sentinelInstances)
		if err != nil {
			return err
		}
		return nil
	})
	g.Go(func() error {
		proxyInstances, err := lib.QueryProxyInstances(deployment.Name, true)
		if err != nil {
			return err
		}
		err = ensureWhitelist(deployment, common.COMPONENT_ROUTER, proxyInstances)
		if err != nil {
			return err
		}
		return nil
	})
	if err := g.Wait(); err != nil {
		return err
	}

	return nil
}

// ===========================================
//               检查interface
// ===========================================

// 检查是否存在打着污点但没有屏蔽的Proxy
func IsThereTaintedProxy(deployment *Cluster) (bool, error) {
	// 获取proxy pod list
	podList, err := lib.ListPods(deployment.Name, common.COMPONENT_ROUTER, true)
	if err != nil {
		return false, err
	}
	for _, pod := range podList {
		if pod.Labels[common.LABEL_TAINT] == "true" {
			return true, nil
		}
	}

	return false, nil
}
