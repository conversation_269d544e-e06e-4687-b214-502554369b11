/*
 * @Author: jiayiming
 * @Date: 2025-01-03 16:36
 * @Last Modified by: jiayiming
 * @Description: Cluster Renderer 负责将deployment配置渲染成一个基于k8s多集群的容器redis集群，主要包括：
 *（1）实例保持：
 *		(a) pod数量保持，线上生效pod少于配置需求时，申请新pod
 *		(b) 新pod生效后，将其加入到对应的bns中，并更新数据库
 *（2）配置一致性：
 *		(a) redis容器实例各项配置、分片关系保持
 *		(b) sentinel monitor配置
 *		(c) proxy配置
 *		(d) 白名单
 *
 * Render()方法只增不减，退还操作需要走专用方法，需要人工触发。对于实例数量超出预期的情况，报警处理，不自动修复。
 */
package renderer

import (
	"context"
	"errors"
	"fmt"
	"net"
	"regexp"
	"slices"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"dt-common/apptree"
	"dt-common/logger"
	"dt-common/noah"
	"dt-common/omodel"
	"dt-common/redisc"
	"redis-cmanager/library/fec"
	"redis-cmanager/library/ragent"
	"redis-cmanager/library/renderer/cli"
	"redis-cmanager/library/renderer/common"
	"redis-cmanager/library/renderer/lib"
)

const (
	STEP_INCOMPLETE = iota // Step阶段未完成
	STEP_COMPLETED         // Step阶段完成
	STEP_ABNORMAL          // Step阶段异常

	INSPECTION_STATE_INIT   = ""       // Render空白
	INSPECTION_STATE_NORMAL = "NORMAL" // Render成功
	INSPECTION_STATE_ERROR  = "ERROR"  // Render失败
	INSPECTION_STATE_TEST   = "TEST"   // 单测专用

	INDEX_NEW_REDIS    = 0 // 用于记录是否有新申请的Pod
	INDEX_NEW_SENTINEL = 1 // 用于记录是否有新申请的Pod
	INDEX_NEW_ROUTER   = 2 // 用于记录是否有新申请的Pod
)

// Render上下文，用于传递参数
type Context struct {
	ApplyNewPods      [3]bool            // 通过比较instance和deployment.Spec，判断本轮是否需要申请新Pods
	RedisInstances    []*omodel.Instance // 集群redis实例
	SentinelInstances []*omodel.Instance // 集群sentinel实例
	RouterInstances   []*omodel.Instance // 集群router实例

	ShardReplicaMap map[string][]*omodel.Instance // 各分片的副本，用于sentinel monitor，副本里可能存在物理实例，数组内0号为主库
	SentinelQuorum  int                           // 集群quorum
	OnlineSentinels []*omodel.Instance            // 可用sentinel实例，修复sentinels时使用
}

type Step struct {
	Name string
	Func func(*Cluster, *Context) error
}

// 集群渲染全部阶段
const (
	step_VALIDATE                        = iota // 合法性检查
	step_CREATE_APPS                            // 创建Noah应用
	step_REDIS_SENTINEL_CREATE_PODS             // Redis/Sentinel POD数量保持
	step_REDIS_SENTINEL_CHECK_POD_STATUS        // Redis/Sentinel POD运行状态检查
	step_REDIS_SENTINEL_ENSURE_WHITELIST        // Redis/Sentinel 白名单对齐
	step_REDIS_ENSURE_MASTER_SLAVES             // Redis 分片主从关系管理
	step_REDIS_ENSURE_CONFIGS                   // Redis 配置管理
	step_SENTINEL_ENSURE_MONITOR                // Sentinel Monitor配置管理
	step_SENTINEL_CHECK_QUORUM                  // Sentinel Quorum检查
	step_SENTINEL_CHECK_AVAILABILITY            // Sentinel 可用性检查
	step_ROUTER_CREATE_PODS                     // Router POD数量保持
	step_ROUTER_CHECK_POD_STATUS                // Router POD运行状态检查
	step_ROUTER_ENSURE_WHITELIST                // Router 白名单对齐
	step_ROUTER_CHECK_AVAILABILITY              // Router 可用性检查

	TOTAL_STEP                 // 总数，用于定义step数组长度
	FIRST_STEP = step_VALIDATE //
)

var (
	stepMap = map[int]*Step{
		step_VALIDATE:                        {Name: "参数检查", Func: validate},
		step_CREATE_APPS:                     {Name: "创建noah app", Func: createApps},
		step_REDIS_SENTINEL_CREATE_PODS:      {Name: "创建redis/sentinel pod资源", Func: createRedisAndSentinelPods},
		step_REDIS_SENTINEL_CHECK_POD_STATUS: {Name: "检查redis/sentinel pod运行状态", Func: saveRedisAndSentinelPods},
		step_REDIS_SENTINEL_ENSURE_WHITELIST: {Name: "创建redis/sentinel pod白名单", Func: ensureRedisAndSentinelWhitelist},
		step_REDIS_ENSURE_MASTER_SLAVES:      {Name: "Redis主从关系检查", Func: checkShardMaster},
		step_REDIS_ENSURE_CONFIGS:            {Name: "Redis配置检查", Func: ensureRedisConfigs},
		step_SENTINEL_ENSURE_MONITOR:         {Name: "Sentinel监控信息检查", Func: EnsureSentinelMonitors},
		step_SENTINEL_CHECK_QUORUM:           {Name: "Sentinel数量检查", Func: CheckSentinelNumbers},
		step_SENTINEL_CHECK_AVAILABILITY:     {Name: "Sentinel可用性检查", Func: checkSentinelAvailability},
		step_ROUTER_CREATE_PODS:              {Name: "创建router pod资源", Func: createRouterPods},
		step_ROUTER_CHECK_POD_STATUS:         {Name: "检查router pod运行状态", Func: saveRouterPods},
		step_ROUTER_ENSURE_WHITELIST:         {Name: "创建router pod白名单", Func: ensureRouterWhitelist},
		step_ROUTER_CHECK_AVAILABILITY:       {Name: "Router可用性检查", Func: checkRouterAvailability},
	}

	renderLock       sync.Map // 用于控制render单发
	asyncSlaveOfLock sync.Map // 用于控制slaveOf单发
)

// 集群渲染入口
// Render will ensure the redis cluster is in the expected state.
func Render(deployment *Cluster) error {
	// 单测用，立即返回
	if deployment.InspectionResult.State == INSPECTION_STATE_TEST {
		if deployment.InspectionResult.ErrMsg != "" {
			return errors.New(deployment.InspectionResult.ErrMsg)
		}
		return nil
	}

	// 加锁，每个集群同时只有一个render任务运行
	if _, isLoaded := renderLock.LoadOrStore(deployment.Name, deployment.InspectionResult); isLoaded {
		return nil
	}
	defer renderLock.Delete(deployment.Name)
	defer UpdateDeploymentResult(deployment.Name, deployment.InspectionResult)

	logger.Debug("[Renderer] start, cluster=%s", deployment.Name)
	ctx := &Context{}
	deployment.InspectionResult.StartedAt = time.Now()
	deployment.InspectionResult.StepProgress = [TOTAL_STEP]int{}
	for idx := FIRST_STEP; idx < TOTAL_STEP; idx++ {
		if _, exist := stepMap[idx]; !exist || stepMap[idx].Func == nil {
			err := fmt.Errorf("missing step func, stepIdx=%d", idx)
			logger.Error("[Renderer] %v", err)
			return err
		}

		err := stepMap[idx].Func(deployment, ctx)
		if err != nil {
			deployment.InspectionResult.StepErrTimes[idx] += 1
			deployment.InspectionResult.StepProgress[idx] = STEP_ABNORMAL
			deployment.InspectionResult.State = INSPECTION_STATE_ERROR
			deployment.InspectionResult.ErrMsg = err.Error()
			logger.Warn("[Renderer] end up with exception, cluster=%s, step=%s, error=(%v)", deployment.Name, stepMap[idx].Name, err)
			return err
		}
		deployment.InspectionResult.StepErrTimes[idx] = 0
		deployment.InspectionResult.StepProgress[idx] = STEP_COMPLETED
		logger.Debug("[Renderer] step succeed, cluster=%s, step=%s", deployment.Name, stepMap[idx].Name)
	}

	// 渲染成功，state改为normal，清空stepErrTimes重新计数
	deployment.InspectionResult.State = INSPECTION_STATE_NORMAL
	deployment.InspectionResult.ErrMsg = ""
	logger.Debug("[Renderer] end up with success, cluster=%s", deployment.Name)

	return nil
}

// =======================================
// 		STEP0 参数校验
// =======================================

// 去除空str，禁止出现重复key，如果出现重复key，则报错
func noDuplicateKey(strSlice []string) ([]string, error) {
	allKeys := make(map[string]bool)
	list := []string{}
	for _, item := range strSlice {
		itemSlice := strings.Split(item, " ")
		if len(itemSlice) <= 0 || itemSlice[0] == "" {
			continue
		}
		if _, value := allKeys[itemSlice[0]]; !value {
			allKeys[itemSlice[0]] = true
			list = append(list, item)
		} else {
			return list, fmt.Errorf("duplicate key exists, key=[%v]", itemSlice[0])
		}
	}
	return list, nil
}

// ! step0. Validate set the values by default if not defined and checks if the values given are valid
func validate(deployment *Cluster, ctx *Context) error {
	// (1) 集群名长度检查
	if len(deployment.Name) > common.CLUSTER_NAME_LENGTH_LIMIT {
		err := fmt.Errorf("name length cannot be higher than %d", common.CLUSTER_NAME_LENGTH_LIMIT)
		logger.Error("%v, cluster=%s", err, deployment.Name)
		return err
	}

	// (2) RedisClusterType合法性检查
	switch deployment.InspectionMode {
	case omodel.MODE_FULL_CARE, omodel.MODE_KEEP_POD_ALIVE, omodel.MODE_NOT_CARE:
		break
	default:
		deployment.InspectionMode = omodel.MODE_FULL_CARE
	}

	// (3) 自定义redis配置去重
	var err error
	deployment.Spec.Redis.CustomConfig, err = noDuplicateKey(deployment.Spec.Redis.CustomConfig)
	if err != nil {
		logger.Error("key of CustomConfig must be unique, cluster=%s, error=(%v)", deployment.Name, err)
		return err
	}

	// (4) 镜像检查
	if deployment.Spec.Redis.Image == "" {
		deployment.Spec.Redis.Image = common.DEFAULT_REDIS_IMAGE
	}
	if deployment.Spec.Redis.ImageVersion == "" {
		deployment.Spec.Redis.ImageVersion = common.DEFAULT_IMAGE_VERSION
	}
	if deployment.Spec.Sentinel.Image == "" {
		deployment.Spec.Sentinel.Image = common.DEFAULT_SENTINEL_IMAGE
	}
	if deployment.Spec.Sentinel.ImageVersion == "" {
		deployment.Spec.Sentinel.ImageVersion = common.DEFAULT_IMAGE_VERSION
	}
	if deployment.Spec.Router.Image == "" {
		deployment.Spec.Router.Image = common.DEFAULT_ROUTER_IMAGE
	}
	if deployment.Spec.Router.ImageVersion == "" {
		deployment.Spec.Router.ImageVersion = common.DEFAULT_IMAGE_VERSION
	}

	// (5) 分片数大于0检查
	if deployment.Spec.Redis.NumOfShards <= 0 {
		err := fmt.Errorf("deployment.Spec.Redis.NumOfShards is invalid, it should more than 0")
		logger.Error("%v, cluster=%s", err, deployment.Name)
		return err
	}

	// (6) 各分片副本数检查
	for az, replica := range deployment.Spec.Redis.Replicas {
		if replica < 0 {
			deployment.Spec.Redis.Replicas[az] = 0
		}
	}

	// (7) sentinel副本数检查
	if deployment.Spec.Sentinel.Replicas == nil {
		deployment.Spec.Sentinel.Replicas = map[string]int{
			"hba": 1,
			"hbb": 1,
			"hbc": 1,
		}
	}
	totalSentinelNum := 0
	for _, num := range deployment.Spec.Sentinel.Replicas {
		totalSentinelNum += num
	}
	if totalSentinelNum%2 == 0 {
		err := fmt.Errorf("the number of sentinels cannot be an even number, cluster=%s", deployment.Name)
		logger.Error(err.Error())
		return err
	}
	if totalSentinelNum < 3 {
		err := fmt.Errorf("the number of sentinels cannot be less than 3, cluster=%s", deployment.Name)
		logger.Error(err.Error())
		return err
	}
	// 计算quorum
	ctx.SentinelQuorum = (totalSentinelNum/2 + 1)

	// (8) router副本数检查
	if deployment.Spec.Router.Replicas == nil {
		deployment.Spec.Router.Replicas = map[string]int{
			"hba": common.DEFAULT_ROUTER_NUMBER,
			"hbb": common.DEFAULT_ROUTER_NUMBER,
		}
	}

	// (9) 端口号非0检查
	if deployment.Spec.Redis.Port <= 0 {
		deployment.Spec.Redis.Port = common.DEFAULT_REDIS_PORT
	}
	if deployment.Spec.Sentinel.Port <= 0 {
		deployment.Spec.Sentinel.Port = common.DEFAULT_SENTINEL_PORT
	}
	if deployment.Spec.Router.Port <= 0 {
		deployment.Spec.Router.Port = common.DEFAULT_ROUTER_PORT
	}
	if deployment.Spec.Router.SPort <= 0 {
		deployment.Spec.Router.SPort = common.DEFAULT_ROUTER_SPORT
	}

	// (10) 巡检状态检查
	if deployment.InspectionResult.State == INSPECTION_STATE_INIT {
		deployment.InspectionResult.StepErrTimes = [TOTAL_STEP]int{}
	}

	// 合法性检查11 产品线、子系统配置非空检查
	if deployment.Spec.App.ProductLine == "" {
		err := fmt.Errorf("deployment.Spec.App.ProductLine can not be nil, cluster=%s", deployment.Name)
		logger.Error("%v, cluster=%s", err, deployment.Name)
		return err
	}
	if deployment.Spec.App.Subsystem == "" {
		err := fmt.Errorf("deployment.Spec.App.Subsystems can not be nil, cluster=%s", deployment.Name)
		logger.Error("%v, cluster=%s", err, deployment.Name)
		return err
	}
	if deployment.Spec.App.SubsystemAlias == "" {
		deployment.Spec.App.SubsystemAlias = deployment.Spec.App.Subsystem
	}

	if deployment.Spec.App.SentinelSubsystem == "" {
		err := fmt.Errorf("deployment.Spec.App.SentinelSubSystems can not be nil, cluster=%s", deployment.Name)
		logger.Error("%v, cluster=%s", err, deployment.Name)
		return err
	}
	if deployment.Spec.App.AppPrefix == "" {
		err := fmt.Errorf("deployment.Spec.App.AppPrefix can not be nil, cluster=%s", deployment.Name)
		logger.Error("%v, cluster=%s", err, deployment.Name)
		return err
	}
	if deployment.Spec.App.AppPrefixAlias == "" {
		deployment.Spec.App.AppPrefixAlias = deployment.Spec.App.AppPrefix
	}

	return nil
}

// =======================================
// 		STEP1 APP创建
// =======================================

// ! step1. 检查3个bns的存在，不存在则创建
func createApps(deployment *Cluster, _ *Context) error {
	// 只有部署状态下才会去创建APP，集群部署完就不需要执行这个阶段了
	if deployment.Status != DEPLOYMENT_STATUS_INIT {
		return nil
	}

	// 创建redis bns
	err := lib.CreateApp(&apptree.App{
		Product:        deployment.Spec.App.ProductLine,
		ProductAlias:   deployment.Spec.App.ProductLine,
		Subsystem:      deployment.Spec.App.Subsystem,
		SubsystemAlias: deployment.Spec.App.Subsystem,
		Name:           common.GetRedisAppName(deployment.Spec.App.AppPrefix),
		Alias:          common.GetRedisAppName(deployment.Spec.App.AppPrefixAlias),
		Level:          deployment.Spec.App.Level,
		DepartID:       int32(deployment.Spec.App.DepartmentId),
		DepartName:     deployment.Spec.App.DepartmentName,
		Description:    deployment.Spec.App.AppPrefix,
		RdOwner:        deployment.Spec.App.RDOwner,
		OpOwner:        deployment.Spec.App.OPOwner,
		Applicant:      deployment.Spec.App.RDOwner,
	}, deployment.Spec.Redis.Port)
	if err != nil {
		return err
	}

	// 创建sentinel bns
	err = lib.CreateApp(&apptree.App{
		Product:        deployment.Spec.App.ProductLine,
		ProductAlias:   deployment.Spec.App.ProductLine,
		Subsystem:      deployment.Spec.App.SentinelSubsystem,
		SubsystemAlias: deployment.Spec.App.SentinelSubsystem,
		Name:           common.GetSentinelAppName(deployment.Spec.App.AppPrefix),
		Alias:          common.GetSentinelAppName(deployment.Spec.App.AppPrefixAlias),
		Level:          deployment.Spec.App.Level,
		DepartID:       int32(deployment.Spec.App.DepartmentId),
		DepartName:     deployment.Spec.App.DepartmentName,
		Description:    deployment.Spec.App.AppPrefix,
		RdOwner:        deployment.Spec.App.RDOwner,
		OpOwner:        deployment.Spec.App.OPOwner,
		Applicant:      deployment.Spec.App.RDOwner,
	}, deployment.Spec.Sentinel.Port)
	if err != nil {
		return err
	}

	// 创建router bns
	err = lib.CreateApp(&apptree.App{
		Product:        deployment.Spec.App.ProductLine,
		ProductAlias:   deployment.Spec.App.ProductLine,
		Subsystem:      deployment.Spec.App.Subsystem,
		SubsystemAlias: deployment.Spec.App.Subsystem,
		Name:           common.GetRouterAppName(deployment.Spec.App.AppPrefix),
		Alias:          common.GetRouterAppName(deployment.Spec.App.AppPrefixAlias),
		Level:          deployment.Spec.App.Level,
		DepartID:       int32(deployment.Spec.App.DepartmentId),
		DepartName:     deployment.Spec.App.DepartmentName,
		Description:    deployment.Spec.App.AppPrefix,
		RdOwner:        deployment.Spec.App.RDOwner,
		OpOwner:        deployment.Spec.App.OPOwner,
		Applicant:      deployment.Spec.App.RDOwner,
	}, deployment.Spec.Router.Port)
	if err != nil {
		return err
	}

	return nil
}

// =======================================
// 		STEP 2&10 前置：实例数量检查
// =======================================

// 检查Redis的数据库实例数量是否符合预期，判断是否需要申请新实例
// Context 赋值RedisInstances
// return true，nil 数据库实例不够，需要走申请流程
// return false     可能异常报错，如果没报错说明数量跟配置是一致的
func isNewRedisNeeded(deployment *Cluster, ctx *Context) (bool, error) {
	var err error
	// 获取未打污点的实例列表
	ctx.RedisInstances, err = lib.QueryRedisInstances(deployment.Name)
	if err != nil {
		logger.Error("failed to query redis instances, cluster=%s, error=(%v)", deployment.Name, err)
		return false, err
	}

	// 转换成map方便比对，[分片名][机房]=数量
	checkMap := map[string]map[string]int{}
	for _, instance := range ctx.RedisInstances {
		if _, ok := checkMap[instance.Name]; !ok {
			checkMap[instance.Name] = map[string]int{}
		}
		checkMap[instance.Name][instance.IDC]++
	}

	for index := 0; index < deployment.Spec.Redis.NumOfShards; index++ {
		name := fmt.Sprintf("%s-server%d", deployment.Name, index+1)
		for _, idc := range deployment.EnabledAZ {
			// 对应机房实例数量少
			if checkMap[name][idc] < deployment.Spec.Redis.Replicas[idc] {
				logger.Info("number of redis instances is less than expected, cluster=%s, idc=%s, shardName=%s, currentNum=%d, expectedNum=%d", deployment.Name, idc, name, checkMap[name][idc], deployment.Spec.Redis.Replicas[idc])
				return true, nil
			}
			// 对应机房实例数量多
			if checkMap[name][idc] > deployment.Spec.Redis.Replicas[idc] {
				logger.Error("number of redis instances is greater than expected, cluster=%s, idc=%s, shardName=%s, currentNum=%d, expectedNum=%d", deployment.Name, idc, name, checkMap[name][idc], deployment.Spec.Redis.Replicas[idc])
				return false, fmt.Errorf("number of redis instances in %s is greater than expected", idc)
			}
			// 从map中删除以方便比较是否有多处来的未记录实例
			delete(checkMap[name], idc)
		}
	}
	// 分片数一旦确定不会改变，EnabledAZ通常也不会变化，如果要替换机房也应该先对被替换的机房实例打污点
	for k, v := range checkMap {
		if len(v) != 0 {
			logger.Error("there are redis instances that should not exist, cluster=%s, shardName=%s, map=[%+v]", deployment.Name, k, v)
			return false, fmt.Errorf("redis instances should not exist")
		}
	}

	return false, nil
}

// 检查Sentinel的数据库实例数量是否符合预期，判断是否需要申请新实例
// Context 赋值SentinelInstances
// return true，nil 数据库实例不够，需要走申请流程
// return false     可能异常报错，如果没报错说明数量跟配置是一致的
func isNewSentinelNeeded(deployment *Cluster, ctx *Context) (bool, error) {
	var err error
	// 获取未打污点的实例列表
	ctx.SentinelInstances, err = lib.QuerySentinelInstances(deployment.Name)
	if err != nil {
		logger.Error("failed to query sentinel instances, cluster=%s, error=(%v)", deployment.Name, err)
		return false, err
	}

	// 转换成map方便比对，[机房]=数量
	checkMap := map[string]int{}
	for _, instance := range ctx.SentinelInstances {
		checkMap[instance.IDC]++
	}

	for _, idc := range deployment.EnabledAZ {
		// 对应机房实例数量少
		if checkMap[idc] < deployment.Spec.Sentinel.Replicas[idc] {
			logger.Info("number of sentinel instances is less than expected, cluster=%s, idc=%s, currentNum=%d, expectedNum=%d", deployment.Name, idc, checkMap[idc], deployment.Spec.Sentinel.Replicas[idc])
			return true, nil
		}
		// 对应机房实例数量多
		if checkMap[idc] > deployment.Spec.Sentinel.Replicas[idc] {
			logger.Error("number of sentinel instances is greater than expected, cluster=%s, idc=%s, currentNum=%d, expectedNum=%d", deployment.Name, idc, checkMap[idc], deployment.Spec.Sentinel.Replicas[idc])
			return false, fmt.Errorf("number of sentinel instances in %s is greater than expected", idc)
		}
		// 从map中删除以方便比较是否有多处来的未记录实例
		delete(checkMap, idc)
	}

	if len(checkMap) != 0 {
		logger.Error("there are sentinel instances that should not exist, cluster=%s, map=[%+v]", deployment.Name, checkMap)
		return false, fmt.Errorf("sentinel instances should not exist")
	}

	return false, nil
}

// 检查Proxy的数据库实例数量是否符合预期，判断是否需要申请新实例
// Context 赋值RouterInstances
// return true，nil 数据库实例不够，需要走申请流程
// return false     可能异常报错，如果没报错说明数量跟配置是一致的
func isNewRouterNeeded(deployment *Cluster, ctx *Context) (bool, error) {
	var err error
	// 获取未打污点的实例列表
	ctx.RouterInstances, err = lib.QueryProxyInstances(deployment.Name)
	if err != nil {
		logger.Error("failed to query proxy instances, cluster=%s, error=(%v)", deployment.Name, err)
		return false, err
	}

	// 转换成map方便比对，[机房]=数量
	checkMap := map[string]int{}
	for _, instance := range ctx.RouterInstances {
		checkMap[instance.IDC]++
	}

	for _, idc := range deployment.EnabledAZ {
		// 对应机房实例数量少
		if checkMap[idc] < deployment.Spec.Router.Replicas[idc] {
			logger.Info("number of proxy instances is less than expected, cluster=%s, idc=%s, currentNum=%d, expectedNum=%d", deployment.Name, idc, checkMap[idc], deployment.Spec.Router.Replicas[idc])
			return true, nil
		}
		// 对应机房实例数量多
		if checkMap[idc] > deployment.Spec.Router.Replicas[idc] {
			logger.Error("number of proxy instances is greater than expected, cluster=%s, idc=%s, currentNum=%d, expectedNum=%d", deployment.Name, idc, checkMap[idc], deployment.Spec.Router.Replicas[idc])
			return false, fmt.Errorf("number of proxy instances in %s is greater than expected", idc)
		}
		// 从map中删除以方便比较是否有多处来的未记录实例
		delete(checkMap, idc)
	}

	if len(checkMap) != 0 {
		logger.Error("there are proxy instances that should not exist, cluster=%s, map=[%+v]", deployment.Name, checkMap)
		return false, fmt.Errorf("proxy instances should not exist")
	}

	return false, nil
}

// =======================================
// 		STEP 2&10 POD申请
// =======================================

// redis pod数量少时申请redis pod
// 1、如果UnconfirmedPods[Redis]=1，ListPod，如果=0，SelectRedis
// 2、根据第一步得到的结果，比对deployment.Spec.Redis检查对应机房是否有足够的running状态的容器实例
// 3.1、如果数量不足，调用fec接口申请新pod，并将UnconfirmedPods[Redis]置为1
// 3.2、如果数量超了，打印error日志并返回错误
func createRedisPod(deployment *Cluster) error {
	podList, err := lib.ListPods(deployment.Alias, common.COMPONENT_REDIS)
	if err != nil {
		logger.Error("failed to list pods, cluster=%s, error=(%v)", deployment.Name, err)
		return err
	}

	// 以idx-idc为key转成map
	podMap := map[string][]*fec.PodInfo{}
	for _, pod := range podList {
		index, ok := pod.Labels[common.LABEL_INDEX]
		if !ok {
			errMsg := fmt.Sprintf("pod %v has no shard label", pod.PodName)
			logger.Error(errMsg)
			return errors.New(errMsg)
		}
		idc, ok := pod.Labels[common.LABEL_IDC]
		if !ok {
			errMsg := fmt.Sprintf("pod %v has no idc label", pod.PodName)
			logger.Error(errMsg)
			return errors.New(errMsg)
		}

		k := fmt.Sprintf("%s-%s", index, idc)
		if _, ok := podMap[k]; !ok {
			podMap[k] = []*fec.PodInfo{}
		}
		podMap[k] = append(podMap[k], pod)
	}

	for index := 0; index < deployment.Spec.Redis.NumOfShards; index++ {
		for _, az := range deployment.EnabledAZ {
			k := fmt.Sprintf("%d-%s", index, az)
			// pod比预期多
			if len(podMap[k]) > deployment.Spec.Redis.Replicas[az] {
				logger.Error("too many redis pods, stop render, cluster=%s, idc=%s, shardName=server%d, currentNum=%d, expectedNum=%d", deployment.Name, az, index+1, len(podMap[k]), deployment.Spec.Redis.Replicas[az])
				return errors.New("too many redis pods")
			}
			// pod比预期少
			if len(podMap[k]) < deployment.Spec.Redis.Replicas[az] {
				logger.Info("too few redis pods, apply new pods, cluster=%s, idc=%s, shardName=server%d, currentNum=%d, expectedNum=%d", deployment.Name, az, index+1, len(podMap[k]), deployment.Spec.Redis.Replicas[az])
				for i := 0; i < deployment.Spec.Redis.Replicas[az]-len(podMap[k]); i++ {
					err := lib.ApplyRedisPods(&lib.PodSetting{
						Image:           deployment.Spec.Redis.Image,
						ImageVersion:    deployment.Spec.Redis.ImageVersion,
						ImagePullPolicy: deployment.Spec.Redis.ImagePullPolicy,
						Resource: &lib.Resource{
							CPU: deployment.Spec.Redis.Resource.CPU,
							Mem: deployment.Spec.Redis.Resource.Mem,
						},
						Port:        deployment.Spec.Redis.Port,
						IDC:         az,
						ProductLine: deployment.Spec.App.ProductLine,
						Alias:       deployment.Alias,
						Redis: &lib.RedisCustom{
							Index: index,
						},
					})
					if err != nil {
						logger.Error("failed to apply redis pod, cluster=%s, error=(%v)", deployment.Name, err)
						return err
					}
				}
			}
		}
	}

	return nil
}

// sentinel pod数量少时申请sentinel pod
func createSentinelPod(deployment *Cluster) error {
	podList, err := lib.ListPods(deployment.Alias, common.COMPONENT_SENTINEL)
	if err != nil {
		logger.Error("failed to list pods, cluster=%s, error=(%v)", deployment.Name, err)
		return err
	}
	podMap, err := lib.MapByIDC(podList)
	if err != nil {
		return err
	}

	// 检查各生效机房pod数量
	for _, az := range deployment.EnabledAZ {
		if _, ok := deployment.Spec.Sentinel.Replicas[az]; !ok {
			continue
		}
		// pod 比预期多
		if len(podMap[az]) > deployment.Spec.Sentinel.Replicas[az] {
			logger.Error("too many sentinel pods, stop render, cluster=%s, idc=%s, currentNum=%d, expectedNum=%d", deployment.Name, az, len(podMap[az]), deployment.Spec.Sentinel.Replicas[az])
			return errors.New("too many sentinel pods")
		}
		// pod 比预期少
		if len(podMap[az]) < deployment.Spec.Sentinel.Replicas[az] {
			logger.Info("too few sentinel pods, apply new pods, cluster=%s, idc=%s, currentNum=%d, expectedNum=%d", deployment.Name, az, len(podMap[az]), deployment.Spec.Sentinel.Replicas[az])
			for i := 0; i < deployment.Spec.Sentinel.Replicas[az]-len(podMap[az]); i++ {
				err := lib.ApplySentinelPods(&lib.PodSetting{
					ProductLine:     deployment.Spec.App.ProductLine,
					Alias:           deployment.Alias,
					Image:           deployment.Spec.Sentinel.Image,
					ImageVersion:    deployment.Spec.Sentinel.ImageVersion,
					ImagePullPolicy: deployment.Spec.Sentinel.ImagePullPolicy,
					Resource: &lib.Resource{
						CPU: deployment.Spec.Sentinel.Resource.CPU,
						Mem: deployment.Spec.Sentinel.Resource.Mem,
					},
					Port: deployment.Spec.Sentinel.Port,
					IDC:  az,
				})
				if err != nil {
					logger.Error("failed to apply sentinel pod, cluster=%s, error=(%v)", deployment.Name, err)
					return err
				}
			}
		}
	}

	return nil
}

// proxy pod数量少时申请proxy pod
func createRouterPod(deployment *Cluster) error {
	podList, err := lib.ListPods(deployment.Alias, common.COMPONENT_ROUTER)
	if err != nil {
		logger.Error("failed to list pods, cluster=%s, error=(%v)", deployment.Name, err)
		return err
	}
	podMap, err := lib.MapByIDC(podList)
	if err != nil {
		return err
	}

	for _, az := range deployment.EnabledAZ {
		if _, ok := deployment.Spec.Router.Replicas[az]; !ok {
			continue
		}
		if len(podMap[az]) > deployment.Spec.Router.Replicas[az] {
			logger.Error("too many router pods, stop render, cluster=%s, idc=%s, currentNum=%d, expectedNum=%d", deployment.Name, az, len(podMap[az]), deployment.Spec.Router.Replicas[az])
			return errors.New("too many router pods")
		}
		if len(podMap[az]) < deployment.Spec.Router.Replicas[az] {
			logger.Info("too few router pods, apply new pods, cluster=%s, idc=%s, currentNum=%d, expectedNum=%d", deployment.Name, az, len(podMap[az]), deployment.Spec.Router.Replicas[az])
			for i := 0; i < deployment.Spec.Router.Replicas[az]-len(podMap[az]); i++ {
				err := lib.ApplyRouterPods(&lib.PodSetting{
					ProductLine:     deployment.Spec.App.ProductLine,
					Alias:           deployment.Alias,
					Image:           deployment.Spec.Router.Image,
					ImageVersion:    deployment.Spec.Router.ImageVersion,
					ImagePullPolicy: deployment.Spec.Router.ImagePullPolicy,
					Resource: &lib.Resource{
						CPU: deployment.Spec.Router.Resource.CPU,
						Mem: deployment.Spec.Router.Resource.Mem,
					},
					Port: deployment.Spec.Router.Port,
					IDC:  az,
					Router: &lib.RouterCustom{
						SentinelPort:      deployment.Spec.Sentinel.Port,
						ClientAuth:        deployment.Spec.Router.ClientAuth,
						ClientConnections: deployment.Spec.Router.ClientConnections,
						NumOfShards:       deployment.Spec.Redis.NumOfShards,
					},
				})
				if err != nil {
					logger.Error("failed to apply router pod, cluster=%s, error=(%v)", deployment.Name, err)
					return err
				}
			}
		}
	}

	return nil
}

// ! step2.创建redis/sentinel Pod资源
func createRedisAndSentinelPods(deployment *Cluster, ctx *Context) error {
	var err error
	// 检查数据库记录与预期是否一致
	ctx.ApplyNewPods[INDEX_NEW_REDIS], err = isNewRedisNeeded(deployment, ctx)
	if err != nil {
		return err
	}
	ctx.ApplyNewPods[INDEX_NEW_SENTINEL], err = isNewSentinelNeeded(deployment, ctx)
	if err != nil {
		return err
	}

	// 如果一致就跳过，不申请新pod
	if ctx.ApplyNewPods[INDEX_NEW_REDIS] {
		err = createRedisPod(deployment)
		if err != nil {
			return err
		}
	}
	if ctx.ApplyNewPods[INDEX_NEW_SENTINEL] {
		err = createSentinelPod(deployment)
		if err != nil {
			return err
		}
	}

	return nil
}

// ! step10.创建router Pod资源
func createRouterPods(deployment *Cluster, ctx *Context) error {
	var err error
	// 检查数据库router的记录与预期是否一致
	ctx.ApplyNewPods[INDEX_NEW_ROUTER], err = isNewRouterNeeded(deployment, ctx)
	if err != nil || !ctx.ApplyNewPods[INDEX_NEW_ROUTER] {
		return err
	}
	err = createRouterPod(deployment)
	if err != nil {
		return err
	}

	return nil
}

// =======================================
//    STEP 3&11 后置：检查状态、挂实例、落库
// =======================================

// 更新app实例列表，并确保app挂载已生效。
func upsertNoahInstances(podList []*fec.PodInfo, productLine, subsystem, app, deployPath string, port int, disable bool) error {
	// 1.将Pod转化成noahInstance
	podInstanceMap := lib.PodsToInstances(podList, port, deployPath, disable)

	// 2.获取noah bns中所有实例信息
	noahInstanceList, err := noah.GetInstances(productLine, app)
	if err != nil {
		logger.Error("failed to get bns instances, error=(%v)", err)
		return err
	}

	// 3.检查bns中容器实例信息是否缺失、多余
	needToAddInstance, _, needToUpdateInstance := lib.DiffInstances(noahInstanceList, podInstanceMap)

	// 4.根据缺失列表，进行添加
	if len(needToAddInstance) > 0 {
		err = noah.AddInstances(productLine, subsystem, app, needToAddInstance)
		if err != nil {
			logger.Error("failed to add noah instance, error=(%v), instance=[%v]", err, needToAddInstance)
			return err
		}
	}

	// 5.根据更新列表，进行更新
	if len(needToUpdateInstance) > 0 {
		for _, instance := range needToUpdateInstance {
			err = noah.UpdateInstance(productLine, app, instance)
			if err != nil {
				logger.Error("failed to update noah instances, error=(%v), instance=[%v]", err, needToUpdateInstance)
				return err
			}
		}
	}

	return nil
}

// redis pod 状态检查及后置操作（落库、挂实例）
func saveRedisPods(deployment *Cluster, ctx *Context) error {
	if !ctx.ApplyNewPods[INDEX_NEW_REDIS] {
		return nil
	}

	timeout, cancel := context.WithTimeout(context.Background(), time.Minute*2)
	defer cancel()
	ticker := time.NewTicker(10 * time.Second) // 定时检查同步状态，每15秒检查一次
	defer ticker.Stop()
	for {
		// 获取redis pods检查状态，如果没有全部running就等下一轮直到超时
		podList, err := lib.ListPods(deployment.Alias, common.COMPONENT_REDIS)
		if err != nil {
			return err
		}
		allPodRunning := true
		for _, pod := range podList {
			if pod.Status != "Running" {
				allPodRunning = false
				break
			}
		}
		if allPodRunning {
			// 更新redis bns实例
			err = upsertNoahInstances(podList, deployment.Spec.App.ProductLine, deployment.Spec.App.Subsystem, common.GetRedisAppName(deployment.Spec.App.AppPrefix), common.INSTANCE_DEPLOY_REDIS, deployment.Spec.Redis.Port, false)
			if err != nil {
				logger.Error("failed to upsert redis noah instances, cluster=%s, error=(%v)", deployment.Name, err)
				return err
			}

			// 最后再落库，避免先落库后实例挂载失败，下一轮就不会再触发了
			bns := common.GetRedisAppName(deployment.Spec.App.AppPrefix) + "." + deployment.Spec.App.ProductLine
			port := deployment.Spec.Redis.Port
			err := lib.BulkCreateRedis(deployment.ID, deployment.Name, bns, port, podList)
			if err != nil {
				return err
			}

			// 重新校验一遍，如果还有问题就报错
			notOK, err := isNewRedisNeeded(deployment, ctx)
			if err != nil {
				return err
			}
			if notOK {
				return fmt.Errorf("the number of redis instance is still less than spec, cluster=%s", deployment.Name)
			}

			return nil
		}

		select {
		case <-ticker.C: // 阻塞
		case <-timeout.Done():
			return fmt.Errorf("redis pods check timeout")
		}
	}
}

// sentinel pod 状态检查及后置操作（落库、挂实例）
func saveSentinelPods(deployment *Cluster, ctx *Context) error {
	if !ctx.ApplyNewPods[INDEX_NEW_SENTINEL] {
		return nil
	}

	timeout, cancel := context.WithTimeout(context.Background(), time.Minute*2)
	defer cancel()
	ticker := time.NewTicker(10 * time.Second) // 定时检查同步状态，每15秒检查一次
	defer ticker.Stop()
	for {
		// 获取sentinel pods检查状态，如果没有全部running就等下一轮直到超时
		podList, err := lib.ListPods(deployment.Alias, common.COMPONENT_SENTINEL)
		if err != nil {
			return err
		}
		allPodRunning := true
		for _, pod := range podList {
			if pod.Status != "Running" {
				allPodRunning = false
				break
			}
		}
		// 落库
		if allPodRunning {
			disable := false
			// 迁移部署时，需要屏蔽掉sentinel，避免proxy连上容器sentinel，后续扩容的sentinel，都不需要屏蔽
			if deployment.Status == DEPLOYMENT_STATUS_INIT && len(deployment.EnabledAZ) < len(deployment.Spec.Sentinel.Replicas) {
				disable = true
			}
			// 更新sentinel bns实例
			err = upsertNoahInstances(podList, deployment.Spec.App.ProductLine, deployment.Spec.App.SentinelSubsystem, common.GetSentinelAppName(deployment.Spec.App.AppPrefix), common.INSTANCE_DEPLOY_SENTINEL, deployment.Spec.Sentinel.Port, disable)
			if err != nil {
				logger.Error("failed to upsert sentinel noah instances, cluster=%s, error=(%v)", deployment.Name, err)
				return err
			}

			// 落库
			bns := common.GetSentinelAppName(deployment.Spec.App.AppPrefix) + "." + deployment.Spec.App.ProductLine
			port := deployment.Spec.Sentinel.Port
			err := lib.BulkCreateSentinel(deployment.ID, deployment.Name, bns, port, podList)
			if err != nil {
				return err
			}

			// 重新校验一遍，如果还有问题就报错
			notOK, err := isNewSentinelNeeded(deployment, ctx)
			if err != nil {
				return err
			}
			if notOK {
				return fmt.Errorf("the number of sentinel instance is still less than spec, cluster=%s", deployment.Name)
			}

			return nil
		}

		select {
		case <-ticker.C: // 阻塞
		case <-timeout.Done():
			return fmt.Errorf("sentinel pods check timeout")
		}
	}
}

// ! step3.检查redis/sentinel pod运行状态是否正常，此阶段耗时会比较久，设置2min超时15s重试
func saveRedisAndSentinelPods(deployment *Cluster, ctx *Context) error {
	err := saveRedisPods(deployment, ctx)
	if err != nil {
		return err
	}

	err = saveSentinelPods(deployment, ctx)
	if err != nil {
		return err
	}

	return nil
}

// ! step11. router pod 状态检查及后置操作（落库、挂实例）
func saveRouterPods(deployment *Cluster, ctx *Context) error {
	if !ctx.ApplyNewPods[INDEX_NEW_ROUTER] {
		return nil
	}

	timeout, cancel := context.WithTimeout(context.Background(), time.Minute*2)
	defer cancel()
	ticker := time.NewTicker(10 * time.Second) // 定时检查同步状态，每10秒检查一次
	defer ticker.Stop()
	for {
		podList, err := lib.ListPods(deployment.Alias, common.COMPONENT_ROUTER)
		if err != nil {
			return err
		}
		allPodRunning := true
		for _, pod := range podList {
			// 检查Pod是否running
			if pod.Status != "Running" {
				allPodRunning = false
				break
			}
			// 检查redis-agent是否ready，会影响白名单更新
			err := ragent.HealthCheck(pod.PodIp)
			if err != nil {
				logger.Debug("redis agent not ready, cluster=%s, podIp=%s, error=(%v)", deployment.Name, pod.PodIp, err)
				allPodRunning = false
				break
			}
		}
		// 落库
		if allPodRunning {
			disable := true
			// 新部署的集群可以解开屏蔽，后续扩的都得屏蔽着挂进去
			if deployment.Status == DEPLOYMENT_STATUS_INIT && len(deployment.EnabledAZ) >= len(deployment.Spec.Sentinel.Replicas) {
				disable = false
			}
			// 更新proxy bns实例
			err = upsertNoahInstances(podList, deployment.Spec.App.ProductLine, deployment.Spec.App.Subsystem, common.GetRouterAppName(deployment.Spec.App.AppPrefix), common.INSTANCE_DEPLOY_ROUTER, deployment.Spec.Router.Port, disable)
			if err != nil {
				logger.Error("failed to upsert proxy noah instances, cluster=%s, error=(%v)", deployment.Name, err)
				return err
			}

			// 落库
			bns := common.GetRouterAppName(deployment.Spec.App.AppPrefix) + "." + deployment.Spec.App.ProductLine
			port := deployment.Spec.Router.Port
			err := lib.BulkCreateRouter(deployment.ID, deployment.Name, bns, port, podList)
			if err != nil {
				return err
			}

			// 重新校验一遍，如果还有问题就报错
			notOK, err := isNewRouterNeeded(deployment, ctx)
			if err != nil {
				return err
			}
			if notOK {
				return fmt.Errorf("the number of proxy instance is still less than spec, cluster=%s", deployment.Name)
			}

			return nil
		}

		select {
		case <-ticker.C: // 阻塞
		case <-timeout.Done():
			return fmt.Errorf("proxy pods check timeout")
		}
	}
}

// =======================================
// 		 STEP 4&12 POD白名单
// =======================================

// 更新白名单
func ensureWhitelist(deployment *Cluster, component string, instances []*omodel.Instance) error {
	var port int
	var privilege, privilegeX string
	var bnsWhitelist, ipWhitelist []string

	// 获取配置中的白名单BNS（rw:全组件读写/ro:redis只读）
	rwBns, roBns, err := lib.GetManagementWhiteListFromDB()
	if err != nil {
		logger.Error("failed to GetManagementWhiteListFromDB, cluster=%s, error=(%v)", deployment.Name, err)
		return err
	}

	// 根据组件不同确定对应的权限和额外白名单
	switch component {
	case common.COMPONENT_SENTINEL:
		port = deployment.Spec.Sentinel.Port
		privilege = common.PRIVILEGE_X
		privilegeX = common.PRIVILEGE_X
	case common.COMPONENT_REDIS:
		port = deployment.Spec.Redis.Port
		privilege = common.PRIVILEGE_RW
		privilegeX = common.PRIVILEGE_RWX

		// redis只读
		for _, bns := range roBns {
			bnsWhitelist = append(bnsWhitelist, fmt.Sprintf("%s %s", bns, common.PRIVILEGE_R))
		}
	case common.COMPONENT_ROUTER:
		port = deployment.Spec.Router.Port
		privilege = common.PRIVILEGE_RW
		privilegeX = common.PRIVILEGE_RWX

		// 获取业务白名单
		var err error
		bnsWhitelist, ipWhitelist, err = lib.GetProxyWhiteListFromDB(deployment.Name)
		if err != nil {
			return err
		}
		_, invalid := lib.RouterWhiteListValidate(bnsWhitelist)
		if len(invalid) != 0 {
			logger.Error("proxy has invalid bns whitelist, cluster=%s, whitelists=(%v)", deployment.Name, strings.Join(invalid, ","))
			return fmt.Errorf("invalid bns whitelist")
		}
		_, invalid = lib.RouterWhiteListValidate(ipWhitelist)
		if len(invalid) != 0 {
			logger.Error("proxy has invalid ip whitelist, cluster=%s, whitelists=(%v)", deployment.Name, strings.Join(invalid, ","))
			return fmt.Errorf("invalid ip whitelist")
		}
	}

	// 拼接权限
	for _, bns := range rwBns {
		bnsWhitelist = append(bnsWhitelist, fmt.Sprintf("%s %s", bns, privilegeX))
	}
	for _, bns := range deployment.Spec.ClusterWhitelist {
		bnsWhitelist = append(bnsWhitelist, fmt.Sprintf("%s %s", bns, privilege))
	}
	// 追加组件白名单
	bnsWhitelist = append(bnsWhitelist,
		fmt.Sprintf("%s.%s %s", common.GetRedisAppName(deployment.Spec.App.AppPrefix), deployment.Spec.App.ProductLine, privilege),
		fmt.Sprintf("%s.%s %s", common.GetSentinelAppName(deployment.Spec.App.AppPrefix), deployment.Spec.App.ProductLine, privilege),
		fmt.Sprintf("%s.%s %s", common.GetRouterAppName(deployment.Spec.App.AppPrefix), deployment.Spec.App.ProductLine, privilege),
	)
	// 追加本地IP
	ipWhitelist = append(ipWhitelist,
		fmt.Sprintf("%s %s", common.DEFAULT_WHITEIP, privilege),
		fmt.Sprintf("%s %s", common.DEFAULT_HOST_WHITEIP, privilege),
	)

	// 白名单去重
	slices.Sort(bnsWhitelist)
	bnsWhitelist = slices.Compact(bnsWhitelist)
	slices.Sort(ipWhitelist)
	ipWhitelist = slices.Compact(ipWhitelist)
	logger.Debug("cluster=%s, bnsWhiteListNeedToAdd=(%v), ipWhiteListNeedToAdd=(%v)", deployment.Name, bnsWhitelist, ipWhitelist)

	// 调用agent添加白名单
	whitelistParams := &ragent.UpdateParams{
		Port:    port,
		BnsList: bnsWhitelist,
		IpList:  ipWhitelist,
		Action:  "cover",
	}
	for _, instance := range instances {
		err := ragent.UpdateWhitelist(instance.IP, whitelistParams)
		if err == nil {
			continue
		}
		// pod刚running时agent可能还没ready，报错直接退出会吞掉ctx.ApplyNewPods[INDEX_NEW_REDIS]
		if !strings.Contains(err.Error(), "failed to call redis agent") {
			logger.Error("failed to rewrite whitelist, cluster=%s, bnsWhiteListNeedToAdd=(%v), ipWhiteListNeedToAdd=(%v), error=(%v)", deployment.Name, bnsWhitelist, ipWhitelist, err)
			return err
		}

		// 打印日志，等待10s后重试
		logger.Warn("redis-agent may not ready, wait 10s and retry update whitelist, cluster=%s, ip=%s, error=(%v)", deployment.Name, instance.IP, err)
		time.Sleep(10 * time.Second)
		err = ragent.UpdateWhitelist(instance.IP, whitelistParams)
		if err != nil {
			logger.Error("failed to rewrite whitelist, cluster=%s, bnsWhiteListNeedToAdd=(%v), ipWhiteListNeedToAdd=(%v), error=(%v)", deployment.Name, bnsWhitelist, ipWhitelist, err)
			return err
		}
	}

	return nil
}

// ! step4.创建redis/sentinel 白名单
func ensureRedisAndSentinelWhitelist(deployment *Cluster, ctx *Context) error {
	err := ensureWhitelist(deployment, common.COMPONENT_REDIS, ctx.RedisInstances)
	if err != nil {
		return err
	}
	err = ensureWhitelist(deployment, common.COMPONENT_SENTINEL, ctx.SentinelInstances)
	if err != nil {
		return err
	}
	return nil
}

// ! step12.创建router白名单
func ensureRouterWhitelist(deployment *Cluster, ctx *Context) error {
	return ensureWhitelist(deployment, common.COMPONENT_ROUTER, ctx.RouterInstances)
}

// =======================================
// 		STEP 5  Redis分片关系管理
// =======================================

// 递归找到分片中所有副本
func findAllReplicas(ip string, port int, masters, slaves, flexs *[]*omodel.Instance) {
	info, err := redisc.Info(ip, port, redisc.REPLICATION)
	if err != nil {
		logger.Error("failed to exec redis info, ip=%s, port=%d, error=(%v)", ip, port, err)
		return
	}

	// master，需要去重，但是slave不需要去重
	if info["role"] == omodel.REDIS_ROLE_MASTER {
		for _, item := range *masters {
			if item.IP == ip && item.Port == port {
				return
			}
		}

		*masters = append(*masters, &omodel.Instance{IP: ip, Port: port})
		keyRe := regexp.MustCompile(`^slave[0-9]+$`)
		valRe := regexp.MustCompile(`ip=([^,]+),port=([^,]+)`)
		// slave0:ip=*********,port=7000,state=online,offset=4473
		for key, val := range info {
			if !keyRe.MatchString(key) {
				continue
			}

			matches := valRe.FindAllStringSubmatch(val, -1)
			slavePort, _ := strconv.Atoi(matches[0][2])
			*slaves = append(*slaves, &omodel.Instance{IP: matches[0][1], Port: slavePort})
		}
		return
	}

	// flex，不需要去重
	if info["role"] == omodel.REDIS_ROLE_SLAVE && info["master_host"] == "127.0.0.1" {
		*flexs = append(*flexs, &omodel.Instance{IP: ip, Port: port})
		return
	}

	// slave
	masterPort, _ := strconv.Atoi(info["master_port"])
	findAllReplicas(info["master_host"], masterPort, masters, slaves, flexs)
}

// 异步并发SlaveOf
// 为避免一个大集群多个分片同时slaveOf导致线上有感，对slaveOf动作进行并发控制
// 为不阻塞render主流程，slaveOf为异步执行
// 为避免第二轮render开始时slaveOf还没执行完，asyncSlaveOf需要加集群锁
func asyncSlaveOf(deployment *Cluster, pairs [][2]*omodel.Instance) {
	if deployment.InspectionResult.State == INSPECTION_STATE_TEST {
		return
	}

	// 1. 获取集群锁，防止重复执行
	now := time.Now()
	if _, loaded := asyncSlaveOfLock.LoadOrStore(deployment.Name, now); loaded {
		return
	}
	defer asyncSlaveOfLock.Delete(deployment.Name)
	logger.Info("start async slave of, cluster=%s, pairs=%d", deployment.Name, len(pairs))

	// 2. 计算并发度 (最多5轮)
	concurrency := (deployment.Spec.Redis.NumOfShards + 4) / 5 // 向上取整计算批次数
	// 3. 创建控制通道和上下文
	sem := make(chan struct{}, concurrency) // 使用空结构体节省内存
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	wg := &sync.WaitGroup{}
	var hasError atomic.Bool

	// 4. 并发执行 SlaveOf 操作
L:
	for _, instances := range pairs {
		if hasError.Load() {
			break // 快速失败
		}

		master, slave := instances[0], instances[1]
		select {
		case <-ctx.Done():
			break L
		case sem <- struct{}{}:
			wg.Add(1)
			go func(masterIP string, masterPort int, slaveIp string, slavePort int) {
				defer wg.Done()

				// 4.1 执行 SlaveOf
				err := cli.MakeSlave(masterIP, masterPort, slaveIp, slavePort)
				if err != nil {
					logger.Error("failed to exec [slaveof %s %d], cluster=%s, redis=[%s:%d], error=(%v)", masterIP, masterPort, deployment.Name, slaveIp, slavePort, err)
					hasError.Store(true)
					cancel()
					return
				}
				logger.Info("succeed to exec [slaveof %s %d], cluster=%s, redis=[%s:%d]", masterIP, masterPort, deployment.Name, slaveIp, slavePort)

				// 4.2 等待同步完成
				err = redisc.WaitForSyncComplete(masterIP, masterPort, slaveIp, slavePort)
				if err != nil {
					logger.Error("master<->slave sync failed, cluster=%s, slave=[%s:%d], master=[%s:%d], error=(%v)", deployment.Name, slaveIp, slavePort, masterIP, masterPort, err)
					hasError.Store(true)
					cancel()
					return
				}

				<-sem // 释放信号量
				logger.Info("master<->slave sync finished, cluster=%s, slave=[%s:%d], master=[%s:%d]", deployment.Name, slaveIp, slavePort, masterIP, masterPort)
			}(master.IP, master.Port, slave.IP, slave.Port)
		}
	}

	wg.Wait()

	if hasError.Load() {
		logger.Error("async slaveof completed with errors, cluster=%s, cost=%v", deployment.Name, time.Since(now))
	} else {
		logger.Info("async slaveof completed successfully, cluster=%s, cost=%v", deployment.Name, time.Since(now))
	}
}

// ! step5.各分片redis主库健康检查（确保每个分片有且只有1主）
// 如何验证redis分片是否正确：
//
//	(1) 定义3个list，masterList，slaveList和flexList，遍历每个redis-server
//		(a) 如果是master，将pod放进masterList，获取其所有的slaves放进slaveList
//		(b) 如果是slave，并且slave of 127.0.0.1，放进flexList，否则（3）
//		(c) 将其master放进masterList，获取其所有的slaves放进slaveList
//	(2) masterList去重后
//		(a) 如果长度等于0，且flexList长度不为0，从flexList中随机选择一个，作为master，剩下的作为slave
//		(b) 如果长度等于1，将flexList中所有作为slave
//		(c) 如果长度大于1，分片中存在多个主库，不符合预期，可能脑裂了，需要电话报出
func checkShardMaster(deployment *Cluster, ctx *Context) error {
	// 如果有新实例申请，需要等待50s让白名单生效
	if ctx.ApplyNewPods[INDEX_NEW_REDIS] {
		logger.Info("wait 50s for redis instances to refresh redis whitelist, cluster=%s", deployment.Name)
		time.Sleep(50 * time.Second)
	}

	// 按分片整理redis instances
	ctx.ShardReplicaMap = map[string][]*omodel.Instance{}
	// 需要执行slaveOf的副本
	asyncSlaveOfs := [][2]*omodel.Instance{}
	for _, instance := range ctx.RedisInstances {
		if _, ok := ctx.ShardReplicaMap[instance.Name]; !ok {
			ctx.ShardReplicaMap[instance.Name] = []*omodel.Instance{}
		}
		ctx.ShardReplicaMap[instance.Name] = append(ctx.ShardReplicaMap[instance.Name], instance)
	}

	// 遍历所有分片，每个分片梳理出所有的master、slave和flex
	for idx := 0; idx < deployment.Spec.Redis.NumOfShards; idx++ {
		shardName := fmt.Sprintf("%s-server%d", deployment.Name, idx+1)
		_, ok := ctx.ShardReplicaMap[shardName]
		if !ok {
			err := fmt.Errorf("missing pods of shard %s", shardName)
			return err
		}

		// 获取分片内副本全集
		masters, slaves, flexs := []*omodel.Instance{}, []*omodel.Instance{}, []*omodel.Instance{}
		for _, instance := range ctx.ShardReplicaMap[shardName] {
			findAllReplicas(instance.IP, deployment.Spec.Redis.Port, &masters, &slaves, &flexs)
		}

		// 分片所有的副本，idx0是主库
		replicas := []*omodel.Instance{}
		switch len(masters) {
		case 0:
			// 非部署中集群不允许选主，避免极端情况下的脑裂
			if deployment.Status != DEPLOYMENT_STATUS_INIT {
				logger.Error("found no master in shard, cluster=%s, shardNo=%d", deployment.Name, idx)
				return fmt.Errorf("found no master in shard %v", idx)
			}
			// 没master自然也不会有从库，如果也没flex，理论上不可能出现
			if len(flexs) == 0 {
				logger.Error("found no master and no flex redis, cluster=%s, shardNo=%d", deployment.Name, idx)
				return fmt.Errorf("found no master and no flex redis in shard %v", idx)
			}

			// 选第一个flex replica作为主库
			err := cli.MakeMaster(flexs[0].IP, flexs[0].Port)
			if err != nil {
				logger.Error("failed to exec [slaveof no one], cluster=%s, redis=[%s:%d], error=(%v)", deployment.Name, flexs[0].IP, flexs[0].Port, err)
				return err
			}
			logger.Info("succeed to exec [slaveof no one], cluster=%s, redis=[%s:%d]", deployment.Name, flexs[0].IP, flexs[0].Port)
			err = lib.MakeMaster(flexs[0].IP, flexs[0].Port)
			if err != nil {
				logger.Error("failed to update redis role to master, cluster=%s, redis=[%s:%d], error=(%v)", deployment.Name, flexs[0].IP, flexs[0].Port, err)
				return err
			}
			// 剩余的flex去异步执行slaveOf
			for _, instance := range flexs[1:] {
				asyncSlaveOfs = append(asyncSlaveOfs, [2]*omodel.Instance{flexs[0], instance})
			}

			// 主库idx0，从库排后面
			replicas = append(replicas, flexs...)
		case 1:
			// flex去异步执行slaveOf
			for _, instance := range flexs {
				asyncSlaveOfs = append(asyncSlaveOfs, [2]*omodel.Instance{masters[0], instance})
			}

			// 主库idx0，从库排后面
			replicas = append(masters, slaves...)
			replicas = append(replicas, flexs...)
		default:
			// 多个主库，不符合预期
			masterLog := make([]string, len(masters))
			for i, master := range masters {
				masterLog[i] = master.ToString()
			}
			logger.Error("more than one master in one shard, cluster=%s, shardNo=%d, masters=[%v]", deployment.Name, idx, strings.Join(masterLog, ","))
			return fmt.Errorf("more than one master in one shard")
		}

		// 副本列表记到ctx中以供sentinel monitor使用
		ctx.ShardReplicaMap[shardName] = replicas
	}

	// 异步并发slaveOf
	if len(asyncSlaveOfs) != 0 {
		go asyncSlaveOf(deployment, asyncSlaveOfs)
	}

	return nil
}

// =======================================
// 		STEP 6  Redis配置管理
// =======================================

// 将maxmemory配置转换成字节
func convertToBytes(value string) (int, error) {
	value = strings.ToUpper(value)
	re := regexp.MustCompile(`^(\d+)([GMKB]*)$`)
	params := re.FindStringSubmatch(value)

	num := 0
	unit := ""
	switch len(params) {
	case 3:
		num, _ = strconv.Atoi(params[1])
		unit = params[2]
	default:
		return -1, fmt.Errorf("maxmemory value is illegal, maxmemory=%v", value)
	}

	switch unit {
	case "GB":
		num = num * 1024 * 1024 * 1024
	case "MB":
		num = num * 1024 * 1024
	case "KB":
		num = num * 1024
	case "B":
		break
	case "":
		break
	case "G":
		num = num * 1000 * 1000 * 1000
	case "M":
		num = num * 1000 * 1000
	case "K":
		num = num * 1000
	default:
		return -1, fmt.Errorf("maxmemory unit is illegal, maxmemory=%v, unit=%v", value, unit)
	}

	return num, nil
}

// 将configs数组转换成map
func configsToMap(configs []string) (map[string]string, error) {
	if len(configs) == 0 {
		return map[string]string{}, nil
	}

	// 数组转map
	configMap := map[string]string{}
	for _, config := range configs {
		config = strings.TrimSpace(config)
		strs := strings.Split(config, " ")
		if len(strs) != 2 {
			return nil, fmt.Errorf("redis custom config got wrong item %s", config)
		}
		// 将内存相关的配置转换成bytes
		if strs[0] == "maxmemory" || strs[0] == "repl-backlog-size" {
			bytes, err := convertToBytes(strs[1])
			if err != nil {
				return nil, err
			}
			strs[1] = strconv.Itoa(bytes)
		} else if strs[1] == "\"\"" {
			strs[1] = ""
		}
		configMap[strs[0]] = strs[1]
	}

	return configMap, nil
}

// 如果diff就设置config
func redisSetDiffConfig(deployment *Cluster, ip string, port int, key string, val string) (bool, error) {
	client, err := redisc.Client(ip, port)
	if err != nil {
		logger.Error("failed to init redis cli, cluster=%s, redis=[%s:%d], error=(%v)", deployment.Name, ip, port, err)
		return false, err
	}
	defer client.Close()

	ctx, cancel := redisc.ReadTimeout()
	curVal, err := client.ConfigGet(ctx, key).Result()
	cancel()
	if err != nil {
		logger.Error("failed to exec [config get %s], cluster=%s, redis=[%s:%d], error=(%v)", key, deployment.Name, ip, port, err)
		return false, err
	}
	// 如果相同就跳过
	if len(curVal) == 2 && curVal[0] == key && curVal[1] == val {
		logger.Debug("redis config is the same, skip set, cluster=%s, redis=[%s:%d], key=%s, val=%s", deployment.Name, ip, port, key, val)
		return false, nil
	}

	// set
	ctx, cancel = redisc.WriteTimeout()
	err = client.ConfigSet(ctx, key, val).Err()
	cancel()
	if err != nil {
		logger.Error("failed to exec [config set %s %s], cluster=%s, redis=[%s:%d], error=(%v)", key, val, deployment.Name, ip, port, err)
		return false, err
	}
	logger.Info("succeed to exec [config set %s %s], cluster=%s, redis=[%s:%d]", key, val, deployment.Name, ip, port)

	return true, nil
}

// ! step6.redis配置设置，只针对Pod实例
func ensureRedisConfigs(deployment *Cluster, ctx *Context) error {
	configMap, err := configsToMap(deployment.Spec.Redis.CustomConfig)
	if err != nil {
		return err
	}

	// 配置项预处理：maxmemory安全检测，需要转换成int，不然比较大小会有问题
	configMaxmemory, _ := strconv.Atoi(configMap["maxmemory"])

	// 遍历pod，检查并更新配置
	port := deployment.Spec.Redis.Port
	for _, instance := range ctx.RedisInstances {
		info, err := redisc.Info(instance.IP, port)
		if err != nil {
			logger.Error("failed to get redis info, cluster=%s, redis=[%s:%d], error=(%v)", deployment.Name, instance.IP, port, err)
			return err
		}
		// 为方便比较大小先转换成int
		infoUsedMemory, _ := strconv.Atoi(info["used_memory"])
		infoMaxmemory, _ := strconv.Atoi(info["maxmemory"])
		infoReplBacklogSize, _ := strconv.Atoi(info["repl_backlog_size"])

		// 配置项预处理：开/关aof
		if info["role"] == omodel.REDIS_ROLE_MASTER {
			configMap["appendonly"] = "no"
		} else {
			configMap["appendonly"] = "yes"
		}

		rewrite := false
		for key, val := range configMap {
			switch key {
			// 配置项预处理：maxmemory设置前判断，避免引发内存淘汰
			case "maxmemory":
				if configMaxmemory == infoMaxmemory {
					continue
				}
				if configMaxmemory < infoUsedMemory {
					logger.Error("maxmemory is less than current used_memory, cannot set config, cluster=%s, redis=[%s:%d], maxmemory=%d, usedMemory=%d", deployment.Name, instance.IP, port, configMaxmemory, infoUsedMemory)
					continue
				}
			// 配置项预处理：repl-backlog-size设置前判断，避免引发内存淘汰
			case "repl-backlog-size":
				configReplBacklogSize, _ := strconv.Atoi(val)
				if configReplBacklogSize == infoReplBacklogSize {
					continue
				}
				if (infoUsedMemory - infoReplBacklogSize + configReplBacklogSize) >= configMaxmemory {
					logger.Error("maxmemory will be less than used_memory when the new repl-backlog-size config tasks effect, cluster=%s, redis=[%s:%d], maxmemory=%d, usedMemory=%d, replBacklogSize=%d", deployment.Name, instance.IP, port, configMaxmemory, infoUsedMemory, configReplBacklogSize)
					continue
				}
			case "appendonly":
				if (val == "no" && info["aof_enabled"] == "0") || (val == "yes" && info["aof_enabled"] == "1") {
					continue
				}
			}

			// 执行config set
			setOK, err := redisSetDiffConfig(deployment, instance.IP, port, key, val)
			if err != nil {
				return err
			}
			if setOK {
				rewrite = true
			}
		}

		// 覆写配置文件
		if rewrite {
			client, err := redisc.Client(instance.IP, port)
			if err != nil {
				logger.Error("failed to init redis cli, cluster=%s, redis=[%s:%d], error=(%v)", deployment.Name, instance.IP, port, err)
				return err
			}
			defer client.Close()

			ctx, cancel := redisc.WriteTimeout()
			err = client.ConfigRewrite(ctx).Err()
			cancel()
			if err != nil {
				logger.Error("failed to exec [config rewrite], cluster=%s, redis=[%s:%d], error=(%v)", deployment.Name, instance.IP, port, err)
				return err
			}
			logger.Info("failed to exec [config rewrite], cluster=%s, redis=[%s:%d]", deployment.Name, instance.IP, port)
		}
	}

	return nil
}

// =======================================
// 		STEP 7  Sentinel管理
// =======================================

// 修复sentinel的montior信息，返回同组的所有其他sentinel
// return alived, otherSentinels, error
func fixSentinelMontior(deployment *Cluster, ctx *Context, sentinelIp string, sentinelPort int) (bool, map[string]*omodel.Instance, error) {
	// 获取info，检查sentinel是否可访问，不可访问是预期内的一种情况，不返回error
	sentinelInfo, err := redisc.Info(sentinelIp, sentinelPort, redisc.SENTINEL)
	if err != nil {
		logger.Warn("failed to get sentinel info, cluster=%s, sentinel=[%s:%d], error=(%s)", deployment.Name, sentinelIp, sentinelPort, err)
		return false, nil, nil
	}

	otherSentinels := map[string]*omodel.Instance{}
	// 检查每个分片的monitor是否正确
	monitorMap := redisc.FormatMasters(sentinelInfo)
	for idx := 0; idx < deployment.Spec.Redis.NumOfShards; idx++ {
		name := fmt.Sprintf("%s-server%d", deployment.Name, idx+1)
		masterIp := ctx.ShardReplicaMap[name][0].IP
		masterPort := ctx.ShardReplicaMap[name][0].Port

		// 1、缺；新建的容器集群会走这条路径
		if _, ok := monitorMap[name]; !ok {
			err := redisc.SentinelMonitor(sentinelIp, sentinelPort, name, masterIp, masterPort, ctx.SentinelQuorum)
			if err != nil {
				logger.Error("failed to monitor master, cluster=%s, sentinel=[%s:%d], name=%s, master=[%s:%d], error=(%v)", deployment.Name, sentinelIp, sentinelPort, name, masterIp, masterPort, err)
				return true, otherSentinels, err
			}
			logger.Info("succeed to monitor master, cluster=%s, sentinel=[%s:%d], name=%s, master=[%s:%d]", deployment.Name, sentinelIp, sentinelPort, name, masterIp, masterPort)
			continue
		}

		// 2、错；sentinel指错了redis-server，理论上不会出现，如果出现了，remove后再monitor
		// 为避免非预期的级联，不用sentinel config set
		masterRight := false
		for _, replica := range ctx.ShardReplicaMap[name] {
			if monitorMap[name].IP == replica.IP && monitorMap[name].Port == replica.Port {
				masterRight = true
				delete(monitorMap, name)
				break
			}
		}
		if !masterRight {
			logger.Error("sentinel monitored wrong master, cluster=%s, sentinel=[%s:%d], name=%s, master=[%s:%d]", deployment.Name, sentinelIp, sentinelPort, name, monitorMap[name].IP, monitorMap[name].Port)
			err := redisc.SentinelRemove(sentinelIp, sentinelPort, name)
			if err != nil {
				logger.Error("failed to exec sentinel remove, cluster=%s, sentinel=[%s:%d], name=%s, error=(%v)", deployment.Name, sentinelIp, sentinelPort, name, err)
				return true, nil, err
			}
			logger.Info("succeed to exec sentinel remove, cluster=%s, sentinel=[%s:%d], name=%s", deployment.Name, sentinelIp, sentinelPort, name)

			err = redisc.SentinelMonitor(sentinelIp, sentinelPort, name, masterIp, masterPort, ctx.SentinelQuorum)
			if err != nil {
				logger.Error("failed to monitor master, cluster=%s, sentinel=[%s:%d], name=%s, master=[%s:%d], error=(%v)", deployment.Name, sentinelIp, sentinelPort, name, masterIp, masterPort, err)
				return true, nil, err
			}
			logger.Info("fix sentinel montior, change master to [%s:%d], cluster=%s, sentinel=[%s:%d], name=%s", masterIp, masterPort, deployment.Name, sentinelIp, sentinelPort, name)
			delete(monitorMap, name)
			continue
		}

		//! 执行sentinel sentinels <name>, 记录到otherSentinels中
		sentinels, err := redisc.SentinelSentinels(sentinelIp, sentinelPort, name)
		if err != nil {
			logger.Error("failed to exec sentinel sentinels, cluster=%s, sentinel=[%s:%d], name=%s, error=(%v)", deployment.Name, sentinelIp, sentinelPort, name, err)
			return true, nil, err
		}
		for _, sentinel := range sentinels {
			key := fmt.Sprintf("%s:%d", sentinel.IP, sentinel.Port)
			if _, exist := otherSentinels[key]; !exist {
				otherSentinels[key] = &omodel.Instance{IP: sentinel.IP, Port: sentinel.Port}
			}
		}
	}
	// 3、多；分组名不对
	if len(monitorMap) > 0 {
		for name := range monitorMap {
			logger.Error("sentinel got unexpected montior, try to remove, cluster=%s, sentinel=[%s:%d], name=%s, master=[%s:%d]", deployment.Name, sentinelIp, sentinelPort, name, monitorMap[name].IP, monitorMap[name].Port)
			err := redisc.SentinelRemove(sentinelIp, sentinelPort, name)
			if err != nil {
				logger.Error("failed to exec sentinel remove, cluster=%s, sentinel=[%s:%d], name=%s, error=(%v)", deployment.Name, sentinelIp, sentinelPort, name, err)
				return true, nil, err
			}
			logger.Info("succeed to exec sentinel remove, cluster=%s, sentinel=[%s:%d], name=%s", deployment.Name, sentinelIp, sentinelPort, name)
		}
	}

	return true, otherSentinels, nil
}

// 将sentinel从组中驱逐，失败不终止流程
func expelSentinel(deployment *Cluster, sentinelIp string, sentinelPort int) error {
	logger.Error("find sentinel should not in this cluster, cluster=%s, sentinel=[%s:%d]", deployment.Name, sentinelIp, sentinelPort)
	for idx := 0; idx < deployment.Spec.Redis.NumOfShards; idx++ {
		name := fmt.Sprintf("%s-server%d", deployment.Name, idx+1)
		err := redisc.SentinelRemove(sentinelIp, sentinelPort, name)
		if err != nil {
			logger.Warn("failed to exec [sentinel remove %s], cluster=%s, sentinel=[%s:%d], error=(%v)", name, deployment.Name, sentinelIp, sentinelPort, err)
			break
		}
		logger.Info("succeed to exec [sentinel remove %s], cluster=%s, sentinel=[%s:%d]", name, deployment.Name, sentinelIp, sentinelPort)
	}
	logger.Info("succeed to expel sentinel, cluster=%s, sentinel=[%s:%d]", deployment.Name, sentinelIp, sentinelPort)

	return nil
}

// ! step7. 检查sentinel，修复集群中所有sentinel的monitor信息
// 从pod入手，通过sentinel sentinels命令把同组的sentinel拉出来加入到遍历数组里
// 以bns实例为范围，不在bns中的ip:port统统移除出组
func EnsureSentinelMonitors(deployment *Cluster, ctx *Context) error {
	if len(ctx.SentinelInstances) == 0 {
		return nil
	}

	// 如果申请了新的sentinel，需要等待50s让白名单生效
	if ctx.ApplyNewPods[INDEX_NEW_SENTINEL] && !ctx.ApplyNewPods[INDEX_NEW_REDIS] {
		logger.Info("wait 50s for redis instances to refresh sentinel whitelist, cluster=%s", deployment.Name)
		time.Sleep(50 * time.Second)
	}

	// 从BNS中获取所有sentinel实例，集群的sentinel不能超过这个范围
	bnsInstanceList, err := noah.GetInstancesV2(deployment.Spec.App.ProductLine, common.GetSentinelAppName(deployment.Spec.App.AppPrefix))
	if err != nil {
		logger.Error("failed to get instances from bns, cluster=%s, bns=%s.%s, error=(%v)", deployment.Name, common.GetSentinelAppName(deployment.Spec.App.AppPrefix), deployment.Spec.App.ProductLine, err)
		return err
	}
	// ctrlMap有两个作用：（1）避免sentinel重复修复（2）判断sentinel是否超出了BNS范围
	ctrlMap := map[string]bool{}
	for _, instance := range bnsInstanceList {
		ctrlMap[fmt.Sprintf("%s:%d", instance.IP, instance.PortInfo.Main)] = false
	}

	// 初始化sentinel chan，默认pods都在bns里
	chanSentinels := make(chan *omodel.Instance, 10)
	for _, instance := range ctx.SentinelInstances {
		ctrlMap[fmt.Sprintf("%s:%d", instance.IP, deployment.Spec.Sentinel.Port)] = true
		chanSentinels <- instance
	}
	// 修复每个查询出的sentinel
	for sentinel := range chanSentinels {
		alived, otherSentinels, err := fixSentinelMontior(deployment, ctx, sentinel.IP, sentinel.Port)
		if err != nil {
			logger.Error("failed to fix sentinel monitor, cluster=%s, sentinel=[%s:%d], error=(%v)", deployment.Name, sentinel.IP, sentinel.Port, err)
			return err
		}
		if alived {
			ctx.OnlineSentinels = append(ctx.OnlineSentinels, sentinel)
			// 查出的新sentinel也走一遍修复
			for key, otherSentinel := range otherSentinels {
				done, exist := ctrlMap[key]
				// 不在bns里，说明不是本集群的sentinel，需要remove
				if !exist {
					ctrlMap[key] = true // 避免重复remove，也记下来
					err := expelSentinel(deployment, otherSentinel.IP, otherSentinel.Port)
					if err != nil {
						logger.Error("failed to expel sentinel, cluster=%s, sentinel=[%s:%d], error=(%v)", deployment.Name, otherSentinel.IP, otherSentinel.Port, err)
						return err
					}
				}
				// 存在但没chan<-的push
				if exist && !done {
					ctrlMap[key] = true
					chanSentinels <- otherSentinel
				}
			}
		}

		// 直到没有新的sentinel，关闭chan结束循环
		if len(chanSentinels) == 0 {
			close(chanSentinels)
		}
	}

	return nil
}

// ===================================================
// 		STEP 8  Sentinel数量检查
//
// 	实际可用sentinel < quorum，故障转移失效
// 	实际可用sentinel >= quorum*2，有脑裂风险
// 	sentinels总数大于实际可用sentinel，有故障转移失效风险
// 	sentinels总数小于实际可用sentinel，有风险
// 	sentinels总数是偶数，有故障转移失效风险
// ===================================================

// ! step8. 检查sentinel数量
func CheckSentinelNumbers(deployment *Cluster, ctx *Context) error {
	if len(ctx.OnlineSentinels) < ctx.SentinelQuorum {
		logger.Warn("not enough sentinels to mark odown, cluster=%s, quorum=%d, onlineSentinelNum=%d", deployment.Name, ctx.SentinelQuorum, len(ctx.OnlineSentinels))
	}
	if len(ctx.OnlineSentinels) >= ctx.SentinelQuorum*2 {
		logger.Warn("there are too many sentinels in cluster, may have a risk of split-brain, cluster=%s, quorum=%d, onlineSentinelNum=%d", deployment.Name, ctx.SentinelQuorum, len(ctx.OnlineSentinels))
	}

	for _, sentinel := range ctx.OnlineSentinels {
		// 访问不通跳过，可能是刚好宕机了，依然沿用len(ctx.OnlineSentinels)比对其他的sentinel
		info, err := redisc.Info(sentinel.IP, sentinel.Port, redisc.SENTINEL)
		if err != nil {
			logger.Warn("failed to get sentinel info, skip ckquorum, cluster=%s, sentinel=[%s:%d], error=(%v)", deployment.Name, sentinel.IP, sentinel.Port, err)
			continue
		}

		// 经过上一阶段的修复，monitor的信息理论上都是正确的
		monitorMap := redisc.FormatMasters(info)
		for idx := 0; idx < deployment.Spec.Redis.NumOfShards; idx++ {
			name := fmt.Sprintf("%s-server%d", deployment.Name, idx+1)
			//! 不应该缺了，还缺就得人工介入了
			if _, ok := monitorMap[name]; !ok {
				logger.Error("missing sentinel monitor, cluster=%s, sentinel=[%s:%d], name=%s", deployment.Name, sentinel.IP, sentinel.Port, name)
				continue
			}

			// ckquorum
			if err := redisc.SentinelCkquorum(sentinel.IP, sentinel.Port, name); err != nil {
				logger.Error("failed to ckquorum, cluster=%s, sentinel=[%s:%d], name=%s, error=(%v)", deployment.Name, sentinel.IP, sentinel.Port, name, err)
			}

			//! 数量少，不正常，需要人工介入排查问题
			if monitorMap[name].SentinelNum < len(ctx.OnlineSentinels) && monitorMap[name].SentinelNum != 0 {
				logger.Error("there are fewer sentinels in the same group than expected, cluster=%s, sentinel=[%s:%d], name=%s, count=%d", deployment.Name, sentinel.IP, sentinel.Port, name, monitorMap[name].SentinelNum)
				continue
			}

			// 数量多，需要reset一下
			if monitorMap[name].SentinelNum > len(ctx.OnlineSentinels) {
				err := redisc.SentinelReset(sentinel.IP, sentinel.Port, name)
				if err != nil {
					logger.Error("failed to reset sentinel monitor, cluster=%s, sentinel=[%s:%d], name=%s, error=(%v)", deployment.Name, sentinel.IP, sentinel.Port, name, err)
				}
				logger.Info("succeed to reset sentinel monitor, cluster=%s, sentinel=[%s:%d], name=%s", deployment.Name, sentinel.IP, sentinel.Port, name)
				continue
			}
		}
	}

	return nil
}

// =======================================
// 		STEP 9  Sentinel可用性检查
// =======================================

// ! step9. sentinel可用性检查
func checkSentinelAvailability(deployment *Cluster, ctx *Context) error {
	// 验证app存在可用实例，设置2s超时，10s重试，sentinel不可用的话，proxy也启动不起来
	address := fmt.Sprintf("%s.%s.serv:%d", common.GetSentinelAppName(deployment.Alias), deployment.Spec.App.ProductLine, deployment.Spec.Sentinel.Port)
	loop := 3
	for {
		_, err := net.DialTimeout("tcp", address, common.DIAL_TIMEOUT)
		// 没报错，进入下一阶段
		if err == nil {
			return nil
		}
		// 报错超过3次，中断流程
		if loop <= 0 {
			return fmt.Errorf("sentinel is not accessible, cluster=%s, address=[%v]", deployment.Name, address)
		}
		// 打印日志，等待10s重试
		logger.Warn("failed to dial %s, retryRemain=%d, cluster=%s, error=(%v)", address, loop, deployment.Name, err)
		time.Sleep(10 * time.Second)
		loop -= 1
	}
}

// =======================================
// 		STEP 13  Proxy可用性检查
// =======================================

// ! step13.检查router状态是否正确
func checkRouterAvailability(deployment *Cluster, _ *Context) error {
	// 验证app存在可用实例，设置2s超时，20s重试
	address := fmt.Sprintf("%s.%s.serv:%d", common.GetRouterAppName(deployment.Alias), deployment.Spec.App.ProductLine, deployment.Spec.Router.Port)
	loop := 3
	for {
		_, err := net.DialTimeout("tcp", address, common.DIAL_TIMEOUT)
		// 没报错，进入下一阶段
		if err == nil {
			return nil
		}

		switch loop {
		case 3:
			// 检查app中是否存在非屏蔽实例
			productLine := deployment.Spec.App.ProductLine
			app := common.GetRouterAppName(deployment.Spec.App.AppPrefix)
			// 获取APP实例
			instanceList, err := noah.GetInstancesV2(productLine, app)
			if err != nil {
				logger.Error("failed to get instances, cluster=%s, bns=%s.%s, error=(%v)", deployment.Name, app, productLine, err)
				return err
			}

			// 检查是否存在生效中的实例
			isAllProxyDisabled := true
			for _, instance := range instanceList {
				if !instance.Disable {
					isAllProxyDisabled = false
					break
				}
			}
			// 如果都屏蔽着就不用再检查了
			if isAllProxyDisabled {
				logger.Debug("proxy instances all disbaled, cluster=%s, bns=%s.%s", deployment.Name, app, productLine)
				return nil
			}
		case 0:
			// 报错超过3次，中断流程
			return fmt.Errorf("proxy is not accessible, cluster=%s, address=[%v]", deployment.Name, address)
		}

		// 打印日志，等待10s重试
		logger.Warn("failed to dial %s, retryRemain=%d, cluster=%s, error=(%v)", address, loop, deployment.Name, err)
		time.Sleep(10 * time.Second)
		loop -= 1
	}
}

// =======================================
//		   	日志、其他
// =======================================

// 巡检结果转换为可读信息
func ResultInHuman(result *InspectionResult) string {
	if len(result.StepProgress) != TOTAL_STEP {
		return fmt.Sprintf("阶段总数与预期不符, 进度条只有%d个阶段, 预期为%d个阶段", len(result.StepProgress), TOTAL_STEP)
	}

	for i, r := range result.StepProgress {
		stepName := stepMap[i].Name
		switch r {
		case STEP_COMPLETED: // 已完成跳过
		case STEP_INCOMPLETE:
			str := fmt.Sprintf("阶段: %s 运行中", stepName)
			if result.StepErrTimes[i] != 0 {
				str += fmt.Sprintf(", 已重试%d次, 上一次失败原因: %s", result.StepErrTimes[i], result.ErrMsg)
			}
			return str
		case STEP_ABNORMAL:
			return fmt.Sprintf("阶段: %s 执行失败, 失败原因: %s, 剩余重试次数: %d次", stepName, result.ErrMsg, 5-result.StepErrTimes[i])
		}
	}

	return "渲染阶段已全部完成"
}

// 获取渲染进度
func GetInspectionProgress(clusterName string) (*InspectionResult, error) {
	value, isLoaded := renderLock.Load(clusterName)
	if !isLoaded {
		return nil, fmt.Errorf("rendering of cluster %s is not running", clusterName)
	}

	return value.(*InspectionResult), nil
}
