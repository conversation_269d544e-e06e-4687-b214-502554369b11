package renderer

import (
	"context"
	"encoding/json"
	"net/http"
	"regexp"
	"testing"

	"github.com/jarcoal/httpmock"

	"dt-common/ent/proxy"
	"dt-common/ent/redis"
	"dt-common/ent/sentinel"
	"dt-common/mysql"
	"redis-cmanager/env"
	"redis-cmanager/library/ragent"
)

func TestEnsureWhitelists(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")
	objC := env.MockCluster("whitelist_test")
	db, _ := mysql.Database()
	// 初始化redis实例
	db.Redis.Delete().Where(redis.ClusterName(objC.Name)).Exec(context.Background())
	db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("whitelist-test-redis.siod-redis").SetName("whitelist_test-server1").SetIP("*********").SetPort(7000).SetIdc("hbb").SetRole("slave").ExecX(context.Background())
	db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("whitelist-test-redis.siod-redis").SetName("whitelist_test-server2").SetIP("*********").SetPort(7000).SetIdc("hbb").SetRole("slave").ExecX(context.Background())
	db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetTaint(true).SetBns("whitelist-test-redis.siod-redis").SetName("whitelist_test-server2").SetIP("*********").SetPort(7000).SetIdc("hbb").SetRole("slave").ExecX(context.Background())
	// 初始化sentinel实例
	db.Sentinel.Delete().Where(sentinel.ClusterName(objC.Name)).Exec(context.Background())
	db.Sentinel.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("whitelist-test-sentinel.siod-redis").SetIP("*********").SetPort(9001).SetIdc("hbb").ExecX(context.Background())
	// 初始化proxy实例
	db.Proxy.Delete().Where(proxy.ClusterName(objC.Name)).Exec(context.Background())
	db.Proxy.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("whitelist-test-router.siod-redis").SetIP("*********1").SetPort(8001).SetIdc("hbb").ExecX(context.Background())
	db.Proxy.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("whitelist-test-router.siod-redis").SetIP("*********2").SetPort(8001).SetIdc("hbb").ExecX(context.Background())
	// 初始化白名单
	db.Whitelist.Delete().Exec(context.Background())
	db.Whitelist.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetType("bns").SetValue("ajtt.loan-risk").SetPrivilege("rw").Save(context.Background())
	// 初始化管理白名单
	db.Configuration.Delete().Exec(context.Background())
	db.Configuration.Create().SetName("management_bns").SetValue("redis-manager.siod-redis").Exec(context.Background())

	httpmock.Activate()
	defer httpmock.Deactivate()

	type args struct {
		deployment *Cluster
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
	}{
		{
			name: "success",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`http://10.85.0.[\w\W]+/whitelist/update`),
					func(req *http.Request) (*http.Response, error) {
						var params ragent.UpdateParams
						json.NewDecoder(req.Body).Decode(&params)
						for _, v := range params.BnsList {
							if v == "r3-test-uniq.siod-redis rw" {
								return httpmock.NewStringResponse(200, `{"code": "00000", "data": "mockok"}`), nil
							}
						}
						return httpmock.NewStringResponse(500, `{"code": "S0001", "data": ""}`), nil
					})

				httpmock.RegisterResponder("POST", "http://*********:8433/whitelist/update",
					func(req *http.Request) (*http.Response, error) {
						var params ragent.UpdateParams
						json.NewDecoder(req.Body).Decode(&params)
						for _, v := range params.BnsList {
							if v == "r3-test-uniq.siod-redis x" {
								return httpmock.NewStringResponse(200, `{"code": "00000", "data": "mockok"}`), nil
							}
						}
						return httpmock.NewStringResponse(500, `{"code": "S0001", "data": ""}`), nil
					})
			},
			args: args{
				deployment: &Cluster{
					ID:    objC.ID,
					Name:  "whitelist_test",
					Alias: objC.Name,
					Spec: &Spec{
						ClusterWhitelist: []string{"r3-test-uniq.siod-redis"},
						Redis:            &RedisSettings{Port: 7000},
						Sentinel:         &SentinelSettings{Port: 9001},
						Router:           &RouterSettings{Port: 8001},
						App: &AppSettings{
							ProductLine: "siod-redis",
							AppPrefix:   "whitelist-test",
						},
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := EnsureWhitelists(tt.args.deployment)
			if (err != nil) != tt.wantErr {
				t.Errorf("EnsureWhitelists() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

// 单测：IsThereTaintedProxy
func TestIsThereTaintedProxy(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")

	httpmock.Activate()
	defer httpmock.Deactivate()

	type args struct {
		deployment *Cluster
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, bool)
	}{
		{
			name: "true",
			before: func() {
				httpmock.RegisterResponder("POST", "http://*************:8536/fec-manager/api/v1/fec/listPod",
					httpmock.NewStringResponder(200, `{"errno": 0,"errmsg": "","data": [
						{"id": "","podName": "p-test-az","productName": "siod-redis","appName": "test-router","podIp": "*********","idc": "BDDX","comboDesc": "","safeRegion": "test_internal","owner": "jiayiming_dxm","applicant": "","status": "Running","labels": {"avoid_delete_by_accident": "true","dxm-redis/component": "router","dxm-redis/name": "p-test","dxm-redis/taint": "false","fec2/app_name": "p-test-router","fec2/bnsMount": "false","fec2/budget_execution_id": "","fec2/budget_number": "","fec2/buget-checked": "pass","fec2/cni-safe-region": "test_internal","fec2/combo_code": "567756428","fec2/device-id": "fec-PMOYhZDwP","fec2/disable-inject-sidecar": "false","fec2/fec-id": "fid-XxqWgKwoo","fec2/idc": "BDDX","fec2/over_budget": "true","fec2/owner": "jiayiming_dxm","fec2/product_name": "siod-kafka","fec2/retain": "true","fec2/saas": "redis","fec2/service_level": "","fec2/version": "v0.0.2","fec2/white": "false"},"hostname": "p-test-az.fec.kj01.bddx.dxm-int.com","scheduleLimit": null,"containerInfo": [{"containerName": "r-r3-rd-test-0","imageName": "r.duxiaoman-int.com/siod_redis/redis-docker","imageVersion": "v2024072402","imagePullPolicy": "IfNotPresent","cpu": 1,"mem": 1,"port": null,"entryCommand": "","execArgs": null,"envArgs": {"CLOUD_TYPE": "kj","CONTAINER_NAME": "r-r3-rd-test-0","CONTAINER_TYPE": "main","DEVICE_TYPE": "pod","FECID": "fid-XxqWgKwoo","FEC_OPERATOR_URL": "https://************:11443/fec2","HAWKING_ENV": "container","INIT_NOAH_USER": "redis","MAX_MEMORY_POLICY": "volatile-lru","NODE_IP": "","POD_IP": "","POD_NAME": "","REDIS_PORT": "7000"},"diskApplication": 0,"mountType": "","volumeMounts": null,"status": "Running","privileged": false}]},
						{"id": "","podName": "p-test-gh","productName": "siod-redis","appName": "test-router","podIp": "*********","idc": "BDDX","comboDesc": "","safeRegion": "test_internal","owner": "jiayiming_dxm","applicant": "","status": "Running","labels": {"avoid_delete_by_accident": "true","dxm-redis/component": "router","dxm-redis/name": "p-test","fec2/app_name": "p-test-router","fec2/bnsMount": "false","fec2/budget_execution_id": "","fec2/budget_number": "","fec2/buget-checked": "pass","fec2/cni-safe-region": "test_internal","fec2/combo_code": "567756428","fec2/device-id": "fec-PMOYhZDwP","fec2/disable-inject-sidecar": "false","fec2/fec-id": "fid-XxqWgKwoo","fec2/idc": "BDDX","fec2/over_budget": "true","fec2/owner": "jiayiming_dxm","fec2/product_name": "siod-kafka","fec2/retain": "true","fec2/saas": "redis","fec2/service_level": "","fec2/version": "v0.0.2","fec2/white": "false"},"hostname": "p-test-gh.fec.kj01.bddx.dxm-int.com","scheduleLimit": null,"containerInfo": [{"containerName": "r-r3-rd-test-0","imageName": "r.duxiaoman-int.com/siod_redis/redis-docker","imageVersion": "v2024072402","imagePullPolicy": "IfNotPresent","cpu": 1,"mem": 1,"port": null,"entryCommand": "","execArgs": null,"envArgs": {"CLOUD_TYPE": "kj","CONTAINER_NAME": "r-r3-rd-test-0","CONTAINER_TYPE": "main","DEVICE_TYPE": "pod","FECID": "fid-XxqWgKwoo","FEC_OPERATOR_URL": "https://************:11443/fec2","HAWKING_ENV": "container","INIT_NOAH_USER": "redis","MAX_MEMORY_POLICY": "volatile-lru","NODE_IP": "","POD_IP": "","POD_NAME": "","REDIS_PORT": "7000"},"diskApplication": 0,"mountType": "","volumeMounts": null,"status": "Running","privileged": false}]},
						{"id": "","podName": "p-test-sc","productName": "siod-redis","appName": "test-router","podIp": "*********","idc": "ZZJG","comboDesc": "","safeRegion": "test_internal","owner": "jiayiming_dxm","applicant": "","status": "Running","labels": {"avoid_delete_by_accident": "true","dxm-redis/component": "router","dxm-redis/name": "p-test","dxm-redis/taint": "false","fec2/app_name": "p-test-router","fec2/bnsMount": "false","fec2/budget_execution_id": "","fec2/budget_number": "","fec2/buget-checked": "pass","fec2/cni-safe-region": "test_internal","fec2/combo_code": "567756428","fec2/device-id": "fec-PMOYhZDwP","fec2/disable-inject-sidecar": "false","fec2/fec-id": "fid-XxqWgKwoo","fec2/idc": "BDDX","fec2/over_budget": "true","fec2/owner": "jiayiming_dxm","fec2/product_name": "siod-kafka","fec2/retain": "true","fec2/saas": "redis","fec2/service_level": "","fec2/version": "v0.0.2","fec2/white": "false"},"hostname": "p-test-sc.fec.kj01.bddx.dxm-int.com","scheduleLimit": null,"containerInfo": [{"containerName": "r-r3-rd-test-0","imageName": "r.duxiaoman-int.com/siod_redis/redis-docker","imageVersion": "v2024072402","imagePullPolicy": "IfNotPresent","cpu": 1,"mem": 1,"port": null,"entryCommand": "","execArgs": null,"envArgs": {"CLOUD_TYPE": "kj","CONTAINER_NAME": "r-r3-rd-test-0","CONTAINER_TYPE": "main","DEVICE_TYPE": "pod","FECID": "fid-XxqWgKwoo","FEC_OPERATOR_URL": "https://************:11443/fec2","HAWKING_ENV": "container","INIT_NOAH_USER": "redis","MAX_MEMORY_POLICY": "volatile-lru","NODE_IP": "","POD_IP": "","POD_NAME": "","REDIS_PORT": "7000"},"diskApplication": 0,"mountType": "","volumeMounts": null,"status": "Running","privileged": false}]},
						{"id": "","podName": "p-test-d4","productName": "siod-redis","appName": "test-router","podIp": "*********","idc": "ZZJG","comboDesc": "","safeRegion": "test_internal","owner": "jiayiming_dxm","applicant": "","status": "Running","labels": {"avoid_delete_by_accident": "true","dxm-redis/component": "router","dxm-redis/name": "p-test","dxm-redis/taint": "true","fec2/app_name": "p-test-router","fec2/bnsMount": "false","fec2/budget_execution_id": "","fec2/budget_number": "","fec2/buget-checked": "pass","fec2/cni-safe-region": "test_internal","fec2/combo_code": "567756428","fec2/device-id": "fec-PMOYhZDwP","fec2/disable-inject-sidecar": "false","fec2/fec-id": "fid-XxqWgKwoo","fec2/idc": "BDDX","fec2/over_budget": "true","fec2/owner": "jiayiming_dxm","fec2/product_name": "siod-kafka","fec2/retain": "true","fec2/saas": "redis","fec2/service_level": "","fec2/version": "v0.0.2","fec2/white": "false"},"hostname": "p-test-d4.fec.kj01.bddx.dxm-int.com","scheduleLimit": null,"containerInfo": [{"containerName": "r-r3-rd-test-0","imageName": "r.duxiaoman-int.com/siod_redis/redis-docker","imageVersion": "v2024072402","imagePullPolicy": "IfNotPresent","cpu": 1,"mem": 1,"port": null,"entryCommand": "","execArgs": null,"envArgs": {"CLOUD_TYPE": "kj","CONTAINER_NAME": "r-r3-rd-test-0","CONTAINER_TYPE": "main","DEVICE_TYPE": "pod","FECID": "fid-XxqWgKwoo","FEC_OPERATOR_URL": "https://************:11443/fec2","HAWKING_ENV": "container","INIT_NOAH_USER": "redis","MAX_MEMORY_POLICY": "volatile-lru","NODE_IP": "","POD_IP": "","POD_NAME": "","REDIS_PORT": "7000"},"diskApplication": 0,"mountType": "","volumeMounts": null,"status": "Running","privileged": false}]}
					]}`),
				)
			},
			args: args{
				deployment: &Cluster{Name: "test"},
			},
			wantErr: false,
			expect: func(t *testing.T, b bool) {
				if !b {
					t.Errorf("expect true, but got %v", b)
				}
			},
		},
		{
			name: "false",
			before: func() {
				httpmock.RegisterResponder("POST", "http://*************:8536/fec-manager/api/v1/fec/listPod",
					httpmock.NewStringResponder(200, `{"errno": 0,"errmsg": "","data": [
						{"id": "","podName": "p-test-az","productName": "siod-redis","appName": "test-router","podIp": "*********","idc": "BDDX","comboDesc": "","safeRegion": "test_internal","owner": "jiayiming_dxm","applicant": "","status": "Running","labels": {"avoid_delete_by_accident": "true","dxm-redis/component": "router","dxm-redis/name": "p-test","fec2/app_name": "p-test-router","fec2/bnsMount": "false","fec2/budget_execution_id": "","fec2/budget_number": "","fec2/buget-checked": "pass","fec2/cni-safe-region": "test_internal","fec2/combo_code": "567756428","fec2/device-id": "fec-PMOYhZDwP","fec2/disable-inject-sidecar": "false","fec2/fec-id": "fid-XxqWgKwoo","fec2/idc": "BDDX","fec2/over_budget": "true","fec2/owner": "jiayiming_dxm","fec2/product_name": "siod-kafka","fec2/retain": "true","fec2/saas": "redis","fec2/service_level": "","fec2/version": "v0.0.2","fec2/white": "false"},"hostname": "p-test-az.fec.kj01.bddx.dxm-int.com","scheduleLimit": null,"containerInfo": [{"containerName": "r-r3-rd-test-0","imageName": "r.duxiaoman-int.com/siod_redis/redis-docker","imageVersion": "v2024072402","imagePullPolicy": "IfNotPresent","cpu": 1,"mem": 1,"port": null,"entryCommand": "","execArgs": null,"envArgs": {"CLOUD_TYPE": "kj","CONTAINER_NAME": "r-r3-rd-test-0","CONTAINER_TYPE": "main","DEVICE_TYPE": "pod","FECID": "fid-XxqWgKwoo","FEC_OPERATOR_URL": "https://************:11443/fec2","HAWKING_ENV": "container","INIT_NOAH_USER": "redis","MAX_MEMORY_POLICY": "volatile-lru","NODE_IP": "","POD_IP": "","POD_NAME": "","REDIS_PORT": "7000"},"diskApplication": 0,"mountType": "","volumeMounts": null,"status": "Running","privileged": false}]},
						{"id": "","podName": "p-test-gh","productName": "siod-redis","appName": "test-router","podIp": "*********","idc": "BDDX","comboDesc": "","safeRegion": "test_internal","owner": "jiayiming_dxm","applicant": "","status": "Running","labels": {"avoid_delete_by_accident": "true","dxm-redis/component": "router","dxm-redis/name": "p-test","fec2/app_name": "p-test-router","fec2/bnsMount": "false","fec2/budget_execution_id": "","fec2/budget_number": "","fec2/buget-checked": "pass","fec2/cni-safe-region": "test_internal","fec2/combo_code": "567756428","fec2/device-id": "fec-PMOYhZDwP","fec2/disable-inject-sidecar": "false","fec2/fec-id": "fid-XxqWgKwoo","fec2/idc": "BDDX","fec2/over_budget": "true","fec2/owner": "jiayiming_dxm","fec2/product_name": "siod-kafka","fec2/retain": "true","fec2/saas": "redis","fec2/service_level": "","fec2/version": "v0.0.2","fec2/white": "false"},"hostname": "p-test-gh.fec.kj01.bddx.dxm-int.com","scheduleLimit": null,"containerInfo": [{"containerName": "r-r3-rd-test-0","imageName": "r.duxiaoman-int.com/siod_redis/redis-docker","imageVersion": "v2024072402","imagePullPolicy": "IfNotPresent","cpu": 1,"mem": 1,"port": null,"entryCommand": "","execArgs": null,"envArgs": {"CLOUD_TYPE": "kj","CONTAINER_NAME": "r-r3-rd-test-0","CONTAINER_TYPE": "main","DEVICE_TYPE": "pod","FECID": "fid-XxqWgKwoo","FEC_OPERATOR_URL": "https://************:11443/fec2","HAWKING_ENV": "container","INIT_NOAH_USER": "redis","MAX_MEMORY_POLICY": "volatile-lru","NODE_IP": "","POD_IP": "","POD_NAME": "","REDIS_PORT": "7000"},"diskApplication": 0,"mountType": "","volumeMounts": null,"status": "Running","privileged": false}]},
						{"id": "","podName": "p-test-sc","productName": "siod-redis","appName": "test-router","podIp": "*********","idc": "ZZJG","comboDesc": "","safeRegion": "test_internal","owner": "jiayiming_dxm","applicant": "","status": "Running","labels": {"avoid_delete_by_accident": "true","dxm-redis/component": "router","dxm-redis/name": "p-test","fec2/app_name": "p-test-router","fec2/bnsMount": "false","fec2/budget_execution_id": "","fec2/budget_number": "","fec2/buget-checked": "pass","fec2/cni-safe-region": "test_internal","fec2/combo_code": "567756428","fec2/device-id": "fec-PMOYhZDwP","fec2/disable-inject-sidecar": "false","fec2/fec-id": "fid-XxqWgKwoo","fec2/idc": "BDDX","fec2/over_budget": "true","fec2/owner": "jiayiming_dxm","fec2/product_name": "siod-kafka","fec2/retain": "true","fec2/saas": "redis","fec2/service_level": "","fec2/version": "v0.0.2","fec2/white": "false"},"hostname": "p-test-sc.fec.kj01.bddx.dxm-int.com","scheduleLimit": null,"containerInfo": [{"containerName": "r-r3-rd-test-0","imageName": "r.duxiaoman-int.com/siod_redis/redis-docker","imageVersion": "v2024072402","imagePullPolicy": "IfNotPresent","cpu": 1,"mem": 1,"port": null,"entryCommand": "","execArgs": null,"envArgs": {"CLOUD_TYPE": "kj","CONTAINER_NAME": "r-r3-rd-test-0","CONTAINER_TYPE": "main","DEVICE_TYPE": "pod","FECID": "fid-XxqWgKwoo","FEC_OPERATOR_URL": "https://************:11443/fec2","HAWKING_ENV": "container","INIT_NOAH_USER": "redis","MAX_MEMORY_POLICY": "volatile-lru","NODE_IP": "","POD_IP": "","POD_NAME": "","REDIS_PORT": "7000"},"diskApplication": 0,"mountType": "","volumeMounts": null,"status": "Running","privileged": false}]},
						{"id": "","podName": "p-test-d4","productName": "siod-redis","appName": "test-router","podIp": "*********","idc": "ZZJG","comboDesc": "","safeRegion": "test_internal","owner": "jiayiming_dxm","applicant": "","status": "Running","labels": {"avoid_delete_by_accident": "true","dxm-redis/component": "router","dxm-redis/name": "p-test","fec2/app_name": "p-test-router","fec2/bnsMount": "false","fec2/budget_execution_id": "","fec2/budget_number": "","fec2/buget-checked": "pass","fec2/cni-safe-region": "test_internal","fec2/combo_code": "567756428","fec2/device-id": "fec-PMOYhZDwP","fec2/disable-inject-sidecar": "false","fec2/fec-id": "fid-XxqWgKwoo","fec2/idc": "BDDX","fec2/over_budget": "true","fec2/owner": "jiayiming_dxm","fec2/product_name": "siod-kafka","fec2/retain": "true","fec2/saas": "redis","fec2/service_level": "","fec2/version": "v0.0.2","fec2/white": "false"},"hostname": "p-test-d4.fec.kj01.bddx.dxm-int.com","scheduleLimit": null,"containerInfo": [{"containerName": "r-r3-rd-test-0","imageName": "r.duxiaoman-int.com/siod_redis/redis-docker","imageVersion": "v2024072402","imagePullPolicy": "IfNotPresent","cpu": 1,"mem": 1,"port": null,"entryCommand": "","execArgs": null,"envArgs": {"CLOUD_TYPE": "kj","CONTAINER_NAME": "r-r3-rd-test-0","CONTAINER_TYPE": "main","DEVICE_TYPE": "pod","FECID": "fid-XxqWgKwoo","FEC_OPERATOR_URL": "https://************:11443/fec2","HAWKING_ENV": "container","INIT_NOAH_USER": "redis","MAX_MEMORY_POLICY": "volatile-lru","NODE_IP": "","POD_IP": "","POD_NAME": "","REDIS_PORT": "7000"},"diskApplication": 0,"mountType": "","volumeMounts": null,"status": "Running","privileged": false}]}
					]}`),
				)
			},
			args: args{
				deployment: &Cluster{Name: "test"},
			},
			wantErr: false,
			expect: func(t *testing.T, b bool) {
				if b {
					t.Errorf("expect false, but got %v", b)
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			b, err := IsThereTaintedProxy(tt.args.deployment)
			if (err != nil) != tt.wantErr {
				t.Errorf("IsThereTaintedProxy() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, b)
			}
		})
	}
}
