package lib

import (
	"fmt"
	"strconv"

	"dt-common/ent"
	"dt-common/ent/predicate"
	"dt-common/ent/proxy"
	"dt-common/ent/redis"
	"dt-common/ent/sentinel"
	"dt-common/logger"
	"dt-common/mysql"
	"dt-common/omodel"
	"redis-cmanager/library/fec"
	"redis-cmanager/library/renderer/common"
)

// ==============================
// 			 Insert
// ==============================

// 批量创建redis记录
func BulkCreateRedis(clusterID int64, clusterName, bns string, port int, podList []*fec.PodInfo) error {
	db, err := mysql.Database()
	if err != nil {
		logger.Warn("failed to get mysql connection, error=(%v)", err)
		return err
	}

	// 先查询
	ctx, cancel := mysql.ContextWithTimeout()
	redisList, err := db.Redis.Query().Select(redis.FieldIP, redis.FieldPort).
		Where(redis.ClusterName(clusterName), redis.Docker(omodel.DEPLOY_ENV_DOCKER)).
		All(ctx)
	cancel()
	if err != nil {
		logger.Warn("failed to query redis, error=(%v)", err)
		return err
	}
	// 转map
	redisMap := map[string]*ent.Redis{}
	for _, row := range redisList {
		k := fmt.Sprintf("%s:%d", row.IP, row.Port)
		redisMap[k] = row
	}

	// diff
	var bulkCreate []*ent.RedisCreate
	for _, pod := range podList {
		k := fmt.Sprintf("%s:%d", pod.PodIp, port)
		if _, exist := redisMap[k]; exist {
			continue
		}

		iStr, ok := pod.Labels[common.LABEL_INDEX]
		if !ok {
			logger.Warn("pod %v has no shard label", pod.PodName)
			continue
		}
		index, err := strconv.Atoi(iStr)
		if err != nil {
			return fmt.Errorf("failed to parse indexString to int, error=(%v)", err)
		}
		shardName := fmt.Sprintf("%s-server%d", clusterName, index+1)

		bulkCreate = append(bulkCreate, db.Redis.Create().
			SetClusterID(clusterID).SetClusterName(clusterName).
			SetBns(bns).SetIP(pod.PodIp).SetPort(port).
			SetIdc(pod.Labels[common.LABEL_IDC]).SetName(shardName).SetRole(omodel.REDIS_ROLE_SLAVE).
			SetMaxmemory(0).SetUsedMemory(0).SetDocker(omodel.DEPLOY_ENV_DOCKER).
			SetStatus(omodel.INSTANCE_STATUS_NORMAL))
	}
	ctx, cancel = mysql.ContextWithTimeout()
	err = db.Redis.CreateBulk(bulkCreate...).Exec(ctx)
	cancel()
	if err != nil {
		return err
	}

	return nil
}

// 批量创建sentinel记录
func BulkCreateSentinel(clusterID int64, clusterName, bns string, port int, podList []*fec.PodInfo) error {
	db, err := mysql.Database()
	if err != nil {
		logger.Warn("failed to get mysql connection, error=(%v)", err)
		return err
	}

	// 先查询
	ctx, cancel := mysql.ContextWithTimeout()
	sentinelList, err := db.Sentinel.Query().Where(sentinel.ClusterName(clusterName), sentinel.Docker(omodel.DEPLOY_ENV_DOCKER)).All(ctx)
	cancel()
	if err != nil {
		logger.Warn("failed to query sentinel, error=(%v)", err)
		return err
	}
	// 转map
	sentinelMap := map[string]*ent.Sentinel{}
	for _, row := range sentinelList {
		k := fmt.Sprintf("%s:%d", row.IP, row.Port)
		sentinelMap[k] = row
	}

	// diff
	var bulkCreate []*ent.SentinelCreate
	for _, pod := range podList {
		k := fmt.Sprintf("%s:%d", pod.PodIp, port)
		if _, exist := sentinelMap[k]; exist {
			continue
		}

		bulkCreate = append(bulkCreate, db.Sentinel.Create().
			SetClusterID(clusterID).SetClusterName(clusterName).
			SetBns(bns).SetIP(pod.PodIp).SetIdc(pod.Labels[common.LABEL_IDC]).
			SetPort(port).SetDocker(omodel.DEPLOY_ENV_DOCKER).
			SetStatus(omodel.INSTANCE_STATUS_NORMAL))
	}
	ctx, cancel = mysql.ContextWithTimeout()
	err = db.Sentinel.CreateBulk(bulkCreate...).Exec(ctx)
	cancel()
	if err != nil {
		return err
	}

	return nil
}

// 批量创建proxy记录
func BulkCreateRouter(clusterID int64, clusterName, bns string, port int, podList []*fec.PodInfo) error {
	db, err := mysql.Database()
	if err != nil {
		logger.Warn("failed to get mysql connection, error=(%v)", err)
		return err
	}

	// 先查询
	ctx, cancel := mysql.ContextWithTimeout()
	proxyList, err := db.Proxy.Query().Where(proxy.ClusterName(clusterName), proxy.Docker(omodel.DEPLOY_ENV_DOCKER)).All(ctx)
	cancel()
	if err != nil {
		logger.Warn("failed to query proxy, error=(%v)", err)
		return err
	}
	// 转map
	proxyMap := map[string]*ent.Proxy{}
	for _, row := range proxyList {
		k := fmt.Sprintf("%s:%d", row.IP, row.Port)
		proxyMap[k] = row
	}

	var bulkCreate []*ent.ProxyCreate
	for _, pod := range podList {
		k := fmt.Sprintf("%s:%d", pod.PodIp, port)
		if _, exist := proxyMap[k]; exist {
			continue
		}

		bulkCreate = append(bulkCreate, db.Proxy.Create().
			SetClusterID(clusterID).SetClusterName(clusterName).
			SetBns(bns).SetIP(pod.PodIp).SetPort(port).
			SetIdc(pod.Labels[common.LABEL_IDC]).SetDocker(omodel.DEPLOY_ENV_DOCKER).
			SetStatus(omodel.INSTANCE_STATUS_NORMAL))
	}
	ctx, cancel = mysql.ContextWithTimeout()
	err = db.Proxy.CreateBulk(bulkCreate...).Exec(ctx)
	cancel()
	if err != nil {
		return err
	}

	return nil
}

// ==============================
// 			 Update
// ==============================

// 更新redis记录
func MakeMaster(ip string, port int) error {
	db, err := mysql.Database()
	if err != nil {
		logger.Warn("failed to get mysql connection, error=(%v)", err)
		return err
	}

	// 先查询
	ctx, cancel := mysql.ContextWithTimeout()
	updatedCount, err := db.Redis.Update().SetRole(omodel.REDIS_ROLE_MASTER).
		Where(redis.IP(ip), redis.Port(port)).Save(ctx)
	cancel()
	if err != nil {
		return err
	}

	if updatedCount == 0 {
		logger.Warn("try to update role to master, but updatedCount is 0, redis=[%s:%d]", ip, port)
	}

	return nil
}

// ==============================
// 			 Select
// ==============================

// 查询redis容器实例
func QueryRedisInstances(clusterName string, showAll ...bool) ([]*omodel.Instance, error) {
	db, err := mysql.Database()
	if err != nil {
		logger.Warn("failed to get mysql connection, error=(%v)", err)
		return nil, err
	}

	// 查询条件
	conds := []predicate.Redis{
		redis.ClusterName(clusterName),
		redis.Docker(omodel.DEPLOY_ENV_DOCKER),
	}

	// 过滤污点实例
	if len(showAll) == 0 || !showAll[0] {
		conds = append(conds, redis.TaintNEQ(true))
	}

	// 查询
	var instances []*omodel.Instance
	ctx, cancel := mysql.ContextWithTimeout()
	err = db.Redis.Query().Where(conds...).
		Select(redis.FieldIP, redis.FieldPort, redis.FieldName, redis.FieldBns, redis.FieldIdc, redis.FieldRole, redis.FieldDocker, redis.FieldTaint).
		Scan(ctx, &instances)
	cancel()
	if err != nil {
		return nil, err
	}

	return instances, nil
}

// 查询Sentinel容器实例
func QuerySentinelInstances(clusterName string, showAll ...bool) ([]*omodel.Instance, error) {
	db, err := mysql.Database()
	if err != nil {
		logger.Warn("failed to get mysql connection, error=(%v)", err)
		return nil, err
	}

	// 查询条件
	conds := []predicate.Sentinel{
		sentinel.ClusterName(clusterName),
		sentinel.Docker(omodel.DEPLOY_ENV_DOCKER),
	}

	// 过滤污点实例
	if len(showAll) == 0 || !showAll[0] {
		conds = append(conds, sentinel.TaintNEQ(true))
	}

	// 查询
	var instances []*omodel.Instance
	ctx, cancel := mysql.ContextWithTimeout()
	err = db.Sentinel.Query().Where(conds...).
		Select(sentinel.FieldIP, sentinel.FieldPort, sentinel.FieldBns, sentinel.FieldIdc, sentinel.FieldDocker, sentinel.FieldTaint).
		Scan(ctx, &instances)
	cancel()
	if err != nil {
		return nil, err
	}

	return instances, nil
}

// 查询Proxy容器实例
func QueryProxyInstances(clusterName string, showAll ...bool) ([]*omodel.Instance, error) {
	db, err := mysql.Database()
	if err != nil {
		logger.Warn("failed to get mysql connection, error=(%v)", err)
		return nil, err
	}

	// 查询条件
	conds := []predicate.Proxy{
		proxy.ClusterName(clusterName),
		proxy.Docker(omodel.DEPLOY_ENV_DOCKER),
	}

	// 过滤污点实例
	if len(showAll) == 0 || !showAll[0] {
		conds = append(conds, proxy.TaintNEQ(true))
	}

	// 查询
	var instances []*omodel.Instance
	ctx, cancel := mysql.ContextWithTimeout()
	err = db.Proxy.Query().Where(conds...).
		Select(proxy.FieldIP, proxy.FieldPort, proxy.FieldBns, proxy.FieldIdc, proxy.FieldDocker, proxy.FieldTaint).
		Scan(ctx, &instances)
	cancel()
	if err != nil {
		return nil, err
	}

	return instances, nil
}

// =========================================================
// 			 Delete：先打污点，再删除
// =========================================================

// pod信息转换成where条件
func podToConditions(podList []*fec.PodInfo) ([]string, []string, []string, error) {
	// 根据component分组
	redisIps, sentinelIps, proxyIps := []string{}, []string{}, []string{}
	for _, pod := range podList {
		switch pod.Labels[common.LABEL_COMPONENT] {
		case common.COMPONENT_REDIS:
			redisIps = append(redisIps, pod.PodIp)
		case common.COMPONENT_SENTINEL:
			sentinelIps = append(sentinelIps, pod.PodIp)
		case common.COMPONENT_ROUTER:
			proxyIps = append(proxyIps, pod.PodIp)
		default:
			return nil, nil, nil, fmt.Errorf("pod %s has wrong component tag, sames not a valid pod, tag value=%s", pod.PodName, pod.Labels[common.LABEL_COMPONENT])
		}
	}
	return redisIps, sentinelIps, proxyIps, nil
}

// 更新taint字段打污点
func TaintFromPods(clusterName string, podList []*fec.PodInfo, taint bool) error {
	db, err := mysql.Database()
	if err != nil {
		logger.Error("failed to get mysql connection, error=(%v)", err)
		return err
	}

	// 根据component分组
	redisIps, sentinelIps, proxyIps, err := podToConditions(podList)
	if err != nil {
		return err
	}

	// 更新
	if len(redisIps) > 0 {
		ctx, cancel := mysql.ContextWithTimeout()
		err := db.Redis.Update().SetTaint(taint).Where(
			redis.ClusterName(clusterName), redis.Docker(omodel.DEPLOY_ENV_DOCKER), redis.IPIn(redisIps...),
		).Exec(ctx)
		cancel()
		if err != nil {
			logger.Error("failed to update field taint from redis, taint=%v, error=(%v)", taint, err)
			return err
		}
	}
	if len(sentinelIps) > 0 {
		ctx, cancel := mysql.ContextWithTimeout()
		err := db.Sentinel.Update().SetTaint(taint).Where(
			sentinel.ClusterName(clusterName), sentinel.Docker(omodel.DEPLOY_ENV_DOCKER), sentinel.IPIn(sentinelIps...),
		).Exec(ctx)
		cancel()
		if err != nil {
			logger.Error("failed to update field taint from sentinel, taint=%v, error=(%v)", taint, err)
			return err
		}
	}
	if len(proxyIps) > 0 {
		ctx, cancel := mysql.ContextWithTimeout()
		err := db.Proxy.Update().SetTaint(taint).Where(
			proxy.ClusterName(clusterName), proxy.Docker(omodel.DEPLOY_ENV_DOCKER), proxy.IPIn(proxyIps...),
		).Exec(ctx)
		cancel()
		if err != nil {
			logger.Error("failed to update field taint from proxy, taint=%v, error=(%v)", taint, err)
			return err
		}
	}

	return nil
}

// 删除redis/sentinel/proxy记录
func DeleteFromPods(clusterName string, podList []*fec.PodInfo) error {
	db, err := mysql.Database()
	if err != nil {
		logger.Error("failed to get mysql connection, error=(%v)", err)
		return err
	}

	// 根据component分组
	redisIps, sentinelIps, proxyIps, err := podToConditions(podList)
	if err != nil {
		return err
	}

	// 若有，删除redis
	if len(redisIps) > 0 {
		ctx, cancel := mysql.ContextWithTimeout()
		count, err := db.Redis.Delete().Where(
			redis.ClusterName(clusterName), redis.Docker(omodel.DEPLOY_ENV_DOCKER), redis.Taint(true), redis.IPIn(redisIps...),
		).Exec(ctx)
		cancel()
		if err != nil {
			logger.Error("failed to delete from redis, error=(%v)", err)
			return err
		}
		if count != len(redisIps) {
			logger.Warn("num of deleted redis pod is not equal to num of deleted database record, cluster=%s, deletedPods=[%+v], deletedNum=%d", clusterName, podList, count)
		}
	}

	// 若有，删除sentinel
	if len(sentinelIps) > 0 {
		ctx, cancel := mysql.ContextWithTimeout()
		count, err := db.Sentinel.Delete().Where(
			sentinel.ClusterName(clusterName), sentinel.Docker(omodel.DEPLOY_ENV_DOCKER), sentinel.Taint(true), sentinel.IPIn(sentinelIps...),
		).Exec(ctx)
		cancel()
		if err != nil {
			logger.Error("failed to delete from sentinel, error=(%v)", err)
			return err
		}
		if count != len(sentinelIps) {
			logger.Warn("num of deleted sentinel pod is not equal to num of deleted database record, cluster=%s, deletedPods=[%+v], deletedNum=%d", clusterName, podList, count)
		}
	}

	// 若有，删除proxy
	if len(proxyIps) > 0 {
		ctx, cancel := mysql.ContextWithTimeout()
		count, err := db.Proxy.Delete().Where(
			proxy.ClusterName(clusterName), proxy.Docker(omodel.DEPLOY_ENV_DOCKER), proxy.Taint(true), proxy.IPIn(proxyIps...),
		).Exec(ctx)
		cancel()
		if err != nil {
			logger.Error("failed to delete from proxy, error=(%v)", err)
			return err
		}
		if count != len(proxyIps) {
			logger.Warn("num of deleted proxy pod is not equal to num of deleted database record, cluster=%s, deletedPods=[%+v], deletedNum=%d", clusterName, podList, count)
		}
	}

	return nil
}

// 删除集群所有的容器实例记录
func DeleteAllInstances(clusterName string) error {
	db, err := mysql.Database()
	if err != nil {
		logger.Error("failed to get mysql connection, error=(%v)", err)
		return err
	}

	// 删除proxy
	ctx, cancel := mysql.ContextWithTimeout()
	_, err = db.Proxy.Delete().Where(
		proxy.ClusterName(clusterName), proxy.Docker(omodel.DEPLOY_ENV_DOCKER),
	).Exec(ctx)
	cancel()
	if err != nil {
		logger.Error("failed to delete from proxy, error=(%v)", err)
		return err
	}

	// 删除sentinel
	ctx, cancel = mysql.ContextWithTimeout()
	_, err = db.Sentinel.Delete().Where(
		sentinel.ClusterName(clusterName), sentinel.Docker(omodel.DEPLOY_ENV_DOCKER),
	).Exec(ctx)
	cancel()
	if err != nil {
		logger.Error("failed to delete from sentinel, error=(%v)", err)
		return err
	}

	// 若有，删除redis
	ctx, cancel = mysql.ContextWithTimeout()
	_, err = db.Redis.Delete().Where(
		redis.ClusterName(clusterName), redis.Docker(omodel.DEPLOY_ENV_DOCKER),
	).Exec(ctx)
	cancel()
	if err != nil {
		logger.Error("failed to delete from redis, error=(%v)", err)
		return err
	}

	return nil
}
