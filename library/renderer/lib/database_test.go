package lib

import (
	"context"
	"testing"

	"dt-common/ent/proxy"
	"dt-common/ent/redis"
	"dt-common/ent/sentinel"
	"dt-common/mysql"
	"dt-common/omodel"
	"redis-cmanager/env"
	"redis-cmanager/library/fec"
	"redis-cmanager/library/renderer/common"
)

// ========================================
//	            查询
// ========================================

func TestQueryRedisInstances(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")
	objC := env.MockCluster("query_test")
	db, _ := mysql.Database()
	db.Redis.Delete().Where(redis.ClusterName(objC.Name)).Exec(context.Background())
	db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(0).SetTaint(false).SetBns("query-test-redis.siod-redis").SetName("query_test-server1").SetIP("*********").SetPort(7000).SetIdc("hba").SetRole("master").ExecX(context.Background())
	db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(0).SetTaint(false).SetBns("query-test-redis.siod-redis").SetName("query_test-server2").SetIP("*********").SetPort(7000).SetIdc("hba").SetRole("master").ExecX(context.Background())
	db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetTaint(false).SetBns("query-test-redis.siod-redis").SetName("query_test-server1").SetIP("*********").SetPort(7000).SetIdc("hbb").SetRole("slave").ExecX(context.Background())
	db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetTaint(true).SetBns("query-test-redis.siod-redis").SetName("query_test-server2").SetIP("*********").SetPort(7000).SetIdc("hbb").SetRole("slave").ExecX(context.Background())

	type args struct {
		clusterName string
		showAll     bool
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, []*omodel.Instance)
	}{
		{
			name: "not tainted",
			args: args{
				clusterName: objC.Name,
			},
			wantErr: false,
			expect: func(t *testing.T, instances []*omodel.Instance) {
				if len(instances) != 1 {
					t.Errorf("expect instances len 1, but got %d", len(instances))
					return
				}
				if instances[0].IP != "*********" {
					t.Errorf("expect instances[0] ip *********, but got %s", instances[0].IP)
				}
			},
		},
		{
			name: "show all",
			args: args{
				clusterName: objC.Name,
				showAll:     true,
			},
			wantErr: false,
			expect: func(t *testing.T, instances []*omodel.Instance) {
				if len(instances) != 2 {
					t.Errorf("expect instances len 2, but got %d", len(instances))
					return
				}
				if !instances[0].Taint && !instances[1].Taint {
					t.Errorf("expect one instances taint true, but got none")
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}

			instances, err := QueryRedisInstances(tt.args.clusterName, tt.args.showAll)
			if (err != nil) != tt.wantErr {
				t.Errorf("QueryRedisInstances() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, instances)
			}
		})
	}
}

func TestQuerySentinelInstances(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")
	objC := env.MockCluster("query_test")
	db, _ := mysql.Database()
	db.Sentinel.Delete().Where(sentinel.ClusterName(objC.Name)).Exec(context.Background())
	db.Sentinel.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(0).SetTaint(false).SetBns("query-test-sentinel.siod-redis").SetIP("*********").SetPort(9001).SetIdc("hba").ExecX(context.Background())
	db.Sentinel.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(0).SetTaint(false).SetBns("query-test-sentinel.siod-redis").SetIP("*********").SetPort(9001).SetIdc("hba").ExecX(context.Background())
	db.Sentinel.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetTaint(false).SetBns("query-test-sentinel.siod-redis").SetIP("*********").SetPort(9001).SetIdc("hbb").ExecX(context.Background())
	db.Sentinel.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetTaint(true).SetBns("query-test-sentinel.siod-redis").SetIP("*********").SetPort(9001).SetIdc("hbb").ExecX(context.Background())

	type args struct {
		clusterName string
		showAll     bool
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, []*omodel.Instance)
	}{
		{
			name: "not tainted",
			args: args{
				clusterName: objC.Name,
			},
			wantErr: false,
			expect: func(t *testing.T, instances []*omodel.Instance) {
				if len(instances) != 1 {
					t.Errorf("expect instances len 1, but got %d", len(instances))
					return
				}
				if instances[0].IP != "*********" {
					t.Errorf("expect instances[0] ip *********, but got %s", instances[0].IP)
				}
			},
		},
		{
			name: "show all",
			args: args{
				clusterName: objC.Name,
				showAll:     true,
			},
			wantErr: false,
			expect: func(t *testing.T, instances []*omodel.Instance) {
				if len(instances) != 2 {
					t.Errorf("expect instances len 2, but got %d", len(instances))
					return
				}
				if !instances[0].Taint && !instances[1].Taint {
					t.Errorf("expect one instances taint true, but got none")
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			instances, err := QuerySentinelInstances(tt.args.clusterName, tt.args.showAll)
			if (err != nil) != tt.wantErr {
				t.Errorf("QuerySentinelInstances() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, instances)
			}
		})
	}
}

func TestQueryProxyInstances(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")
	objC := env.MockCluster("query_test")
	db, _ := mysql.Database()
	db.Proxy.Delete().Where(proxy.ClusterName(objC.Name)).Exec(context.Background())
	db.Proxy.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(0).SetTaint(false).SetBns("query-test-router.siod-redis").SetIP("*********").SetPort(8001).SetIdc("hba").ExecX(context.Background())
	db.Proxy.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(0).SetTaint(false).SetBns("query-test-router.siod-redis").SetIP("*********").SetPort(8001).SetIdc("hba").ExecX(context.Background())
	db.Proxy.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetTaint(false).SetBns("query-test-router.siod-redis").SetIP("*********").SetPort(8001).SetIdc("hbb").ExecX(context.Background())
	db.Proxy.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetTaint(true).SetBns("query-test-router.siod-redis").SetIP("*********").SetPort(8001).SetIdc("hbb").ExecX(context.Background())

	type args struct {
		clusterName string
		showAll     bool
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, []*omodel.Instance)
	}{
		{
			name: "not tainted",
			args: args{
				clusterName: objC.Name,
			},
			wantErr: false,
			expect: func(t *testing.T, instances []*omodel.Instance) {
				if len(instances) != 1 {
					t.Errorf("expect instances len 1, but got %d", len(instances))
					return
				}
				if instances[0].IP != "*********" {
					t.Errorf("expect instances[0] ip *********, but got %s", instances[0].IP)
				}
			},
		},
		{
			name: "show all",
			args: args{
				clusterName: objC.Name,
				showAll:     true,
			},
			wantErr: false,
			expect: func(t *testing.T, instances []*omodel.Instance) {
				if len(instances) != 2 {
					t.Errorf("expect instances len 2, but got %d", len(instances))
					return
				}
				if !instances[0].Taint && !instances[1].Taint {
					t.Errorf("expect one instances taint true, but got none")
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			instances, err := QueryProxyInstances(tt.args.clusterName, tt.args.showAll)
			if (err != nil) != tt.wantErr {
				t.Errorf("QueryProxyInstances() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, instances)
			}
		})
	}
}

// ========================================
//	            申请：POD落库
// ========================================

func TestBulkCreateRedis(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")
	objC := env.MockCluster("r3_rd_test")

	type args struct {
		clusterID   int64
		clusterName string
		bns         string
		port        int
		podList     []*fec.PodInfo
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
	}{
		{
			name: "TEST1",
			args: args{
				clusterID:   objC.ID,
				clusterName: objC.Name,
				bns:         "test3.siod-kafka",
				port:        7000,
				podList: []*fec.PodInfo{
					{
						PodName: "r3_rd_test-0",
						PodIp:   "***********",
						Labels: map[string]string{
							"dxm-redis/idc":   "hbb",
							"dxm-redis/index": "1",
						},
					},
					{
						PodName: "r3_rd_test-1",
						PodIp:   "***********",
						Labels: map[string]string{
							"dxm-redis/idc":   "hbb",
							"dxm-redis/index": "1",
						},
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}

			err := BulkCreateRedis(tt.args.clusterID, tt.args.clusterName, tt.args.bns, tt.args.port, tt.args.podList)
			if (err != nil) != tt.wantErr {
				t.Errorf("BulkCreateRedis() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestBulkCreateSentinel(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")
	objC := env.MockCluster("r3_rd_test")

	type args struct {
		clusterID   int64
		clusterName string
		bns         string
		port        int
		podList     []*fec.PodInfo
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "TEST1",
			args: args{
				clusterID:   objC.ID,
				clusterName: objC.Name,
				bns:         "test3.siod-kafka",
				port:        9001,
				podList: []*fec.PodInfo{
					{
						PodName: "r3_rd_test-0",
						PodIp:   "***********",
						Labels: map[string]string{
							"dxm-redis/idc": "hbb",
						},
					},
					{
						PodName: "r3_rd_test-1",
						PodIp:   "***********",
						Labels: map[string]string{
							"dxm-redis/idc": "hbb",
						},
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := BulkCreateSentinel(tt.args.clusterID, tt.args.clusterName, tt.args.bns, tt.args.port, tt.args.podList)
			if (err != nil) != tt.wantErr {
				t.Errorf("BulkCreateSentinel() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestBulkCreateRouter(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")
	objC := env.MockCluster("r3_rd_test")

	type args struct {
		clusterID   int64
		clusterName string
		bns         string
		port        int
		podList     []*fec.PodInfo
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "TEST1",
			args: args{
				clusterID:   objC.ID,
				clusterName: objC.Name,
				bns:         "test3.siod-kafka",
				port:        8001,
				podList: []*fec.PodInfo{
					{
						PodName: "r3_rd_test-0",
						PodIp:   "***********",
						Labels: map[string]string{
							"dxm-redis/idc": "hbb",
						},
					},
					{
						PodName: "r3_rd_test-1",
						PodIp:   "***********",
						Labels: map[string]string{
							"dxm-redis/idc": "hbb",
						},
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			err := BulkCreateRouter(tt.args.clusterID, tt.args.clusterName, tt.args.bns, tt.args.port, tt.args.podList)
			if (err != nil) != tt.wantErr {
				t.Errorf("BulkCreateProxy() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

// ========================================
//				  更新记录
// ========================================

func TestMakeMaster(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")
	objC := env.MockCluster("make_master_test")
	db, _ := mysql.Database()

	type args struct {
		ip   string
		port int
	}
	tests := []struct {
		name     string
		before   func()
		args     args
		wantErr  bool
		expected func(*testing.T)
	}{
		{
			name: "TEST1",
			before: func() {
				db.Redis.Delete().Exec(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("make-master-test-redis.siod-redis").SetName("make_master_test-server1").SetIP("***********").SetPort(7000).SetIdc("hbb").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("make-master-test-redis.siod-redis").SetName("make_master_test-server2").SetIP("*********").SetPort(7000).SetIdc("hbb").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
			},
			args: args{
				ip:   "***********",
				port: 7000,
			},
			wantErr: false,
			expected: func(t *testing.T) {
				redisList, err := db.Redis.Query().Where(redis.ClusterID(objC.ID)).All(context.Background())
				if err != nil {
					t.Errorf("unexpected error when query redis, error=(%v)", err)
					return
				}

				for _, r := range redisList {
					if r.IP == "***********" {
						if r.Role != "master" {
							t.Errorf("expect ***********:7000 master, but got %s", r.Role)
						}
					}
				}
			},
		},
		{
			name: "already master",
			before: func() {
				db.Redis.Delete().Exec(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("make-master-test-redis.siod-redis").SetName("make_master_test-server1").SetIP("***********").SetPort(7000).SetIdc("hbb").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("make-master-test-redis.siod-redis").SetName("make_master_test-server2").SetIP("*********").SetPort(7000).SetIdc("hbb").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
			},
			args: args{
				ip:   "***********",
				port: 7000,
			},
			wantErr: false,
			expected: func(t *testing.T) {
				redisList, err := db.Redis.Query().Where(redis.ClusterID(objC.ID)).All(context.Background())
				if err != nil {
					t.Errorf("unexpected error when query redis, error=(%v)", err)
					return
				}

				for _, r := range redisList {
					if r.IP == "***********" {
						if r.Role != "master" {
							t.Errorf("expect ***********:7000 master, but got %s", r.Role)
						}
					}
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := MakeMaster(tt.args.ip, tt.args.port)
			if (err != nil) != tt.wantErr {
				t.Errorf("MakeMaster() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

// ========================================
//	              删除记录
// ========================================

func Test_podToConditions(t *testing.T) {
	type args struct {
		podList []*fec.PodInfo
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, []string, []string, []string)
	}{
		{
			name: "success",
			args: args{
				podList: []*fec.PodInfo{
					{
						PodIp:  "************",
						Labels: map[string]string{"dxm-redis/component": common.COMPONENT_REDIS},
					},
					{
						PodIp:  "************2",
						Labels: map[string]string{"dxm-redis/component": common.COMPONENT_REDIS},
					},
					{
						PodIp:  "************",
						Labels: map[string]string{"dxm-redis/component": common.COMPONENT_SENTINEL},
					},
					{
						PodIp:  "*************",
						Labels: map[string]string{"dxm-redis/component": common.COMPONENT_ROUTER},
					},
				},
			},
			wantErr: false,
			expect: func(t *testing.T, r []string, p []string, s []string) {
				if len(r) != 2 {
					t.Errorf("expect redis count 2, but got %d", len(r))
				}
				if len(p) != 1 {
					t.Errorf("expect sentinel count 1, but got %d", len(p))
				}
				if len(s) != 1 {
					t.Errorf("expect proxy count 1, but got %d", len(s))
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			r, p, s, err := podToConditions(tt.args.podList)
			if (err != nil) != tt.wantErr {
				t.Errorf("podToConditions() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, r, p, s)
			}
		})
	}
}

// 单测：taint instances
func TestTaintFromPods(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")
	objC := env.MockCluster("taint_test")
	db, _ := mysql.Database()

	type args struct {
		clusterName string
		podList     []*fec.PodInfo
		taint       bool
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "delete proxy",
			before: func() {
				db.Redis.Delete().ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-qa-test-redis.siod-redis").SetName("r3_qa_test-server1").SetIP("************").SetPort(7000).SetIdc("hbb").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Sentinel.Delete().ExecX(context.Background())
				db.Sentinel.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-qa-test-sentinel.siod-redis").SetIP("************").SetPort(9001).SetIdc("hba").ExecX(context.Background())
				db.Proxy.Delete().ExecX(context.Background())
				db.Proxy.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-qa-test-router.siod-redis").SetIP("*************").SetPort(8002).SetIdc("hba").ExecX(context.Background())
			},
			args: args{
				clusterName: objC.Name,
				podList: []*fec.PodInfo{
					{
						PodIp:  "************",
						Labels: map[string]string{"dxm-redis/component": common.COMPONENT_REDIS},
					},
					{
						PodIp:  "************",
						Labels: map[string]string{"dxm-redis/component": common.COMPONENT_SENTINEL},
					},
					{
						PodIp:  "*************",
						Labels: map[string]string{"dxm-redis/component": common.COMPONENT_ROUTER},
					},
				},
				taint: true,
			},
			wantErr: false,
			expect: func(t *testing.T) {
				db, _ := mysql.Database()
				redisResults, err := db.Redis.Query().Where(redis.Taint(true)).All(context.Background())
				if err != nil {
					t.Errorf("unexpected error: %v", err)
				}
				if len(redisResults) != 1 {
					t.Errorf("expect redis count 1, but got %d", len(redisResults))
				}

				sentinelResults, err := db.Sentinel.Query().Where(sentinel.Taint(true)).All(context.Background())
				if err != nil {
					t.Errorf("unexpected error: %v", err)
				}
				if len(sentinelResults) != 1 {
					t.Errorf("expect sentinel count 1, but got %d", len(sentinelResults))
				}

				proxyResults, err := db.Proxy.Query().Where(proxy.Taint(true)).All(context.Background())
				if err != nil {
					t.Errorf("unexpected error: %v", err)
				}
				if len(proxyResults) != 1 {
					t.Errorf("expect proxy count 1, but got %d", len(proxyResults))
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := TaintFromPods(tt.args.clusterName, tt.args.podList, tt.args.taint)
			if (err != nil) != tt.wantErr {
				t.Errorf("TaintFromPods() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

// 单测：删除记录
func TestDeleteFromPods(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")
	objC := env.MockCluster("delete_test")
	db, _ := mysql.Database()

	type args struct {
		clusterName string
		podList     []*fec.PodInfo
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "failed",
			before: func() {
				db.Redis.Delete().ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-qa-test-redis.siod-redis").SetName("r3_qa_test-server1").SetIP("************").SetPort(7000).SetIdc("hbb").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Sentinel.Delete().ExecX(context.Background())
				db.Sentinel.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-qa-test-sentinel.siod-redis").SetIP("************").SetPort(9001).SetIdc("hba").ExecX(context.Background())
				db.Proxy.Delete().ExecX(context.Background())
				db.Proxy.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-qa-test-router.siod-redis").SetIP("*************").SetPort(8002).SetIdc("hba").ExecX(context.Background())
			},
			args: args{
				clusterName: objC.Name,
				podList: []*fec.PodInfo{
					{
						PodIp:  "************",
						Labels: map[string]string{"dxm-redis/component": common.COMPONENT_REDIS},
					},
					{
						PodIp:  "************",
						Labels: map[string]string{"dxm-redis/component": common.COMPONENT_SENTINEL},
					},
					{
						PodIp:  "*************",
						Labels: map[string]string{"dxm-redis/component": common.COMPONENT_ROUTER},
					},
				},
			},
			wantErr: false,
			expect: func(t *testing.T) {
				db, _ := mysql.Database()
				redisResults, err := db.Redis.Query().All(context.Background())
				if err != nil {
					t.Errorf("unexpected error: %v", err)
				}
				if len(redisResults) != 1 {
					t.Errorf("expect redis count 1, but got %d", len(redisResults))
				}

				sentinelResults, err := db.Sentinel.Query().All(context.Background())
				if err != nil {
					t.Errorf("unexpected error: %v", err)
				}
				if len(sentinelResults) != 1 {
					t.Errorf("expect sentinel count 1, but got %d", len(sentinelResults))
				}

				proxyResults, err := db.Proxy.Query().All(context.Background())
				if err != nil {
					t.Errorf("unexpected error: %v", err)
				}
				if len(proxyResults) != 1 {
					t.Errorf("expect proxy count 1, but got %d", len(proxyResults))
				}
			},
		},
		{
			name: "success",
			before: func() {
				db.Redis.Delete().ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetTaint(true).SetBns("r3-qa-test-redis.siod-redis").SetName("r3_qa_test-server1").SetIP("************").SetPort(7000).SetIdc("hbb").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Sentinel.Delete().ExecX(context.Background())
				db.Sentinel.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetTaint(true).SetBns("r3-qa-test-sentinel.siod-redis").SetIP("************").SetPort(9001).SetIdc("hba").ExecX(context.Background())
				db.Proxy.Delete().ExecX(context.Background())
				db.Proxy.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetTaint(true).SetBns("r3-qa-test-router.siod-redis").SetIP("*************").SetPort(8002).SetIdc("hba").ExecX(context.Background())
			},
			args: args{
				clusterName: objC.Name,
				podList: []*fec.PodInfo{
					{
						PodIp:  "************",
						Labels: map[string]string{"dxm-redis/component": common.COMPONENT_REDIS},
					},
					{
						PodIp:  "************",
						Labels: map[string]string{"dxm-redis/component": common.COMPONENT_SENTINEL},
					},
					{
						PodIp:  "*************",
						Labels: map[string]string{"dxm-redis/component": common.COMPONENT_ROUTER},
					},
				},
			},
			wantErr: false,
			expect: func(t *testing.T) {
				db, _ := mysql.Database()
				redisResults, err := db.Redis.Query().All(context.Background())
				if err != nil {
					t.Errorf("unexpected error: %v", err)
				}
				if len(redisResults) != 0 {
					t.Errorf("expect redis count 0, but got %d", len(redisResults))
				}

				sentinelResults, err := db.Sentinel.Query().All(context.Background())
				if err != nil {
					t.Errorf("unexpected error: %v", err)
				}
				if len(sentinelResults) != 0 {
					t.Errorf("expect sentinel count 0, but got %d", len(sentinelResults))
				}

				proxyResults, err := db.Proxy.Query().All(context.Background())
				if err != nil {
					t.Errorf("unexpected error: %v", err)
				}
				if len(proxyResults) != 0 {
					t.Errorf("expect proxy count 0, but got %d", len(proxyResults))
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := DeleteFromPods(tt.args.clusterName, tt.args.podList)
			if (err != nil) != tt.wantErr {
				t.Errorf("DeleteFromPods() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

func TestDeleteAllInstances(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")
	objC := env.MockCluster("delete_test")
	db, _ := mysql.Database()

	type args struct {
		clusterName string
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "success",
			before: func() {
				db.Redis.Delete().ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-qa-test-redis.siod-redis").SetName("r3_qa_test-server1").SetIP("************").SetPort(7000).SetIdc("hbb").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Sentinel.Delete().ExecX(context.Background())
				db.Sentinel.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-qa-test-sentinel.siod-redis").SetIP("************").SetPort(9001).SetIdc("hba").ExecX(context.Background())
				db.Proxy.Delete().ExecX(context.Background())
				db.Proxy.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-qa-test-router.siod-redis").SetIP("*************").SetPort(8002).SetIdc("hba").ExecX(context.Background())
			},
			args: args{
				clusterName: objC.Name,
			},
			wantErr: false,
			expect: func(t *testing.T) {
				db, _ := mysql.Database()
				redisResults, err := db.Redis.Query().All(context.Background())
				if err != nil {
					t.Errorf("unexpected error: %v", err)
				}
				if len(redisResults) != 0 {
					t.Errorf("expect redis count 0, but got %d", len(redisResults))
				}

				sentinelResults, err := db.Sentinel.Query().All(context.Background())
				if err != nil {
					t.Errorf("unexpected error: %v", err)
				}
				if len(sentinelResults) != 0 {
					t.Errorf("expect sentinel count 0, but got %d", len(sentinelResults))
				}

				proxyResults, err := db.Proxy.Query().All(context.Background())
				if err != nil {
					t.Errorf("unexpected error: %v", err)
				}
				if len(proxyResults) != 0 {
					t.Errorf("expect proxy count 0, but got %d", len(proxyResults))
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := DeleteAllInstances(tt.args.clusterName)
			if (err != nil) != tt.wantErr {
				t.Errorf("DeleteAllInstances() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}
