package lib

import (
	"reflect"
	"testing"

	"github.com/jarcoal/httpmock"

	"dt-common/apptree"
	"dt-common/noah"
	"redis-cmanager/env"
	"redis-cmanager/library/fec"
	"redis-cmanager/library/renderer/common"
)

func TestCreateApp(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")

	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		app  *apptree.App
		port int
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
	}{
		{
			name: "get app failed",
			args: args{
				app: &apptree.App{
					Product:   "siod-kafka",
					Subsystem: "r3-test",
					Name:      "r3-test-router",
				},
				port: 7000,
			},
			wantErr: true,
		},
		{
			name: "app not exist create subsystem failed",
			before: func() {
				httpmock.RegisterResponder("GET", "http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-kafka/subSystems/r3-test/apps/r3-test-router",
					httpmock.NewStringResponder(200, `{"success": false, "message": "is not exist", "data": ""}`),
				)
			},
			args: args{
				app: &apptree.App{
					Product:   "siod-kafka",
					Subsystem: "r3-test",
					Name:      "r3-test-router",
				},
				port: 7000,
			},
			wantErr: true,
		},
		{
			name: "app not exist create app failed",
			before: func() {
				httpmock.RegisterResponder("GET", "http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-kafka/subSystems/r3-test/apps/r3-test-router",
					httpmock.NewStringResponder(200, `{"success": false, "message": "is not exist", "data": ""}`),
				)

				httpmock.RegisterResponder("POST", "http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-kafka/subSystems",
					httpmock.NewStringResponder(200, `{"success": false, "message": "is not exist", "data": ""}`),
				)
			},
			args: args{
				app: &apptree.App{
					Product:   "siod-kafka",
					Subsystem: "r3-test",
					Name:      "r3-test-router",
				},
				port: 7000,
			},
			wantErr: true,
		},
		{
			name: "UpdatePortCheckPolicy failed",
			before: func() {
				httpmock.RegisterResponder("GET", "http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-kafka/subSystems/r3-test/apps/r3-test-router",
					httpmock.NewStringResponder(200, `{"success": true, "message": "OK", "data": {"name": "0.r3-test-router.siod-kafka"}}`),
				)
			},
			args: args{
				app: &apptree.App{
					Product:   "siod-kafka",
					Subsystem: "r3-test",
					Name:      "r3-test-router",
				},
				port: 7000,
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}

			err := CreateApp(tt.args.app, tt.args.port)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateApp() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestPodsToInstances(t *testing.T) {
	common.CFG = common.Config{
		App: &common.AppConfig{
			ProductLine: "siod-kafka",
		},
		IDC: map[string]string{
			"hba": "BDDX",
			"hbb": "ZZJG",
			"hbc": "BDDX",
		},
		Application: &common.Application{
			Port:  6379,
			BNS:   "redis-cmanager.siod-kafka",
			Token: "123",
		},
	}

	type args struct {
		podList    []*fec.PodInfo
		port       int
		deployPath string
		disable    bool
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, map[string]*noah.Instance)
	}{
		{
			name: "component instance",
			args: args{
				podList: []*fec.PodInfo{
					{
						PodIp:    "***********",
						HostName: "bddx",
						Labels: map[string]string{
							common.LABEL_IDC: "hba",
						},
					},
				},
				port:       6379,
				deployPath: "/home/<USER>/local/sentinel",
				disable:    false,
			},
			expect: func(t *testing.T, got map[string]*noah.Instance) {
				want := map[string]*noah.Instance{
					"***********": {
						IP:      "***********",
						Disable: false,
						RunUser: common.INSTANCE_RUN_USER,
						PortInfo: &noah.PortInfo{
							Main: 6379,
						},
						DeployInfo: &noah.DeployInfo{
							DeployPath: "/home/<USER>/local/sentinel",
						},
						Tags: map[string]string{
							common.INSTANCE_TAG_KEY_DEPLOY:         common.INSTANCE_TAG_VALUE_DEPLOY,
							common.INSTANCE_TAG_KEY_REGION:         "hba",
							common.INSTANCE_TAG_KEY_REGION_SERVICE: "hbas",
						},
						HostName: "bddx",
					},
				}
				if !reflect.DeepEqual(got, want) {
					t.Errorf("pod2instance() = %v, expect = %v", got, want)
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			got := PodsToInstances(tt.args.podList, tt.args.port, tt.args.deployPath, tt.args.disable)
			if tt.expect != nil {
				tt.expect(t, got)
			}
		})
	}
}

func TestDiffInstances(t *testing.T) {
	type args struct {
		podInstanceList    map[string]*noah.Instance
		instanceListInNoah []*noah.Instance
	}
	tests := []struct {
		name                     string
		args                     args
		wantNeedToAddInstance    []*noah.Instance
		wantNeedToDeleteInstance []*noah.Instance
		wantNeedToUpdateInstance []*noah.Instance
	}{
		{
			name: "通过",
			args: args{
				podInstanceList: map[string]*noah.Instance{
					"***********": {
						IP:      "***********",
						Disable: false,
						RunUser: common.INSTANCE_RUN_USER,
						PortInfo: &noah.PortInfo{
							Main: 6379,
						},
						DeployInfo: &noah.DeployInfo{
							DeployPath: "/home/<USER>",
						},
						Tags: map[string]string{
							common.INSTANCE_TAG_KEY_DEPLOY: common.INSTANCE_TAG_VALUE_DEPLOY,
						},
					},
					"***********": {
						IP:      "***********",
						Disable: false,
						RunUser: common.INSTANCE_RUN_USER,
						PortInfo: &noah.PortInfo{
							Main: 6379,
						},
						DeployInfo: &noah.DeployInfo{
							DeployPath: "/home/<USER>",
						},
						Tags: map[string]string{
							common.INSTANCE_TAG_KEY_DEPLOY: common.INSTANCE_TAG_VALUE_DEPLOY,
						},
					},
					"***********": {
						IP:      "***********",
						Disable: false,
						RunUser: common.INSTANCE_RUN_USER,
						PortInfo: &noah.PortInfo{
							Main: 6379,
						},
						DeployInfo: &noah.DeployInfo{
							DeployPath: "/home/<USER>",
						},
						Tags: map[string]string{
							common.INSTANCE_TAG_KEY_DEPLOY: common.INSTANCE_TAG_VALUE_DEPLOY,
						},
					},
				},
				instanceListInNoah: []*noah.Instance{
					{
						IP:      "***********",
						Disable: false,
						RunUser: common.INSTANCE_RUN_USER,
						PortInfo: &noah.PortInfo{
							Main: 6379,
						},
						DeployInfo: &noah.DeployInfo{DeployPath: "/home/<USER>"},
						Tags: map[string]string{
							common.INSTANCE_TAG_KEY_DEPLOY: common.INSTANCE_TAG_VALUE_DEPLOY,
						},
					},
					{
						IP:      "***********",
						Disable: false,
						RunUser: common.INSTANCE_RUN_USER,
						PortInfo: &noah.PortInfo{
							Main: 6379,
						},
						DeployInfo: &noah.DeployInfo{DeployPath: "/home/<USER>"},
						Tags: map[string]string{
							common.INSTANCE_TAG_KEY_DEPLOY: common.INSTANCE_TAG_VALUE_DEPLOY,
						},
					},
					{
						IP:      "***********",
						Disable: false,
						RunUser: common.INSTANCE_RUN_USER,
						PortInfo: &noah.PortInfo{
							Main: 6379,
						},
						DeployInfo: &noah.DeployInfo{DeployPath: "/home/<USER>"},
						Tags: map[string]string{
							common.INSTANCE_TAG_KEY_DEPLOY: common.INSTANCE_TAG_VALUE_DEPLOY,
						},
					},
				},
			},
			wantNeedToAddInstance: []*noah.Instance{{
				IP:      "***********",
				Disable: false,
				RunUser: common.INSTANCE_RUN_USER,
				PortInfo: &noah.PortInfo{
					Main: 6379,
				},
				DeployInfo: &noah.DeployInfo{
					DeployPath: "/home/<USER>",
				},
				Tags: map[string]string{
					common.INSTANCE_TAG_KEY_DEPLOY: common.INSTANCE_TAG_VALUE_DEPLOY,
				},
			}},
			wantNeedToDeleteInstance: []*noah.Instance{
				{
					IP:      "***********",
					Disable: false,
					RunUser: common.INSTANCE_RUN_USER,
					PortInfo: &noah.PortInfo{
						Main: 6379,
					},
					DeployInfo: &noah.DeployInfo{DeployPath: "/home/<USER>"},
					Tags: map[string]string{
						common.INSTANCE_TAG_KEY_DEPLOY: common.INSTANCE_TAG_VALUE_DEPLOY,
					},
				},
			},
			wantNeedToUpdateInstance: []*noah.Instance{
				{
					IP:      "***********",
					Disable: false,
					RunUser: common.INSTANCE_RUN_USER,
					PortInfo: &noah.PortInfo{
						Main: 6379,
					},
					DeployInfo: &noah.DeployInfo{
						DeployPath: "/home/<USER>",
					},
					Tags: map[string]string{
						common.INSTANCE_TAG_KEY_DEPLOY: common.INSTANCE_TAG_VALUE_DEPLOY,
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotNeedToAddInstance, gotNeedToDeleteInstance, gotNeedToUpdateInstance := DiffInstances(tt.args.instanceListInNoah, tt.args.podInstanceList)
			if !reflect.DeepEqual(gotNeedToAddInstance, tt.wantNeedToAddInstance) {
				t.Errorf("diffInstances) gotNeedToAddInstance = %v, want %v", gotNeedToAddInstance, tt.wantNeedToAddInstance)
			}
			if !reflect.DeepEqual(gotNeedToDeleteInstance, tt.wantNeedToDeleteInstance) {
				t.Errorf("diffInstances) gotNeedToDeleteInstance = %v, want %v", gotNeedToDeleteInstance, tt.wantNeedToDeleteInstance)
			}
			if !reflect.DeepEqual(gotNeedToUpdateInstance, tt.wantNeedToUpdateInstance) {
				t.Errorf("diffInstances) gotNeedToUpdateInstance = %v, want %v", gotNeedToUpdateInstance, tt.wantNeedToUpdateInstance)
			}
		})
	}
}
