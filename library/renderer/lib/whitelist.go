package lib

import (
	"fmt"
	"strings"

	"dt-common/ent/configuration"
	"dt-common/ent/whitelist"
	"dt-common/logger"
	"dt-common/mysql"
	"dt-common/omodel"
)

// 从数据库获取proxy bns&ip 白名单
func GetProxyWhiteListFromDB(clusterName string) ([]string, []string, error) {
	db, err := mysql.Database()
	if err != nil {
		logger.Error("failed to get mysql connection, error=(%v)", err)
		return nil, nil, err
	}

	ctx, cancel := mysql.ContextWithTimeout()
	whiteList, err := db.Whitelist.Query().Where(whitelist.ClusterNameEQ(clusterName)).All(ctx)
	cancel()
	if err != nil {
		logger.Error("failed to get proxy whitelist from db, error=(%v)", err)
		return nil, nil, err
	}

	bnsWhiteList := make([]string, 0)
	ipWhiteList := make([]string, 0)
	for _, v := range whiteList {
		if v.Type == "bns" {
			bnsWhiteList = append(bnsWhiteList, v.Value+" "+v.Privilege)
		} else {
			ipWhiteList = append(ipWhiteList, v.Value+" "+v.Privilege)
		}
	}

	return bnsWhiteList, ipWhiteList, nil
}

// 从数据库获取管理白名单
func GetManagementWhiteListFromDB() (rwBnsList []string, roBnsList []string, err error) {
	db, err := mysql.Database()
	if err != nil {
		logger.Error("failed to get mysql connection, error=(%v)", err)
		return nil, nil, err
	}

	ctx, cancel := mysql.ContextWithTimeout()
	rows, err := db.Configuration.Query().Where(
		configuration.Or(
			configuration.NameEQ(omodel.CONFIG_MANAGEMENT_BNS),     // 全组件读写
			configuration.NameEQ(omodel.CONFIG_REDIS_READONLY_BNS), // redis只读
		),
	).All(ctx)
	cancel()
	if err != nil {
		logger.Error("failed to get configuration bns whitelist, error=(%v)", err)
		return nil, nil, err
	}

	for _, row := range rows {
		if row.Value == "" {
			continue
		}
		switch row.Name {
		case omodel.CONFIG_MANAGEMENT_BNS:
			rwBnsList = strings.Split(row.Value, ",")
		case omodel.CONFIG_REDIS_READONLY_BNS:
			roBnsList = strings.Split(row.Value, ",")
		default:
			return nil, nil, fmt.Errorf("unknown configuration name: %s", row.Name)
		}
	}

	return
}

// RouterWhiteListValidate 对Router白名单列表进行合法性校验
// valid返回whiteList中合法的白名单列表
// invalid返回whiteList中不合法的白名单列表
func RouterWhiteListValidate(whiteList []string) (valid []string, invalid []string) {
	valid = make([]string, 0, len(whiteList))
	invalid = make([]string, 0, len(whiteList))
	set := map[string]bool{
		"r":  true,
		"w":  true,
		"rw": true,
	}

	for _, line := range whiteList {
		item := strings.Split(line, " ")
		if len(item) != 2 {
			invalid = append(invalid, line)
			continue
		}
		// 不合法的行直接忽略掉
		if set[item[1]] {
			valid = append(valid, line)
		} else {
			invalid = append(invalid, line)
		}
	}
	return
}
