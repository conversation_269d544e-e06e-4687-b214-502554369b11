package lib

import (
	"context"
	"reflect"
	"testing"

	"dt-common/mysql"
	"dt-common/omodel"
	"redis-cmanager/env"
)

// 获取业务白名单
func TestGetProxyWhiteListFromDB(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")
	objC := env.MockCluster("whitelist_test")

	db, _ := mysql.Database()
	db.Whitelist.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetType("bns").SetValue("codex-huisu.etron").SetPrivilege("rw").Save(context.Background())
	db.Whitelist.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetType("ip").SetValue("*******").SetPrivilege("rw").Save(context.Background())

	type args struct {
		clusterName string
	}
	tests := []struct {
		name    string
		args    args
		want    []string
		want1   []string
		wantErr bool
	}{
		{
			name: "test1",
			args: args{
				clusterName: objC.Name,
			},
			want:    []string{"codex-huisu.etron rw"},
			want1:   []string{"******* rw"},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1, err := GetProxyWhiteListFromDB(tt.args.clusterName)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetProxyWhiteListFromDB() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetProxyWhiteListFromDB() got = %v, want %v", got, tt.want)
			}
			if !reflect.DeepEqual(got1, tt.want1) {
				t.Errorf("GetProxyWhiteListFromDB() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

// 获取管理白名单
func TestGetManagementWhiteListFromDB(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")

	type args struct{}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, []string, []string)
	}{
		{
			name: "test1",
			before: func() {
				db, _ := mysql.Database()
				db.Configuration.Create().SetName(omodel.CONFIG_MANAGEMENT_BNS).SetValue("redis-cmanager.siod-redis,redis-control.siod-redis").Save(context.Background())
				db.Configuration.Create().SetName(omodel.CONFIG_REDIS_READONLY_BNS).SetValue("SecurityAuditEngine.hawking").Save(context.Background())
			},
			args:    args{},
			wantErr: false,
			expect: func(t *testing.T, rw, ro []string) {
				if !reflect.DeepEqual(rw, []string{"redis-cmanager.siod-redis", "redis-control.siod-redis"}) {
					t.Errorf("GetManagementWhiteListFromDB() rw = %v, want %v", rw, []string{"redis-cmanager.siod-redis", "redis-control.siod-redis"})
				}
				if !reflect.DeepEqual(ro, []string{"SecurityAuditEngine.hawking"}) {
					t.Errorf("GetManagementWhiteListFromDB() ro = %v, want %v", ro, []string{"SecurityAuditEngine.hawking"})
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			rw, ro, err := GetManagementWhiteListFromDB()
			if (err != nil) != tt.wantErr {
				t.Errorf("GetManagementWhiteListFromDB() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.expect != nil {
				tt.expect(t, rw, ro)
			}
		})
	}
}

// 校验白名单
func TestRouterWhiteListValidate(t *testing.T) {
	type args struct {
		whiteList []string
	}
	tests := []struct {
		name        string
		args        args
		wantValid   []string
		wantInvalid []string
	}{
		{
			name: "test",
			args: args{
				whiteList: []string{
					"127.0.0.1 rw",
					"10.21.178.226 w",
					"10.157.21.101 r",
					"10.157.21.102 x",
					"10.157.21.103 rr",
					"10.157.21.104 rwx",
					"10.157.21.105 wx",
					"10.157.21.106 xx",
					"10.157.21.107      rw",
					"10.157.21.108 r w",
					"10.157.21.109",
				},
			},
			wantValid: []string{
				"127.0.0.1 rw",
				"10.21.178.226 w",
				"10.157.21.101 r",
			},
			wantInvalid: []string{
				"10.157.21.102 x",
				"10.157.21.103 rr",
				"10.157.21.104 rwx",
				"10.157.21.105 wx",
				"10.157.21.106 xx",
				"10.157.21.107      rw",
				"10.157.21.108 r w",
				"10.157.21.109",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotValid, gotInvalid := RouterWhiteListValidate(tt.args.whiteList)
			if !reflect.DeepEqual(gotValid, tt.wantValid) {
				t.Errorf("RouterWhiteListValidate() gotValid = %v, want %v", gotValid, tt.wantValid)
			}
			if !reflect.DeepEqual(gotInvalid, tt.wantInvalid) {
				t.Errorf("RouterWhiteListValidate() gotInvalid = %v, want %v", gotInvalid, tt.wantInvalid)
			}
		})
	}
}
