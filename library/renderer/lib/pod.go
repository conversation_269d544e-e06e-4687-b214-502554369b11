package lib

import (
	"errors"
	"fmt"
	"strconv"
	"strings"

	"dt-common/logger"
	"dt-common/omodel"
	"dt-common/redisc"
	"redis-cmanager/library/fec"
	"redis-cmanager/library/ragent"
	"redis-cmanager/library/renderer/common"
)

// ========================================
//	           申请Pods: Redis
// ========================================

type Resource struct {
	CPU int `json:"cpu,omitempty"` // cpu 单位m(千分之一核)
	Mem int `json:"mem,omitempty"` // mem 单位MiB(兆字节)
}

type PodSetting struct {
	ProductLine     string    // 挂载产品线
	Alias           string    // 集群别名
	Image           string    // 镜像
	ImageVersion    string    // 镜像版本
	ImagePullPolicy string    // 镜像拉取策略
	Resource        *Resource // 资源配置，用于设置cpu、mem的 limit和request值
	Port            int       // 端口
	IDC             string    // 机房
	Redis           *RedisCustom
	Router          *RouterCustom
}

type RedisCustom struct {
	Index int // 索引
}

// 申请redis pods
func ApplyRedisPods(setting *PodSetting) error {
	if setting.Redis == nil {
		return fmt.Errorf("setting redis can not be nil")
	}

	podName := common.GetRedisPodName(setting.Alias, setting.Redis.Index)
	podLabelName := common.GetRedisLabelName(setting.Alias)
	appName := common.GetRedisAppName(setting.Alias)

	params := &fec.ApplyPodRequstParam{
		IsBindingBns:    false,
		NoahProduct:     setting.ProductLine,
		NoahApp:         appName,
		Owner:           common.CFG.App.Owner,
		OwnerDepartment: common.CFG.App.Department,
		Comment:         appName,
		PodNamePrefix:   podName,
		PodName:         podName,
		RestartPolicy:   "Always",
		SafeRegion:      common.CFG.SafeRegion,
		ComboCPU:        float64(setting.Resource.CPU) / 1000,
		ComboMem:        float64(setting.Resource.Mem) / 1024,
		IdcDetail: []*fec.IdcDetail{{
			Idc: common.CFG.IDC[setting.IDC],
			Num: 1,
		}},
		Containers: []*fec.Container{{
			ContainerName:   podName,
			ImageName:       setting.Image,
			ImagePullPolicy: "Always",
			ImageVersion:    setting.ImageVersion,
			EntryCommand:    "",
			ExecArgs:        []string{},
			EnvArgs: map[string]string{
				"REDIS_PORT":        fmt.Sprintf("%[1]v", setting.Port),
				"MAX_MEMORY_POLICY": "noeviction",
				"CMANAGER_URL":      fmt.Sprintf("%s.serv:%d", common.CFG.Application.BNS, common.CFG.Application.Port),
				"CMANAGER_TOKEN":    common.CFG.Application.Token,
			},
			DiskInfos: []fec.VolumeInfo{
				{
					IsEphemeral: true,
					VolumeType:  fec.PV_TYPE_SHARE,
					DiskType:    fec.NVME_SSD,
					Capacity:    10,
				},
				{
					IsEphemeral: false,
					Path:        []string{"/home/<USER>"},
					VolumeType:  fec.PV_TYPE_SHARE,
					DiskType:    fec.NVME_SSD,
					Capacity:    20,
				},
			},
		}},
		Label: map[string]string{
			common.LABEL_IDC:       setting.IDC,
			common.LABEL_COMPONENT: common.COMPONENT_REDIS,
			common.LABEL_NAME:      podLabelName,
			common.LABEL_INDEX:     fmt.Sprintf("%d", setting.Redis.Index),
		},
		PreStartSleep: 60,
		SidecarContainerResource: []*fec.SidecarContainerResource{
			{
				ContainerName: "noah-agent",
				CpuLimit:      0.4,
				MemLimit:      0.4,
			},
		},
	}

	err := fec.ApplyPods(params)
	if err != nil {
		logger.Error("failed to ApplyPod, cluster=%s, error=(%v), params=%s", setting.Alias, err, params.String())
		return err
	}

	return nil
}

// ========================================
//	          申请Pods: Sentinel
// ========================================

// 申请sentinel pods
func ApplySentinelPods(setting *PodSetting) error {
	podName := common.GetSentinelPodName(setting.Alias)
	podLabelName := common.GetSentinelLabelName(setting.Alias)
	appName := common.GetSentinelAppName(setting.Alias)

	params := &fec.ApplyPodRequstParam{
		IsBindingBns:    false,
		NoahProduct:     setting.ProductLine,
		NoahApp:         appName,
		Owner:           common.CFG.App.Owner,
		OwnerDepartment: common.CFG.App.Department,
		Comment:         appName,
		PodNamePrefix:   podName,
		PodName:         podName,
		RestartPolicy:   "Always",
		SafeRegion:      common.CFG.SafeRegion,
		ComboCPU:        float64(setting.Resource.CPU) / 1000,
		ComboMem:        float64(setting.Resource.Mem) / 1024,
		IdcDetail: []*fec.IdcDetail{{
			Idc: common.CFG.IDC[setting.IDC],
			Num: 1,
		}},
		Containers: []*fec.Container{{
			ContainerName:   podName,
			ImageName:       setting.Image,
			ImagePullPolicy: "Always",
			ImageVersion:    setting.ImageVersion,
			EntryCommand:    "",
			ExecArgs:        []string{},
			EnvArgs: map[string]string{
				"SENTINEL_PORT":  fmt.Sprintf("%[1]v", setting.Port),
				"CMANAGER_URL":   fmt.Sprintf("%s.serv:%d", common.CFG.Application.BNS, common.CFG.Application.Port),
				"CMANAGER_TOKEN": common.CFG.Application.Token,
			},
			DiskInfos: []fec.VolumeInfo{
				{
					IsEphemeral: true,
					VolumeType:  fec.PV_TYPE_SHARE,
					DiskType:    fec.NVME_SSD,
					Capacity:    10,
				},
				{
					IsEphemeral: false,
					Path:        []string{"/home/<USER>"},
					VolumeType:  fec.PV_TYPE_SHARE,
					DiskType:    fec.NVME_SSD,
					Capacity:    15,
				},
			},
		}},
		ScheduleLimit: []*fec.ScheduleLimit{
			{
				LimitType:     "label",
				LabelKey:      common.LABEL_NAME,
				LabelValue:    podLabelName,
				MaxNumOneNode: 1,
			},
		},
		Label: map[string]string{
			common.LABEL_IDC:       setting.IDC,
			common.LABEL_COMPONENT: common.COMPONENT_SENTINEL,
			common.LABEL_NAME:      podLabelName,
		},
		PreStartSleep: 60,
		SidecarContainerResource: []*fec.SidecarContainerResource{
			{
				ContainerName: "noah-agent",
				CpuLimit:      0.4,
				MemLimit:      0.4,
			},
		},
	}

	err := fec.ApplyPods(params)
	if err != nil {
		logger.Error("failed to ApplyPod, cluster=%s, error=(%v), params=%s", setting.Alias, err, params.String())
		return err
	}

	return nil
}

// ========================================
//	           申请Pods: Router
// ========================================

// 用于生成初始化路由信息为127.0.0.1:port，各分片路由信息之间用分号分隔
// router启动后会根据sentinel中信息自动修改为正确的路由信息，这里仅保证分片数对应即可
func initShardingsConfig(shardNum int) string {
	var shardsSlice []string
	for index := 0; index < shardNum; index++ {
		shardsSlice = append(shardsSlice, "127.0.0.1:7000")
	}
	return strings.Join(shardsSlice, ";")
}

// Router自定义
type RouterCustom struct {
	SentinelPort      int    //用于Router启动参数stat port设置
	ClientAuth        string //用于Router配置文件中密码设置
	ClientConnections int    //用于Router配置文件中最大客户端连接数的配置
	NumOfShards       int
}

// 申请router pods
func ApplyRouterPods(setting *PodSetting) error {
	if setting.Router == nil {
		return fmt.Errorf("setting router should not be nil")
	}

	podName := common.GetRouterPodName(setting.Alias)
	podLabelName := common.GetRouterLabelName(setting.Alias)
	appName := common.GetRouterAppName(setting.Alias)
	sentinelAppName := common.GetSentinelAppName(setting.Alias)

	params := &fec.ApplyPodRequstParam{
		IsBindingBns:    false,
		NoahProduct:     setting.ProductLine,
		NoahApp:         appName,
		Owner:           common.CFG.App.Owner,
		OwnerDepartment: common.CFG.App.Department,
		Comment:         appName,
		PodNamePrefix:   podName,
		PodName:         podName,
		RestartPolicy:   "Always",
		SafeRegion:      common.CFG.SafeRegion,
		ComboCPU:        float64(setting.Resource.CPU) / 1000,
		ComboMem:        float64(setting.Resource.Mem) / 1024,
		IdcDetail: []*fec.IdcDetail{{
			Idc: common.CFG.IDC[setting.IDC],
			Num: 1,
		}},
		Containers: []*fec.Container{{
			ContainerName:   podName,
			ImageName:       setting.Image,
			ImagePullPolicy: "Always",
			ImageVersion:    setting.ImageVersion,
			EntryCommand:    "",
			ExecArgs:        []string{},
			EnvArgs: map[string]string{
				"CLUSTER_NAME":       strings.ReplaceAll(setting.Alias, "-", "_"),
				"ROUTER_PORT":        fmt.Sprintf("%[1]v", setting.Port),
				"STAT_PORT":          fmt.Sprintf("%[1]v", setting.Port+1000),
				"SENTINEL_BNS":       fmt.Sprintf("%s.%s", sentinelAppName, setting.ProductLine),
				"SENTINEL_PORT":      fmt.Sprintf("%[1]v", setting.Router.SentinelPort),
				"CLIENT_AUTH":        setting.Router.ClientAuth,
				"CLIENT_CONNECTIONS": fmt.Sprintf("%d", common.DEFAULT_CLIENT_CONNECTIONS),
				"SHARDINGS":          initShardingsConfig(setting.Router.NumOfShards), // router环境变量 SHARDINGS 用于初始化指定路由信息
				"CMANAGER_URL":       fmt.Sprintf("%s.serv:%d", common.CFG.Application.BNS, common.CFG.Application.Port),
				"CMANAGER_TOKEN":     common.CFG.Application.Token,
			},
			DiskInfos: []fec.VolumeInfo{
				{
					IsEphemeral: true,              // 标识是否是临时存储
					VolumeType:  fec.PV_TYPE_SHARE, // 标识使用共享还是独占
					DiskType:    fec.NVME_SSD,      // 标识使用磁盘类型
					Capacity:    10,                // 标识磁盘使用量 单为Gi
				},
				{
					IsEphemeral: false,                   // 标识是否是临时存储
					Path:        []string{"/home/<USER>"}, // 持久化路径
					VolumeType:  fec.PV_TYPE_SHARE,       // 标识使用共享还是独占
					DiskType:    fec.NVME_SSD,            // 标识使用磁盘类型
					Capacity:    15,                      // 标识磁盘使用量 单为Gi
				},
			},
		}},
		ScheduleLimit: []*fec.ScheduleLimit{
			{
				LimitType:     "label",
				LabelKey:      common.LABEL_NAME,
				LabelValue:    podLabelName,
				MaxNumOneNode: 1,
			},
		},
		Label: map[string]string{
			common.LABEL_IDC:       setting.IDC,
			common.LABEL_COMPONENT: common.COMPONENT_ROUTER,
			common.LABEL_NAME:      podLabelName,
		},
		PreStartSleep: 60,
		SidecarContainerResource: []*fec.SidecarContainerResource{
			{
				ContainerName: "noah-agent",
				CpuLimit:      0.4,
				MemLimit:      0.4,
			},
		},
	}

	err := fec.ApplyPods(params)
	if err != nil {
		logger.Error("failed to ApplyPod, cluster=%s, error=(%v), params=%s", setting.Alias, err, params.String())
		return err
	}
	return nil
}

// ========================================
//	               污点Pods
// ========================================

// 给pod打污点，污点pod在渲染器看来就像是被删除了
// 主要应用在proxy替换、从库替换场景下，实现+1再-1效果
func TaintPods(podList []*fec.PodInfo, taint bool) error {
	for _, pod := range podList {
		err := fec.LabelPod(&fec.LabelPodRequstParam{
			PodName: pod.PodName,
			Labels:  map[string]string{common.LABEL_TAINT: strconv.FormatBool(taint)},
		})
		if err != nil {
			logger.Error("failed to update label dxm-redis/taint, podName=%s, error=(%v)", pod.PodName, err)
			return err
		}
	}

	return nil
}

// ========================================
//	               退还Pods
// ========================================

// 删除pod，只能退还打了污点的pod，如果删proxy需要检查是否还有qps，如果要删redis需要检查是否是从库
// 如果强制删除则不进行任何判断
func DeletePods(podList []*fec.PodInfo, force ...bool) error {
	podReturnList := []*fec.ReturnPod{}
	for _, pod := range podList {
		// 强制删除不进行任何判断，只有在集群下线时会用到
		if len(force) == 0 || !force[0] {
			if pod.Labels[common.LABEL_TAINT] != "true" {
				logger.Warn("pod %s is not a tainted pod, cannot be deleted", pod.PodName)
				continue
			}

			switch pod.Labels[common.LABEL_COMPONENT] {
			case common.COMPONENT_ROUTER:
				// 获取proxy端口
				var port int
				for _, ci := range pod.ContainerInfo {
					if ci.ContainerName == pod.PodName {
						iPort, err := strconv.Atoi(ci.EnvArgs["ROUTER_PORT"])
						if err != nil {
							logger.Warn("router pod %s can not convert ROUTER_PORT to int, env=%s", pod.PodName, ci.EnvArgs["ROUTER_PORT"])
							return err
						}
						port = iPort
						break
					}
				}
				// 调用agent接口检查流量，只判断qps，不判断长连接，调不通按无流量处理
				conns, err := ragent.ProxyConns(pod.PodIp, port)
				if err == nil && conns.QPS != 0 {
					return fmt.Errorf("proxy still has %d qps", conns.QPS)
				}
			case common.COMPONENT_REDIS:
				// 判断角色，如果调不通redis按slave处理
				info, err := redisc.Info(pod.PodIp, common.DEFAULT_REDIS_PORT, redisc.REPLICATION)
				if err == nil && info["role"] == omodel.REDIS_ROLE_MASTER {
					return fmt.Errorf("can not return master pod")
				}
			}
		}

		podReturnList = append(podReturnList, &fec.ReturnPod{
			PodName:  pod.PodName,
			IsRetain: false,
			Idc:      pod.Idc,
		})
	}

	if len(podReturnList) == 0 {
		return fmt.Errorf("no tainted pods were found that could be deleted")
	}
	if err := fec.ReturnPods(&fec.ReturnPodRequstParam{PodList: podReturnList}); err != nil {
		logger.Error("failed to delete pods, error=(%v)", err)
		return err
	}

	return nil
}

// ========================================
//	             Query Pods
// ========================================

// ListPods 查询sentinel/router pod
// 默认不包含污点Pods，需要一并返回污点Pods的话需要传递 all 参数
func ListPods(clusterName string, component string, all ...bool) ([]*fec.PodInfo, error) {
	alias := strings.ReplaceAll(clusterName, "_", "-")

	var podLabels = map[string]string{common.LABEL_COMPONENT: component}
	switch component {
	case common.COMPONENT_REDIS:
		podLabels[common.LABEL_NAME] = common.GetRedisLabelName(alias)
	case common.COMPONENT_SENTINEL:
		podLabels[common.LABEL_NAME] = common.GetSentinelLabelName(alias)
	case common.COMPONENT_ROUTER:
		podLabels[common.LABEL_NAME] = common.GetRouterLabelName(alias)
	default:
		return nil, fmt.Errorf("undefined component %s", component)
	}

	pods, err := fec.ListPods(&fec.PodListRequstParam{
		LabelSelector: podLabels,
	})
	if err != nil {
		return nil, err
	}

	// 过滤掉污点pod
	if len(all) == 0 || !all[0] {
		podList := []*fec.PodInfo{}
		for _, pod := range pods {
			taint, ok := pod.Labels[common.LABEL_TAINT]
			if ok && taint == "true" {
				logger.Debug("pod %v has taint tag, skip this pod", pod.PodName)
				continue
			}
			podList = append(podList, pod)
		}
		pods = podList
	}

	return pods, nil
}

// ========================================
//	             Formatter
// ========================================

// MapByShards 按分片索引将podList转为map
func MapByShards(pods []*fec.PodInfo) (map[int][]*fec.PodInfo, error) {
	result := map[int][]*fec.PodInfo{}
	for _, pod := range pods {
		iStr, ok := pod.Labels[common.LABEL_INDEX]
		if !ok {
			err := fmt.Errorf("pod %v has no shard label", pod.PodName)
			logger.Warn(err.Error())
			return nil, err
		}

		// str转int
		index, err := strconv.Atoi(iStr)
		if err != nil {
			logger.Warn("pod %v shard label illegal", iStr)
			return nil, err
		}

		if _, ok := result[index]; !ok {
			result[index] = []*fec.PodInfo{}
		}

		result[index] = append(result[index], pod)
	}

	return result, nil
}

// MapByIDC 按机房索引将podList转为map
func MapByIDC(pods []*fec.PodInfo) (map[string][]*fec.PodInfo, error) {
	result := map[string][]*fec.PodInfo{}
	for _, pod := range pods {
		idc, ok := pod.Labels[common.LABEL_IDC]
		if !ok {
			errMsg := fmt.Sprintf("pod %v has no idc label", pod.PodName)
			logger.Error(errMsg)
			return nil, errors.New(errMsg)
		}
		if _, ok := result[idc]; !ok {
			result[idc] = []*fec.PodInfo{}
		}

		result[idc] = append(result[idc], pod)
	}

	return result, nil
}
