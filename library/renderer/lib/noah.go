package lib

import (
	"errors"
	"fmt"
	"strings"

	"dt-common/apptree"
	"dt-common/logger"
	"dt-common/noah"
	"redis-cmanager/library/fec"
	"redis-cmanager/library/renderer/common"
)

// ensureApp 创建APP crd。如果不存在则创建，如果存在无需执行任何操作。
// 这里不能使用CreateOrUpdateAPP。原因是该app crd对象存在时不可以进行更新操作，程序执行到这里还不能确定app中instance列表，
// generateInitAppCrd中也不会构造instance列表。如果使用CreateOrUpdateAPP，将导致app中挂载的实例被清空。
func CreateApp(app *apptree.App, port int) error {
	// 判断noah中是否存在该app资源
	_, isAppExist, err := noah.GetApp(app.Product, app.Subsystem, app.Name)
	if err != nil && !strings.Contains(err.Error(), "is not exist") {
		errMsg := fmt.Sprintf("GetApp() failed. appName=[%+v] productName=[%v] subSystemName=[%v] error=[%v]", app.Name, app.Product, app.Subsystem, err)
		logger.Error(errMsg)
		return errors.New(errMsg)
	}
	// 若app不存在进行app创建
	if !isAppExist {
		logger.Info("start to create app. app=[%+v]", app)

		err := noah.CreateSubsystem(app.Product, &noah.Subsystem{Name: app.Subsystem, Alias: app.Subsystem})
		if err != nil && !(strings.Contains(err.Error(), "exist") || strings.Contains(err.Error(), "已存在")) {
			logger.Error("create subsystem failed. subsystem.Name=[%v] productName=[%v] error=[%v]", app.Subsystem, app.Product, err)
			return err
		}

		err = apptree.CreateApp(app)
		if err != nil {
			logger.Error("failed to create app, app=[%+v], error=[%v]", app, err)
			return err
		}
	}

	err = noah.UpdatePortCheckPolicy(app.Product, app.Subsystem, app.Name, &noah.HealthCheckPolicy{
		CheckType:   noah.NOAH_HEALTH_CHECK_PROCESS_PORT,
		ProcessPath: "",
		Port:        port,
		ScriptPath:  "",
	})
	if err != nil && !(strings.Contains(err.Error(), "exist") || strings.Contains(err.Error(), "已存在")) {
		errMsg := fmt.Sprintf("UpdatePortCheckPolicy() failed. appName=[%+v] productName=[%v] subSystemName=[%v] error=[%v]", app.Name, app.Product, app.Subsystem, err)
		logger.Error(errMsg)
		return errors.New(errMsg)
	}

	return nil
}

//
//  instance
//

// 构造 新的实例列表appInstance，用于更新redisApp.Spec.AppInstances
// 遍历podList,对于每一个podItem，查看app.Spec.AppInstances中是否存在该podItem对应实例，
// 如果存在，则保持原状；如果不存在，则app中新增该podItem对应实例。
// ! 因为需要HostName，此处只能用podInfo
func PodsToInstances(podList []*fec.PodInfo, port int, deployPath string, disable bool) map[string]*noah.Instance {
	appInstance := make(map[string]*noah.Instance)
	for _, podItem := range podList {
		tags := map[string]string{common.INSTANCE_TAG_KEY_DEPLOY: common.INSTANCE_TAG_VALUE_DEPLOY} // 用于sentinel部署两套监控报警
		if _, ok := common.CFG.IDC[podItem.Labels[common.LABEL_IDC]]; ok {
			tags[common.INSTANCE_TAG_KEY_REGION] = podItem.Labels[common.LABEL_IDC] // 用于smart转发规则
			tags[common.INSTANCE_TAG_KEY_REGION_SERVICE] = podItem.Labels[common.LABEL_IDC] + "s"
		}

		appInstance[podItem.PodIp] = &noah.Instance{
			IP:      podItem.PodIp,
			Disable: disable,
			RunUser: common.INSTANCE_RUN_USER,
			PortInfo: &noah.PortInfo{
				Main: port,
			},
			DeployInfo: &noah.DeployInfo{
				DeployPath: deployPath,
			},
			Tags:     tags,
			HostName: podItem.HostName,
		}
	}
	return appInstance
}

func DiffInstances(noahInstanceList []*noah.Instance, podInstanceMap map[string]*noah.Instance) (needToAddInstance, needToDeleteInstance, needToUpdateInstance []*noah.Instance) {
	// List转Map
	flagMap := make(map[string]string)
	noahInstanceMap := make(map[string]*noah.Instance)
	for _, instance := range noahInstanceList {
		flagMap[instance.IP] = "-"
		noahInstanceMap[instance.IP] = instance
	}

	// 以podInstanceMap为低，比对两个map，需要新增「+」
	for _, instance := range podInstanceMap {
		_, ok := flagMap[instance.IP]
		if !ok {
			flagMap[instance.IP] = "+"
		} else {
			flagMap[instance.IP] = "|"
		}
	}

	// +为insert，-为delete，|为update
	needToAddInstance, needToDeleteInstance, needToUpdateInstance = make([]*noah.Instance, 0), make([]*noah.Instance, 0), make([]*noah.Instance, 0)
	for ip, state := range flagMap {
		switch state {
		case "+":
			needToAddInstance = append(needToAddInstance, podInstanceMap[ip])
		case "-":
			if _, ok := noahInstanceMap[ip].Tags[common.INSTANCE_TAG_KEY_DEPLOY]; ok && noahInstanceMap[ip].Tags[common.INSTANCE_TAG_KEY_DEPLOY] == common.INSTANCE_TAG_VALUE_DEPLOY {
				needToDeleteInstance = append(needToDeleteInstance, noahInstanceMap[ip])
			}
		case "|":
			if checkInstanceNeedToUpdate(podInstanceMap[ip], noahInstanceMap[ip]) {
				// Name、实例状态、屏蔽状态不能变
				podInstanceMap[ip].Name = noahInstanceMap[ip].Name
				podInstanceMap[ip].Status = noahInstanceMap[ip].Status
				podInstanceMap[ip].Disable = noahInstanceMap[ip].Disable
				// tag取并集
				for k, v := range noahInstanceMap[ip].Tags {
					if _, ok := podInstanceMap[ip].Tags[k]; !ok {
						podInstanceMap[ip].Tags[k] = v
					}
				}
				needToUpdateInstance = append(needToUpdateInstance, podInstanceMap[ip])
			}
		default:
			logger.Error("unknown diff state: %s", state)
		}
	}

	return needToAddInstance, needToDeleteInstance, needToUpdateInstance
}

func checkInstanceNeedToUpdate(podInstance *noah.Instance, noahInstance *noah.Instance) bool {
	// pod实例上有noah实例上没有的tag，返回true
	for k := range podInstance.Tags {
		if _, ok := noahInstance.Tags[k]; !ok {
			return true
		}
	}

	return podInstance.HostName != noahInstance.HostName ||
		podInstance.RunUser != noahInstance.RunUser ||
		podInstance.PortInfo.Main != noahInstance.PortInfo.Main ||
		podInstance.DeployInfo.DeployPath != noahInstance.DeployInfo.DeployPath
}
