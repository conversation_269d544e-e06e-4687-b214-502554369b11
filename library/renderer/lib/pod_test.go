package lib

import (
	"reflect"
	"testing"

	"github.com/jarcoal/httpmock"

	"dt-common/redisc"
	"redis-cmanager/env"
	"redis-cmanager/library/fec"
	"redis-cmanager/library/renderer/common"
)

// ========================================
//	               申请：Pods
// ========================================

func TestApplyRedisPods(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")
	common.CFG = common.Config{
		App: &common.AppConfig{
			Owner:      "jiayiming_dxm",
			Department: "系统运维部",
		},
		Application: &common.Application{
			BNS:   "redis-cmanager.siod-kafka",
			Port:  8811,
			Token: "1231231231",
		},
		SafeRegion: "测试INTER",
	}

	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		setting *PodSetting
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "nil redis",
			args: args{
				setting: &PodSetting{
					ProductLine:     "siod-kafka",
					Alias:           "r3-test",
					Image:           "1",
					ImageVersion:    "v0.0.1",
					ImagePullPolicy: "Always",
					Resource: &Resource{
						CPU: 1,
						Mem: 4,
					},
					Port: 7000,
					IDC:  "hbb",
				},
			},
			wantErr: true,
		},
		{
			name: "combo code not match",
			before: func() {
				httpmock.RegisterResponder("POST", "http://10.32.140.215:8700/v2/api/billing/budget/getResourceCombos",
					httpmock.NewStringResponder(200, `{"errno":0,"errmsg":"","data":[{"id":37,"packageId":"BBC-K8KT1Pu2","parentResourceId":"r-W89zIydp","name":"均衡类-BBC1","desc":"均衡类-BBC1-2023【CPU:1核 内存:4G 数据盘:50G  】","resourceId":100056,"isAble":2,"creator":"yangpengyi_dxm","createdAt":"2022-12-10T13:59:50+08:00","updatedAt":"2023-12-07T10:41:33+08:00","unitInfo":"[{\"key\":\"cpu\",\"unit\":\"核\",\"value\":\"1\"},{\"key\":\"内存\",\"unit\":\"G\",\"value\":\"4\"},{\"key\":\"磁盘\",\"unit\":\"G\",\"value\":\"50\"}]","priceType":1,"price":68.56,"priceNoTax":0,"code":100056199037,"abilites":0,"dynamicComboId":"","idc":"","cost_price":0,"machineNums":0,"resourceName":"BBC"}]}`))
			},
			args: args{
				setting: &PodSetting{
					ProductLine:     "siod-kafka",
					Alias:           "r3-test",
					Image:           "1",
					ImageVersion:    "v0.0.1",
					ImagePullPolicy: "Always",
					Resource:        &Resource{CPU: 500, Mem: 4096},
					Port:            7000,
					IDC:             "hbb",
					Redis: &RedisCustom{
						Index: 1,
					},
				},
			},
			wantErr: true,
		},
		{
			name: "call failed",
			before: func() {
				httpmock.RegisterResponder("POST", "http://10.32.140.215:8700/v2/api/billing/budget/getResourceCombos",
					httpmock.NewStringResponder(200, `{"errno":0,"errmsg":"","data":[{"id":37,"packageId":"BBC-K8KT1Pu2","parentResourceId":"r-W89zIydp","name":"均衡类-BBC1","desc":"均衡类-BBC1-2023【CPU:1核 内存:4G 数据盘:50G  】","resourceId":100056,"isAble":2,"creator":"yangpengyi_dxm","createdAt":"2022-12-10T13:59:50+08:00","updatedAt":"2023-12-07T10:41:33+08:00","unitInfo":"[{\"key\":\"cpu\",\"unit\":\"核\",\"value\":\"1\"},{\"key\":\"内存\",\"unit\":\"G\",\"value\":\"4\"},{\"key\":\"磁盘\",\"unit\":\"G\",\"value\":\"50\"}]","priceType":1,"price":68.56,"priceNoTax":0,"code":100056199037,"abilites":0,"dynamicComboId":"","idc":"","cost_price":0,"machineNums":0,"resourceName":"BBC"}]}`))
			},
			args: args{
				setting: &PodSetting{
					ProductLine:     "siod-kafka",
					Alias:           "r3-test",
					Image:           "1",
					ImageVersion:    "v0.0.1",
					ImagePullPolicy: "Always",
					Resource:        &Resource{CPU: 1000, Mem: 4096},
					Port:            7000,
					IDC:             "hbb",
					Redis: &RedisCustom{
						Index: 1,
					},
				},
			},
			wantErr: true,
		},
		{
			name: "success",
			before: func() {
				httpmock.RegisterResponder("POST", "http://10.32.140.215:8700/v2/api/billing/budget/getResourceCombos",
					httpmock.NewStringResponder(200, `{"errno":0,"errmsg":"","data":[{"id":37,"packageId":"BBC-K8KT1Pu2","parentResourceId":"r-W89zIydp","name":"均衡类-BBC1","desc":"均衡类-BBC1-2023【CPU:1核 内存:4G 数据盘:50G  】","resourceId":100056,"isAble":2,"creator":"yangpengyi_dxm","createdAt":"2022-12-10T13:59:50+08:00","updatedAt":"2023-12-07T10:41:33+08:00","unitInfo":"[{\"key\":\"cpu\",\"unit\":\"核\",\"value\":\"1\"},{\"key\":\"内存\",\"unit\":\"G\",\"value\":\"4\"},{\"key\":\"磁盘\",\"unit\":\"G\",\"value\":\"50\"}]","priceType":1,"price":68.56,"priceNoTax":0,"code":100056199037,"abilites":0,"dynamicComboId":"","idc":"","cost_price":0,"machineNums":0,"resourceName":"BBC"}]}`))
				httpmock.RegisterResponder("POST", "http://*************:8536/fec-manager/api/v1/fec/applyPod",
					httpmock.NewStringResponder(200, "{\"errno\": 0,\"errmsg\": \"\",\"data\": \"ok\"}"))
			},
			args: args{
				setting: &PodSetting{
					ProductLine:     "siod-kafka",
					Alias:           "r3-test",
					Image:           "1",
					ImageVersion:    "v0.0.1",
					ImagePullPolicy: "Always",
					Resource:        &Resource{CPU: 1000, Mem: 4096},
					Port:            7000,
					IDC:             "hbb",
					Redis: &RedisCustom{
						Index: 1,
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := ApplyRedisPods(tt.args.setting)
			if (err != nil) != tt.wantErr {
				t.Errorf("ApplyRedisPods() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

func TestApplySentinelPods(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")
	common.CFG = common.Config{
		App: &common.AppConfig{
			Owner:      "jiayiming_dxm",
			Department: "系统运维部",
		},
		Application: &common.Application{
			BNS:   "redis-cmanager.siod-kafka",
			Port:  8811,
			Token: "1231231231",
		},
		SafeRegion: "测试INTER",
	}

	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		setting *PodSetting
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "combo code not match",
			before: func() {
				httpmock.RegisterResponder("POST", "http://10.32.140.215:8700/v2/api/billing/budget/getResourceCombos",
					httpmock.NewStringResponder(200, `{"errno":0,"errmsg":"","data":[{"id":37,"packageId":"BBC-K8KT1Pu2","parentResourceId":"r-W89zIydp","name":"均衡类-BBC1","desc":"均衡类-BBC1-2023【CPU:1核 内存:4G 数据盘:50G  】","resourceId":100056,"isAble":2,"creator":"yangpengyi_dxm","createdAt":"2022-12-10T13:59:50+08:00","updatedAt":"2023-12-07T10:41:33+08:00","unitInfo":"[{\"key\":\"cpu\",\"unit\":\"核\",\"value\":\"1\"},{\"key\":\"内存\",\"unit\":\"G\",\"value\":\"4\"},{\"key\":\"磁盘\",\"unit\":\"G\",\"value\":\"50\"}]","priceType":1,"price":68.56,"priceNoTax":0,"code":100056199037,"abilites":0,"dynamicComboId":"","idc":"","cost_price":0,"machineNums":0,"resourceName":"BBC"}]}`))
			},
			args: args{
				setting: &PodSetting{
					ProductLine:     "siod-kafka",
					Alias:           "r3-test",
					Image:           "1",
					ImageVersion:    "v0.0.1",
					ImagePullPolicy: "Always",
					Resource:        &Resource{CPU: 500, Mem: 4096},
					Port:            7000,
					IDC:             "hbb",
				},
			},
			wantErr: true,
		},
		{
			name: "call failed",
			before: func() {
				httpmock.RegisterResponder("POST", "http://10.32.140.215:8700/v2/api/billing/budget/getResourceCombos",
					httpmock.NewStringResponder(200, `{"errno":0,"errmsg":"","data":[{"id":37,"packageId":"BBC-K8KT1Pu2","parentResourceId":"r-W89zIydp","name":"均衡类-BBC1","desc":"均衡类-BBC1-2023【CPU:1核 内存:4G 数据盘:50G  】","resourceId":100056,"isAble":2,"creator":"yangpengyi_dxm","createdAt":"2022-12-10T13:59:50+08:00","updatedAt":"2023-12-07T10:41:33+08:00","unitInfo":"[{\"key\":\"cpu\",\"unit\":\"核\",\"value\":\"1\"},{\"key\":\"内存\",\"unit\":\"G\",\"value\":\"4\"},{\"key\":\"磁盘\",\"unit\":\"G\",\"value\":\"50\"}]","priceType":1,"price":68.56,"priceNoTax":0,"code":100056199037,"abilites":0,"dynamicComboId":"","idc":"","cost_price":0,"machineNums":0,"resourceName":"BBC"}]}`))
			},
			args: args{
				setting: &PodSetting{
					ProductLine:     "siod-kafka",
					Alias:           "r3-test",
					Image:           "1",
					ImageVersion:    "v0.0.1",
					ImagePullPolicy: "Always",
					Resource:        &Resource{CPU: 1000, Mem: 4096},
					Port:            7000,
					IDC:             "hbb",
				},
			},
			wantErr: true,
		},
		{
			name: "success",
			before: func() {
				httpmock.RegisterResponder("POST", "http://10.32.140.215:8700/v2/api/billing/budget/getResourceCombos",
					httpmock.NewStringResponder(200, `{"errno":0,"errmsg":"","data":[{"id":37,"packageId":"BBC-K8KT1Pu2","parentResourceId":"r-W89zIydp","name":"均衡类-BBC1","desc":"均衡类-BBC1-2023【CPU:1核 内存:4G 数据盘:50G  】","resourceId":100056,"isAble":2,"creator":"yangpengyi_dxm","createdAt":"2022-12-10T13:59:50+08:00","updatedAt":"2023-12-07T10:41:33+08:00","unitInfo":"[{\"key\":\"cpu\",\"unit\":\"核\",\"value\":\"1\"},{\"key\":\"内存\",\"unit\":\"G\",\"value\":\"4\"},{\"key\":\"磁盘\",\"unit\":\"G\",\"value\":\"50\"}]","priceType":1,"price":68.56,"priceNoTax":0,"code":100056199037,"abilites":0,"dynamicComboId":"","idc":"","cost_price":0,"machineNums":0,"resourceName":"BBC"}]}`))
				httpmock.RegisterResponder("POST", "http://*************:8536/fec-manager/api/v1/fec/applyPod",
					httpmock.NewStringResponder(200, "{\"errno\": 0,\"errmsg\": \"\",\"data\": \"ok\"}"))
			},
			args: args{
				setting: &PodSetting{
					ProductLine:     "siod-kafka",
					Alias:           "r3-test",
					Image:           "1",
					ImageVersion:    "v0.0.1",
					ImagePullPolicy: "Always",
					Resource:        &Resource{CPU: 1000, Mem: 4096},
					Port:            7000,
					IDC:             "hbb",
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := ApplySentinelPods(tt.args.setting)
			if (err != nil) != tt.wantErr {
				t.Errorf("ApplySentinelPods() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

func Test_initShardingsConfig(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")

	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		shardNum int
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, string)
	}{
		{
			name: "success",
			args: args{
				shardNum: 2,
			},
			wantErr: true,
			expect: func(t *testing.T, s string) {
				if s != "127.0.0.1:7000;127.0.0.1:7000" {
					t.Errorf("result not expected")
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			s := initShardingsConfig(tt.args.shardNum)
			// if (err != nil) != tt.wantErr {
			// 	t.Errorf("GenerateFecComboLabel() error = %v, wantErr %v", err, tt.wantErr)
			// 	return
			// }
			if tt.expect != nil {
				tt.expect(t, s)
			}
		})
	}
}

func TestApplyRouterPods(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")
	common.CFG = common.Config{
		App: &common.AppConfig{
			Owner:      "jiayiming_dxm",
			Department: "系统运维部",
		},
		IDC: map[string]string{
			"hba": "BDDX",
			"hbb": "ZZJG",
		},
		Application: &common.Application{
			BNS:   "redis-cmanager.siod-kafka",
			Port:  8811,
			Token: "1231231231",
		},
		SafeRegion: "测试INTER",
	}

	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		setting *PodSetting
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "nil router",
			args: args{
				setting: &PodSetting{
					ProductLine:     "siod-kafka",
					Alias:           "r3-test",
					Image:           "1",
					ImageVersion:    "v0.0.1",
					ImagePullPolicy: "Always",
					Resource: &Resource{
						CPU: 1,
						Mem: 4,
					},
					Port: 7000,
					IDC:  "hbb",
				},
			},
			wantErr: true,
		},
		{
			name: "combo code not match",
			before: func() {
				httpmock.RegisterResponder("POST", "http://10.32.140.215:8700/v2/api/billing/budget/getResourceCombos",
					httpmock.NewStringResponder(200, `{"errno":0,"errmsg":"","data":[{"id":37,"packageId":"BBC-K8KT1Pu2","parentResourceId":"r-W89zIydp","name":"均衡类-BBC1","desc":"均衡类-BBC1-2023【CPU:1核 内存:4G 数据盘:50G  】","resourceId":100056,"isAble":2,"creator":"yangpengyi_dxm","createdAt":"2022-12-10T13:59:50+08:00","updatedAt":"2023-12-07T10:41:33+08:00","unitInfo":"[{\"key\":\"cpu\",\"unit\":\"核\",\"value\":\"1\"},{\"key\":\"内存\",\"unit\":\"G\",\"value\":\"4\"},{\"key\":\"磁盘\",\"unit\":\"G\",\"value\":\"50\"}]","priceType":1,"price":68.56,"priceNoTax":0,"code":100056199037,"abilites":0,"dynamicComboId":"","idc":"","cost_price":0,"machineNums":0,"resourceName":"BBC"}]}`))
			},
			args: args{
				setting: &PodSetting{
					ProductLine:     "siod-kafka",
					Alias:           "r3-test",
					Image:           "1",
					ImageVersion:    "v0.0.1",
					ImagePullPolicy: "Always",
					Resource:        &Resource{CPU: 500, Mem: 4096},
					Port:            7000,
					IDC:             "hbb",
					Router: &RouterCustom{
						SentinelPort:      9001,
						ClientAuth:        "",
						ClientConnections: 10000,
						NumOfShards:       8,
					},
				},
			},
			wantErr: true,
		},
		{
			name: "call failed",
			before: func() {
				httpmock.RegisterResponder("POST", "http://10.32.140.215:8700/v2/api/billing/budget/getResourceCombos",
					httpmock.NewStringResponder(200, `{"errno":0,"errmsg":"","data":[{"id":37,"packageId":"BBC-K8KT1Pu2","parentResourceId":"r-W89zIydp","name":"均衡类-BBC1","desc":"均衡类-BBC1-2023【CPU:1核 内存:4G 数据盘:50G  】","resourceId":100056,"isAble":2,"creator":"yangpengyi_dxm","createdAt":"2022-12-10T13:59:50+08:00","updatedAt":"2023-12-07T10:41:33+08:00","unitInfo":"[{\"key\":\"cpu\",\"unit\":\"核\",\"value\":\"1\"},{\"key\":\"内存\",\"unit\":\"G\",\"value\":\"4\"},{\"key\":\"磁盘\",\"unit\":\"G\",\"value\":\"50\"}]","priceType":1,"price":68.56,"priceNoTax":0,"code":100056199037,"abilites":0,"dynamicComboId":"","idc":"","cost_price":0,"machineNums":0,"resourceName":"BBC"}]}`))
			},
			args: args{
				setting: &PodSetting{
					ProductLine:     "siod-kafka",
					Alias:           "r3-test",
					Image:           "1",
					ImageVersion:    "v0.0.1",
					ImagePullPolicy: "Always",
					Resource:        &Resource{CPU: 1000, Mem: 4096},
					Port:            7000,
					IDC:             "hbb",
					Router: &RouterCustom{
						SentinelPort:      9001,
						ClientAuth:        "",
						ClientConnections: 10000,
						NumOfShards:       8,
					},
				},
			},
			wantErr: true,
		},
		{
			name: "success",
			before: func() {
				httpmock.RegisterResponder("POST", "http://10.32.140.215:8700/v2/api/billing/budget/getResourceCombos",
					httpmock.NewStringResponder(200, `{"errno":0,"errmsg":"","data":[{"id":37,"packageId":"BBC-K8KT1Pu2","parentResourceId":"r-W89zIydp","name":"均衡类-BBC1","desc":"均衡类-BBC1-2023【CPU:1核 内存:4G 数据盘:50G  】","resourceId":100056,"isAble":2,"creator":"yangpengyi_dxm","createdAt":"2022-12-10T13:59:50+08:00","updatedAt":"2023-12-07T10:41:33+08:00","unitInfo":"[{\"key\":\"cpu\",\"unit\":\"核\",\"value\":\"1\"},{\"key\":\"内存\",\"unit\":\"G\",\"value\":\"4\"},{\"key\":\"磁盘\",\"unit\":\"G\",\"value\":\"50\"}]","priceType":1,"price":68.56,"priceNoTax":0,"code":100056199037,"abilites":0,"dynamicComboId":"","idc":"","cost_price":0,"machineNums":0,"resourceName":"BBC"}]}`))
				httpmock.RegisterResponder("POST", "http://*************:8536/fec-manager/api/v1/fec/applyPod",
					httpmock.NewStringResponder(200, "{\"errno\": 0,\"errmsg\": \"\",\"data\": \"ok\"}"))
			},
			args: args{
				setting: &PodSetting{
					ProductLine:     "siod-kafka",
					Alias:           "r3-test",
					Image:           "1",
					ImageVersion:    "v0.0.1",
					ImagePullPolicy: "Always",
					Resource:        &Resource{CPU: 1000, Mem: 4096},
					Port:            7000,
					IDC:             "hbb",
					Router: &RouterCustom{
						SentinelPort:      9001,
						ClientAuth:        "",
						ClientConnections: 10000,
						NumOfShards:       8,
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := ApplyRouterPods(tt.args.setting)
			if (err != nil) != tt.wantErr {
				t.Errorf("ApplyRouterPods() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

// ========================================
//	               污点Pods
// ========================================

func TestTaintPods(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		pods  []*fec.PodInfo
		taint bool
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "master cannot be tainted",
			before: func() {
				redisc.Mock().ExpectInfo("replication").SetVal("role:master")
			},
			args: args{
				pods: []*fec.PodInfo{
					{PodName: "r-r3-rd-test-rp", PodIp: "************", Idc: "ZZJG", Labels: map[string]string{"dxm-redis/component": common.COMPONENT_REDIS}},
				},
				taint: true,
			},
			wantErr: true,
		},
		{
			name: "通过",
			before: func() {
				redisc.Mock().ExpectInfo("replication").SetVal("role:slave")
				httpmock.RegisterResponder("POST", "http://*************:8536/fec-manager/api/v1/fec/labelPod",
					httpmock.NewStringResponder(200, "{\"errno\": 0,\"errmsg\": \"\",\"data\": \"ok\"}"))
			},
			args: args{
				pods: []*fec.PodInfo{
					{PodName: "p-r3-rd-test-rp", PodIp: "************", Idc: "ZZJG", Labels: map[string]string{"dxm-redis/component": common.COMPONENT_ROUTER}},
					{PodName: "r-r3-rd-test-sp", PodIp: "************", Idc: "ZZJG", Labels: map[string]string{"dxm-redis/component": common.COMPONENT_REDIS}},
				},
				taint: true,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := TaintPods(tt.args.pods, tt.args.taint)
			if (err != nil) != tt.wantErr {
				t.Errorf("TaintPods() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

// ========================================
//	               退还Pods
// ========================================

func TestDeletePods(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")

	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		pods  []*fec.PodInfo
		force bool
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "not tainted",
			before: func() {
				httpmock.RegisterResponder("POST", "http://*************:8536/fec-manager/api/v1/fec/returnPod",
					httpmock.NewStringResponder(200, "{\"errno\": 0,\"errmsg\": \"\",\"data\": \"ok\"}"))
			},
			args: args{
				pods: []*fec.PodInfo{
					{
						PodName: "s-r3-rd-test-rp",
						PodIp:   "************",
						Idc:     "ZZJG",
						Labels:  map[string]string{common.LABEL_COMPONENT: common.COMPONENT_SENTINEL}},
				},
				force: false,
			},
			wantErr: true,
		},
		{
			name: "proxy still has qps",
			before: func() {
				httpmock.RegisterResponder("GET", "http://*********:8433/proxy/conns?port=8001",
					httpmock.NewStringResponder(200, `{"code": "00000", "data": {"qps":40,"conns":["************"]}}`))
			},
			args: args{
				pods: []*fec.PodInfo{
					{
						PodName:     "p-test-14",
						ProductName: "siod-kafka",
						AppName:     "test-router",
						PodIp:       "*********",
						Idc:         "ZZJG",
						ComboDesc:   "",
						SafeRegion:  "test_internal",
						Owner:       "jiayiming_dxm",
						Applicant:   "",
						Status:      "Init",
						Labels: map[string]string{
							"avoid_delete_by_accident": "true",
							"dxm-redis/component":      "router",
							"dxm-redis/idc":            "hbb",
							"dxm-redis/name":           "test",
							"dxm-redis/taint":          "true",
						},
						HostName: "p-test-14.redis.fec.kj01.zzjg.dxm-int.com",
						ContainerInfo: []*fec.ContainerInfo{
							{
								ContainerName:   "p-test-14",
								ImageName:       "r.duxiaoman-int.com/siod_redis/redis-docker",
								ImageVersion:    "v2024072402",
								ImagePullPolicy: "IfNotPresent",
								Cpu:             1, Mem: 1,
								EnvArgs: map[string]string{
									"ROUTER_PORT": "8001",
								},
								DiskApplication: 0,
								MountType:       "",
								Status:          "Running",
								Privileged:      false,
							},
						},
					},
				},
				force: false,
			},
			wantErr: true,
		},
		{
			name: "redis is master",
			before: func() {
				redisc.Mock().ExpectInfo(redisc.REPLICATION).SetVal("role:master")
			},
			args: args{
				pods: []*fec.PodInfo{
					{
						PodName:     "r-test-1-a4",
						ProductName: "siod-kafka",
						AppName:     "test-redis",
						PodIp:       "*********",
						Idc:         "ZZJG",
						Labels: map[string]string{
							"avoid_delete_by_accident": "true",
							common.LABEL_COMPONENT:     common.COMPONENT_REDIS,
							"dxm-redis/idc":            "hbb",
							"dxm-redis/name":           "test",
							"dxm-redis/taint":          "true",
							"dxm-redis/index":          "1",
						},
					},
				},
				force: false,
			},
			wantErr: true,
		},
		{
			name: "force delete",
			before: func() {
				httpmock.RegisterResponder("POST", "http://*************:8536/fec-manager/api/v1/fec/returnPod",
					httpmock.NewStringResponder(200, "{\"errno\": 0,\"errmsg\": \"\",\"data\": \"ok\"}"))
			},
			args: args{
				pods: []*fec.PodInfo{
					{PodName: "s-r3-rd-test-rp", PodIp: "************", Idc: "ZZJG", Labels: map[string]string{common.LABEL_COMPONENT: common.COMPONENT_SENTINEL}},
				},
				force: true,
			},
			wantErr: false,
		},
		{
			name: "taint delete",
			before: func() {
				httpmock.RegisterResponder("POST", "http://*************:8536/fec-manager/api/v1/fec/returnPod",
					httpmock.NewStringResponder(200, "{\"errno\": 0,\"errmsg\": \"\",\"data\": \"ok\"}"))
			},
			args: args{
				pods: []*fec.PodInfo{
					{PodName: "s-r3-rd-test-rp", PodIp: "************", Idc: "ZZJG", Labels: map[string]string{common.LABEL_COMPONENT: common.COMPONENT_SENTINEL, common.LABEL_TAINT: "true"}},
				},
				force: false,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := DeletePods(tt.args.pods, tt.args.force)
			if (err != nil) != tt.wantErr {
				t.Errorf("DeletePods() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

// =========================================
//                 查询Pods
// =========================================

func TestListPods(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")

	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		alias     string
		component string
		all       []bool
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, []*fec.PodInfo)
	}{
		{
			name: "undefined component",
			args: args{
				alias:     "r3-rd-test",
				component: "test",
			},
			wantErr: true,
		},
		{
			name: "listpod failed",
			args: args{
				alias:     "r3-rd-test",
				component: common.COMPONENT_REDIS,
			},
			wantErr: true,
		},
		{
			name: "success",
			before: func() {
				httpmock.RegisterResponder("POST", "http://*************:8536/fec-manager/api/v1/fec/listPod",
					httpmock.NewStringResponder(200, "{\"errno\": 0,\"errmsg\": \"\",\"data\": [{\"id\": \"\",\"podName\": \"p-r3-rd-test-x4\",\"productName\": \"siod-kafka\",\"appName\": \"r3-rd-test-redis\",\"podIp\": \"************\",\"idc\": \"BDDX\",\"comboDesc\": \"\",\"safeRegion\": \"test_internal\",\"owner\": \"jiayiming_dxm\",\"applicant\": \"\",\"status\": \"Running\",\"labels\": {\"avoid_delete_by_accident\": \"true\",\"dxm-redis/component\": \"router\",\"dxm-redis/idc\": \"hbb\",\"dxm-redis/index\": \"0\",\"dxm-redis/name\": \"r-r3-rd-test\",\"dxm-redis/taint\": \"true\",\"fec2/app_name\": \"r3-rd-test-router\",\"fec2/bnsMount\": \"false\",\"fec2/budget_execution_id\": \"\",\"fec2/budget_number\": \"\",\"fec2/buget-checked\": \"pass\",\"fec2/cni-safe-region\": \"test_internal\",\"fec2/combo_code\": \"567756428\",\"fec2/device-id\": \"fec-PMOYhZDwP\",\"fec2/disable-inject-sidecar\": \"false\",\"fec2/fec-id\": \"fid-XxqWgKwoo\",\"fec2/idc\": \"BDDX\",\"fec2/over_budget\": \"true\",\"fec2/owner\": \"jiayiming_dxm\",\"fec2/product_name\": \"siod-kafka\",\"fec2/retain\": \"true\",\"fec2/saas\": \"redis\",\"fec2/service_level\": \"\",\"fec2/version\": \"v0.0.2\",\"fec2/white\": \"false\"},\"hostname\": \"r-r3-rd-test-0-1bj0uqn4.redis.fec.kj01.bddx.dxm-int.com\",\"scheduleLimit\": null,\"containerInfo\": [{\"containerName\": \"r-r3-rd-test-0\",\"imageName\": \"r.duxiaoman-int.com/siod_redis/redis-docker\",\"imageVersion\": \"v2024072402\",\"imagePullPolicy\": \"IfNotPresent\",\"cpu\": 1,\"mem\": 1,\"port\": null,\"entryCommand\": \"\",\"execArgs\": null,\"envArgs\": {\"CLOUD_TYPE\": \"kj\",\"CONTAINER_NAME\": \"r-r3-rd-test-0\",\"CONTAINER_TYPE\": \"main\",\"DEVICE_TYPE\": \"pod\",\"FECID\": \"fid-XxqWgKwoo\",\"FEC_OPERATOR_URL\": \"https://************:11443/fec2\",\"HAWKING_ENV\": \"container\",\"INIT_NOAH_USER\": \"redis\",\"MAX_MEMORY_POLICY\": \"volatile-lru\",\"NODE_IP\": \"\",\"POD_IP\": \"\",\"POD_NAME\": \"\",\"REDIS_PORT\": \"7000\"},\"diskApplication\": 0,\"mountType\": \"\",\"volumeMounts\": null,\"status\": \"Running\",\"privileged\": false}]},{\"id\": \"\",\"podName\": \"r-r3-rd-test-0-1b\",\"productName\": \"siod-kafka\",\"appName\": \"r3-rd-test-redis\",\"podIp\": \"************\",\"idc\": \"BDDX\",\"comboDesc\": \"\",\"safeRegion\": \"test_internal\",\"owner\": \"jiayiming_dxm\",\"applicant\": \"\",\"status\": \"Running\",\"labels\": {\"avoid_delete_by_accident\": \"true\",\"dxm-redis/component\": \"redis\",\"dxm-redis/idc\": \"hbb\",\"dxm-redis/index\": \"0\",\"dxm-redis/name\": \"r-r3-rd-test\",\"fec2/app_name\": \"r3-rd-test-redis\",\"fec2/bnsMount\": \"false\",\"fec2/budget_execution_id\": \"\",\"fec2/budget_number\": \"\",\"fec2/buget-checked\": \"pass\",\"fec2/cni-safe-region\": \"test_internal\",\"fec2/combo_code\": \"567756428\",\"fec2/device-id\": \"fec-PMOYhZDwP\",\"fec2/disable-inject-sidecar\": \"false\",\"fec2/fec-id\": \"fid-XxqWgKwoo\",\"fec2/idc\": \"BDDX\",\"fec2/over_budget\": \"true\",\"fec2/owner\": \"jiayiming_dxm\",\"fec2/product_name\": \"siod-kafka\",\"fec2/retain\": \"true\",\"fec2/saas\": \"redis\",\"fec2/service_level\": \"\",\"fec2/version\": \"v0.0.2\",\"fec2/white\": \"false\"},\"hostname\": \"r-r3-rd-test-0-1bj0uqn4.redis.fec.kj01.bddx.dxm-int.com\",\"scheduleLimit\": null,\"containerInfo\": [{\"containerName\": \"r-r3-rd-test-0\",\"imageName\": \"r.duxiaoman-int.com/siod_redis/redis-docker\",\"imageVersion\": \"v2024072402\",\"imagePullPolicy\": \"IfNotPresent\",\"cpu\": 1,\"mem\": 1,\"port\": null,\"entryCommand\": \"\",\"execArgs\": null,\"envArgs\": {\"CLOUD_TYPE\": \"kj\",\"CONTAINER_NAME\": \"r-r3-rd-test-0\",\"CONTAINER_TYPE\": \"main\",\"DEVICE_TYPE\": \"pod\",\"FECID\": \"fid-XxqWgKwoo\",\"FEC_OPERATOR_URL\": \"https://************:11443/fec2\",\"HAWKING_ENV\": \"container\",\"INIT_NOAH_USER\": \"redis\",\"MAX_MEMORY_POLICY\": \"volatile-lru\",\"NODE_IP\": \"\",\"POD_IP\": \"\",\"POD_NAME\": \"\",\"REDIS_PORT\": \"7000\"},\"diskApplication\": 0,\"mountType\": \"\",\"volumeMounts\": null,\"status\": \"Running\",\"privileged\": false}]}]}"))
			},
			args: args{
				alias:     "r3-rd-test",
				component: common.COMPONENT_REDIS,
			},
			wantErr: false,
			expect: func(t *testing.T, pods []*fec.PodInfo) {
				if len(pods) != 1 {
					t.Errorf("expect non-tainted pods, but got %d", len(pods))
				}
			},
		},
		{
			name: "return taint pod",
			args: args{
				alias:     "r3-rd-test",
				component: common.COMPONENT_SENTINEL,
				all:       []bool{true},
			},
			wantErr: false,
			expect: func(t *testing.T, pods []*fec.PodInfo) {
				if len(pods) != 2 {
					t.Errorf("expect all pods, but got %d", len(pods))
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			ret, err := ListPods(tt.args.alias, tt.args.component, tt.args.all...)
			if (err != nil) != tt.wantErr {
				t.Errorf("ListPods() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.expect != nil {
				tt.expect(t, ret)
			}
		})
	}
}

func TestMapByShards(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")

	type args struct {
		pods []*fec.PodInfo
	}
	tests := []struct {
		name    string
		args    args
		want    map[int][]*fec.PodInfo
		wantErr bool
	}{
		{
			name: "通过",
			args: args{
				pods: []*fec.PodInfo{
					{
						PodName: "test1",
						Labels: map[string]string{
							common.LABEL_INDEX: "0",
						},
					},
				},
			},
			want: map[int][]*fec.PodInfo{0: {
				{
					PodName: "test1",
					Labels: map[string]string{
						common.LABEL_INDEX: "0",
					},
				},
			}},
			wantErr: false,
		},
		{
			name: "no label",
			args: args{
				pods: []*fec.PodInfo{
					{
						PodName: "test1",
						Labels:  map[string]string{},
					},
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "no label",
			args: args{
				pods: []*fec.PodInfo{
					{
						PodName: "test1",
						Labels: map[string]string{
							common.LABEL_INDEX: "0x",
						},
					},
				},
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := MapByShards(tt.args.pods)
			if (err != nil) != tt.wantErr {
				t.Errorf("MapByShards() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("MapByShards() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMapByIDC(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")

	type args struct {
		pods []*fec.PodInfo
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
		want    map[string][]*fec.PodInfo
	}{
		{
			name: "has no idc tag",
			args: args{
				pods: []*fec.PodInfo{{PodIp: "*************"}},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "pass",
			args: args{
				pods: []*fec.PodInfo{
					{
						PodIp: "*************",
						Labels: map[string]string{
							"dxm-redis/idc": "hbb",
						},
					},
				},
			},
			want: map[string][]*fec.PodInfo{
				"hbb": {
					{
						PodIp: "*************",
						Labels: map[string]string{
							"dxm-redis/idc": "hbb",
						},
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := MapByIDC(tt.args.pods)
			if (err != nil) != tt.wantErr {
				t.Errorf("MapByIDC() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("MapByIDC() = %v, want %v", got, tt.want)
			}
		})
	}
}
