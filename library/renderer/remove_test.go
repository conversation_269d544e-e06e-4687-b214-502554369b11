package renderer

import (
	"context"
	"encoding/json"
	"net/http"
	"testing"

	"github.com/jarcoal/httpmock"

	"dt-common/mysql"
	"dt-common/redisc"
	"redis-cmanager/env"
	"redis-cmanager/library/fec"
	"redis-cmanager/library/renderer/common"
)

func TestTaintPods(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")
	objC := env.MockCluster("taint_test")
	db, _ := mysql.Database()

	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		clusterName string
		podNames    []string
		taint       bool
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
	}{
		{
			name: "success",
			before: func() {
				db.Proxy.Delete().ExecX(context.Background())
				db.Proxy.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("ivbu-plm-router.siod-redis").SetIP("**********").SetPort(8001).SetIdc("hba").ExecX(context.Background())
				db.Proxy.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("ivbu-plm-router.siod-redis").SetIP("**********").SetPort(8001).SetIdc("hba").ExecX(context.Background())
				db.Redis.Delete().ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("ivbu-plm-redis.siod-redis").SetName("ivbu_plm-server1").SetIP("***********").SetPort(7000).SetIdc("hbb").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("ivbu-plm-redis.siod-redis").SetName("ivbu_plm-server2").SetIP("*********").SetPort(7000).SetIdc("hbb").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Sentinel.Delete().ExecX(context.Background())
				db.Sentinel.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("ivbu-plm-sentinel.siod-redis").SetIP("*********").SetPort(9001).SetIdc("hba").ExecX(context.Background())

				httpmock.RegisterResponder("POST", "http://*************:8536/fec-manager/api/v1/fec/listPod",
					httpmock.NewStringResponder(200, `{"errno": 0,"errmsg": "","data": [
						{"id": "","podName": "p-ivbu-plm-c0","productName": "siod-kafka","appName": "ivbu-plm-router","podIp": "**********","idc": "ZZJG","comboDesc": "","safeRegion": "test_internal","owner": "jiayiming_dxm","applicant": "","status": "Running","labels": {"avoid_delete_by_accident": "true","dxm-redis/component": "router","dxm-redis/idc": "hbb","dxm-redis/index": "0","dxm-redis/name": "p-ivbu-plm","dxm-redis/taint": "true","fec2/app_name": "ivbu-plm-router","fec2/bnsMount": "false","fec2/budget_execution_id": "","fec2/budget_number": "","fec2/buget-checked": "pass","fec2/cni-safe-region": "test_internal","fec2/combo_code": "567756428","fec2/device-id": "fec-PMOYhZDwP","fec2/disable-inject-sidecar": "false","fec2/fec-id": "fid-XxqWgKwoo","fec2/idc": "BDDX","fec2/over_budget": "true","fec2/owner": "jiayiming_dxm","fec2/product_name": "siod-kafka","fec2/retain": "true","fec2/saas": "redis","fec2/service_level": "","fec2/version": "v0.0.2","fec2/white": "false"},"hostname": "r-ivbu-plm-0-1bj0uqn4.redis.fec.kj01.bddx.dxm-int.com","scheduleLimit": null,"containerInfo": [{"containerName": "r-ivbu-plm-0","imageName": "r.duxiaoman-int.com/siod_redis/redis-docker","imageVersion": "v2024072402","imagePullPolicy": "IfNotPresent","cpu": 1,"mem": 1,"port": null,"entryCommand": "","execArgs": null,"envArgs": {"CLOUD_TYPE": "kj","CONTAINER_NAME": "r-ivbu-plm-0","CONTAINER_TYPE": "main","DEVICE_TYPE": "pod","FECID": "fid-XxqWgKwoo","FEC_OPERATOR_URL": "https://************:11443/fec2","HAWKING_ENV": "container","INIT_NOAH_USER": "redis","MAX_MEMORY_POLICY": "volatile-lru","NODE_IP": "","POD_IP": "","POD_NAME": "","REDIS_PORT": "7000"},"diskApplication": 0,"mountType": "","volumeMounts": null,"status": "Running","privileged": false}]},
						{"id": "","podName": "p-ivbu-plm-dz","productName": "siod-kafka","appName": "ivbu-plm-router","podIp": "**********","idc": "ZZJG","comboDesc": "","safeRegion": "test_internal","owner": "jiayiming_dxm","applicant": "","status": "Running","labels": {"avoid_delete_by_accident": "true","dxm-redis/component": "router","dxm-redis/idc": "hbb","dxm-redis/index": "0","dxm-redis/name": "p-ivbu-plm","fec2/app_name": "ivbu-plm-router","fec2/bnsMount": "false","fec2/budget_execution_id": "","fec2/budget_number": "","fec2/buget-checked": "pass","fec2/cni-safe-region": "test_internal","fec2/combo_code": "567756428","fec2/device-id": "fec-PMOYhZDwP","fec2/disable-inject-sidecar": "false","fec2/fec-id": "fid-XxqWgKwoo","fec2/idc": "BDDX","fec2/over_budget": "true","fec2/owner": "jiayiming_dxm","fec2/product_name": "siod-kafka","fec2/retain": "true","fec2/saas": "redis","fec2/service_level": "","fec2/version": "v0.0.2","fec2/white": "false"},"hostname": "r-ivbu-plm-0-1bj0uqn4.redis.fec.kj01.bddx.dxm-int.com","scheduleLimit": null,"containerInfo": [{"containerName": "r-ivbu-plm-0","imageName": "r.duxiaoman-int.com/siod_redis/redis-docker","imageVersion": "v2024072402","imagePullPolicy": "IfNotPresent","cpu": 1,"mem": 1,"port": null,"entryCommand": "","execArgs": null,"envArgs": {"CLOUD_TYPE": "kj","CONTAINER_NAME": "r-ivbu-plm-0","CONTAINER_TYPE": "main","DEVICE_TYPE": "pod","FECID": "fid-XxqWgKwoo","FEC_OPERATOR_URL": "https://************:11443/fec2","HAWKING_ENV": "container","INIT_NOAH_USER": "redis","MAX_MEMORY_POLICY": "volatile-lru","NODE_IP": "","POD_IP": "","POD_NAME": "","REDIS_PORT": "7000"},"diskApplication": 0,"mountType": "","volumeMounts": null,"status": "Running","privileged": false}]},
						{"id": "","podName": "r-ivbu-plm-0-ly","productName": "siod-kafka","appName": "ivbu-plm-redis","podIp": "***********","idc": "ZZJG","comboDesc": "","safeRegion": "test_internal","owner": "jiayiming_dxm","applicant": "","status": "Running","labels": {"avoid_delete_by_accident": "true","dxm-redis/component": "redis","dxm-redis/idc": "hbb","dxm-redis/index": "0","dxm-redis/name": "r-ivbu-plm","fec2/app_name": "ivbu-plm-redis","fec2/bnsMount": "false","fec2/budget_execution_id": "","fec2/budget_number": "","fec2/buget-checked": "pass","fec2/cni-safe-region": "test_internal","fec2/combo_code": "567756428","fec2/device-id": "fec-PMOYhZDwP","fec2/disable-inject-sidecar": "false","fec2/fec-id": "fid-XxqWgKwoo","fec2/idc": "BDDX","fec2/over_budget": "true","fec2/owner": "jiayiming_dxm","fec2/product_name": "siod-kafka","fec2/retain": "true","fec2/saas": "redis","fec2/service_level": "","fec2/version": "v0.0.2","fec2/white": "false"},"hostname": "r-ivbu-plm-0-1bj0uqn4.redis.fec.kj01.bddx.dxm-int.com","scheduleLimit": null,"containerInfo": [{"containerName": "r-ivbu-plm-0","imageName": "r.duxiaoman-int.com/siod_redis/redis-docker","imageVersion": "v2024072402","imagePullPolicy": "IfNotPresent","cpu": 1,"mem": 1,"port": null,"entryCommand": "","execArgs": null,"envArgs": {"CLOUD_TYPE": "kj","CONTAINER_NAME": "r-ivbu-plm-0","CONTAINER_TYPE": "main","DEVICE_TYPE": "pod","FECID": "fid-XxqWgKwoo","FEC_OPERATOR_URL": "https://************:11443/fec2","HAWKING_ENV": "container","INIT_NOAH_USER": "redis","MAX_MEMORY_POLICY": "volatile-lru","NODE_IP": "","POD_IP": "","POD_NAME": "","REDIS_PORT": "7000"},"diskApplication": 0,"mountType": "","volumeMounts": null,"status": "Running","privileged": false}]},
						{"id": "","podName": "r-ivbu-plm-1-nu","productName": "siod-kafka","appName": "ivbu-plm-redis","podIp": "*********","idc": "ZZJG","comboDesc": "","safeRegion": "test_internal","owner": "jiayiming_dxm","applicant": "","status": "Running","labels": {"avoid_delete_by_accident": "true","dxm-redis/component": "redis","dxm-redis/idc": "hbb","dxm-redis/index": "0","dxm-redis/name": "r-ivbu-plm","fec2/app_name": "ivbu-plm-redis","fec2/bnsMount": "false","fec2/budget_execution_id": "","fec2/budget_number": "","fec2/buget-checked": "pass","fec2/cni-safe-region": "test_internal","fec2/combo_code": "567756428","fec2/device-id": "fec-PMOYhZDwP","fec2/disable-inject-sidecar": "false","fec2/fec-id": "fid-XxqWgKwoo","fec2/idc": "BDDX","fec2/over_budget": "true","fec2/owner": "jiayiming_dxm","fec2/product_name": "siod-kafka","fec2/retain": "true","fec2/saas": "redis","fec2/service_level": "","fec2/version": "v0.0.2","fec2/white": "false"},"hostname": "r-ivbu-plm-0-1bj0uqn4.redis.fec.kj01.bddx.dxm-int.com","scheduleLimit": null,"containerInfo": [{"containerName": "r-ivbu-plm-0","imageName": "r.duxiaoman-int.com/siod_redis/redis-docker","imageVersion": "v2024072402","imagePullPolicy": "IfNotPresent","cpu": 1,"mem": 1,"port": null,"entryCommand": "","execArgs": null,"envArgs": {"CLOUD_TYPE": "kj","CONTAINER_NAME": "r-ivbu-plm-0","CONTAINER_TYPE": "main","DEVICE_TYPE": "pod","FECID": "fid-XxqWgKwoo","FEC_OPERATOR_URL": "https://************:11443/fec2","HAWKING_ENV": "container","INIT_NOAH_USER": "redis","MAX_MEMORY_POLICY": "volatile-lru","NODE_IP": "","POD_IP": "","POD_NAME": "","REDIS_PORT": "7000"},"diskApplication": 0,"mountType": "","volumeMounts": null,"status": "Running","privileged": false}]},
						{"id": "","podName": "s-ivbu-plm-h8","productName": "siod-kafka","appName": "ivbu-plm-sentinel","podIp": "*********","idc": "ZZJG","comboDesc": "","safeRegion": "test_internal","owner": "jiayiming_dxm","applicant": "","status": "Running","labels": {"avoid_delete_by_accident": "true","dxm-redis/component": "sentinel","dxm-redis/idc": "hbb","dxm-redis/index": "0","dxm-redis/name": "s-ivbu-plm","fec2/app_name": "ivbu-plm-sentinel","fec2/bnsMount": "false","fec2/budget_execution_id": "","fec2/budget_number": "","fec2/buget-checked": "pass","fec2/cni-safe-region": "test_internal","fec2/combo_code": "567756428","fec2/device-id": "fec-PMOYhZDwP","fec2/disable-inject-sidecar": "false","fec2/fec-id": "fid-XxqWgKwoo","fec2/idc": "BDDX","fec2/over_budget": "true","fec2/owner": "jiayiming_dxm","fec2/product_name": "siod-kafka","fec2/retain": "true","fec2/saas": "redis","fec2/service_level": "","fec2/version": "v0.0.2","fec2/white": "false"},"hostname": "r-ivbu-plm-0-1bj0uqn4.redis.fec.kj01.bddx.dxm-int.com","scheduleLimit": null,"containerInfo": [{"containerName": "r-ivbu-plm-0","imageName": "r.duxiaoman-int.com/siod_redis/redis-docker","imageVersion": "v2024072402","imagePullPolicy": "IfNotPresent","cpu": 1,"mem": 1,"port": null,"entryCommand": "","execArgs": null,"envArgs": {"CLOUD_TYPE": "kj","CONTAINER_NAME": "r-ivbu-plm-0","CONTAINER_TYPE": "main","DEVICE_TYPE": "pod","FECID": "fid-XxqWgKwoo","FEC_OPERATOR_URL": "https://************:11443/fec2","HAWKING_ENV": "container","INIT_NOAH_USER": "redis","MAX_MEMORY_POLICY": "volatile-lru","NODE_IP": "","POD_IP": "","POD_NAME": "","REDIS_PORT": "7000"},"diskApplication": 0,"mountType": "","volumeMounts": null,"status": "Running","privileged": false}]}
					]}`),
				)
				redisc.Mock().ExpectInfo("replication").SetVal("role:slave")
				redisc.Mock().ExpectInfo("replication").SetVal("role:slave")
				httpmock.RegisterResponder("POST", "http://*************:8536/fec-manager/api/v1/fec/labelPod",
					httpmock.NewStringResponder(200, "{\"errno\": 0,\"errmsg\": \"\",\"data\": \"ok\"}"))
			},
			args: args{
				clusterName: objC.Name,
				podNames: []string{
					"p-ivbu-plm-c0",
					"p-ivbu-plm-dz",
					"r-ivbu-plm-0-ly",
					"r-ivbu-plm-1-nu",
					"s-ivbu-plm-h8",
				},
				taint: true,
			},
			wantErr: false,
		},
		{
			name: "untaint",
			before: func() {
				db.Proxy.Delete().ExecX(context.Background())
				db.Proxy.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("ivbu-plm-router.siod-redis").SetIP("**********").SetPort(8001).SetIdc("hba").ExecX(context.Background())
				db.Proxy.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("ivbu-plm-router.siod-redis").SetIP("**********").SetPort(8001).SetIdc("hba").SetTaint(true).ExecX(context.Background())
				db.Redis.Delete().ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("ivbu-plm-redis.siod-redis").SetName("ivbu_plm-server1").SetIP("***********").SetPort(7000).SetIdc("hbb").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("ivbu-plm-redis.siod-redis").SetName("ivbu_plm-server2").SetIP("*********").SetPort(7000).SetIdc("hbb").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Sentinel.Delete().ExecX(context.Background())
				db.Sentinel.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("ivbu-plm-sentinel.siod-redis").SetIP("*********").SetPort(9001).SetIdc("hba").ExecX(context.Background())

				httpmock.RegisterResponder("POST", "http://*************:8536/fec-manager/api/v1/fec/listPod",
					httpmock.NewStringResponder(200, `{"errno": 0,"errmsg": "","data": [
						{"id": "","podName": "p-ivbu-plm-c0","productName": "siod-kafka","appName": "ivbu-plm-router","podIp": "**********","idc": "ZZJG","comboDesc": "","safeRegion": "test_internal","owner": "jiayiming_dxm","applicant": "","status": "Running","labels": {"avoid_delete_by_accident": "true","dxm-redis/component": "router","dxm-redis/idc": "hbb","dxm-redis/index": "0","dxm-redis/name": "p-ivbu-plm","dxm-redis/taint": "true","fec2/app_name": "ivbu-plm-router","fec2/bnsMount": "false","fec2/budget_execution_id": "","fec2/budget_number": "","fec2/buget-checked": "pass","fec2/cni-safe-region": "test_internal","fec2/combo_code": "567756428","fec2/device-id": "fec-PMOYhZDwP","fec2/disable-inject-sidecar": "false","fec2/fec-id": "fid-XxqWgKwoo","fec2/idc": "BDDX","fec2/over_budget": "true","fec2/owner": "jiayiming_dxm","fec2/product_name": "siod-kafka","fec2/retain": "true","fec2/saas": "redis","fec2/service_level": "","fec2/version": "v0.0.2","fec2/white": "false"},"hostname": "r-ivbu-plm-0-1bj0uqn4.redis.fec.kj01.bddx.dxm-int.com","scheduleLimit": null,"containerInfo": [{"containerName": "r-ivbu-plm-0","imageName": "r.duxiaoman-int.com/siod_redis/redis-docker","imageVersion": "v2024072402","imagePullPolicy": "IfNotPresent","cpu": 1,"mem": 1,"port": null,"entryCommand": "","execArgs": null,"envArgs": {"CLOUD_TYPE": "kj","CONTAINER_NAME": "r-ivbu-plm-0","CONTAINER_TYPE": "main","DEVICE_TYPE": "pod","FECID": "fid-XxqWgKwoo","FEC_OPERATOR_URL": "https://************:11443/fec2","HAWKING_ENV": "container","INIT_NOAH_USER": "redis","MAX_MEMORY_POLICY": "volatile-lru","NODE_IP": "","POD_IP": "","POD_NAME": "","REDIS_PORT": "7000"},"diskApplication": 0,"mountType": "","volumeMounts": null,"status": "Running","privileged": false}]},
						{"id": "","podName": "p-ivbu-plm-dz","productName": "siod-kafka","appName": "ivbu-plm-router","podIp": "**********","idc": "ZZJG","comboDesc": "","safeRegion": "test_internal","owner": "jiayiming_dxm","applicant": "","status": "Running","labels": {"avoid_delete_by_accident": "true","dxm-redis/component": "router","dxm-redis/taint": "true","dxm-redis/idc": "hbb","dxm-redis/index": "0","dxm-redis/name": "p-ivbu-plm","fec2/app_name": "ivbu-plm-router","fec2/bnsMount": "false","fec2/budget_execution_id": "","fec2/budget_number": "","fec2/buget-checked": "pass","fec2/cni-safe-region": "test_internal","fec2/combo_code": "567756428","fec2/device-id": "fec-PMOYhZDwP","fec2/disable-inject-sidecar": "false","fec2/fec-id": "fid-XxqWgKwoo","fec2/idc": "BDDX","fec2/over_budget": "true","fec2/owner": "jiayiming_dxm","fec2/product_name": "siod-kafka","fec2/retain": "true","fec2/saas": "redis","fec2/service_level": "","fec2/version": "v0.0.2","fec2/white": "false"},"hostname": "r-ivbu-plm-0-1bj0uqn4.redis.fec.kj01.bddx.dxm-int.com","scheduleLimit": null,"containerInfo": [{"containerName": "r-ivbu-plm-0","imageName": "r.duxiaoman-int.com/siod_redis/redis-docker","imageVersion": "v2024072402","imagePullPolicy": "IfNotPresent","cpu": 1,"mem": 1,"port": null,"entryCommand": "","execArgs": null,"envArgs": {"CLOUD_TYPE": "kj","CONTAINER_NAME": "r-ivbu-plm-0","CONTAINER_TYPE": "main","DEVICE_TYPE": "pod","FECID": "fid-XxqWgKwoo","FEC_OPERATOR_URL": "https://************:11443/fec2","HAWKING_ENV": "container","INIT_NOAH_USER": "redis","MAX_MEMORY_POLICY": "volatile-lru","NODE_IP": "","POD_IP": "","POD_NAME": "","REDIS_PORT": "7000"},"diskApplication": 0,"mountType": "","volumeMounts": null,"status": "Running","privileged": false}]},
						{"id": "","podName": "r-ivbu-plm-0-ly","productName": "siod-kafka","appName": "ivbu-plm-redis","podIp": "***********","idc": "ZZJG","comboDesc": "","safeRegion": "test_internal","owner": "jiayiming_dxm","applicant": "","status": "Running","labels": {"avoid_delete_by_accident": "true","dxm-redis/component": "redis","dxm-redis/idc": "hbb","dxm-redis/index": "0","dxm-redis/name": "r-ivbu-plm","fec2/app_name": "ivbu-plm-redis","fec2/bnsMount": "false","fec2/budget_execution_id": "","fec2/budget_number": "","fec2/buget-checked": "pass","fec2/cni-safe-region": "test_internal","fec2/combo_code": "567756428","fec2/device-id": "fec-PMOYhZDwP","fec2/disable-inject-sidecar": "false","fec2/fec-id": "fid-XxqWgKwoo","fec2/idc": "BDDX","fec2/over_budget": "true","fec2/owner": "jiayiming_dxm","fec2/product_name": "siod-kafka","fec2/retain": "true","fec2/saas": "redis","fec2/service_level": "","fec2/version": "v0.0.2","fec2/white": "false"},"hostname": "r-ivbu-plm-0-1bj0uqn4.redis.fec.kj01.bddx.dxm-int.com","scheduleLimit": null,"containerInfo": [{"containerName": "r-ivbu-plm-0","imageName": "r.duxiaoman-int.com/siod_redis/redis-docker","imageVersion": "v2024072402","imagePullPolicy": "IfNotPresent","cpu": 1,"mem": 1,"port": null,"entryCommand": "","execArgs": null,"envArgs": {"CLOUD_TYPE": "kj","CONTAINER_NAME": "r-ivbu-plm-0","CONTAINER_TYPE": "main","DEVICE_TYPE": "pod","FECID": "fid-XxqWgKwoo","FEC_OPERATOR_URL": "https://************:11443/fec2","HAWKING_ENV": "container","INIT_NOAH_USER": "redis","MAX_MEMORY_POLICY": "volatile-lru","NODE_IP": "","POD_IP": "","POD_NAME": "","REDIS_PORT": "7000"},"diskApplication": 0,"mountType": "","volumeMounts": null,"status": "Running","privileged": false}]},
						{"id": "","podName": "r-ivbu-plm-1-nu","productName": "siod-kafka","appName": "ivbu-plm-redis","podIp": "*********","idc": "ZZJG","comboDesc": "","safeRegion": "test_internal","owner": "jiayiming_dxm","applicant": "","status": "Running","labels": {"avoid_delete_by_accident": "true","dxm-redis/component": "redis","dxm-redis/idc": "hbb","dxm-redis/index": "0","dxm-redis/name": "r-ivbu-plm","fec2/app_name": "ivbu-plm-redis","fec2/bnsMount": "false","fec2/budget_execution_id": "","fec2/budget_number": "","fec2/buget-checked": "pass","fec2/cni-safe-region": "test_internal","fec2/combo_code": "567756428","fec2/device-id": "fec-PMOYhZDwP","fec2/disable-inject-sidecar": "false","fec2/fec-id": "fid-XxqWgKwoo","fec2/idc": "BDDX","fec2/over_budget": "true","fec2/owner": "jiayiming_dxm","fec2/product_name": "siod-kafka","fec2/retain": "true","fec2/saas": "redis","fec2/service_level": "","fec2/version": "v0.0.2","fec2/white": "false"},"hostname": "r-ivbu-plm-0-1bj0uqn4.redis.fec.kj01.bddx.dxm-int.com","scheduleLimit": null,"containerInfo": [{"containerName": "r-ivbu-plm-0","imageName": "r.duxiaoman-int.com/siod_redis/redis-docker","imageVersion": "v2024072402","imagePullPolicy": "IfNotPresent","cpu": 1,"mem": 1,"port": null,"entryCommand": "","execArgs": null,"envArgs": {"CLOUD_TYPE": "kj","CONTAINER_NAME": "r-ivbu-plm-0","CONTAINER_TYPE": "main","DEVICE_TYPE": "pod","FECID": "fid-XxqWgKwoo","FEC_OPERATOR_URL": "https://************:11443/fec2","HAWKING_ENV": "container","INIT_NOAH_USER": "redis","MAX_MEMORY_POLICY": "volatile-lru","NODE_IP": "","POD_IP": "","POD_NAME": "","REDIS_PORT": "7000"},"diskApplication": 0,"mountType": "","volumeMounts": null,"status": "Running","privileged": false}]},
						{"id": "","podName": "s-ivbu-plm-h8","productName": "siod-kafka","appName": "ivbu-plm-sentinel","podIp": "*********","idc": "ZZJG","comboDesc": "","safeRegion": "test_internal","owner": "jiayiming_dxm","applicant": "","status": "Running","labels": {"avoid_delete_by_accident": "true","dxm-redis/component": "sentinel","dxm-redis/idc": "hbb","dxm-redis/index": "0","dxm-redis/name": "s-ivbu-plm","fec2/app_name": "ivbu-plm-sentinel","fec2/bnsMount": "false","fec2/budget_execution_id": "","fec2/budget_number": "","fec2/buget-checked": "pass","fec2/cni-safe-region": "test_internal","fec2/combo_code": "567756428","fec2/device-id": "fec-PMOYhZDwP","fec2/disable-inject-sidecar": "false","fec2/fec-id": "fid-XxqWgKwoo","fec2/idc": "BDDX","fec2/over_budget": "true","fec2/owner": "jiayiming_dxm","fec2/product_name": "siod-kafka","fec2/retain": "true","fec2/saas": "redis","fec2/service_level": "","fec2/version": "v0.0.2","fec2/white": "false"},"hostname": "r-ivbu-plm-0-1bj0uqn4.redis.fec.kj01.bddx.dxm-int.com","scheduleLimit": null,"containerInfo": [{"containerName": "r-ivbu-plm-0","imageName": "r.duxiaoman-int.com/siod_redis/redis-docker","imageVersion": "v2024072402","imagePullPolicy": "IfNotPresent","cpu": 1,"mem": 1,"port": null,"entryCommand": "","execArgs": null,"envArgs": {"CLOUD_TYPE": "kj","CONTAINER_NAME": "r-ivbu-plm-0","CONTAINER_TYPE": "main","DEVICE_TYPE": "pod","FECID": "fid-XxqWgKwoo","FEC_OPERATOR_URL": "https://************:11443/fec2","HAWKING_ENV": "container","INIT_NOAH_USER": "redis","MAX_MEMORY_POLICY": "volatile-lru","NODE_IP": "","POD_IP": "","POD_NAME": "","REDIS_PORT": "7000"},"diskApplication": 0,"mountType": "","volumeMounts": null,"status": "Running","privileged": false}]}
					]}`),
				)
				redisc.Mock().ExpectInfo("replication").SetVal("role:slave")
				redisc.Mock().ExpectInfo("replication").SetVal("role:slave")
				httpmock.RegisterResponder("POST", "http://*************:8536/fec-manager/api/v1/fec/labelPod",
					httpmock.NewStringResponder(200, "{\"errno\": 0,\"errmsg\": \"\",\"data\": \"ok\"}"))
			},
			args: args{
				clusterName: objC.Name,
				podNames: []string{
					"p-ivbu-plm-c0",
					"p-ivbu-plm-dz",
					"r-ivbu-plm-0-ly",
					"r-ivbu-plm-1-nu",
					"s-ivbu-plm-h8",
				},
				taint: false,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := TaintPods(tt.args.clusterName, tt.args.podNames, tt.args.taint)
			if (err != nil) != tt.wantErr {
				t.Errorf("TaintPods() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestDeletePods(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")
	common.Init(&common.Config{
		App: &common.AppConfig{
			ProductLine: "siod-redis",
		},
		IDC: map[string]string{
			"hba": "ZZJG",
			"hbb": "ZZJG",
			"hbc": "ZZJG",
		},
	})

	objC := env.MockCluster("delete_test")
	db, _ := mysql.Database()

	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		clusterName string
		podNames    []string
		force       bool
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
	}{
		{
			name: "not taint",
			before: func() {
				db.Proxy.Delete().ExecX(context.Background())
				db.Proxy.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("ivbu-plm-router.siod-redis").SetIP("**********").SetPort(8001).SetIdc("hba").ExecX(context.Background())
				db.Proxy.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("ivbu-plm-router.siod-redis").SetIP("**********").SetPort(8001).SetIdc("hba").ExecX(context.Background())
				db.Redis.Delete().ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("ivbu-plm-redis.siod-redis").SetName("ivbu_plm-server1").SetIP("***********").SetPort(7000).SetIdc("hbb").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("ivbu-plm-redis.siod-redis").SetName("ivbu_plm-server2").SetIP("*********").SetPort(7000).SetIdc("hbb").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Sentinel.Delete().ExecX(context.Background())
				db.Sentinel.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("ivbu-plm-sentinel.siod-redis").SetIP("*********").SetPort(9001).SetIdc("hba").ExecX(context.Background())

				httpmock.RegisterResponder("POST", "http://*************:8536/fec-manager/api/v1/fec/listPod",
					httpmock.NewStringResponder(200, `{"errno": 0,"errmsg": "","data": [
						{"id": "","podName": "p-ivbu-plm-c0","productName": "siod-kafka","appName": "ivbu-plm-router","podIp": "**********","idc": "ZZJG","comboDesc": "","safeRegion": "test_internal","owner": "jiayiming_dxm","applicant": "","status": "Running","labels": {"avoid_delete_by_accident": "true","dxm-redis/component": "router","dxm-redis/idc": "hbb","dxm-redis/index": "0","dxm-redis/name": "p-ivbu-plm","dxm-redis/taint": "true","fec2/app_name": "ivbu-plm-router","fec2/bnsMount": "false","fec2/budget_execution_id": "","fec2/budget_number": "","fec2/buget-checked": "pass","fec2/cni-safe-region": "test_internal","fec2/combo_code": "567756428","fec2/device-id": "fec-PMOYhZDwP","fec2/disable-inject-sidecar": "false","fec2/fec-id": "fid-XxqWgKwoo","fec2/idc": "BDDX","fec2/over_budget": "true","fec2/owner": "jiayiming_dxm","fec2/product_name": "siod-kafka","fec2/retain": "true","fec2/saas": "redis","fec2/service_level": "","fec2/version": "v0.0.2","fec2/white": "false"},"hostname": "r-ivbu-plm-0-1bj0uqn4.redis.fec.kj01.bddx.dxm-int.com","scheduleLimit": null,"containerInfo": [{"containerName": "r-ivbu-plm-0","imageName": "r.duxiaoman-int.com/siod_redis/redis-docker","imageVersion": "v2024072402","imagePullPolicy": "IfNotPresent","cpu": 1,"mem": 1,"port": null,"entryCommand": "","execArgs": null,"envArgs": {"CLOUD_TYPE": "kj","CONTAINER_NAME": "r-ivbu-plm-0","CONTAINER_TYPE": "main","DEVICE_TYPE": "pod","FECID": "fid-XxqWgKwoo","FEC_OPERATOR_URL": "https://************:11443/fec2","HAWKING_ENV": "container","INIT_NOAH_USER": "redis","MAX_MEMORY_POLICY": "volatile-lru","NODE_IP": "","POD_IP": "","POD_NAME": "","REDIS_PORT": "7000"},"diskApplication": 0,"mountType": "","volumeMounts": null,"status": "Running","privileged": false}]},
						{"id": "","podName": "p-ivbu-plm-dz","productName": "siod-kafka","appName": "ivbu-plm-router","podIp": "**********","idc": "ZZJG","comboDesc": "","safeRegion": "test_internal","owner": "jiayiming_dxm","applicant": "","status": "Running","labels": {"avoid_delete_by_accident": "true","dxm-redis/component": "router","dxm-redis/idc": "hbb","dxm-redis/index": "0","dxm-redis/name": "p-ivbu-plm","fec2/app_name": "ivbu-plm-router","fec2/bnsMount": "false","fec2/budget_execution_id": "","fec2/budget_number": "","fec2/buget-checked": "pass","fec2/cni-safe-region": "test_internal","fec2/combo_code": "567756428","fec2/device-id": "fec-PMOYhZDwP","fec2/disable-inject-sidecar": "false","fec2/fec-id": "fid-XxqWgKwoo","fec2/idc": "BDDX","fec2/over_budget": "true","fec2/owner": "jiayiming_dxm","fec2/product_name": "siod-kafka","fec2/retain": "true","fec2/saas": "redis","fec2/service_level": "","fec2/version": "v0.0.2","fec2/white": "false"},"hostname": "r-ivbu-plm-0-1bj0uqn4.redis.fec.kj01.bddx.dxm-int.com","scheduleLimit": null,"containerInfo": [{"containerName": "r-ivbu-plm-0","imageName": "r.duxiaoman-int.com/siod_redis/redis-docker","imageVersion": "v2024072402","imagePullPolicy": "IfNotPresent","cpu": 1,"mem": 1,"port": null,"entryCommand": "","execArgs": null,"envArgs": {"CLOUD_TYPE": "kj","CONTAINER_NAME": "r-ivbu-plm-0","CONTAINER_TYPE": "main","DEVICE_TYPE": "pod","FECID": "fid-XxqWgKwoo","FEC_OPERATOR_URL": "https://************:11443/fec2","HAWKING_ENV": "container","INIT_NOAH_USER": "redis","MAX_MEMORY_POLICY": "volatile-lru","NODE_IP": "","POD_IP": "","POD_NAME": "","REDIS_PORT": "7000"},"diskApplication": 0,"mountType": "","volumeMounts": null,"status": "Running","privileged": false}]},
						{"id": "","podName": "r-ivbu-plm-0-ly","productName": "siod-kafka","appName": "ivbu-plm-redis","podIp": "***********","idc": "ZZJG","comboDesc": "","safeRegion": "test_internal","owner": "jiayiming_dxm","applicant": "","status": "Running","labels": {"avoid_delete_by_accident": "true","dxm-redis/component": "redis","dxm-redis/idc": "hbb","dxm-redis/index": "0","dxm-redis/name": "r-ivbu-plm","fec2/app_name": "ivbu-plm-redis","fec2/bnsMount": "false","fec2/budget_execution_id": "","fec2/budget_number": "","fec2/buget-checked": "pass","fec2/cni-safe-region": "test_internal","fec2/combo_code": "567756428","fec2/device-id": "fec-PMOYhZDwP","fec2/disable-inject-sidecar": "false","fec2/fec-id": "fid-XxqWgKwoo","fec2/idc": "BDDX","fec2/over_budget": "true","fec2/owner": "jiayiming_dxm","fec2/product_name": "siod-kafka","fec2/retain": "true","fec2/saas": "redis","fec2/service_level": "","fec2/version": "v0.0.2","fec2/white": "false"},"hostname": "r-ivbu-plm-0-1bj0uqn4.redis.fec.kj01.bddx.dxm-int.com","scheduleLimit": null,"containerInfo": [{"containerName": "r-ivbu-plm-0","imageName": "r.duxiaoman-int.com/siod_redis/redis-docker","imageVersion": "v2024072402","imagePullPolicy": "IfNotPresent","cpu": 1,"mem": 1,"port": null,"entryCommand": "","execArgs": null,"envArgs": {"CLOUD_TYPE": "kj","CONTAINER_NAME": "r-ivbu-plm-0","CONTAINER_TYPE": "main","DEVICE_TYPE": "pod","FECID": "fid-XxqWgKwoo","FEC_OPERATOR_URL": "https://************:11443/fec2","HAWKING_ENV": "container","INIT_NOAH_USER": "redis","MAX_MEMORY_POLICY": "volatile-lru","NODE_IP": "","POD_IP": "","POD_NAME": "","REDIS_PORT": "7000"},"diskApplication": 0,"mountType": "","volumeMounts": null,"status": "Running","privileged": false}]},
						{"id": "","podName": "r-ivbu-plm-1-nu","productName": "siod-kafka","appName": "ivbu-plm-redis","podIp": "*********","idc": "ZZJG","comboDesc": "","safeRegion": "test_internal","owner": "jiayiming_dxm","applicant": "","status": "Running","labels": {"avoid_delete_by_accident": "true","dxm-redis/component": "redis","dxm-redis/idc": "hbb","dxm-redis/index": "0","dxm-redis/name": "r-ivbu-plm","fec2/app_name": "ivbu-plm-redis","fec2/bnsMount": "false","fec2/budget_execution_id": "","fec2/budget_number": "","fec2/buget-checked": "pass","fec2/cni-safe-region": "test_internal","fec2/combo_code": "567756428","fec2/device-id": "fec-PMOYhZDwP","fec2/disable-inject-sidecar": "false","fec2/fec-id": "fid-XxqWgKwoo","fec2/idc": "BDDX","fec2/over_budget": "true","fec2/owner": "jiayiming_dxm","fec2/product_name": "siod-kafka","fec2/retain": "true","fec2/saas": "redis","fec2/service_level": "","fec2/version": "v0.0.2","fec2/white": "false"},"hostname": "r-ivbu-plm-0-1bj0uqn4.redis.fec.kj01.bddx.dxm-int.com","scheduleLimit": null,"containerInfo": [{"containerName": "r-ivbu-plm-0","imageName": "r.duxiaoman-int.com/siod_redis/redis-docker","imageVersion": "v2024072402","imagePullPolicy": "IfNotPresent","cpu": 1,"mem": 1,"port": null,"entryCommand": "","execArgs": null,"envArgs": {"CLOUD_TYPE": "kj","CONTAINER_NAME": "r-ivbu-plm-0","CONTAINER_TYPE": "main","DEVICE_TYPE": "pod","FECID": "fid-XxqWgKwoo","FEC_OPERATOR_URL": "https://************:11443/fec2","HAWKING_ENV": "container","INIT_NOAH_USER": "redis","MAX_MEMORY_POLICY": "volatile-lru","NODE_IP": "","POD_IP": "","POD_NAME": "","REDIS_PORT": "7000"},"diskApplication": 0,"mountType": "","volumeMounts": null,"status": "Running","privileged": false}]},
						{"id": "","podName": "s-ivbu-plm-h8","productName": "siod-kafka","appName": "ivbu-plm-sentinel","podIp": "*********","idc": "ZZJG","comboDesc": "","safeRegion": "test_internal","owner": "jiayiming_dxm","applicant": "","status": "Running","labels": {"avoid_delete_by_accident": "true","dxm-redis/component": "sentinel","dxm-redis/idc": "hbb","dxm-redis/index": "0","dxm-redis/name": "s-ivbu-plm","fec2/app_name": "ivbu-plm-sentinel","fec2/bnsMount": "false","fec2/budget_execution_id": "","fec2/budget_number": "","fec2/buget-checked": "pass","fec2/cni-safe-region": "test_internal","fec2/combo_code": "567756428","fec2/device-id": "fec-PMOYhZDwP","fec2/disable-inject-sidecar": "false","fec2/fec-id": "fid-XxqWgKwoo","fec2/idc": "BDDX","fec2/over_budget": "true","fec2/owner": "jiayiming_dxm","fec2/product_name": "siod-kafka","fec2/retain": "true","fec2/saas": "redis","fec2/service_level": "","fec2/version": "v0.0.2","fec2/white": "false"},"hostname": "r-ivbu-plm-0-1bj0uqn4.redis.fec.kj01.bddx.dxm-int.com","scheduleLimit": null,"containerInfo": [{"containerName": "r-ivbu-plm-0","imageName": "r.duxiaoman-int.com/siod_redis/redis-docker","imageVersion": "v2024072402","imagePullPolicy": "IfNotPresent","cpu": 1,"mem": 1,"port": null,"entryCommand": "","execArgs": null,"envArgs": {"CLOUD_TYPE": "kj","CONTAINER_NAME": "r-ivbu-plm-0","CONTAINER_TYPE": "main","DEVICE_TYPE": "pod","FECID": "fid-XxqWgKwoo","FEC_OPERATOR_URL": "https://************:11443/fec2","HAWKING_ENV": "container","INIT_NOAH_USER": "redis","MAX_MEMORY_POLICY": "volatile-lru","NODE_IP": "","POD_IP": "","POD_NAME": "","REDIS_PORT": "7000"},"diskApplication": 0,"mountType": "","volumeMounts": null,"status": "Running","privileged": false}]}
					]}`),
				)
				redisc.Mock().ExpectInfo("replication").SetVal("role:slave")
				redisc.Mock().ExpectInfo("replication").SetVal("role:slave")
				httpmock.RegisterResponder("POST", "http://*************:8536/fec-manager/api/v1/fec/returnPod",
					httpmock.NewStringResponder(200, "{\"errno\": 0,\"errmsg\": \"\",\"data\": \"ok\"}"))
			},
			args: args{
				clusterName: objC.Name,
				podNames: []string{
					"p-ivbu-plm-c0",
					"p-ivbu-plm-dz",
					"r-ivbu-plm-0-ly",
					"r-ivbu-plm-1-nu",
					"s-ivbu-plm-h8",
				},
			},
			wantErr: true,
		},
		{
			name: "success",
			before: func() {
				// delete database
				db.Proxy.Delete().ExecX(context.Background())
				db.Proxy.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("ivbu-plm-router.siod-redis").SetIP("**********").SetPort(8001).SetIdc("hba").ExecX(context.Background())
				db.Proxy.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("ivbu-plm-router.siod-redis").SetIP("**********").SetPort(8001).SetIdc("hba").ExecX(context.Background())
				db.Redis.Delete().ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("ivbu-plm-redis.siod-redis").SetName("ivbu_plm-server1").SetIP("***********").SetPort(7000).SetIdc("hbb").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("ivbu-plm-redis.siod-redis").SetName("ivbu_plm-server2").SetIP("*********").SetPort(7000).SetIdc("hbb").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Sentinel.Delete().ExecX(context.Background())
				db.Sentinel.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("ivbu-plm-sentinel.siod-redis").SetIP("*********").SetPort(9001).SetIdc("hba").ExecX(context.Background())

				// return pods
				httpmock.RegisterResponder("POST", "http://*************:8536/fec-manager/api/v1/fec/listPod",
					httpmock.NewStringResponder(200, `{"errno": 0,"errmsg": "","data": [
						{"id": "","podName": "p-ivbu-plm-c0","productName": "siod-kafka","appName": "ivbu-plm-router","podIp": "**********","idc": "ZZJG","comboDesc": "","safeRegion": "test_internal","owner": "jiayiming_dxm","applicant": "","status": "Running","labels": {"avoid_delete_by_accident": "true","dxm-redis/component": "router","dxm-redis/idc": "hbb","dxm-redis/index": "0","dxm-redis/name": "p-ivbu-plm","dxm-redis/taint": "true","fec2/app_name": "ivbu-plm-router","fec2/bnsMount": "false","fec2/budget_execution_id": "","fec2/budget_number": "","fec2/buget-checked": "pass","fec2/cni-safe-region": "test_internal","fec2/combo_code": "567756428","fec2/device-id": "fec-PMOYhZDwP","fec2/disable-inject-sidecar": "false","fec2/fec-id": "fid-XxqWgKwoo","fec2/idc": "BDDX","fec2/over_budget": "true","fec2/owner": "jiayiming_dxm","fec2/product_name": "siod-kafka","fec2/retain": "true","fec2/saas": "redis","fec2/service_level": "","fec2/version": "v0.0.2","fec2/white": "false"},"hostname": "r-ivbu-plm-0-1bj0uqn4.redis.fec.kj01.bddx.dxm-int.com","scheduleLimit": null,"containerInfo": [{"containerName": "r-ivbu-plm-0","imageName": "r.duxiaoman-int.com/siod_redis/redis-docker","imageVersion": "v2024072402","imagePullPolicy": "IfNotPresent","cpu": 1,"mem": 1,"port": null,"entryCommand": "","execArgs": null,"envArgs": {"CLOUD_TYPE": "kj","CONTAINER_NAME": "r-ivbu-plm-0","CONTAINER_TYPE": "main","DEVICE_TYPE": "pod","FECID": "fid-XxqWgKwoo","FEC_OPERATOR_URL": "https://************:11443/fec2","HAWKING_ENV": "container","INIT_NOAH_USER": "redis","MAX_MEMORY_POLICY": "volatile-lru","NODE_IP": "","POD_IP": "","POD_NAME": "","REDIS_PORT": "7000"},"diskApplication": 0,"mountType": "","volumeMounts": null,"status": "Running","privileged": false}]},
						{"id": "","podName": "p-ivbu-plm-dz","productName": "siod-kafka","appName": "ivbu-plm-router","podIp": "**********","idc": "ZZJG","comboDesc": "","safeRegion": "test_internal","owner": "jiayiming_dxm","applicant": "","status": "Running","labels": {"avoid_delete_by_accident": "true","dxm-redis/component": "router","dxm-redis/idc": "hbb","dxm-redis/index": "0","dxm-redis/name": "p-ivbu-plm","dxm-redis/taint": "true","fec2/app_name": "ivbu-plm-router","fec2/bnsMount": "false","fec2/budget_execution_id": "","fec2/budget_number": "","fec2/buget-checked": "pass","fec2/cni-safe-region": "test_internal","fec2/combo_code": "567756428","fec2/device-id": "fec-PMOYhZDwP","fec2/disable-inject-sidecar": "false","fec2/fec-id": "fid-XxqWgKwoo","fec2/idc": "BDDX","fec2/over_budget": "true","fec2/owner": "jiayiming_dxm","fec2/product_name": "siod-kafka","fec2/retain": "true","fec2/saas": "redis","fec2/service_level": "","fec2/version": "v0.0.2","fec2/white": "false"},"hostname": "r-ivbu-plm-0-1bj0uqn4.redis.fec.kj01.bddx.dxm-int.com","scheduleLimit": null,"containerInfo": [{"containerName": "r-ivbu-plm-0","imageName": "r.duxiaoman-int.com/siod_redis/redis-docker","imageVersion": "v2024072402","imagePullPolicy": "IfNotPresent","cpu": 1,"mem": 1,"port": null,"entryCommand": "","execArgs": null,"envArgs": {"CLOUD_TYPE": "kj","CONTAINER_NAME": "r-ivbu-plm-0","CONTAINER_TYPE": "main","DEVICE_TYPE": "pod","FECID": "fid-XxqWgKwoo","FEC_OPERATOR_URL": "https://************:11443/fec2","HAWKING_ENV": "container","INIT_NOAH_USER": "redis","MAX_MEMORY_POLICY": "volatile-lru","NODE_IP": "","POD_IP": "","POD_NAME": "","REDIS_PORT": "7000"},"diskApplication": 0,"mountType": "","volumeMounts": null,"status": "Running","privileged": false}]},
						{"id": "","podName": "r-ivbu-plm-0-ly","productName": "siod-kafka","appName": "ivbu-plm-redis","podIp": "***********","idc": "ZZJG","comboDesc": "","safeRegion": "test_internal","owner": "jiayiming_dxm","applicant": "","status": "Running","labels": {"avoid_delete_by_accident": "true","dxm-redis/component": "redis","dxm-redis/idc": "hbb","dxm-redis/index": "0","dxm-redis/name": "r-ivbu-plm","dxm-redis/taint": "true","fec2/app_name": "ivbu-plm-redis","fec2/bnsMount": "false","fec2/budget_execution_id": "","fec2/budget_number": "","fec2/buget-checked": "pass","fec2/cni-safe-region": "test_internal","fec2/combo_code": "567756428","fec2/device-id": "fec-PMOYhZDwP","fec2/disable-inject-sidecar": "false","fec2/fec-id": "fid-XxqWgKwoo","fec2/idc": "BDDX","fec2/over_budget": "true","fec2/owner": "jiayiming_dxm","fec2/product_name": "siod-kafka","fec2/retain": "true","fec2/saas": "redis","fec2/service_level": "","fec2/version": "v0.0.2","fec2/white": "false"},"hostname": "r-ivbu-plm-0-1bj0uqn4.redis.fec.kj01.bddx.dxm-int.com","scheduleLimit": null,"containerInfo": [{"containerName": "r-ivbu-plm-0","imageName": "r.duxiaoman-int.com/siod_redis/redis-docker","imageVersion": "v2024072402","imagePullPolicy": "IfNotPresent","cpu": 1,"mem": 1,"port": null,"entryCommand": "","execArgs": null,"envArgs": {"CLOUD_TYPE": "kj","CONTAINER_NAME": "r-ivbu-plm-0","CONTAINER_TYPE": "main","DEVICE_TYPE": "pod","FECID": "fid-XxqWgKwoo","FEC_OPERATOR_URL": "https://************:11443/fec2","HAWKING_ENV": "container","INIT_NOAH_USER": "redis","MAX_MEMORY_POLICY": "volatile-lru","NODE_IP": "","POD_IP": "","POD_NAME": "","REDIS_PORT": "7000"},"diskApplication": 0,"mountType": "","volumeMounts": null,"status": "Running","privileged": false}]},
						{"id": "","podName": "r-ivbu-plm-1-nu","productName": "siod-kafka","appName": "ivbu-plm-redis","podIp": "*********","idc": "ZZJG","comboDesc": "","safeRegion": "test_internal","owner": "jiayiming_dxm","applicant": "","status": "Running","labels": {"avoid_delete_by_accident": "true","dxm-redis/component": "redis","dxm-redis/idc": "hbb","dxm-redis/index": "0","dxm-redis/name": "r-ivbu-plm","dxm-redis/taint": "true","fec2/app_name": "ivbu-plm-redis","fec2/bnsMount": "false","fec2/budget_execution_id": "","fec2/budget_number": "","fec2/buget-checked": "pass","fec2/cni-safe-region": "test_internal","fec2/combo_code": "567756428","fec2/device-id": "fec-PMOYhZDwP","fec2/disable-inject-sidecar": "false","fec2/fec-id": "fid-XxqWgKwoo","fec2/idc": "BDDX","fec2/over_budget": "true","fec2/owner": "jiayiming_dxm","fec2/product_name": "siod-kafka","fec2/retain": "true","fec2/saas": "redis","fec2/service_level": "","fec2/version": "v0.0.2","fec2/white": "false"},"hostname": "r-ivbu-plm-0-1bj0uqn4.redis.fec.kj01.bddx.dxm-int.com","scheduleLimit": null,"containerInfo": [{"containerName": "r-ivbu-plm-0","imageName": "r.duxiaoman-int.com/siod_redis/redis-docker","imageVersion": "v2024072402","imagePullPolicy": "IfNotPresent","cpu": 1,"mem": 1,"port": null,"entryCommand": "","execArgs": null,"envArgs": {"CLOUD_TYPE": "kj","CONTAINER_NAME": "r-ivbu-plm-0","CONTAINER_TYPE": "main","DEVICE_TYPE": "pod","FECID": "fid-XxqWgKwoo","FEC_OPERATOR_URL": "https://************:11443/fec2","HAWKING_ENV": "container","INIT_NOAH_USER": "redis","MAX_MEMORY_POLICY": "volatile-lru","NODE_IP": "","POD_IP": "","POD_NAME": "","REDIS_PORT": "7000"},"diskApplication": 0,"mountType": "","volumeMounts": null,"status": "Running","privileged": false}]},
						{"id": "","podName": "s-ivbu-plm-h8","productName": "siod-kafka","appName": "ivbu-plm-sentinel","podIp": "*********","idc": "ZZJG","comboDesc": "","safeRegion": "test_internal","owner": "jiayiming_dxm","applicant": "","status": "Running","labels": {"avoid_delete_by_accident": "true","dxm-redis/component": "sentinel","dxm-redis/idc": "hbb","dxm-redis/index": "0","dxm-redis/name": "s-ivbu-plm","dxm-redis/taint": "true","fec2/app_name": "ivbu-plm-sentinel","fec2/bnsMount": "false","fec2/budget_execution_id": "","fec2/budget_number": "","fec2/buget-checked": "pass","fec2/cni-safe-region": "test_internal","fec2/combo_code": "567756428","fec2/device-id": "fec-PMOYhZDwP","fec2/disable-inject-sidecar": "false","fec2/fec-id": "fid-XxqWgKwoo","fec2/idc": "BDDX","fec2/over_budget": "true","fec2/owner": "jiayiming_dxm","fec2/product_name": "siod-kafka","fec2/retain": "true","fec2/saas": "redis","fec2/service_level": "","fec2/version": "v0.0.2","fec2/white": "false"},"hostname": "r-ivbu-plm-0-1bj0uqn4.redis.fec.kj01.bddx.dxm-int.com","scheduleLimit": null,"containerInfo": [{"containerName": "r-ivbu-plm-0","imageName": "r.duxiaoman-int.com/siod_redis/redis-docker","imageVersion": "v2024072402","imagePullPolicy": "IfNotPresent","cpu": 1,"mem": 1,"port": null,"entryCommand": "","execArgs": null,"envArgs": {"CLOUD_TYPE": "kj","CONTAINER_NAME": "r-ivbu-plm-0","CONTAINER_TYPE": "main","DEVICE_TYPE": "pod","FECID": "fid-XxqWgKwoo","FEC_OPERATOR_URL": "https://************:11443/fec2","HAWKING_ENV": "container","INIT_NOAH_USER": "redis","MAX_MEMORY_POLICY": "volatile-lru","NODE_IP": "","POD_IP": "","POD_NAME": "","REDIS_PORT": "7000"},"diskApplication": 0,"mountType": "","volumeMounts": null,"status": "Running","privileged": false}]}
					]}`),
				)
				redisc.Mock().ExpectInfo("replication").SetVal("role:slave")
				redisc.Mock().ExpectInfo("replication").SetVal("role:slave")
				httpmock.RegisterResponder("POST", "http://*************:8536/fec-manager/api/v1/fec/returnPod",
					httpmock.NewStringResponder(200, "{\"errno\": 0,\"errmsg\": \"\",\"data\": \"ok\"}"))
			},
			args: args{
				clusterName: objC.Name,
				podNames: []string{
					"p-ivbu-plm-c0",
					"p-ivbu-plm-dz",
					"r-ivbu-plm-0-ly",
					"r-ivbu-plm-1-nu",
					"s-ivbu-plm-h8",
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := DeletePods(tt.args.clusterName, tt.args.podNames, tt.args.force)
			if (err != nil) != tt.wantErr {
				t.Errorf("DeletePods() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func Test_deleteTaintedPods(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")
	common.Init(&common.Config{
		App: &common.AppConfig{
			ProductLine: "siod-redis",
		},
		IDC: map[string]string{
			"hba": "ZZJG",
			"hbb": "ZZJG",
			"hbc": "ZZJG",
		},
	})

	objC := env.MockCluster("auto_delete_test")
	db, _ := mysql.Database()

	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		clusterName string
		components  []string
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
	}{
		{
			name: "delete proxy",
			before: func() {
				// return pods
				httpmock.RegisterResponder("POST", "http://*************:8536/fec-manager/api/v1/fec/listPod",
					httpmock.NewStringResponder(200, `{"errno": 0,"errmsg": "","data": [
						{"id": "","podName": "p-auto-delete-c0","productName": "siod-kafka","appName": "auto-delete-router","podIp": "**********","idc": "ZZJG","comboDesc": "","safeRegion": "test_internal","owner": "jiayiming_dxm","applicant": "","status": "Running","labels": {"avoid_delete_by_accident": "true","dxm-redis/component": "router","dxm-redis/idc": "hbb","dxm-redis/index": "0","dxm-redis/name": "p-ivbu-plm","dxm-redis/taint": "true","fec2/app_name": "auto-delete-router","fec2/bnsMount": "false","fec2/budget_execution_id": "","fec2/budget_number": "","fec2/buget-checked": "pass","fec2/cni-safe-region": "test_internal","fec2/combo_code": "567756428","fec2/device-id": "fec-PMOYhZDwP","fec2/disable-inject-sidecar": "false","fec2/fec-id": "fid-XxqWgKwoo","fec2/idc": "BDDX","fec2/over_budget": "true","fec2/owner": "jiayiming_dxm","fec2/product_name": "siod-kafka","fec2/retain": "true","fec2/saas": "redis","fec2/service_level": "","fec2/version": "v0.0.2","fec2/white": "false"},"hostname": "r-ivbu-plm-0-1bj0uqn4.redis.fec.kj01.bddx.dxm-int.com","scheduleLimit": null,"containerInfo": [{"containerName": "r-ivbu-plm-0","imageName": "r.duxiaoman-int.com/siod_redis/redis-docker","imageVersion": "v2024072402","imagePullPolicy": "IfNotPresent","cpu": 1,"mem": 1,"port": null,"entryCommand": "","execArgs": null,"envArgs": {"CLOUD_TYPE": "kj","CONTAINER_NAME": "r-ivbu-plm-0","CONTAINER_TYPE": "main","DEVICE_TYPE": "pod","FECID": "fid-XxqWgKwoo","FEC_OPERATOR_URL": "https://************:11443/fec2","HAWKING_ENV": "container","INIT_NOAH_USER": "redis","MAX_MEMORY_POLICY": "volatile-lru","NODE_IP": "","POD_IP": "","POD_NAME": "","REDIS_PORT": "7000"},"diskApplication": 0,"mountType": "","volumeMounts": null,"status": "Running","privileged": false}]},
						{"id": "","podName": "p-auto-delete-dz","productName": "siod-kafka","appName": "auto-delete-router","podIp": "**********","idc": "ZZJG","comboDesc": "","safeRegion": "test_internal","owner": "jiayiming_dxm","applicant": "","status": "Running","labels": {"avoid_delete_by_accident": "true","dxm-redis/component": "router","dxm-redis/idc": "hbb","dxm-redis/index": "0","dxm-redis/name": "p-ivbu-plm","dxm-redis/taint": "true","fec2/app_name": "auto-delete-router","fec2/bnsMount": "false","fec2/budget_execution_id": "","fec2/budget_number": "","fec2/buget-checked": "pass","fec2/cni-safe-region": "test_internal","fec2/combo_code": "567756428","fec2/device-id": "fec-PMOYhZDwP","fec2/disable-inject-sidecar": "false","fec2/fec-id": "fid-XxqWgKwoo","fec2/idc": "BDDX","fec2/over_budget": "true","fec2/owner": "jiayiming_dxm","fec2/product_name": "siod-kafka","fec2/retain": "true","fec2/saas": "redis","fec2/service_level": "","fec2/version": "v0.0.2","fec2/white": "false"},"hostname": "r-ivbu-plm-0-1bj0uqn4.redis.fec.kj01.bddx.dxm-int.com","scheduleLimit": null,"containerInfo": [{"containerName": "r-ivbu-plm-0","imageName": "r.duxiaoman-int.com/siod_redis/redis-docker","imageVersion": "v2024072402","imagePullPolicy": "IfNotPresent","cpu": 1,"mem": 1,"port": null,"entryCommand": "","execArgs": null,"envArgs": {"CLOUD_TYPE": "kj","CONTAINER_NAME": "r-ivbu-plm-0","CONTAINER_TYPE": "main","DEVICE_TYPE": "pod","FECID": "fid-XxqWgKwoo","FEC_OPERATOR_URL": "https://************:11443/fec2","HAWKING_ENV": "container","INIT_NOAH_USER": "redis","MAX_MEMORY_POLICY": "volatile-lru","NODE_IP": "","POD_IP": "","POD_NAME": "","REDIS_PORT": "7000"},"diskApplication": 0,"mountType": "","volumeMounts": null,"status": "Running","privileged": false}]}
					]}`),
				)
				// qps
				httpmock.RegisterResponder("GET", "http://**********:8433/proxy/conns?port=0",
					httpmock.NewStringResponder(200, `{"code": "00000","msg": "","data": {"qps":0, "conns":["*********"]}}`))

				// delete database
				db.Proxy.Delete().ExecX(context.Background())
				db.Proxy.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("auto-delete-router.siod-redis").SetIP("**********").SetPort(8001).SetIdc("hba").ExecX(context.Background())
				db.Proxy.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("auto-delete-router.siod-redis").SetIP("**********").SetPort(8001).SetIdc("hba").ExecX(context.Background())

				httpmock.RegisterResponder("POST", "http://*************:8536/fec-manager/api/v1/fec/returnPod",
					httpmock.NewStringResponder(200, `{"errno": 0,"errmsg": "","data": "ok"}`))
			},
			args: args{
				clusterName: objC.Name,
				components:  []string{common.COMPONENT_ROUTER},
			},
			wantErr: false,
		},
		{
			name: "delete proxy but still has qps",
			before: func() {
				httpmock.Reset()
				httpmock.RegisterResponder("POST", "http://*************:8536/fec-manager/api/v1/fec/listPod",
					httpmock.NewStringResponder(200, `{"errno": 0,"errmsg": "","data": [
						{"id": "","podName": "p-auto-delete-c0","productName": "siod-kafka","appName": "auto-delete-router","podIp": "**********","idc": "ZZJG","comboDesc": "","safeRegion": "test_internal","owner": "jiayiming_dxm","applicant": "","status": "Running","labels": {"avoid_delete_by_accident": "true","dxm-redis/component": "router","dxm-redis/idc": "hbb","dxm-redis/index": "0","dxm-redis/name": "p-auto-delete","dxm-redis/taint": "true","fec2/app_name": "auto-delete-router","fec2/bnsMount": "false","fec2/budget_execution_id": "","fec2/budget_number": "","fec2/buget-checked": "pass","fec2/cni-safe-region": "test_internal","fec2/combo_code": "567756428","fec2/device-id": "fec-PMOYhZDwP","fec2/disable-inject-sidecar": "false","fec2/fec-id": "fid-XxqWgKwoo","fec2/idc": "BDDX","fec2/over_budget": "true","fec2/owner": "jiayiming_dxm","fec2/product_name": "siod-kafka","fec2/retain": "true","fec2/saas": "redis","fec2/service_level": "","fec2/version": "v0.0.2","fec2/white": "false"},"hostname": "r-auto-delete-0-1bj0uqn4.redis.fec.kj01.bddx.dxm-int.com","scheduleLimit": null,"containerInfo": [{"containerName": "r-auto-delete-0","imageName": "r.duxiaoman-int.com/siod_redis/redis-docker","imageVersion": "v2024072402","imagePullPolicy": "IfNotPresent","cpu": 1,"mem": 1,"port": null,"entryCommand": "","execArgs": null,"envArgs": {"CLOUD_TYPE": "kj","CONTAINER_NAME": "r-auto-delete-0","CONTAINER_TYPE": "main","DEVICE_TYPE": "pod","FECID": "fid-XxqWgKwoo","FEC_OPERATOR_URL": "https://************:11443/fec2","HAWKING_ENV": "container","INIT_NOAH_USER": "redis","MAX_MEMORY_POLICY": "volatile-lru","NODE_IP": "","POD_IP": "","POD_NAME": "","REDIS_PORT": "7000"},"diskApplication": 0,"mountType": "","volumeMounts": null,"status": "Running","privileged": false}]},
						{"id": "","podName": "p-auto-delete-dz","productName": "siod-kafka","appName": "auto-delete-router","podIp": "**********","idc": "ZZJG","comboDesc": "","safeRegion": "test_internal","owner": "jiayiming_dxm","applicant": "","status": "Running","labels": {"avoid_delete_by_accident": "true","dxm-redis/component": "router","dxm-redis/idc": "hbb","dxm-redis/index": "0","dxm-redis/name": "p-auto-delete","dxm-redis/taint": "true","fec2/app_name": "auto-delete-router","fec2/bnsMount": "false","fec2/budget_execution_id": "","fec2/budget_number": "","fec2/buget-checked": "pass","fec2/cni-safe-region": "test_internal","fec2/combo_code": "567756428","fec2/device-id": "fec-PMOYhZDwP","fec2/disable-inject-sidecar": "false","fec2/fec-id": "fid-XxqWgKwoo","fec2/idc": "BDDX","fec2/over_budget": "true","fec2/owner": "jiayiming_dxm","fec2/product_name": "siod-kafka","fec2/retain": "true","fec2/saas": "redis","fec2/service_level": "","fec2/version": "v0.0.2","fec2/white": "false"},"hostname": "r-auto-delete-0-1bj0uqn4.redis.fec.kj01.bddx.dxm-int.com","scheduleLimit": null,"containerInfo": [{"containerName": "r-auto-delete-0","imageName": "r.duxiaoman-int.com/siod_redis/redis-docker","imageVersion": "v2024072402","imagePullPolicy": "IfNotPresent","cpu": 1,"mem": 1,"port": null,"entryCommand": "","execArgs": null,"envArgs": {"CLOUD_TYPE": "kj","CONTAINER_NAME": "r-auto-delete-0","CONTAINER_TYPE": "main","DEVICE_TYPE": "pod","FECID": "fid-XxqWgKwoo","FEC_OPERATOR_URL": "https://************:11443/fec2","HAWKING_ENV": "container","INIT_NOAH_USER": "redis","MAX_MEMORY_POLICY": "volatile-lru","NODE_IP": "","POD_IP": "","POD_NAME": "","REDIS_PORT": "7000"},"diskApplication": 0,"mountType": "","volumeMounts": null,"status": "Running","privileged": false}]}
					]}`),
				)
				// qps
				httpmock.RegisterResponder("GET", "http://**********:8433/proxy/conns?port=0",
					httpmock.NewStringResponder(200, `{"code": "00000","msg": "","data": {"qps":10, "conns":["*********"]}}`))
			},
			args: args{
				clusterName: objC.Name,
				components:  []string{common.COMPONENT_ROUTER},
			},
			wantErr: true,
		},
		{
			name: "delete slaves",
			before: func() {
				httpmock.Reset()
				httpmock.RegisterResponder("POST", "http://*************:8536/fec-manager/api/v1/fec/listPod",
					httpmock.NewStringResponder(200, `{"errno": 0,"errmsg": "","data": [
						{"id": "","podName": "r-auto-delete-0-ly","productName": "siod-kafka","appName": "auto-delete-redis","podIp": "***********","idc": "ZZJG","comboDesc": "","safeRegion": "test_internal","owner": "jiayiming_dxm","applicant": "","status": "Running","labels": {"avoid_delete_by_accident": "true","dxm-redis/component": "redis","dxm-redis/idc": "hbb","dxm-redis/index": "0","dxm-redis/name": "r-auto-delete","dxm-redis/taint": "true","fec2/app_name": "auto-delete-redis","fec2/bnsMount": "false","fec2/budget_execution_id": "","fec2/budget_number": "","fec2/buget-checked": "pass","fec2/cni-safe-region": "test_internal","fec2/combo_code": "567756428","fec2/device-id": "fec-PMOYhZDwP","fec2/disable-inject-sidecar": "false","fec2/fec-id": "fid-XxqWgKwoo","fec2/idc": "BDDX","fec2/over_budget": "true","fec2/owner": "jiayiming_dxm","fec2/product_name": "siod-kafka","fec2/retain": "true","fec2/saas": "redis","fec2/service_level": "","fec2/version": "v0.0.2","fec2/white": "false"},"hostname": "r-auto-delete-0-1bj0uqn4.redis.fec.kj01.bddx.dxm-int.com","scheduleLimit": null,"containerInfo": [{"containerName": "r-auto-delete-0","imageName": "r.duxiaoman-int.com/siod_redis/redis-docker","imageVersion": "v2024072402","imagePullPolicy": "IfNotPresent","cpu": 1,"mem": 1,"port": null,"entryCommand": "","execArgs": null,"envArgs": {"CLOUD_TYPE": "kj","CONTAINER_NAME": "r-auto-delete-0","CONTAINER_TYPE": "main","DEVICE_TYPE": "pod","FECID": "fid-XxqWgKwoo","FEC_OPERATOR_URL": "https://************:11443/fec2","HAWKING_ENV": "container","INIT_NOAH_USER": "redis","MAX_MEMORY_POLICY": "volatile-lru","NODE_IP": "","POD_IP": "","POD_NAME": "","REDIS_PORT": "7000"},"diskApplication": 0,"mountType": "","volumeMounts": null,"status": "Running","privileged": false}]},
						{"id": "","podName": "r-auto-delete-1-nu","productName": "siod-kafka","appName": "auto-delete-redis","podIp": "*********","idc": "ZZJG","comboDesc": "","safeRegion": "test_internal","owner": "jiayiming_dxm","applicant": "","status": "Running","labels": {"avoid_delete_by_accident": "true","dxm-redis/component": "redis","dxm-redis/idc": "hbb","dxm-redis/index": "0","dxm-redis/name": "r-auto-delete","dxm-redis/taint": "true","fec2/app_name": "auto-delete-redis","fec2/bnsMount": "false","fec2/budget_execution_id": "","fec2/budget_number": "","fec2/buget-checked": "pass","fec2/cni-safe-region": "test_internal","fec2/combo_code": "567756428","fec2/device-id": "fec-PMOYhZDwP","fec2/disable-inject-sidecar": "false","fec2/fec-id": "fid-XxqWgKwoo","fec2/idc": "BDDX","fec2/over_budget": "true","fec2/owner": "jiayiming_dxm","fec2/product_name": "siod-kafka","fec2/retain": "true","fec2/saas": "redis","fec2/service_level": "","fec2/version": "v0.0.2","fec2/white": "false"},"hostname": "r-auto-delete-0-1bj0uqn4.redis.fec.kj01.bddx.dxm-int.com","scheduleLimit": null,"containerInfo": [{"containerName": "r-auto-delete-0","imageName": "r.duxiaoman-int.com/siod_redis/redis-docker","imageVersion": "v2024072402","imagePullPolicy": "IfNotPresent","cpu": 1,"mem": 1,"port": null,"entryCommand": "","execArgs": null,"envArgs": {"CLOUD_TYPE": "kj","CONTAINER_NAME": "r-auto-delete-0","CONTAINER_TYPE": "main","DEVICE_TYPE": "pod","FECID": "fid-XxqWgKwoo","FEC_OPERATOR_URL": "https://************:11443/fec2","HAWKING_ENV": "container","INIT_NOAH_USER": "redis","MAX_MEMORY_POLICY": "volatile-lru","NODE_IP": "","POD_IP": "","POD_NAME": "","REDIS_PORT": "7000"},"diskApplication": 0,"mountType": "","volumeMounts": null,"status": "Running","privileged": false}]}
					]}`),
				)

				redisc.Mock().ExpectInfo("replication").SetVal("role:slave")
				redisc.Mock().ExpectInfo("replication").SetVal("role:slave")

				db.Redis.Delete().ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("auto-delete-redis.siod-redis").SetName("ivbu_plm-server1").SetIP("***********").SetPort(7000).SetIdc("hbb").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("auto-delete-redis.siod-redis").SetName("ivbu_plm-server2").SetIP("*********").SetPort(7000).SetIdc("hbb").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())

				httpmock.RegisterResponder("POST", "http://*************:8536/fec-manager/api/v1/fec/returnPod",
					httpmock.NewStringResponder(200, `{"errno": 0,"errmsg": "","data": "ok"}`))
			},
			args: args{
				clusterName: objC.Name,
				components:  []string{common.COMPONENT_REDIS},
			},
			wantErr: false,
		},
		{
			name: "delete master",
			before: func() {
				httpmock.Reset()
				httpmock.RegisterResponder("POST", "http://*************:8536/fec-manager/api/v1/fec/listPod",
					httpmock.NewStringResponder(200, `{"errno": 0,"errmsg": "","data": [
						{"id": "","podName": "r-auto-delete-0-ly","productName": "siod-kafka","appName": "auto-delete-redis","podIp": "***********","idc": "ZZJG","comboDesc": "","safeRegion": "test_internal","owner": "jiayiming_dxm","applicant": "","status": "Running","labels": {"avoid_delete_by_accident": "true","dxm-redis/component": "redis","dxm-redis/idc": "hbb","dxm-redis/index": "0","dxm-redis/name": "r-auto-delete","dxm-redis/taint": "true","fec2/app_name": "auto-delete-redis","fec2/bnsMount": "false","fec2/budget_execution_id": "","fec2/budget_number": "","fec2/buget-checked": "pass","fec2/cni-safe-region": "test_internal","fec2/combo_code": "567756428","fec2/device-id": "fec-PMOYhZDwP","fec2/disable-inject-sidecar": "false","fec2/fec-id": "fid-XxqWgKwoo","fec2/idc": "BDDX","fec2/over_budget": "true","fec2/owner": "jiayiming_dxm","fec2/product_name": "siod-kafka","fec2/retain": "true","fec2/saas": "redis","fec2/service_level": "","fec2/version": "v0.0.2","fec2/white": "false"},"hostname": "r-auto-delete-0-1bj0uqn4.redis.fec.kj01.bddx.dxm-int.com","scheduleLimit": null,"containerInfo": [{"containerName": "r-auto-delete-0","imageName": "r.duxiaoman-int.com/siod_redis/redis-docker","imageVersion": "v2024072402","imagePullPolicy": "IfNotPresent","cpu": 1,"mem": 1,"port": null,"entryCommand": "","execArgs": null,"envArgs": {"CLOUD_TYPE": "kj","CONTAINER_NAME": "r-auto-delete-0","CONTAINER_TYPE": "main","DEVICE_TYPE": "pod","FECID": "fid-XxqWgKwoo","FEC_OPERATOR_URL": "https://************:11443/fec2","HAWKING_ENV": "container","INIT_NOAH_USER": "redis","MAX_MEMORY_POLICY": "volatile-lru","NODE_IP": "","POD_IP": "","POD_NAME": "","REDIS_PORT": "7000"},"diskApplication": 0,"mountType": "","volumeMounts": null,"status": "Running","privileged": false}]},
						{"id": "","podName": "r-auto-delete-1-nu","productName": "siod-kafka","appName": "auto-delete-redis","podIp": "*********","idc": "ZZJG","comboDesc": "","safeRegion": "test_internal","owner": "jiayiming_dxm","applicant": "","status": "Running","labels": {"avoid_delete_by_accident": "true","dxm-redis/component": "redis","dxm-redis/idc": "hbb","dxm-redis/index": "0","dxm-redis/name": "r-auto-delete","dxm-redis/taint": "true","fec2/app_name": "auto-delete-redis","fec2/bnsMount": "false","fec2/budget_execution_id": "","fec2/budget_number": "","fec2/buget-checked": "pass","fec2/cni-safe-region": "test_internal","fec2/combo_code": "567756428","fec2/device-id": "fec-PMOYhZDwP","fec2/disable-inject-sidecar": "false","fec2/fec-id": "fid-XxqWgKwoo","fec2/idc": "BDDX","fec2/over_budget": "true","fec2/owner": "jiayiming_dxm","fec2/product_name": "siod-kafka","fec2/retain": "true","fec2/saas": "redis","fec2/service_level": "","fec2/version": "v0.0.2","fec2/white": "false"},"hostname": "r-auto-delete-0-1bj0uqn4.redis.fec.kj01.bddx.dxm-int.com","scheduleLimit": null,"containerInfo": [{"containerName": "r-auto-delete-0","imageName": "r.duxiaoman-int.com/siod_redis/redis-docker","imageVersion": "v2024072402","imagePullPolicy": "IfNotPresent","cpu": 1,"mem": 1,"port": null,"entryCommand": "","execArgs": null,"envArgs": {"CLOUD_TYPE": "kj","CONTAINER_NAME": "r-auto-delete-0","CONTAINER_TYPE": "main","DEVICE_TYPE": "pod","FECID": "fid-XxqWgKwoo","FEC_OPERATOR_URL": "https://************:11443/fec2","HAWKING_ENV": "container","INIT_NOAH_USER": "redis","MAX_MEMORY_POLICY": "volatile-lru","NODE_IP": "","POD_IP": "","POD_NAME": "","REDIS_PORT": "7000"},"diskApplication": 0,"mountType": "","volumeMounts": null,"status": "Running","privileged": false}]}
					]}`),
				)

				redisc.Mock().ExpectInfo("replication").SetVal("role:slave")
				redisc.Mock().ExpectInfo("replication").SetVal("role:master")
			},
			args: args{
				clusterName: objC.Name,
				components:  []string{common.COMPONENT_REDIS},
			},
			wantErr: true,
		},
		{
			name: "delete sentinel",
			before: func() {
				httpmock.Reset()
				httpmock.RegisterResponder("POST", "http://*************:8536/fec-manager/api/v1/fec/listPod",
					httpmock.NewStringResponder(200, `{"errno": 0,"errmsg": "","data": [
						{"id": "","podName": "s-auto-delete-h8","productName": "siod-kafka","appName": "auto-delete-sentinel","podIp": "*********","idc": "ZZJG","comboDesc": "","safeRegion": "test_internal","owner": "jiayiming_dxm","applicant": "","status": "Running","labels": {"avoid_delete_by_accident": "true","dxm-redis/component": "sentinel","dxm-redis/idc": "hbb","dxm-redis/index": "0","dxm-redis/name": "s-auto-delete","dxm-redis/taint": "true","fec2/app_name": "auto-delete-sentinel","fec2/bnsMount": "false","fec2/budget_execution_id": "","fec2/budget_number": "","fec2/buget-checked": "pass","fec2/cni-safe-region": "test_internal","fec2/combo_code": "567756428","fec2/device-id": "fec-PMOYhZDwP","fec2/disable-inject-sidecar": "false","fec2/fec-id": "fid-XxqWgKwoo","fec2/idc": "BDDX","fec2/over_budget": "true","fec2/owner": "jiayiming_dxm","fec2/product_name": "siod-kafka","fec2/retain": "true","fec2/saas": "redis","fec2/service_level": "","fec2/version": "v0.0.2","fec2/white": "false"},"hostname": "r-auto-delete-0-1bj0uqn4.redis.fec.kj01.bddx.dxm-int.com","scheduleLimit": null,"containerInfo": [{"containerName": "r-auto-delete-0","imageName": "r.duxiaoman-int.com/siod_redis/redis-docker","imageVersion": "v2024072402","imagePullPolicy": "IfNotPresent","cpu": 1,"mem": 1,"port": null,"entryCommand": "","execArgs": null,"envArgs": {"CLOUD_TYPE": "kj","CONTAINER_NAME": "r-auto-delete-0","CONTAINER_TYPE": "main","DEVICE_TYPE": "pod","FECID": "fid-XxqWgKwoo","FEC_OPERATOR_URL": "https://************:11443/fec2","HAWKING_ENV": "container","INIT_NOAH_USER": "redis","MAX_MEMORY_POLICY": "volatile-lru","NODE_IP": "","POD_IP": "","POD_NAME": "","REDIS_PORT": "7000"},"diskApplication": 0,"mountType": "","volumeMounts": null,"status": "Running","privileged": false}]}
					]}`),
				)
				db.Sentinel.Delete().ExecX(context.Background())
				db.Sentinel.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("auto-delete-sentinel.siod-redis").SetIP("*********").SetPort(9001).SetIdc("hba").ExecX(context.Background())
				httpmock.RegisterResponder("POST", "http://*************:8536/fec-manager/api/v1/fec/returnPod",
					httpmock.NewStringResponder(200, "{\"errno\": 0,\"errmsg\": \"\",\"data\": \"ok\"}"))
			},
			args: args{
				clusterName: objC.Name,
				components:  []string{common.COMPONENT_SENTINEL},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := deleteTaintedPods(tt.args.clusterName, tt.args.components...)
			if (err != nil) != tt.wantErr {
				t.Errorf("deleteTaintedPods() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

// 单测：
func TestLabelProxyToScaleDown(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")

	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		deployment *Cluster
		proxyNum   int
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
	}{
		{
			name: "scale up",
			before: func() {
				httpmock.Reset()
				httpmock.RegisterResponder("POST", "http://*************:8536/fec-manager/api/v1/fec/listPod",
					httpmock.NewStringResponder(200, `{"errno": 0,"errmsg": "","data": [
						{"id": "","podName": "p-test-az","productName": "siod-redis","appName": "test-router","podIp": "*********","idc": "BDDX","comboDesc": "","safeRegion": "test_internal","owner": "jiayiming_dxm","applicant": "","status": "Running","labels": {"avoid_delete_by_accident": "true","dxm-redis/component": "router","dxm-redis/name": "p-test","dxm-redis/taint": "false","fec2/app_name": "p-test-router","fec2/bnsMount": "false","fec2/budget_execution_id": "","fec2/budget_number": "","fec2/buget-checked": "pass","fec2/cni-safe-region": "test_internal","fec2/combo_code": "567756428","fec2/device-id": "fec-PMOYhZDwP","fec2/disable-inject-sidecar": "false","fec2/fec-id": "fid-XxqWgKwoo","fec2/idc": "BDDX","fec2/over_budget": "true","fec2/owner": "jiayiming_dxm","fec2/product_name": "siod-kafka","fec2/retain": "true","fec2/saas": "redis","fec2/service_level": "","fec2/version": "v0.0.2","fec2/white": "false"},"hostname": "p-test-az.fec.kj01.bddx.dxm-int.com","scheduleLimit": null,"containerInfo": [{"containerName": "r-r3-rd-test-0","imageName": "r.duxiaoman-int.com/siod_redis/redis-docker","imageVersion": "v2024072402","imagePullPolicy": "IfNotPresent","cpu": 1,"mem": 1,"port": null,"entryCommand": "","execArgs": null,"envArgs": {"CLOUD_TYPE": "kj","CONTAINER_NAME": "r-r3-rd-test-0","CONTAINER_TYPE": "main","DEVICE_TYPE": "pod","FECID": "fid-XxqWgKwoo","FEC_OPERATOR_URL": "https://************:11443/fec2","HAWKING_ENV": "container","INIT_NOAH_USER": "redis","MAX_MEMORY_POLICY": "volatile-lru","NODE_IP": "","POD_IP": "","POD_NAME": "","REDIS_PORT": "7000"},"diskApplication": 0,"mountType": "","volumeMounts": null,"status": "Running","privileged": false}]},
						{"id": "","podName": "p-test-gh","productName": "siod-redis","appName": "test-router","podIp": "*********","idc": "BDDX","comboDesc": "","safeRegion": "test_internal","owner": "jiayiming_dxm","applicant": "","status": "Running","labels": {"avoid_delete_by_accident": "true","dxm-redis/component": "router","dxm-redis/name": "p-test","dxm-redis/taint": "false","fec2/app_name": "p-test-router","fec2/bnsMount": "false","fec2/budget_execution_id": "","fec2/budget_number": "","fec2/buget-checked": "pass","fec2/cni-safe-region": "test_internal","fec2/combo_code": "567756428","fec2/device-id": "fec-PMOYhZDwP","fec2/disable-inject-sidecar": "false","fec2/fec-id": "fid-XxqWgKwoo","fec2/idc": "BDDX","fec2/over_budget": "true","fec2/owner": "jiayiming_dxm","fec2/product_name": "siod-kafka","fec2/retain": "true","fec2/saas": "redis","fec2/service_level": "","fec2/version": "v0.0.2","fec2/white": "false"},"hostname": "p-test-gh.fec.kj01.bddx.dxm-int.com","scheduleLimit": null,"containerInfo": [{"containerName": "r-r3-rd-test-0","imageName": "r.duxiaoman-int.com/siod_redis/redis-docker","imageVersion": "v2024072402","imagePullPolicy": "IfNotPresent","cpu": 1,"mem": 1,"port": null,"entryCommand": "","execArgs": null,"envArgs": {"CLOUD_TYPE": "kj","CONTAINER_NAME": "r-r3-rd-test-0","CONTAINER_TYPE": "main","DEVICE_TYPE": "pod","FECID": "fid-XxqWgKwoo","FEC_OPERATOR_URL": "https://************:11443/fec2","HAWKING_ENV": "container","INIT_NOAH_USER": "redis","MAX_MEMORY_POLICY": "volatile-lru","NODE_IP": "","POD_IP": "","POD_NAME": "","REDIS_PORT": "7000"},"diskApplication": 0,"mountType": "","volumeMounts": null,"status": "Running","privileged": false}]},
						{"id": "","podName": "p-test-sc","productName": "siod-redis","appName": "test-router","podIp": "*********","idc": "ZZJG","comboDesc": "","safeRegion": "test_internal","owner": "jiayiming_dxm","applicant": "","status": "Running","labels": {"avoid_delete_by_accident": "true","dxm-redis/component": "router","dxm-redis/name": "p-test","dxm-redis/taint": "false","fec2/app_name": "p-test-router","fec2/bnsMount": "false","fec2/budget_execution_id": "","fec2/budget_number": "","fec2/buget-checked": "pass","fec2/cni-safe-region": "test_internal","fec2/combo_code": "567756428","fec2/device-id": "fec-PMOYhZDwP","fec2/disable-inject-sidecar": "false","fec2/fec-id": "fid-XxqWgKwoo","fec2/idc": "BDDX","fec2/over_budget": "true","fec2/owner": "jiayiming_dxm","fec2/product_name": "siod-kafka","fec2/retain": "true","fec2/saas": "redis","fec2/service_level": "","fec2/version": "v0.0.2","fec2/white": "false"},"hostname": "p-test-sc.fec.kj01.bddx.dxm-int.com","scheduleLimit": null,"containerInfo": [{"containerName": "r-r3-rd-test-0","imageName": "r.duxiaoman-int.com/siod_redis/redis-docker","imageVersion": "v2024072402","imagePullPolicy": "IfNotPresent","cpu": 1,"mem": 1,"port": null,"entryCommand": "","execArgs": null,"envArgs": {"CLOUD_TYPE": "kj","CONTAINER_NAME": "r-r3-rd-test-0","CONTAINER_TYPE": "main","DEVICE_TYPE": "pod","FECID": "fid-XxqWgKwoo","FEC_OPERATOR_URL": "https://************:11443/fec2","HAWKING_ENV": "container","INIT_NOAH_USER": "redis","MAX_MEMORY_POLICY": "volatile-lru","NODE_IP": "","POD_IP": "","POD_NAME": "","REDIS_PORT": "7000"},"diskApplication": 0,"mountType": "","volumeMounts": null,"status": "Running","privileged": false}]},
						{"id": "","podName": "p-test-d4","productName": "siod-redis","appName": "test-router","podIp": "*********","idc": "ZZJG","comboDesc": "","safeRegion": "test_internal","owner": "jiayiming_dxm","applicant": "","status": "Running","labels": {"avoid_delete_by_accident": "true","dxm-redis/component": "router","dxm-redis/name": "p-test","dxm-redis/taint": "true", "fec2/app_name": "p-test-router","fec2/bnsMount": "false","fec2/budget_execution_id": "","fec2/budget_number": "","fec2/buget-checked": "pass","fec2/cni-safe-region": "test_internal","fec2/combo_code": "567756428","fec2/device-id": "fec-PMOYhZDwP","fec2/disable-inject-sidecar": "false","fec2/fec-id": "fid-XxqWgKwoo","fec2/idc": "BDDX","fec2/over_budget": "true","fec2/owner": "jiayiming_dxm","fec2/product_name": "siod-kafka","fec2/retain": "true","fec2/saas": "redis","fec2/service_level": "","fec2/version": "v0.0.2","fec2/white": "false"},"hostname": "p-test-d4.fec.kj01.bddx.dxm-int.com","scheduleLimit": null,"containerInfo": [{"containerName": "r-r3-rd-test-0","imageName": "r.duxiaoman-int.com/siod_redis/redis-docker","imageVersion": "v2024072402","imagePullPolicy": "IfNotPresent","cpu": 1,"mem": 1,"port": null,"entryCommand": "","execArgs": null,"envArgs": {"CLOUD_TYPE": "kj","CONTAINER_NAME": "r-r3-rd-test-0","CONTAINER_TYPE": "main","DEVICE_TYPE": "pod","FECID": "fid-XxqWgKwoo","FEC_OPERATOR_URL": "https://************:11443/fec2","HAWKING_ENV": "container","INIT_NOAH_USER": "redis","MAX_MEMORY_POLICY": "volatile-lru","NODE_IP": "","POD_IP": "","POD_NAME": "","REDIS_PORT": "7000"},"diskApplication": 0,"mountType": "","volumeMounts": null,"status": "Running","privileged": false}]}
					]}`),
				)
				httpmock.RegisterResponder("GET", "http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-redis/apps/test-router/instances?showPage=0",
					httpmock.NewStringResponder(200, `{"success": true, "message": "OK", "data": [
						{"name": "0.test-router.siod-redis","hostName": "p-test-az.fec.kj01.bddx.dxm-int.com","ip": "*********","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8001},"deployInfo": {"deployPath": "/home/<USER>/local/router"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"deploy":"docker","idc": "hba","service":"hbas"},"disable": false,"instanceType": "host","podId": null,"containerList": []},
						{"name": "1.test-router.siod-redis","hostName": "p-test-gh.fec.kj01.bddx.dxm-int.com","ip": "*********","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8001},"deployInfo": {"deployPath": "/home/<USER>/local/router"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"deploy":"docker","idc": "hba","service":"hbas"},"disable": false,"instanceType": "host","podId": null,"containerList": []},
						{"name": "2.test-router.siod-redis","hostName": "p-test-sc.fec.kj01.zzjg.dxm-int.com","ip": "*********","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8001},"deployInfo": {"deployPath": "/home/<USER>/local/router"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"deploy":"docker","idc": "hbb","service":"hbbs"},"disable": false,"instanceType": "host","podId": null,"containerList": []},
						{"name": "3.test-router.siod-redis","hostName": "p-test-d4.fec.kj01.zzjg.dxm-int.com","ip": "*********","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8001},"deployInfo": {"deployPath": "/home/<USER>/local/router"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"deploy":"docker","idc": "hbb","service":"hbbs"},"disable": true,"instanceType": "host","podId": null,"containerList": []}
					]}`),
				)
			},
			args: args{
				deployment: &Cluster{
					Name: "test",
					Spec: &Spec{
						App: &AppSettings{ProductLine: "siod-redis", AppPrefix: "test"},
					},
				},
				proxyNum: 4,
			},
			wantErr: false,
		},
		{
			name: "taint but enabled",
			before: func() {
				httpmock.Reset()
				httpmock.RegisterResponder("POST", "http://*************:8536/fec-manager/api/v1/fec/listPod",
					httpmock.NewStringResponder(200, `{"errno": 0,"errmsg": "","data": [
						{"id": "","podName": "p-test-az","productName": "siod-redis","appName": "test-router","podIp": "*********","idc": "BDDX","comboDesc": "","safeRegion": "test_internal","owner": "jiayiming_dxm","applicant": "","status": "Running","labels": {"avoid_delete_by_accident": "true","dxm-redis/component": "router","dxm-redis/name": "p-test","dxm-redis/taint": "false","fec2/app_name": "p-test-router","fec2/bnsMount": "false","fec2/budget_execution_id": "","fec2/budget_number": "","fec2/buget-checked": "pass","fec2/cni-safe-region": "test_internal","fec2/combo_code": "567756428","fec2/device-id": "fec-PMOYhZDwP","fec2/disable-inject-sidecar": "false","fec2/fec-id": "fid-XxqWgKwoo","fec2/idc": "BDDX","fec2/over_budget": "true","fec2/owner": "jiayiming_dxm","fec2/product_name": "siod-kafka","fec2/retain": "true","fec2/saas": "redis","fec2/service_level": "","fec2/version": "v0.0.2","fec2/white": "false"},"hostname": "p-test-az.fec.kj01.bddx.dxm-int.com","scheduleLimit": null,"containerInfo": [{"containerName": "r-r3-rd-test-0","imageName": "r.duxiaoman-int.com/siod_redis/redis-docker","imageVersion": "v2024072402","imagePullPolicy": "IfNotPresent","cpu": 1,"mem": 1,"port": null,"entryCommand": "","execArgs": null,"envArgs": {"CLOUD_TYPE": "kj","CONTAINER_NAME": "r-r3-rd-test-0","CONTAINER_TYPE": "main","DEVICE_TYPE": "pod","FECID": "fid-XxqWgKwoo","FEC_OPERATOR_URL": "https://************:11443/fec2","HAWKING_ENV": "container","INIT_NOAH_USER": "redis","MAX_MEMORY_POLICY": "volatile-lru","NODE_IP": "","POD_IP": "","POD_NAME": "","REDIS_PORT": "7000"},"diskApplication": 0,"mountType": "","volumeMounts": null,"status": "Running","privileged": false}]},
						{"id": "","podName": "p-test-gh","productName": "siod-redis","appName": "test-router","podIp": "*********","idc": "BDDX","comboDesc": "","safeRegion": "test_internal","owner": "jiayiming_dxm","applicant": "","status": "Running","labels": {"avoid_delete_by_accident": "true","dxm-redis/component": "router","dxm-redis/name": "p-test","dxm-redis/taint": "false","fec2/app_name": "p-test-router","fec2/bnsMount": "false","fec2/budget_execution_id": "","fec2/budget_number": "","fec2/buget-checked": "pass","fec2/cni-safe-region": "test_internal","fec2/combo_code": "567756428","fec2/device-id": "fec-PMOYhZDwP","fec2/disable-inject-sidecar": "false","fec2/fec-id": "fid-XxqWgKwoo","fec2/idc": "BDDX","fec2/over_budget": "true","fec2/owner": "jiayiming_dxm","fec2/product_name": "siod-kafka","fec2/retain": "true","fec2/saas": "redis","fec2/service_level": "","fec2/version": "v0.0.2","fec2/white": "false"},"hostname": "p-test-gh.fec.kj01.bddx.dxm-int.com","scheduleLimit": null,"containerInfo": [{"containerName": "r-r3-rd-test-0","imageName": "r.duxiaoman-int.com/siod_redis/redis-docker","imageVersion": "v2024072402","imagePullPolicy": "IfNotPresent","cpu": 1,"mem": 1,"port": null,"entryCommand": "","execArgs": null,"envArgs": {"CLOUD_TYPE": "kj","CONTAINER_NAME": "r-r3-rd-test-0","CONTAINER_TYPE": "main","DEVICE_TYPE": "pod","FECID": "fid-XxqWgKwoo","FEC_OPERATOR_URL": "https://************:11443/fec2","HAWKING_ENV": "container","INIT_NOAH_USER": "redis","MAX_MEMORY_POLICY": "volatile-lru","NODE_IP": "","POD_IP": "","POD_NAME": "","REDIS_PORT": "7000"},"diskApplication": 0,"mountType": "","volumeMounts": null,"status": "Running","privileged": false}]},
						{"id": "","podName": "p-test-sc","productName": "siod-redis","appName": "test-router","podIp": "*********","idc": "ZZJG","comboDesc": "","safeRegion": "test_internal","owner": "jiayiming_dxm","applicant": "","status": "Running","labels": {"avoid_delete_by_accident": "true","dxm-redis/component": "router","dxm-redis/name": "p-test","dxm-redis/taint": "false","fec2/app_name": "p-test-router","fec2/bnsMount": "false","fec2/budget_execution_id": "","fec2/budget_number": "","fec2/buget-checked": "pass","fec2/cni-safe-region": "test_internal","fec2/combo_code": "567756428","fec2/device-id": "fec-PMOYhZDwP","fec2/disable-inject-sidecar": "false","fec2/fec-id": "fid-XxqWgKwoo","fec2/idc": "BDDX","fec2/over_budget": "true","fec2/owner": "jiayiming_dxm","fec2/product_name": "siod-kafka","fec2/retain": "true","fec2/saas": "redis","fec2/service_level": "","fec2/version": "v0.0.2","fec2/white": "false"},"hostname": "p-test-sc.fec.kj01.bddx.dxm-int.com","scheduleLimit": null,"containerInfo": [{"containerName": "r-r3-rd-test-0","imageName": "r.duxiaoman-int.com/siod_redis/redis-docker","imageVersion": "v2024072402","imagePullPolicy": "IfNotPresent","cpu": 1,"mem": 1,"port": null,"entryCommand": "","execArgs": null,"envArgs": {"CLOUD_TYPE": "kj","CONTAINER_NAME": "r-r3-rd-test-0","CONTAINER_TYPE": "main","DEVICE_TYPE": "pod","FECID": "fid-XxqWgKwoo","FEC_OPERATOR_URL": "https://************:11443/fec2","HAWKING_ENV": "container","INIT_NOAH_USER": "redis","MAX_MEMORY_POLICY": "volatile-lru","NODE_IP": "","POD_IP": "","POD_NAME": "","REDIS_PORT": "7000"},"diskApplication": 0,"mountType": "","volumeMounts": null,"status": "Running","privileged": false}]},
						{"id": "","podName": "p-test-d4","productName": "siod-redis","appName": "test-router","podIp": "*********","idc": "ZZJG","comboDesc": "","safeRegion": "test_internal","owner": "jiayiming_dxm","applicant": "","status": "Running","labels": {"avoid_delete_by_accident": "true","dxm-redis/component": "router","dxm-redis/name": "p-test","dxm-redis/taint": "true", "fec2/app_name": "p-test-router","fec2/bnsMount": "false","fec2/budget_execution_id": "","fec2/budget_number": "","fec2/buget-checked": "pass","fec2/cni-safe-region": "test_internal","fec2/combo_code": "567756428","fec2/device-id": "fec-PMOYhZDwP","fec2/disable-inject-sidecar": "false","fec2/fec-id": "fid-XxqWgKwoo","fec2/idc": "BDDX","fec2/over_budget": "true","fec2/owner": "jiayiming_dxm","fec2/product_name": "siod-kafka","fec2/retain": "true","fec2/saas": "redis","fec2/service_level": "","fec2/version": "v0.0.2","fec2/white": "false"},"hostname": "p-test-d4.fec.kj01.bddx.dxm-int.com","scheduleLimit": null,"containerInfo": [{"containerName": "r-r3-rd-test-0","imageName": "r.duxiaoman-int.com/siod_redis/redis-docker","imageVersion": "v2024072402","imagePullPolicy": "IfNotPresent","cpu": 1,"mem": 1,"port": null,"entryCommand": "","execArgs": null,"envArgs": {"CLOUD_TYPE": "kj","CONTAINER_NAME": "r-r3-rd-test-0","CONTAINER_TYPE": "main","DEVICE_TYPE": "pod","FECID": "fid-XxqWgKwoo","FEC_OPERATOR_URL": "https://************:11443/fec2","HAWKING_ENV": "container","INIT_NOAH_USER": "redis","MAX_MEMORY_POLICY": "volatile-lru","NODE_IP": "","POD_IP": "","POD_NAME": "","REDIS_PORT": "7000"},"diskApplication": 0,"mountType": "","volumeMounts": null,"status": "Running","privileged": false}]}
					]}`),
				)
				httpmock.RegisterResponder("GET", "http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-redis/apps/test-router/instances?showPage=0",
					httpmock.NewStringResponder(200, `{"success": true, "message": "OK", "data": [
						{"name": "0.test-router.siod-redis","hostName": "p-test-az.fec.kj01.bddx.dxm-int.com","ip": "*********","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8001},"deployInfo": {"deployPath": "/home/<USER>/local/router"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"deploy":"docker","idc": "hba","service":"hbas"},"disable": false,"instanceType": "host","podId": null,"containerList": []},
						{"name": "1.test-router.siod-redis","hostName": "p-test-gh.fec.kj01.bddx.dxm-int.com","ip": "*********","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8001},"deployInfo": {"deployPath": "/home/<USER>/local/router"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"deploy":"docker","idc": "hba","service":"hbas"},"disable": false,"instanceType": "host","podId": null,"containerList": []},
						{"name": "2.test-router.siod-redis","hostName": "p-test-sc.fec.kj01.zzjg.dxm-int.com","ip": "*********","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8001},"deployInfo": {"deployPath": "/home/<USER>/local/router"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"deploy":"docker","idc": "hbb","service":"hbbs"},"disable": false,"instanceType": "host","podId": null,"containerList": []},
						{"name": "3.test-router.siod-redis","hostName": "p-test-d4.fec.kj01.zzjg.dxm-int.com","ip": "*********","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8001},"deployInfo": {"deployPath": "/home/<USER>/local/router"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"deploy":"docker","idc": "hbb","service":"hbbs"},"disable": false,"instanceType": "host","podId": null,"containerList": []}
					]}`),
				)
				// 屏蔽
				httpmock.RegisterResponder("POST", "http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-redis/apps/test-router/instances/batchBlock",
					func(req *http.Request) (*http.Response, error) {
						type blockSchema struct {
							InstanceList []string `json:"instanceNameList"`
							Disable      int32    `json:"disable"`
						}
						args := blockSchema{}
						if err := json.NewDecoder(req.Body).Decode(&args); err != nil {
							return httpmock.NewStringResponse(400, ""), nil
						}
						if len(args.InstanceList) != 1 {
							return httpmock.NewStringResponse(200, `{"success": false,"message": "wrong number of instances","data": "ok"}`), nil
						}
						if args.InstanceList[0] != "3.test-router.siod-redis" {
							return httpmock.NewStringResponse(200, `{"success": false,"message": "wrong instance "`+args.InstanceList[0]+`,"data": "ok"}`), nil
						}
						return httpmock.NewStringResponse(200, `{"success": true, "message": "OK", "data": []}`), nil
					},
				)
			},
			args: args{
				deployment: &Cluster{
					Name: "test",
					Spec: &Spec{
						App: &AppSettings{ProductLine: "siod-redis", AppPrefix: "test"},
					},
				},
				proxyNum: 4,
			},
			wantErr: false,
		},
		{
			name: "scale down and pre disabled",
			before: func() {
				httpmock.Reset()
				httpmock.RegisterResponder("POST", "http://*************:8536/fec-manager/api/v1/fec/listPod",
					httpmock.NewStringResponder(200, `{"errno": 0,"errmsg": "","data": [
						{"id": "","podName": "p-test-az","productName": "siod-redis","appName": "test-router","podIp": "*********","idc": "BDDX","comboDesc": "","safeRegion": "test_internal","owner": "jiayiming_dxm","applicant": "","status": "Running","labels": {"avoid_delete_by_accident": "true","dxm-redis/component": "router","dxm-redis/name": "p-test","dxm-redis/taint": "false","fec2/app_name": "p-test-router","fec2/bnsMount": "false","fec2/budget_execution_id": "","fec2/budget_number": "","fec2/buget-checked": "pass","fec2/cni-safe-region": "test_internal","fec2/combo_code": "567756428","fec2/device-id": "fec-PMOYhZDwP","fec2/disable-inject-sidecar": "false","fec2/fec-id": "fid-XxqWgKwoo","fec2/idc": "BDDX","fec2/over_budget": "true","fec2/owner": "jiayiming_dxm","fec2/product_name": "siod-kafka","fec2/retain": "true","fec2/saas": "redis","fec2/service_level": "","fec2/version": "v0.0.2","fec2/white": "false"},"hostname": "p-test-az.fec.kj01.bddx.dxm-int.com","scheduleLimit": null,"containerInfo": [{"containerName": "r-r3-rd-test-0","imageName": "r.duxiaoman-int.com/siod_redis/redis-docker","imageVersion": "v2024072402","imagePullPolicy": "IfNotPresent","cpu": 1,"mem": 1,"port": null,"entryCommand": "","execArgs": null,"envArgs": {"CLOUD_TYPE": "kj","CONTAINER_NAME": "r-r3-rd-test-0","CONTAINER_TYPE": "main","DEVICE_TYPE": "pod","FECID": "fid-XxqWgKwoo","FEC_OPERATOR_URL": "https://************:11443/fec2","HAWKING_ENV": "container","INIT_NOAH_USER": "redis","MAX_MEMORY_POLICY": "volatile-lru","NODE_IP": "","POD_IP": "","POD_NAME": "","REDIS_PORT": "7000"},"diskApplication": 0,"mountType": "","volumeMounts": null,"status": "Running","privileged": false}]},
						{"id": "","podName": "p-test-gh","productName": "siod-redis","appName": "test-router","podIp": "*********","idc": "BDDX","comboDesc": "","safeRegion": "test_internal","owner": "jiayiming_dxm","applicant": "","status": "Running","labels": {"avoid_delete_by_accident": "true","dxm-redis/component": "router","dxm-redis/name": "p-test","dxm-redis/taint": "false","fec2/app_name": "p-test-router","fec2/bnsMount": "false","fec2/budget_execution_id": "","fec2/budget_number": "","fec2/buget-checked": "pass","fec2/cni-safe-region": "test_internal","fec2/combo_code": "567756428","fec2/device-id": "fec-PMOYhZDwP","fec2/disable-inject-sidecar": "false","fec2/fec-id": "fid-XxqWgKwoo","fec2/idc": "BDDX","fec2/over_budget": "true","fec2/owner": "jiayiming_dxm","fec2/product_name": "siod-kafka","fec2/retain": "true","fec2/saas": "redis","fec2/service_level": "","fec2/version": "v0.0.2","fec2/white": "false"},"hostname": "p-test-gh.fec.kj01.bddx.dxm-int.com","scheduleLimit": null,"containerInfo": [{"containerName": "r-r3-rd-test-0","imageName": "r.duxiaoman-int.com/siod_redis/redis-docker","imageVersion": "v2024072402","imagePullPolicy": "IfNotPresent","cpu": 1,"mem": 1,"port": null,"entryCommand": "","execArgs": null,"envArgs": {"CLOUD_TYPE": "kj","CONTAINER_NAME": "r-r3-rd-test-0","CONTAINER_TYPE": "main","DEVICE_TYPE": "pod","FECID": "fid-XxqWgKwoo","FEC_OPERATOR_URL": "https://************:11443/fec2","HAWKING_ENV": "container","INIT_NOAH_USER": "redis","MAX_MEMORY_POLICY": "volatile-lru","NODE_IP": "","POD_IP": "","POD_NAME": "","REDIS_PORT": "7000"},"diskApplication": 0,"mountType": "","volumeMounts": null,"status": "Running","privileged": false}]},
						{"id": "","podName": "p-test-sc","productName": "siod-redis","appName": "test-router","podIp": "*********","idc": "ZZJG","comboDesc": "","safeRegion": "test_internal","owner": "jiayiming_dxm","applicant": "","status": "Running","labels": {"avoid_delete_by_accident": "true","dxm-redis/component": "router","dxm-redis/name": "p-test","dxm-redis/taint": "false","fec2/app_name": "p-test-router","fec2/bnsMount": "false","fec2/budget_execution_id": "","fec2/budget_number": "","fec2/buget-checked": "pass","fec2/cni-safe-region": "test_internal","fec2/combo_code": "567756428","fec2/device-id": "fec-PMOYhZDwP","fec2/disable-inject-sidecar": "false","fec2/fec-id": "fid-XxqWgKwoo","fec2/idc": "BDDX","fec2/over_budget": "true","fec2/owner": "jiayiming_dxm","fec2/product_name": "siod-kafka","fec2/retain": "true","fec2/saas": "redis","fec2/service_level": "","fec2/version": "v0.0.2","fec2/white": "false"},"hostname": "p-test-sc.fec.kj01.bddx.dxm-int.com","scheduleLimit": null,"containerInfo": [{"containerName": "r-r3-rd-test-0","imageName": "r.duxiaoman-int.com/siod_redis/redis-docker","imageVersion": "v2024072402","imagePullPolicy": "IfNotPresent","cpu": 1,"mem": 1,"port": null,"entryCommand": "","execArgs": null,"envArgs": {"CLOUD_TYPE": "kj","CONTAINER_NAME": "r-r3-rd-test-0","CONTAINER_TYPE": "main","DEVICE_TYPE": "pod","FECID": "fid-XxqWgKwoo","FEC_OPERATOR_URL": "https://************:11443/fec2","HAWKING_ENV": "container","INIT_NOAH_USER": "redis","MAX_MEMORY_POLICY": "volatile-lru","NODE_IP": "","POD_IP": "","POD_NAME": "","REDIS_PORT": "7000"},"diskApplication": 0,"mountType": "","volumeMounts": null,"status": "Running","privileged": false}]},
						{"id": "","podName": "p-test-d4","productName": "siod-redis","appName": "test-router","podIp": "*********","idc": "ZZJG","comboDesc": "","safeRegion": "test_internal","owner": "jiayiming_dxm","applicant": "","status": "Running","labels": {"avoid_delete_by_accident": "true","dxm-redis/component": "router","dxm-redis/name": "p-test","dxm-redis/taint": "false", "fec2/app_name": "p-test-router","fec2/bnsMount": "false","fec2/budget_execution_id": "","fec2/budget_number": "","fec2/buget-checked": "pass","fec2/cni-safe-region": "test_internal","fec2/combo_code": "567756428","fec2/device-id": "fec-PMOYhZDwP","fec2/disable-inject-sidecar": "false","fec2/fec-id": "fid-XxqWgKwoo","fec2/idc": "BDDX","fec2/over_budget": "true","fec2/owner": "jiayiming_dxm","fec2/product_name": "siod-kafka","fec2/retain": "true","fec2/saas": "redis","fec2/service_level": "","fec2/version": "v0.0.2","fec2/white": "false"},"hostname": "p-test-d4.fec.kj01.bddx.dxm-int.com","scheduleLimit": null,"containerInfo": [{"containerName": "r-r3-rd-test-0","imageName": "r.duxiaoman-int.com/siod_redis/redis-docker","imageVersion": "v2024072402","imagePullPolicy": "IfNotPresent","cpu": 1,"mem": 1,"port": null,"entryCommand": "","execArgs": null,"envArgs": {"CLOUD_TYPE": "kj","CONTAINER_NAME": "r-r3-rd-test-0","CONTAINER_TYPE": "main","DEVICE_TYPE": "pod","FECID": "fid-XxqWgKwoo","FEC_OPERATOR_URL": "https://************:11443/fec2","HAWKING_ENV": "container","INIT_NOAH_USER": "redis","MAX_MEMORY_POLICY": "volatile-lru","NODE_IP": "","POD_IP": "","POD_NAME": "","REDIS_PORT": "7000"},"diskApplication": 0,"mountType": "","volumeMounts": null,"status": "Running","privileged": false}]}
					]}`),
				)
				httpmock.RegisterResponder("GET", "http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-redis/apps/test-router/instances?showPage=0",
					httpmock.NewStringResponder(200, `{"success": true, "message": "OK", "data": [
						{"name": "0.test-router.siod-redis","hostName": "p-test-az.fec.kj01.bddx.dxm-int.com","ip": "*********","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8001},"deployInfo": {"deployPath": "/home/<USER>/local/router"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"deploy":"docker","idc": "hba","service":"hbas"},"disable": false,"instanceType": "host","podId": null,"containerList": []},
						{"name": "1.test-router.siod-redis","hostName": "p-test-gh.fec.kj01.bddx.dxm-int.com","ip": "*********","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8001},"deployInfo": {"deployPath": "/home/<USER>/local/router"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"deploy":"docker","idc": "hba","service":"hbas"},"disable": true,"instanceType": "host","podId": null,"containerList": []},
						{"name": "2.test-router.siod-redis","hostName": "p-test-sc.fec.kj01.zzjg.dxm-int.com","ip": "*********","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8001},"deployInfo": {"deployPath": "/home/<USER>/local/router"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"deploy":"docker","idc": "hbb","service":"hbbs"},"disable": false,"instanceType": "host","podId": null,"containerList": []},
						{"name": "3.test-router.siod-redis","hostName": "p-test-d4.fec.kj01.zzjg.dxm-int.com","ip": "*********","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8001},"deployInfo": {"deployPath": "/home/<USER>/local/router"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"deploy":"docker","idc": "hbb","service":"hbbs"},"disable": true,"instanceType": "host","podId": null,"containerList": []}
					]}`),
				)
				// 污点
				httpmock.RegisterResponder("POST", "http://*************:8536/fec-manager/api/v1/fec/labelPod",
					func(req *http.Request) (*http.Response, error) {
						args := fec.LabelPodRequstParam{}
						if err := json.NewDecoder(req.Body).Decode(&args); err != nil {
							return httpmock.NewStringResponse(400, ""), nil
						}
						if args.PodName != "p-test-d4" && args.PodName != "p-test-gh" {
							return httpmock.NewStringResponse(200, `{"errno": 1,"errmsg": "wrong pod name "`+args.PodName+`,"data": "ok"}`), nil
						}
						return httpmock.NewStringResponse(200, `{"errno": 0,"errmsg": "","data": "ok"}`), nil
					},
				)
			},
			args: args{
				deployment: &Cluster{
					Name: "test",
					Spec: &Spec{
						App: &AppSettings{ProductLine: "siod-redis", AppPrefix: "test"},
					},
				},
				proxyNum: 1,
			},
			wantErr: false,
		},
		{
			name: "scale down and disabled instances less than expected",
			before: func() {
				httpmock.Reset()
				httpmock.RegisterResponder("POST", "http://*************:8536/fec-manager/api/v1/fec/listPod",
					httpmock.NewStringResponder(200, `{"errno": 0,"errmsg": "","data": [
						{"id": "","podName": "p-test-az","productName": "siod-redis","appName": "test-router","podIp": "*********","idc": "BDDX","comboDesc": "","safeRegion": "test_internal","owner": "jiayiming_dxm","applicant": "","status": "Running","labels": {"avoid_delete_by_accident": "true","dxm-redis/component": "router","dxm-redis/name": "p-test","dxm-redis/taint": "false","fec2/app_name": "p-test-router","fec2/bnsMount": "false","fec2/budget_execution_id": "","fec2/budget_number": "","fec2/buget-checked": "pass","fec2/cni-safe-region": "test_internal","fec2/combo_code": "567756428","fec2/device-id": "fec-PMOYhZDwP","fec2/disable-inject-sidecar": "false","fec2/fec-id": "fid-XxqWgKwoo","fec2/idc": "BDDX","fec2/over_budget": "true","fec2/owner": "jiayiming_dxm","fec2/product_name": "siod-kafka","fec2/retain": "true","fec2/saas": "redis","fec2/service_level": "","fec2/version": "v0.0.2","fec2/white": "false"},"hostname": "p-test-az.fec.kj01.bddx.dxm-int.com","scheduleLimit": null,"containerInfo": [{"containerName": "r-r3-rd-test-0","imageName": "r.duxiaoman-int.com/siod_redis/redis-docker","imageVersion": "v2024072402","imagePullPolicy": "IfNotPresent","cpu": 1,"mem": 1,"port": null,"entryCommand": "","execArgs": null,"envArgs": {"CLOUD_TYPE": "kj","CONTAINER_NAME": "r-r3-rd-test-0","CONTAINER_TYPE": "main","DEVICE_TYPE": "pod","FECID": "fid-XxqWgKwoo","FEC_OPERATOR_URL": "https://************:11443/fec2","HAWKING_ENV": "container","INIT_NOAH_USER": "redis","MAX_MEMORY_POLICY": "volatile-lru","NODE_IP": "","POD_IP": "","POD_NAME": "","REDIS_PORT": "7000"},"diskApplication": 0,"mountType": "","volumeMounts": null,"status": "Running","privileged": false}]},
						{"id": "","podName": "p-test-gh","productName": "siod-redis","appName": "test-router","podIp": "*********","idc": "BDDX","comboDesc": "","safeRegion": "test_internal","owner": "jiayiming_dxm","applicant": "","status": "Running","labels": {"avoid_delete_by_accident": "true","dxm-redis/component": "router","dxm-redis/name": "p-test","dxm-redis/taint": "false","fec2/app_name": "p-test-router","fec2/bnsMount": "false","fec2/budget_execution_id": "","fec2/budget_number": "","fec2/buget-checked": "pass","fec2/cni-safe-region": "test_internal","fec2/combo_code": "567756428","fec2/device-id": "fec-PMOYhZDwP","fec2/disable-inject-sidecar": "false","fec2/fec-id": "fid-XxqWgKwoo","fec2/idc": "BDDX","fec2/over_budget": "true","fec2/owner": "jiayiming_dxm","fec2/product_name": "siod-kafka","fec2/retain": "true","fec2/saas": "redis","fec2/service_level": "","fec2/version": "v0.0.2","fec2/white": "false"},"hostname": "p-test-gh.fec.kj01.bddx.dxm-int.com","scheduleLimit": null,"containerInfo": [{"containerName": "r-r3-rd-test-0","imageName": "r.duxiaoman-int.com/siod_redis/redis-docker","imageVersion": "v2024072402","imagePullPolicy": "IfNotPresent","cpu": 1,"mem": 1,"port": null,"entryCommand": "","execArgs": null,"envArgs": {"CLOUD_TYPE": "kj","CONTAINER_NAME": "r-r3-rd-test-0","CONTAINER_TYPE": "main","DEVICE_TYPE": "pod","FECID": "fid-XxqWgKwoo","FEC_OPERATOR_URL": "https://************:11443/fec2","HAWKING_ENV": "container","INIT_NOAH_USER": "redis","MAX_MEMORY_POLICY": "volatile-lru","NODE_IP": "","POD_IP": "","POD_NAME": "","REDIS_PORT": "7000"},"diskApplication": 0,"mountType": "","volumeMounts": null,"status": "Running","privileged": false}]},
						{"id": "","podName": "p-test-sc","productName": "siod-redis","appName": "test-router","podIp": "*********","idc": "BDDX","comboDesc": "","safeRegion": "test_internal","owner": "jiayiming_dxm","applicant": "","status": "Running","labels": {"avoid_delete_by_accident": "true","dxm-redis/component": "router","dxm-redis/name": "p-test","dxm-redis/taint": "false","fec2/app_name": "p-test-router","fec2/bnsMount": "false","fec2/budget_execution_id": "","fec2/budget_number": "","fec2/buget-checked": "pass","fec2/cni-safe-region": "test_internal","fec2/combo_code": "567756428","fec2/device-id": "fec-PMOYhZDwP","fec2/disable-inject-sidecar": "false","fec2/fec-id": "fid-XxqWgKwoo","fec2/idc": "BDDX","fec2/over_budget": "true","fec2/owner": "jiayiming_dxm","fec2/product_name": "siod-kafka","fec2/retain": "true","fec2/saas": "redis","fec2/service_level": "","fec2/version": "v0.0.2","fec2/white": "false"},"hostname": "p-test-sc.fec.kj01.bddx.dxm-int.com","scheduleLimit": null,"containerInfo": [{"containerName": "r-r3-rd-test-0","imageName": "r.duxiaoman-int.com/siod_redis/redis-docker","imageVersion": "v2024072402","imagePullPolicy": "IfNotPresent","cpu": 1,"mem": 1,"port": null,"entryCommand": "","execArgs": null,"envArgs": {"CLOUD_TYPE": "kj","CONTAINER_NAME": "r-r3-rd-test-0","CONTAINER_TYPE": "main","DEVICE_TYPE": "pod","FECID": "fid-XxqWgKwoo","FEC_OPERATOR_URL": "https://************:11443/fec2","HAWKING_ENV": "container","INIT_NOAH_USER": "redis","MAX_MEMORY_POLICY": "volatile-lru","NODE_IP": "","POD_IP": "","POD_NAME": "","REDIS_PORT": "7000"},"diskApplication": 0,"mountType": "","volumeMounts": null,"status": "Running","privileged": false}]},
						{"id": "","podName": "p-test-ga","productName": "siod-redis","appName": "test-router","podIp": "*********","idc": "ZZJG","comboDesc": "","safeRegion": "test_internal","owner": "jiayiming_dxm","applicant": "","status": "Running","labels": {"avoid_delete_by_accident": "true","dxm-redis/component": "router","dxm-redis/name": "p-test","dxm-redis/taint": "false","fec2/app_name": "p-test-router","fec2/bnsMount": "false","fec2/budget_execution_id": "","fec2/budget_number": "","fec2/buget-checked": "pass","fec2/cni-safe-region": "test_internal","fec2/combo_code": "567756428","fec2/device-id": "fec-PMOYhZDwP","fec2/disable-inject-sidecar": "false","fec2/fec-id": "fid-XxqWgKwoo","fec2/idc": "BDDX","fec2/over_budget": "true","fec2/owner": "jiayiming_dxm","fec2/product_name": "siod-kafka","fec2/retain": "true","fec2/saas": "redis","fec2/service_level": "","fec2/version": "v0.0.2","fec2/white": "false"},"hostname": "p-test-gh.fec.kj01.bddx.dxm-int.com","scheduleLimit": null,"containerInfo": [{"containerName": "r-r3-rd-test-0","imageName": "r.duxiaoman-int.com/siod_redis/redis-docker","imageVersion": "v2024072402","imagePullPolicy": "IfNotPresent","cpu": 1,"mem": 1,"port": null,"entryCommand": "","execArgs": null,"envArgs": {"CLOUD_TYPE": "kj","CONTAINER_NAME": "r-r3-rd-test-0","CONTAINER_TYPE": "main","DEVICE_TYPE": "pod","FECID": "fid-XxqWgKwoo","FEC_OPERATOR_URL": "https://************:11443/fec2","HAWKING_ENV": "container","INIT_NOAH_USER": "redis","MAX_MEMORY_POLICY": "volatile-lru","NODE_IP": "","POD_IP": "","POD_NAME": "","REDIS_PORT": "7000"},"diskApplication": 0,"mountType": "","volumeMounts": null,"status": "Running","privileged": false}]},
						{"id": "","podName": "p-test-cc","productName": "siod-redis","appName": "test-router","podIp": "*********","idc": "ZZJG","comboDesc": "","safeRegion": "test_internal","owner": "jiayiming_dxm","applicant": "","status": "Running","labels": {"avoid_delete_by_accident": "true","dxm-redis/component": "router","dxm-redis/name": "p-test","dxm-redis/taint": "false","fec2/app_name": "p-test-router","fec2/bnsMount": "false","fec2/budget_execution_id": "","fec2/budget_number": "","fec2/buget-checked": "pass","fec2/cni-safe-region": "test_internal","fec2/combo_code": "567756428","fec2/device-id": "fec-PMOYhZDwP","fec2/disable-inject-sidecar": "false","fec2/fec-id": "fid-XxqWgKwoo","fec2/idc": "BDDX","fec2/over_budget": "true","fec2/owner": "jiayiming_dxm","fec2/product_name": "siod-kafka","fec2/retain": "true","fec2/saas": "redis","fec2/service_level": "","fec2/version": "v0.0.2","fec2/white": "false"},"hostname": "p-test-sc.fec.kj01.bddx.dxm-int.com","scheduleLimit": null,"containerInfo": [{"containerName": "r-r3-rd-test-0","imageName": "r.duxiaoman-int.com/siod_redis/redis-docker","imageVersion": "v2024072402","imagePullPolicy": "IfNotPresent","cpu": 1,"mem": 1,"port": null,"entryCommand": "","execArgs": null,"envArgs": {"CLOUD_TYPE": "kj","CONTAINER_NAME": "r-r3-rd-test-0","CONTAINER_TYPE": "main","DEVICE_TYPE": "pod","FECID": "fid-XxqWgKwoo","FEC_OPERATOR_URL": "https://************:11443/fec2","HAWKING_ENV": "container","INIT_NOAH_USER": "redis","MAX_MEMORY_POLICY": "volatile-lru","NODE_IP": "","POD_IP": "","POD_NAME": "","REDIS_PORT": "7000"},"diskApplication": 0,"mountType": "","volumeMounts": null,"status": "Running","privileged": false}]},
						{"id": "","podName": "p-test-d4","productName": "siod-redis","appName": "test-router","podIp": "*********","idc": "ZZJG","comboDesc": "","safeRegion": "test_internal","owner": "jiayiming_dxm","applicant": "","status": "Running","labels": {"avoid_delete_by_accident": "true","dxm-redis/component": "router","dxm-redis/name": "p-test","dxm-redis/taint": "false", "fec2/app_name": "p-test-router","fec2/bnsMount": "false","fec2/budget_execution_id": "","fec2/budget_number": "","fec2/buget-checked": "pass","fec2/cni-safe-region": "test_internal","fec2/combo_code": "567756428","fec2/device-id": "fec-PMOYhZDwP","fec2/disable-inject-sidecar": "false","fec2/fec-id": "fid-XxqWgKwoo","fec2/idc": "BDDX","fec2/over_budget": "true","fec2/owner": "jiayiming_dxm","fec2/product_name": "siod-kafka","fec2/retain": "true","fec2/saas": "redis","fec2/service_level": "","fec2/version": "v0.0.2","fec2/white": "false"},"hostname": "p-test-d4.fec.kj01.bddx.dxm-int.com","scheduleLimit": null,"containerInfo": [{"containerName": "r-r3-rd-test-0","imageName": "r.duxiaoman-int.com/siod_redis/redis-docker","imageVersion": "v2024072402","imagePullPolicy": "IfNotPresent","cpu": 1,"mem": 1,"port": null,"entryCommand": "","execArgs": null,"envArgs": {"CLOUD_TYPE": "kj","CONTAINER_NAME": "r-r3-rd-test-0","CONTAINER_TYPE": "main","DEVICE_TYPE": "pod","FECID": "fid-XxqWgKwoo","FEC_OPERATOR_URL": "https://************:11443/fec2","HAWKING_ENV": "container","INIT_NOAH_USER": "redis","MAX_MEMORY_POLICY": "volatile-lru","NODE_IP": "","POD_IP": "","POD_NAME": "","REDIS_PORT": "7000"},"diskApplication": 0,"mountType": "","volumeMounts": null,"status": "Running","privileged": false}]}
					]}`),
				)
				httpmock.RegisterResponder("GET", "http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-redis/apps/test-router/instances?showPage=0",
					httpmock.NewStringResponder(200, `{"success": true, "message": "OK", "data": [
						{"name": "0.test-router.siod-redis","hostName": "p-test-az.fec.kj01.bddx.dxm-int.com","ip": "*********","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8001},"deployInfo": {"deployPath": "/home/<USER>/local/router"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"deploy":"docker","idc": "hba","service":"hbas"},"disable": false,"instanceType": "host","podId": null,"containerList": []},
						{"name": "1.test-router.siod-redis","hostName": "p-test-gh.fec.kj01.bddx.dxm-int.com","ip": "*********","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8001},"deployInfo": {"deployPath": "/home/<USER>/local/router"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"deploy":"docker","idc": "hba","service":"hbas"},"disable": false,"instanceType": "host","podId": null,"containerList": []},
						{"name": "2.test-router.siod-redis","hostName": "p-test-sc.fec.kj01.bddx.dxm-int.com","ip": "*********","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8001},"deployInfo": {"deployPath": "/home/<USER>/local/router"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"deploy":"docker","idc": "hba","service":"hbas"},"disable": false,"instanceType": "host","podId": null,"containerList": []},
						{"name": "3.test-router.siod-redis","hostName": "p-test-ga.fec.kj01.zzjg.dxm-int.com","ip": "*********","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8001},"deployInfo": {"deployPath": "/home/<USER>/local/router"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"deploy":"docker","idc": "hbb","service":"hbbs"},"disable": false,"instanceType": "host","podId": null,"containerList": []},
						{"name": "4.test-router.siod-redis","hostName": "p-test-cc.fec.kj01.zzjg.dxm-int.com","ip": "*********","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8001},"deployInfo": {"deployPath": "/home/<USER>/local/router"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"deploy":"docker","idc": "hbb","service":"hbbs"},"disable": true,"instanceType": "host","podId": null,"containerList": []},
						{"name": "5.test-router.siod-redis","hostName": "p-test-d4.fec.kj01.zzjg.dxm-int.com","ip": "*********","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8001},"deployInfo": {"deployPath": "/home/<USER>/local/router"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"deploy":"docker","idc": "hbb","service":"hbbs"},"disable": false,"instanceType": "host","podId": null,"containerList": []}
					]}`),
				)
				// 屏蔽
				httpmock.RegisterResponder("POST", "http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-redis/apps/test-router/instances/batchBlock",
					httpmock.NewStringResponder(200, `{"success": true, "message": "OK", "data": []}`),
				)
				// 污点
				httpmock.RegisterResponder("POST", "http://*************:8536/fec-manager/api/v1/fec/labelPod",
					func(req *http.Request) (*http.Response, error) {
						args := fec.LabelPodRequstParam{}
						if err := json.NewDecoder(req.Body).Decode(&args); err != nil {
							return httpmock.NewStringResponse(400, ""), nil
						}
						if args.PodName != "p-test-d4" && args.PodName != "p-test-sc" && args.PodName != "p-test-gh" && args.PodName != "p-test-cc" {
							return httpmock.NewStringResponse(200, `{"errno": 1,"errmsg": "wrong pod name "`+args.PodName+`,"data": "ok"}`), nil
						}
						return httpmock.NewStringResponse(200, `{"errno": 0,"errmsg": "","data": "ok"}`), nil
					},
				)
			},
			args: args{
				deployment: &Cluster{
					Name: "test",
					Spec: &Spec{
						App: &AppSettings{ProductLine: "siod-redis", AppPrefix: "test"},
					},
				},
				proxyNum: 1,
			},
			wantErr: false,
		},
		{
			name: "scale down and disabled instances more than expected",
			before: func() {
				httpmock.Reset()
				httpmock.RegisterResponder("POST", "http://*************:8536/fec-manager/api/v1/fec/listPod",
					httpmock.NewStringResponder(200, `{"errno": 0,"errmsg": "","data": [
						{"id": "","podName": "p-test-az","productName": "siod-redis","appName": "test-router","podIp": "*********","idc": "BDDX","comboDesc": "","safeRegion": "test_internal","owner": "jiayiming_dxm","applicant": "","status": "Running","labels": {"avoid_delete_by_accident": "true","dxm-redis/component": "router","dxm-redis/name": "p-test","dxm-redis/taint": "false","fec2/app_name": "p-test-router","fec2/bnsMount": "false","fec2/budget_execution_id": "","fec2/budget_number": "","fec2/buget-checked": "pass","fec2/cni-safe-region": "test_internal","fec2/combo_code": "567756428","fec2/device-id": "fec-PMOYhZDwP","fec2/disable-inject-sidecar": "false","fec2/fec-id": "fid-XxqWgKwoo","fec2/idc": "BDDX","fec2/over_budget": "true","fec2/owner": "jiayiming_dxm","fec2/product_name": "siod-kafka","fec2/retain": "true","fec2/saas": "redis","fec2/service_level": "","fec2/version": "v0.0.2","fec2/white": "false"},"hostname": "p-test-az.fec.kj01.bddx.dxm-int.com","scheduleLimit": null,"containerInfo": [{"containerName": "r-r3-rd-test-0","imageName": "r.duxiaoman-int.com/siod_redis/redis-docker","imageVersion": "v2024072402","imagePullPolicy": "IfNotPresent","cpu": 1,"mem": 1,"port": null,"entryCommand": "","execArgs": null,"envArgs": {"CLOUD_TYPE": "kj","CONTAINER_NAME": "r-r3-rd-test-0","CONTAINER_TYPE": "main","DEVICE_TYPE": "pod","FECID": "fid-XxqWgKwoo","FEC_OPERATOR_URL": "https://************:11443/fec2","HAWKING_ENV": "container","INIT_NOAH_USER": "redis","MAX_MEMORY_POLICY": "volatile-lru","NODE_IP": "","POD_IP": "","POD_NAME": "","REDIS_PORT": "7000"},"diskApplication": 0,"mountType": "","volumeMounts": null,"status": "Running","privileged": false}]},
						{"id": "","podName": "p-test-gh","productName": "siod-redis","appName": "test-router","podIp": "*********","idc": "BDDX","comboDesc": "","safeRegion": "test_internal","owner": "jiayiming_dxm","applicant": "","status": "Running","labels": {"avoid_delete_by_accident": "true","dxm-redis/component": "router","dxm-redis/name": "p-test","dxm-redis/taint": "false","fec2/app_name": "p-test-router","fec2/bnsMount": "false","fec2/budget_execution_id": "","fec2/budget_number": "","fec2/buget-checked": "pass","fec2/cni-safe-region": "test_internal","fec2/combo_code": "567756428","fec2/device-id": "fec-PMOYhZDwP","fec2/disable-inject-sidecar": "false","fec2/fec-id": "fid-XxqWgKwoo","fec2/idc": "BDDX","fec2/over_budget": "true","fec2/owner": "jiayiming_dxm","fec2/product_name": "siod-kafka","fec2/retain": "true","fec2/saas": "redis","fec2/service_level": "","fec2/version": "v0.0.2","fec2/white": "false"},"hostname": "p-test-gh.fec.kj01.bddx.dxm-int.com","scheduleLimit": null,"containerInfo": [{"containerName": "r-r3-rd-test-0","imageName": "r.duxiaoman-int.com/siod_redis/redis-docker","imageVersion": "v2024072402","imagePullPolicy": "IfNotPresent","cpu": 1,"mem": 1,"port": null,"entryCommand": "","execArgs": null,"envArgs": {"CLOUD_TYPE": "kj","CONTAINER_NAME": "r-r3-rd-test-0","CONTAINER_TYPE": "main","DEVICE_TYPE": "pod","FECID": "fid-XxqWgKwoo","FEC_OPERATOR_URL": "https://************:11443/fec2","HAWKING_ENV": "container","INIT_NOAH_USER": "redis","MAX_MEMORY_POLICY": "volatile-lru","NODE_IP": "","POD_IP": "","POD_NAME": "","REDIS_PORT": "7000"},"diskApplication": 0,"mountType": "","volumeMounts": null,"status": "Running","privileged": false}]},
						{"id": "","podName": "p-test-sc","productName": "siod-redis","appName": "test-router","podIp": "*********","idc": "BDDX","comboDesc": "","safeRegion": "test_internal","owner": "jiayiming_dxm","applicant": "","status": "Running","labels": {"avoid_delete_by_accident": "true","dxm-redis/component": "router","dxm-redis/name": "p-test","dxm-redis/taint": "false","fec2/app_name": "p-test-router","fec2/bnsMount": "false","fec2/budget_execution_id": "","fec2/budget_number": "","fec2/buget-checked": "pass","fec2/cni-safe-region": "test_internal","fec2/combo_code": "567756428","fec2/device-id": "fec-PMOYhZDwP","fec2/disable-inject-sidecar": "false","fec2/fec-id": "fid-XxqWgKwoo","fec2/idc": "BDDX","fec2/over_budget": "true","fec2/owner": "jiayiming_dxm","fec2/product_name": "siod-kafka","fec2/retain": "true","fec2/saas": "redis","fec2/service_level": "","fec2/version": "v0.0.2","fec2/white": "false"},"hostname": "p-test-sc.fec.kj01.bddx.dxm-int.com","scheduleLimit": null,"containerInfo": [{"containerName": "r-r3-rd-test-0","imageName": "r.duxiaoman-int.com/siod_redis/redis-docker","imageVersion": "v2024072402","imagePullPolicy": "IfNotPresent","cpu": 1,"mem": 1,"port": null,"entryCommand": "","execArgs": null,"envArgs": {"CLOUD_TYPE": "kj","CONTAINER_NAME": "r-r3-rd-test-0","CONTAINER_TYPE": "main","DEVICE_TYPE": "pod","FECID": "fid-XxqWgKwoo","FEC_OPERATOR_URL": "https://************:11443/fec2","HAWKING_ENV": "container","INIT_NOAH_USER": "redis","MAX_MEMORY_POLICY": "volatile-lru","NODE_IP": "","POD_IP": "","POD_NAME": "","REDIS_PORT": "7000"},"diskApplication": 0,"mountType": "","volumeMounts": null,"status": "Running","privileged": false}]},
						{"id": "","podName": "p-test-ga","productName": "siod-redis","appName": "test-router","podIp": "*********","idc": "ZZJG","comboDesc": "","safeRegion": "test_internal","owner": "jiayiming_dxm","applicant": "","status": "Running","labels": {"avoid_delete_by_accident": "true","dxm-redis/component": "router","dxm-redis/name": "p-test","dxm-redis/taint": "false","fec2/app_name": "p-test-router","fec2/bnsMount": "false","fec2/budget_execution_id": "","fec2/budget_number": "","fec2/buget-checked": "pass","fec2/cni-safe-region": "test_internal","fec2/combo_code": "567756428","fec2/device-id": "fec-PMOYhZDwP","fec2/disable-inject-sidecar": "false","fec2/fec-id": "fid-XxqWgKwoo","fec2/idc": "BDDX","fec2/over_budget": "true","fec2/owner": "jiayiming_dxm","fec2/product_name": "siod-kafka","fec2/retain": "true","fec2/saas": "redis","fec2/service_level": "","fec2/version": "v0.0.2","fec2/white": "false"},"hostname": "p-test-gh.fec.kj01.bddx.dxm-int.com","scheduleLimit": null,"containerInfo": [{"containerName": "r-r3-rd-test-0","imageName": "r.duxiaoman-int.com/siod_redis/redis-docker","imageVersion": "v2024072402","imagePullPolicy": "IfNotPresent","cpu": 1,"mem": 1,"port": null,"entryCommand": "","execArgs": null,"envArgs": {"CLOUD_TYPE": "kj","CONTAINER_NAME": "r-r3-rd-test-0","CONTAINER_TYPE": "main","DEVICE_TYPE": "pod","FECID": "fid-XxqWgKwoo","FEC_OPERATOR_URL": "https://************:11443/fec2","HAWKING_ENV": "container","INIT_NOAH_USER": "redis","MAX_MEMORY_POLICY": "volatile-lru","NODE_IP": "","POD_IP": "","POD_NAME": "","REDIS_PORT": "7000"},"diskApplication": 0,"mountType": "","volumeMounts": null,"status": "Running","privileged": false}]},
						{"id": "","podName": "p-test-cc","productName": "siod-redis","appName": "test-router","podIp": "*********","idc": "ZZJG","comboDesc": "","safeRegion": "test_internal","owner": "jiayiming_dxm","applicant": "","status": "Running","labels": {"avoid_delete_by_accident": "true","dxm-redis/component": "router","dxm-redis/name": "p-test","dxm-redis/taint": "false","fec2/app_name": "p-test-router","fec2/bnsMount": "false","fec2/budget_execution_id": "","fec2/budget_number": "","fec2/buget-checked": "pass","fec2/cni-safe-region": "test_internal","fec2/combo_code": "567756428","fec2/device-id": "fec-PMOYhZDwP","fec2/disable-inject-sidecar": "false","fec2/fec-id": "fid-XxqWgKwoo","fec2/idc": "BDDX","fec2/over_budget": "true","fec2/owner": "jiayiming_dxm","fec2/product_name": "siod-kafka","fec2/retain": "true","fec2/saas": "redis","fec2/service_level": "","fec2/version": "v0.0.2","fec2/white": "false"},"hostname": "p-test-sc.fec.kj01.bddx.dxm-int.com","scheduleLimit": null,"containerInfo": [{"containerName": "r-r3-rd-test-0","imageName": "r.duxiaoman-int.com/siod_redis/redis-docker","imageVersion": "v2024072402","imagePullPolicy": "IfNotPresent","cpu": 1,"mem": 1,"port": null,"entryCommand": "","execArgs": null,"envArgs": {"CLOUD_TYPE": "kj","CONTAINER_NAME": "r-r3-rd-test-0","CONTAINER_TYPE": "main","DEVICE_TYPE": "pod","FECID": "fid-XxqWgKwoo","FEC_OPERATOR_URL": "https://************:11443/fec2","HAWKING_ENV": "container","INIT_NOAH_USER": "redis","MAX_MEMORY_POLICY": "volatile-lru","NODE_IP": "","POD_IP": "","POD_NAME": "","REDIS_PORT": "7000"},"diskApplication": 0,"mountType": "","volumeMounts": null,"status": "Running","privileged": false}]},
						{"id": "","podName": "p-test-d4","productName": "siod-redis","appName": "test-router","podIp": "*********","idc": "ZZJG","comboDesc": "","safeRegion": "test_internal","owner": "jiayiming_dxm","applicant": "","status": "Running","labels": {"avoid_delete_by_accident": "true","dxm-redis/component": "router","dxm-redis/name": "p-test","dxm-redis/taint": "false", "fec2/app_name": "p-test-router","fec2/bnsMount": "false","fec2/budget_execution_id": "","fec2/budget_number": "","fec2/buget-checked": "pass","fec2/cni-safe-region": "test_internal","fec2/combo_code": "567756428","fec2/device-id": "fec-PMOYhZDwP","fec2/disable-inject-sidecar": "false","fec2/fec-id": "fid-XxqWgKwoo","fec2/idc": "BDDX","fec2/over_budget": "true","fec2/owner": "jiayiming_dxm","fec2/product_name": "siod-kafka","fec2/retain": "true","fec2/saas": "redis","fec2/service_level": "","fec2/version": "v0.0.2","fec2/white": "false"},"hostname": "p-test-d4.fec.kj01.bddx.dxm-int.com","scheduleLimit": null,"containerInfo": [{"containerName": "r-r3-rd-test-0","imageName": "r.duxiaoman-int.com/siod_redis/redis-docker","imageVersion": "v2024072402","imagePullPolicy": "IfNotPresent","cpu": 1,"mem": 1,"port": null,"entryCommand": "","execArgs": null,"envArgs": {"CLOUD_TYPE": "kj","CONTAINER_NAME": "r-r3-rd-test-0","CONTAINER_TYPE": "main","DEVICE_TYPE": "pod","FECID": "fid-XxqWgKwoo","FEC_OPERATOR_URL": "https://************:11443/fec2","HAWKING_ENV": "container","INIT_NOAH_USER": "redis","MAX_MEMORY_POLICY": "volatile-lru","NODE_IP": "","POD_IP": "","POD_NAME": "","REDIS_PORT": "7000"},"diskApplication": 0,"mountType": "","volumeMounts": null,"status": "Running","privileged": false}]}
					]}`),
				)
				httpmock.RegisterResponder("GET", "http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-redis/apps/test-router/instances?showPage=0",
					httpmock.NewStringResponder(200, `{"success": true, "message": "OK", "data": [
						{"name": "0.test-router.siod-redis","hostName": "p-test-az.fec.kj01.bddx.dxm-int.com","ip": "*********","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8001},"deployInfo": {"deployPath": "/home/<USER>/local/router"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"deploy":"docker","idc": "hba","service":"hbas"},"disable": true,"instanceType": "host","podId": null,"containerList": []},
						{"name": "1.test-router.siod-redis","hostName": "p-test-gh.fec.kj01.bddx.dxm-int.com","ip": "*********","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8001},"deployInfo": {"deployPath": "/home/<USER>/local/router"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"deploy":"docker","idc": "hba","service":"hbas"},"disable": false,"instanceType": "host","podId": null,"containerList": []},
						{"name": "2.test-router.siod-redis","hostName": "p-test-sc.fec.kj01.bddx.dxm-int.com","ip": "*********","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8001},"deployInfo": {"deployPath": "/home/<USER>/local/router"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"deploy":"docker","idc": "hba","service":"hbas"},"disable": true,"instanceType": "host","podId": null,"containerList": []},
						{"name": "3.test-router.siod-redis","hostName": "p-test-ga.fec.kj01.zzjg.dxm-int.com","ip": "*********","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8001},"deployInfo": {"deployPath": "/home/<USER>/local/router"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"deploy":"docker","idc": "hbb","service":"hbbs"},"disable": true,"instanceType": "host","podId": null,"containerList": []},
						{"name": "4.test-router.siod-redis","hostName": "p-test-cc.fec.kj01.zzjg.dxm-int.com","ip": "*********","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8001},"deployInfo": {"deployPath": "/home/<USER>/local/router"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"deploy":"docker","idc": "hbb","service":"hbbs"},"disable": true,"instanceType": "host","podId": null,"containerList": []},
						{"name": "5.test-router.siod-redis","hostName": "p-test-d4.fec.kj01.zzjg.dxm-int.com","ip": "*********","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8001},"deployInfo": {"deployPath": "/home/<USER>/local/router"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"deploy":"docker","idc": "hbb","service":"hbbs"},"disable": false,"instanceType": "host","podId": null,"containerList": []}
					]}`),
				)
				// 污点
				httpmock.RegisterResponder("POST", "http://*************:8536/fec-manager/api/v1/fec/labelPod",
					func(req *http.Request) (*http.Response, error) {
						args := fec.LabelPodRequstParam{}
						if err := json.NewDecoder(req.Body).Decode(&args); err != nil {
							return httpmock.NewStringResponse(400, ""), nil
						}
						if args.PodName != "p-test-cc" && args.PodName != "p-test-sc" {
							return httpmock.NewStringResponse(200, `{"errno": 1,"errmsg": "podName either p-test-cc nor p-test-sc","data": "ok"}`), nil
						}
						return httpmock.NewStringResponse(200, `{"errno": 0,"errmsg": "","data": "ok"}`), nil
					},
				)
			},
			args: args{
				deployment: &Cluster{Name: "test", Spec: &Spec{App: &AppSettings{ProductLine: "siod-redis", AppPrefix: "test"}}},
				proxyNum:   2,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := LabelProxyToScaleDown(tt.args.deployment, tt.args.proxyNum)
			if (err != nil) != tt.wantErr {
				t.Errorf("LabelProxyToScaleDown() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}
