package renderer

import (
	"context"
	"strings"
	"testing"

	"dt-common/ent/deployment"
	"dt-common/mysql"
	"dt-common/omodel"
	"redis-cmanager/env"
)

func TestGetDeploymentFromDB(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")
	objC := env.MockCluster("deployment_test")

	type args struct {
		clusterName string
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
	}{
		{
			name: "failed",
			args: args{
				clusterName: objC.Name,
			},
			wantErr: true,
		},
		{
			name: "success",
			before: func() {
				db, _ := mysql.Database()
				db.Deployment.Create().
					SetClusterID(objC.ID).
					SetClusterName(objC.Name).
					SetAlias(strings.ReplaceAll(objC.Name, "_", "-")).
					SetVersion(1).
					SetSpec((&Spec{}).ToString()).
					SetEnabledAz("hbb").
					SetInspectionMode(omodel.MODE_FULL_CARE).
					SetInspectionResult((&InspectionResult{}).ToString()).
					SetStatus("init").
					OnConflict().UpdateSpec().UpdateEnabledAz().UpdateInspectionMode().UpdateInspectionResult().
					Exec(context.Background())
			},
			args: args{
				clusterName: objC.Name,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			_, err := GetDeploymentFromDB(tt.args.clusterName)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetDeploymentFromDB() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

func TestSaveNewDeployment(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")

	objC := env.MockCluster("test")
	redisCluster := &Cluster{
		ID:    objC.ID,
		Name:  "test",
		Alias: "test",
		Spec: &Spec{
			App: &AppSettings{
				AppPrefix: "test",
			},
		},
		EnabledAZ:        []string{"hbb"},
		Version:          1,
		InspectionMode:   omodel.MODE_FULL_CARE,
		InspectionResult: &InspectionResult{},
		Status:           "init",
	}

	type args struct {
		deploymentInfo *Cluster
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "test",
			args: args{
				deploymentInfo: redisCluster,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := SaveNewDeployment(tt.args.deploymentInfo); (err != nil) != tt.wantErr {
				t.Errorf("SaveNewDeployment() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestUpdateDeploymentResult(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")
	objC := env.MockCluster("deployment_test")

	SaveNewDeployment(&Cluster{
		ID:    objC.ID,
		Name:  objC.Name,
		Alias: strings.ReplaceAll(objC.Name, "_", "-"),
		Spec: &Spec{
			App: &AppSettings{
				AppPrefix: "test",
			},
		},
		Version:          0,
		EnabledAZ:        []string{"hbb"},
		InspectionMode:   omodel.MODE_FULL_CARE,
		InspectionResult: &InspectionResult{},
		Status:           "init",
	})
	SaveNewDeployment(&Cluster{
		ID:    objC.ID,
		Name:  objC.Name,
		Alias: strings.ReplaceAll(objC.Name, "_", "-"),
		Spec: &Spec{
			App: &AppSettings{
				AppPrefix: "test",
			},
		},
		EnabledAZ:        []string{"hbb"},
		InspectionMode:   omodel.MODE_FULL_CARE,
		InspectionResult: &InspectionResult{},
		Version:          1,
		Status:           "init",
	})

	type args struct {
		clusterName string
		result      *InspectionResult
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test",
			args: args{
				clusterName: objC.Name,
				result: &InspectionResult{
					State: INSPECTION_STATE_NORMAL,
				},
			},
			wantErr: false,
			expect: func(t *testing.T) {
				deployment, err := GetDeploymentFromDB(objC.Name)
				if err != nil {
					t.Errorf("unexpected error (%v)", err)
					return
				}
				if deployment.InspectionResult.State != INSPECTION_STATE_NORMAL {
					t.Errorf("expect phase test, but got %v", deployment.InspectionResult.State)
				}
				if deployment.Version != 2 {
					t.Errorf("expect version 2, but got %v", deployment.Version)
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := UpdateDeploymentResult(tt.args.clusterName, tt.args.result); (err != nil) != tt.wantErr {
				t.Errorf("UpdateDeploymentResult() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

func TestUpdateDeploymentMode(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")

	objC := env.MockCluster("mode_test")
	SaveNewDeployment(&Cluster{
		ID:    objC.ID,
		Name:  objC.Name,
		Alias: objC.Name,
		Spec: &Spec{
			App: &AppSettings{
				AppPrefix: "test",
			},
		},
		EnabledAZ:        []string{"hbb"},
		Version:          0,
		InspectionMode:   omodel.MODE_FULL_CARE,
		InspectionResult: &InspectionResult{},
		Status:           "init",
	})
	SaveNewDeployment(&Cluster{
		ID:    objC.ID,
		Name:  objC.Name,
		Alias: strings.ReplaceAll(objC.Name, "_", "-"),
		Spec: &Spec{
			App: &AppSettings{
				AppPrefix: "test",
			},
		},
		EnabledAZ:        []string{"hbb"},
		Version:          1,
		InspectionMode:   omodel.MODE_FULL_CARE,
		InspectionResult: &InspectionResult{},
		Status:           "normal",
	})

	type args struct {
		clusterName string
		mod         string
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test",
			args: args{
				clusterName: objC.Name,
				mod:         omodel.MODE_NOT_CARE,
			},
			wantErr: false,
			expect: func(t *testing.T) {
				db, _ := mysql.Database()
				r, _ := db.Deployment.Query().Where(deployment.ClusterName(objC.Name), deployment.Version(2)).Only(context.Background())
				if r.InspectionMode != omodel.MODE_NOT_CARE {
					t.Errorf("expect mode notCare, but got %s", r.InspectionMode)
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := UpdateDeploymentMode(tt.args.clusterName, tt.args.mod); (err != nil) != tt.wantErr {
				t.Errorf("UpdateDeploymentMode() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

func TestInitDeploymentRender(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")

	objC := env.MockCluster("update_deployment_test")
	SaveNewDeployment(&Cluster{
		ID:    objC.ID,
		Name:  objC.Name,
		Alias: objC.Name,
		Spec: &Spec{
			App: &AppSettings{
				AppPrefix: "test",
			},
		},
		EnabledAZ:        []string{"hbb"},
		Version:          0,
		InspectionMode:   omodel.MODE_FULL_CARE,
		InspectionResult: &InspectionResult{},
		Status:           "init",
	})
	SaveNewDeployment(&Cluster{
		ID:    objC.ID,
		Name:  objC.Name,
		Alias: strings.ReplaceAll(objC.Name, "_", "-"),
		Spec: &Spec{
			App: &AppSettings{
				AppPrefix: "test",
			},
		},
		EnabledAZ:        []string{"hbb"},
		Version:          1,
		InspectionMode:   omodel.MODE_NOT_CARE,
		InspectionResult: &InspectionResult{},
		Status:           "normal",
	})

	type args struct {
		clusterName string
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test",
			args: args{
				clusterName: objC.Name,
			},
			wantErr: false,
			expect: func(t *testing.T) {
				deployment, err := GetDeploymentFromDB(objC.Name)
				if err != nil {
					t.Errorf("unexpected error (%v)", err)
					return
				}
				if deployment.InspectionMode != omodel.MODE_FULL_CARE {
					t.Errorf("expect mode fullCare, but got %s", deployment.InspectionMode)
				}
				if deployment.InspectionResult.State != INSPECTION_STATE_INIT {
					t.Errorf("expect phase init, but got %v", deployment.InspectionResult.State)
				}
				if deployment.Version != 2 {
					t.Errorf("expect version 2, but got %v", deployment.Version)
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := InitDeploymentRender(tt.args.clusterName); (err != nil) != tt.wantErr {
				t.Errorf("InitDeploymentRender() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}
