package renderer

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"

	"entgo.io/ent/dialect/sql"

	"dt-common/ent"
	"dt-common/ent/deployment"
	"dt-common/logger"
	"dt-common/mysql"
	"dt-common/omodel"
)

// ===========================
// 			 Define
// ===========================

const (
	DEPLOYMENT_STATUS_INIT   = "init"   // 新建集群
	DEPLOYMENT_STATUS_NORMAL = "normal" // 运行中集群
)

// Cluster 集群信息，会记录到数据库中
type Cluster struct {
	ID               int64             `json:"id,omitempty"`               // 集群ID
	Name             string            `json:"name,omitempty"`             // 集群名称
	Alias            string            `json:"alias,omitempty"`            // 在K8S中的别名
	Spec             *Spec             `json:"spec"`                       // 集群配置
	EnabledAZ        []string          `json:"mainAZ,omitempty"`           // 主可用区，在单机房集群时启用
	Version          int               `json:"version,omitempty"`          // 配置版本
	InspectionMode   string            `json:"inspectionMode"`             // 托管模式
	InspectionResult *InspectionResult `json:"inspectionResult,omitempty"` // 渲染结果
	Status           string            `json:"status,omitempty"`           // 集群状态，初建init、运行中normal、已下线deleted
}

// RedisClusterSpec 集群配置
type Spec struct {
	Namespace        string            `json:"namespace,omitempty"`        // 集群命名空间
	Labels           map[string]string `json:"labels,omitempty"`           // 集群标签
	Annotations      map[string]string `json:"annotations,omitempty"`      // 集群注释
	Redis            *RedisSettings    `json:"redis,omitempty"`            // redis 设置
	Sentinel         *SentinelSettings `json:"sentinel,omitempty"`         // sentinel 设置
	Router           *RouterSettings   `json:"router,omitempty"`           // Router 设置
	App              *AppSettings      `json:"bns,omitempty"`              // App 设置
	ClusterWhitelist []string          `json:"clusterWhitelist,omitempty"` // 集群白名单
}

func (s *Spec) ToString() string {
	spec, _ := json.Marshal(s)
	return string(spec)
}

type Resource struct {
	CPU int `json:"cpu,omitempty"` // cpu 单位m(千分之一核)
	Mem int `json:"mem,omitempty"` // mem 单位MiB(兆字节)
}

// RedisSettings redis配置
// CustomConfig: save，maxmeory，maxmemory-policy
type RedisSettings struct {
	Image           string         `json:"image,omitempty"`           // 镜像
	ImageVersion    string         `json:"imageVersion,omitempty"`    // 镜像版本
	ImagePullPolicy string         `json:"imagePullPolicy,omitempty"` // 镜像拉取策略
	Resource        *Resource      `json:"resources,omitempty"`       // 资源配置，用于设置cpu、mem的 limit和request值
	Replicas        map[string]int `json:"replicas,omitempty"`        // 分片副本数分布，例如{"hba":1, "hbb": 1}表示1主1从
	Command         []string       `json:"command,omitempty"`         // 启动命令，可由redis运维同学设置启动命令，默认无启动命令
	Port            int            `json:"port,omitempty"`            // 端口

	NumOfShards  int      `json:"numOfShards,omitempty"`  // 分片数量
	CustomConfig []string `json:"customConfig,omitempty"` // 自定义redis配置，在集群关系管理时对redis执行，由redis运维人员配置。例如 "tcp-keepalive 60"、"replica-priority 100"，实际会执行 "config set tcp-keepalive 60" 、"config set replica-priority 100"
}

// RouterSettings router配置
type RouterSettings struct {
	Image           string         `json:"image,omitempty"`           // 镜像
	ImageVersion    string         `json:"imageVersion,omitempty"`    // 镜像版本
	ImagePullPolicy string         `json:"imagePullPolicy,omitempty"` // 镜像拉取策略
	Resource        *Resource      `json:"resources,omitempty"`       // 资源配置，用于设置cpu、mem的 limit和request值
	Replicas        map[string]int `json:"replicas,omitempty"`        // 各分片副本数，len(Replicas)需要等于NumOfShards，否则视为异常
	Command         []string       `json:"command,omitempty"`         // 启动命令，可由redis运维同学设置启动命令，默认无启动命令
	Port            int            `json:"port,omitempty"`            // 端口

	SPort             int    `json:"sPort,omitempty"`             //用于Router启动参数stat port设置
	ClientAuth        string `json:"clientAuth,omitempty"`        //用于Router配置文件中密码设置
	ClientConnections int    `json:"clientConnections,omitempty"` //用于Router配置文件中最大客户端连接数的配置
}

// SentinelSettings sentinel配置
type SentinelSettings struct {
	Image           string         `json:"image,omitempty"`           // 镜像
	ImageVersion    string         `json:"imageVersion,omitempty"`    // 镜像版本
	ImagePullPolicy string         `json:"imagePullPolicy,omitempty"` // 镜像拉取策略
	Resource        *Resource      `json:"resources,omitempty"`       // 资源配置，用于设置cpu、mem的 limit和request值
	Replicas        map[string]int `json:"replicas,omitempty"`        // 各分片副本数，len(Replicas)需要等于NumOfShards，否则视为异常
	Command         []string       `json:"command,omitempty"`         // 启动命令，可由redis运维同学设置启动命令，默认无启动命令
	Port            int            `json:"port,omitempty"`            // 端口

	CustomConfig []string `json:"customConfig,omitempty"`
}

// RedisSettings defines the specification of the redis cluster
type AppSettings struct {
	ProductLine       string `json:"productLine,omitempty"`       // 产品线名称
	Subsystem         string `json:"subsystem,omitempty"`         // 子系统名称
	SubsystemAlias    string `json:"subsystemAlias,omitempty"`    // 子系统别名
	SentinelSubsystem string `json:"sentinelSubsystem,omitempty"` // sentinel子系统名称
	AppPrefix         string `json:"appPrefix,omitempty"`         // app前缀。例如AppPrefix=test ProductName=siod-redis，那么redis bns为test-redis.siod-redis，sentinel bns为test-sentinel.siod-redis，router bns为test-router.siod-redis。通常情况SubSystems与AppPrefix应相同
	AppPrefixAlias    string `json:"appPrefixAlias,omitempty"`    // app别名前缀 通常情况SubSystems与AppPrefix应相同
	Level             string `json:"level,omitempty"`             // 服务等级
	DepartmentId      int    `json:"departmentId,omitempty"`      // 部门ID  eg：27113
	DepartmentName    string `json:"departmentName,omitempty"`    // 部门名字 eg："DXM系统运维部"
	Description       string `json:"description,omitempty"`       // app描述
	RDOwner           string `json:"rdOwner,omitempty"`           // RD负责人
	OPOwner           string `json:"opOwner,omitempty"`           // OP负责人
}

// InspectionResult 渲染结果
type InspectionResult struct {
	StartedAt    time.Time       `json:"startedAt,omitempty"`    // 当前轮启动时间，单位s
	StepProgress [TOTAL_STEP]int `json:"stepProgress,omitempty"` // 当前轮阶段进度
	StepErrTimes [TOTAL_STEP]int `json:"stepErrTimes,omitempty"` // 当前轮阶段重试次数
	State        string          `json:"state,omitempty"`        // 上一轮渲染结果
	ErrMsg       string          `json:"errMsg,omitempty"`       // 上一轮失败日志
}

func (r *InspectionResult) ToString() string {
	result, _ := json.Marshal(r)
	return string(result)
}

// ===========================
// 			  Funs
// ===========================

// GetDeploymentFromDB 从数据库获取版本号最大的deployment
func GetDeploymentFromDB(clusterName string) (*Cluster, error) {
	db, err := mysql.Database()
	if err != nil {
		errMsg := fmt.Sprintf("failed to get mysql connection, error=(%v)", err)
		logger.Error(errMsg)
		return nil, errors.New(errMsg)
	}

	ctx, cancel := mysql.ContextWithTimeout()
	deploymentList, err := db.Deployment.Query().Where(deployment.ClusterName(clusterName)).All(ctx)
	cancel()
	if err != nil {
		errMsg := fmt.Sprintf("failed to get deployment from db, error=(%v)", err)
		logger.Error(errMsg)
		return nil, errors.New(errMsg)
	}

	if len(deploymentList) == 0 {
		return nil, errors.New("no deployment found")
	}

	maxVersionIndex := 0
	for i := 0; i < len(deploymentList); i++ {
		if deploymentList[i].Version > deploymentList[maxVersionIndex].Version {
			maxVersionIndex = i
		}
	}

	spec := Spec{}
	err = json.Unmarshal([]byte(deploymentList[maxVersionIndex].Spec), &spec)
	if err != nil {
		logger.Error("failed to unmarshal spec", err)
		return nil, err
	}

	iResult := InspectionResult{}
	err = json.Unmarshal([]byte(deploymentList[maxVersionIndex].InspectionResult), &iResult)
	if err != nil {
		logger.Error("failed to unmarshal status", err)
		return nil, err
	}

	cluster := &Cluster{
		ID:               deploymentList[maxVersionIndex].ClusterID,
		Name:             deploymentList[maxVersionIndex].ClusterName,
		Alias:            deploymentList[maxVersionIndex].Alias,
		Spec:             &spec,
		EnabledAZ:        strings.Split(deploymentList[maxVersionIndex].EnabledAz, ","),
		Version:          deploymentList[maxVersionIndex].Version,
		InspectionMode:   deploymentList[maxVersionIndex].InspectionMode,
		InspectionResult: &iResult,
		Status:           deploymentList[maxVersionIndex].Status,
	}
	return cluster, nil
}

// SaveNewDeployment 记录deployment到数据库，每次会自动把version加1
func SaveNewDeployment(deploymentInfo *Cluster) error {
	db, err := mysql.Database()
	if err != nil {
		logger.Error("failed to get mysql connection, cluster=%s, error=(%v)", deploymentInfo.Name, err)
		return err
	}

	spec, err := json.Marshal(deploymentInfo.Spec)
	if err != nil {
		logger.Error("failed to marshal spec, cluster=%s, error=(%v)", deploymentInfo.Name, err)
		return err
	}
	enableAzString := strings.Join(deploymentInfo.EnabledAZ, ",")
	iResult, err := json.Marshal(deploymentInfo.InspectionResult)
	if err != nil {
		logger.Error("failed to marshal status, cluster=%s, error=(%v)", deploymentInfo.Name, err)
		return err
	}

	ctx, cancel := mysql.ContextWithTimeout()
	err = db.Deployment.Create().
		SetClusterID(deploymentInfo.ID).
		SetClusterName(deploymentInfo.Name).
		SetAlias(deploymentInfo.Alias).
		SetVersion(deploymentInfo.Version + 1).
		SetSpec(string(spec)).
		SetEnabledAz(enableAzString).
		SetInspectionMode(deploymentInfo.InspectionMode).
		SetInspectionResult(string(iResult)).
		SetStatus(deploymentInfo.Status).
		OnConflict().UpdateSpec().UpdateEnabledAz().UpdateInspectionMode().UpdateInspectionResult().UpdateStatus().
		Exec(ctx)
	cancel()
	if err != nil {
		logger.Error("failed to create deployment, cluster=%s, error=(%v)", deploymentInfo.Name, err)
		return err
	}

	return nil
}

// 更新渲染（巡检）结果，不升级版本号
func UpdateDeploymentResult(clusterName string, iResult *InspectionResult) error {
	resultByte, err := json.Marshal(iResult)
	if err != nil {
		return err
	}

	db, err := mysql.Database()
	if err != nil {
		logger.Error("failed to get mysql connection, error=(%v)", err)
		return err
	}
	return db.Transaction(context.Background(), func(tx *ent.Tx) error {
		// 获取最新版本的deployment
		ctx, cancel := mysql.ContextWithTimeout()
		data, err := tx.Deployment.Query().
			Where(deployment.ClusterName(clusterName)).
			Order(deployment.ByVersion(sql.OrderDesc())).
			Limit(1).
			First(ctx)
		cancel()
		if err != nil {
			logger.Error("failed to get deployment from db, error=(%v)", err)
			return err
		}

		// 更新status
		ctx, cancel = mysql.ContextWithTimeout()
		_, err = tx.Deployment.Update().SetInspectionResult(string(resultByte)).Where(deployment.ID(data.ID)).Save(ctx)
		cancel()
		if err != nil {
			logger.Error("failed to update cluster status, error=(%v)", err)
			return err
		}
		return nil
	})
}

// 更新InspectionMode，不升级版本号
func UpdateDeploymentMode(clusterName string, mode string) error {
	db, err := mysql.Database()
	if err != nil {
		logger.Error("failed to get mysql connection, error=(%v)", err)
		return err
	}
	return db.Transaction(context.Background(), func(tx *ent.Tx) error {
		// 获取最新版本的deployment
		ctx, cancel := mysql.ContextWithTimeout()
		data, err := tx.Deployment.Query().
			Where(deployment.ClusterName(clusterName)).
			Order(deployment.ByVersion(sql.OrderDesc())).
			Limit(1).
			First(ctx)
		cancel()
		if err != nil {
			logger.Error("failed to get deployment from db, error=(%v)", err)
			return err
		}

		// 更新mode
		ctx, cancel = mysql.ContextWithTimeout()
		_, err = tx.Deployment.Update().SetInspectionMode(mode).Where(deployment.ID(data.ID)).Save(ctx)
		cancel()
		if err != nil {
			logger.Error("failed to update deployment mode, error=(%v)", err)
			return err
		}

		return nil
	})
}

// 更新Status，不升级版本号
// 使用场景：初建集群(init)部署完成后改运行中(normal)
func UpdateDeploymentStatus(clusterName string, status string) error {
	db, err := mysql.Database()
	if err != nil {
		logger.Error("failed to get mysql connection, error=(%v)", err)
		return err
	}
	return db.Transaction(context.Background(), func(tx *ent.Tx) error {
		// 获取最新版本的deployment
		ctx, cancel := mysql.ContextWithTimeout()
		data, err := tx.Deployment.Query().
			Where(deployment.ClusterName(clusterName)).
			Order(deployment.ByVersion(sql.OrderDesc())).
			Limit(1).
			First(ctx)
		cancel()
		if err != nil {
			logger.Error("failed to get deployment from db, error=(%v)", err)
			return err
		}

		// 更新mode
		ctx, cancel = mysql.ContextWithTimeout()
		_, err = tx.Deployment.Update().SetStatus(status).Where(deployment.ID(data.ID)).Save(ctx)
		cancel()
		if err != nil {
			logger.Error("failed to update deployment mode, error=(%v)", err)
			return err
		}

		return nil
	})
}

// 初始化集群渲染
func InitDeploymentRender(clusterName string) error {
	results := &InspectionResult{
		State:        INSPECTION_STATE_INIT, // 改成""用于检测渲染结果
		StepErrTimes: [TOTAL_STEP]int{},     // 初始化阶段状态
	}
	resultBytes, _ := json.Marshal(results)

	db, err := mysql.Database()
	if err != nil {
		logger.Error("failed to get mysql connection, error=(%v)", err)
		return err
	}
	return db.Transaction(context.Background(), func(tx *ent.Tx) error {
		// 获取最新版本的deployment
		ctx, cancel := mysql.ContextWithTimeout()
		data, err := tx.Deployment.Query().
			Where(deployment.ClusterName(clusterName)).
			Order(deployment.ByVersion(sql.OrderDesc())).
			Limit(1).First(ctx)
		cancel()
		if err != nil {
			logger.Error("failed to get deployment from db, cluster=%s, error=(%v)", clusterName, err)
			return err
		}

		// 更新status
		ctx, cancel = mysql.ContextWithTimeout()
		_, err = tx.Deployment.Update().
			SetInspectionResult(string(resultBytes)).
			SetInspectionMode(omodel.MODE_FULL_CARE).
			Where(deployment.ID(data.ID)).
			Save(ctx)
		cancel()
		if err != nil {
			logger.Error("failed to init deployment render, cluster=%s, error=(%v)", clusterName, err)
			return err
		}
		return nil
	})
}
