package errc

import (
	"net/http"

	"dt-common/errs"
)

const (
	CodeClusterAbnormal     errs.Code = "S1001"
	CodeRollbackUnsupported errs.Code = "S1002"
	CodeUndefinedValue      errs.Code = "S1003"
	CodeStageParameter      errs.Code = "S1004"
	CodeHaveNoPermission    errs.Code = "U1000"
	CodeAdminOnly           errs.Code = "U1001"
	CodeBillNotFound        errs.Code = "E2002"
)

func Init() {
	errs.Register(CodeClusterAbnormal, "实例状态异常", http.StatusInternalServerError, 11001)
	errs.Register(CodeRollbackUnsupported, "阶段不支持回滚", http.StatusInternalServerError, 11002)
	errs.Register(CodeUndefinedValue, "未定义的值", http.StatusInternalServerError, 11003)
	errs.Register(CodeStageParameter, "阶段参数错误", http.StatusInternalServerError, 11004)
	errs.Register(CodeHaveNoPermission, "没有访问权限", http.StatusUnauthorized, 21000)
	errs.Register(CodeAdminOnly, "仅管理员可见", http.StatusInternalServerError, 21001)
	errs.Register(CodeBillNotFound, "未找到账单", http.StatusInternalServerError, 32002)
}
