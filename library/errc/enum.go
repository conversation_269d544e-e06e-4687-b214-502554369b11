package errc

import (
	"net/http"

	"dt-common/errs"
)

const (
	CodePasswordError      errs.Code = "S1000"
	CodeUndefinedReplyType errs.Code = "S1001"
	CodeOperationError     errs.Code = "S1002"
)

const (
	CodeForbiddenCommand errs.Code = "A1000"
	CodeRedisExecFailed  errs.Code = "A1001"
)

func Init() {
	errs.Register(CodePasswordError, "can not get password", http.StatusInternalServerError, 11000)
	errs.Register(CodeUndefinedReplyType, "undefined reply type", http.StatusInternalServerError, 11001)
	errs.Register(CodeOperationError, "operation failed", http.StatusInternalServerError, 11002)

	errs.Register(CodeForbiddenCommand, "forbidden command", http.StatusBadRequest, 21000)
	errs.Register(CodeRedisExecFailed, "redis exec failed", http.StatusBadRequest, 21001)
}
