package ragent

import (
	"regexp"
	"testing"

	"github.com/jarcoal/httpmock"
)

// 单测：Proxy启动
func TestStartProxy(t *testing.T) {
	Init(&Config{
		Token:   "123",
		Port:    6379,
		Timeout: 1,
	})

	// MOCK HTTP
	httpmock.Activate()

	type args struct {
		ip   string
		port int
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(t *testing.T)
	}{
		{
			name: "test1",
			before: func() {
				// mock redis-agent
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/proxy/start`),
					httpmock.NewStringResponder(200, `{"code": "00000", "data": "mockok"}`))
			},
			args: args{
				ip:   "127.0.0.1",
				port: 6379,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		if tt.before != nil {
			tt.before()
		}
		t.Run(tt.name, func(t *testing.T) {
			err := ProxyStart(tt.args.ip, tt.args.port)
			if (err != nil) != tt.wantErr {
				t.Errorf("Start() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

// 单测：Proxy关停
func TestStopProxy(t *testing.T) {
	Init(&Config{
		Token:   "123",
		Port:    6379,
		Timeout: 1,
	})

	// MOCK HTTP
	httpmock.Activate()

	type args struct {
		ip   string
		port int
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(t *testing.T)
	}{
		{
			name: "test1",
			before: func() {
				// mock redis-agent
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/proxy/stop`),
					httpmock.NewStringResponder(200, `{"code": "00000", "data": "mockok"}`))
			},
			args: args{
				ip:   "127.0.0.1",
				port: 6379,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		if tt.before != nil {
			tt.before()
		}
		t.Run(tt.name, func(t *testing.T) {
			err := ProxyStop(tt.args.ip, tt.args.port)
			if (err != nil) != tt.wantErr {
				t.Errorf("ProxyStop() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

// 单测：Proxy进程状态
func TestProxyStatus(t *testing.T) {
	Init(&Config{
		Token:   "123",
		Port:    6379,
		Timeout: 1,
	})

	// MOCK HTTP
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		ip   string
		port int
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, bool)
	}{
		{
			name: "test1",
			before: func() {
				// mock redis-agent
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/proxy/status`),
					httpmock.NewStringResponder(200, `{"code": "00000", "data": true}`))
			},
			args: args{
				ip:   "127.0.0.1",
				port: 6379,
			},
			wantErr: false,
			expect: func(t *testing.T, b bool) {
				if !b {
					t.Errorf("expect true but got false")
				}
			},
		},
	}
	for _, tt := range tests {
		if tt.before != nil {
			tt.before()
		}
		t.Run(tt.name, func(t *testing.T) {
			isAlive, err := ProxyStatus(tt.args.ip, tt.args.port)
			if (err != nil) != tt.wantErr {
				t.Errorf("ProxyStatus() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, isAlive)
			}
		})
	}
}

// 单测：Proxy当前流量
func TestProxyConns(t *testing.T) {
	Init(&Config{
		Token:   "123",
		Port:    6379,
		Timeout: 1,
	})

	// MOCK HTTP
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		ip   string
		port int
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, *Connections)
	}{
		{
			name: "test1",
			before: func() {
				// mock redis-agent
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/proxy/conns`),
					httpmock.NewStringResponder(200, `{"code": "00000", "data": {"qps":40,"conns":["************"]}}`))
			},
			args: args{
				ip:   "127.0.0.1",
				port: 6379,
			},
			wantErr: false,
			expect: func(t *testing.T, conns *Connections) {
				if conns.QPS != 40 {
					t.Errorf("expect 40 but got %v", conns.QPS)
				}
			},
		},
	}
	for _, tt := range tests {
		if tt.before != nil {
			tt.before()
		}
		t.Run(tt.name, func(t *testing.T) {
			conns, err := ProxyConns(tt.args.ip, tt.args.port)
			if (err != nil) != tt.wantErr {
				t.Errorf("ProxyConns() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, conns)
			}
		})
	}
}

// 单测：Proxy拓扑结构
func TestProxyTopo(t *testing.T) {
	Init(&Config{
		Token:   "dxm_test_123456",
		Port:    8433,
		Timeout: 2,
	})

	// MOCK HTTP
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		ip   string
		port int
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, []string)
	}{
		{
			name: "test1",
			before: func() {
				// mock redis-agent
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/proxy/topo`),
					httpmock.NewStringResponder(200, `{"code": "00000", "data": ["************:7020","************:7021"]}`))
			},
			args: args{
				ip:   "************",
				port: 8001,
			},
			wantErr: false,
			expect: func(t *testing.T, servers []string) {
				if len(servers) != 2 {
					t.Errorf("expect true but got false")
				}
			},
		},
	}
	for _, tt := range tests {
		if tt.before != nil {
			tt.before()
		}
		t.Run(tt.name, func(t *testing.T) {
			servers, err := ProxyTopo(tt.args.ip, tt.args.port)
			if (err != nil) != tt.wantErr {
				t.Errorf("ProxyTopo() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, servers)
			}
		})
	}
}
