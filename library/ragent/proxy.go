package ragent

import (
	"encoding/json"
	"errors"
	"fmt"

	"dt-common/errs"
)

type OperateParams struct {
	Port  int  `json:"port"`
	Force bool `json:"force,omitempty"`
}

// Proxy启动
func ProxyStart(ip string, port int) error {
	operateParams := OperateParams{
		Port: port,
	}
	// 发送请求，post超时时间：5s
	subUrl := "/proxy/start"
	resp, err := post(ip, subUrl, &operateParams)
	if err != nil {
		return fmt.Errorf("failed to call redis agent, ip=%s, error=(%v)", ip, err)
	}

	// 校验结果
	if resp.Code != string(errs.Success) {
		return fmt.Errorf("response code=%s, message=(%s)", resp.Code, resp.Message)
	}

	return nil
}

// Proxy关停
func ProxyStop(ip string, port int) error {
	operateParams := OperateParams{
		Port: port,
	}
	// 发送请求，post超时时间：5s
	subUrl := "/proxy/stop"
	resp, err := post(ip, subUrl, &operateParams)
	if err != nil {
		return fmt.Errorf("failed to call redis agent, ip=%s, error=(%v)", ip, err)
	}

	// 校验结果
	if resp.Code != string(errs.Success) {
		return fmt.Errorf("response code=%s, message=(%s)", resp.Code, resp.Message)
	}

	return nil
}

// 获取proxy存活状态
func ProxyStatus(ip string, port int) (bool, error) {
	subUrl := "/proxy/status"
	resp, err := get(ip, subUrl, fmt.Sprintf("port=%d", port))
	if err != nil {
		return false, fmt.Errorf("failed to call redis agent, ip=%s, error=(%v)", ip, err)
	}

	// 执行成功返回结果
	if resp.Code == string(errs.Success) {
		var result bool
		err = json.Unmarshal(resp.Data, &result)
		if err != nil {
			return false, err
		}
		return result, nil
	}

	// 执行失败返回错误信息
	return false, errors.New(resp.Message)
}

type Connections struct {
	QPS   int      `json:"qps"`
	Conns []string `json:"conns"`
}

// 获取proxy的realQps和连接信息
func ProxyConns(ip string, port int) (*Connections, error) {
	subUrl := "/proxy/conns"
	resp, err := get(ip, subUrl, fmt.Sprintf("port=%d", port))
	if err != nil {
		return nil, fmt.Errorf("failed to call redis agent, ip=%s, error=(%v)", ip, err)
	}

	// 执行成功返回结果
	if resp.Code == string(errs.Success) {
		var result Connections
		err = json.Unmarshal(resp.Data, &result)
		if err != nil {
			return nil, err
		}
		return &result, nil
	}

	// 执行失败返回错误信息
	return nil, errors.New(resp.Message)
}

// 获取proxy拓扑
func ProxyTopo(ip string, port int) ([]string, error) {
	subUrl := "/proxy/topo"
	resp, err := get(ip, subUrl, fmt.Sprintf("port=%d", port))
	if err != nil {
		return nil, fmt.Errorf("failed to call redis agent, ip=%s, error=(%v)", ip, err)
	}

	// 执行成功返回结果
	if resp.Code != string(errs.Success) {
		return nil, fmt.Errorf("response code=%s, message=(%s)", resp.Code, resp.Message)
	}

	var result []string
	err = json.Unmarshal(resp.Data, &result)
	if err != nil {
		return nil, err
	}

	return result, nil
}
