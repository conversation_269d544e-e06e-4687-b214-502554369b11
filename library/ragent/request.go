package ragent

import (
	"dt-common/errs"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"
)

const (
	defaultTimeout = 5 // 单位：秒
)

var cfg *Config

// agent配置参数
type Config struct {
	Token   string        `yaml:"token"`   // redis-agent通讯token
	Port    int           `yaml:"port"`    // redis-agent端口
	Timeout time.Duration `yaml:"timeout"` // 超时时间, 默认5秒
}

// 初始化配置
func Init(conf *Config) error {
	if conf.Token == "" {
		return fmt.Errorf("agent token is empty")
	}

	if conf.Timeout == 0 {
		conf.Timeout = defaultTimeout
	}

	cfg = conf
	return nil
}

// agent返回值结构
type response struct {
	Code    string          `json:"code"`
	Message string          `json:"msg"`
	Data    json.RawMessage `json:"data"` // 返回结果可能是字符串，也可能是数组
}

// 向agent发送请求
func post(ip string, subUrl string, reqBody interface{}) (*response, error) {
	if subUrl == "" {
		return nil, fmt.Errorf("subURL is empty")
	}
	url := fmt.Sprintf("http://%s:%d%s", ip, cfg.Port, subUrl)

	// request body
	body, err := json.Marshal(reqBody)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest(http.MethodPost, url, strings.NewReader(string(body)))
	if err != nil {
		return nil, err
	}

	req.Header.Set("Authorization", cfg.Token)
	req.Header.Set("Content-Type", "application/json;charset=UTF-8")
	client := &http.Client{
		Timeout: cfg.Timeout * time.Second,
	}

	res, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer res.Body.Close()

	resBody, err := io.ReadAll(res.Body)
	if err != nil {
		return nil, err
	}

	result := response{}
	err = json.Unmarshal(resBody, &result)
	if err != nil {
		return nil, err
	}

	return &result, nil
}

// 向agent发送请求
func get(ip string, subUrl string, queryString ...string) (*response, error) {
	if subUrl == "" {
		return nil, fmt.Errorf("subURL is empty")
	}
	var url string
	if len(queryString) > 0 {
		url = fmt.Sprintf("http://%s:%d%s?%s", ip, cfg.Port, subUrl, queryString[0])
	} else {
		url = fmt.Sprintf("http://%s:%d%s", ip, cfg.Port, subUrl)
	}

	req, err := http.NewRequest(http.MethodGet, url, nil)
	if err != nil {
		return nil, err
	}

	req.Header.Set("Authorization", cfg.Token)
	req.Header.Set("Content-Type", "application/json;charset=UTF-8")
	client := &http.Client{
		Timeout: cfg.Timeout * time.Second,
	}

	res, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer res.Body.Close()

	resBody, err := io.ReadAll(res.Body)
	if err != nil {
		return nil, err
	}

	result := response{}
	err = json.Unmarshal(resBody, &result)
	if err != nil {
		return nil, err
	}

	return &result, nil
}

// 健康检查
func HealthCheck(ip string) error {
	// 发送请求
	subUrl := "/healthCheck"
	resp, err := get(ip, subUrl)
	if err != nil {
		return fmt.Errorf("failed to ping redis agent, ip=%s, error=(%v)", ip, err)
	}

	// 校验结果
	if resp.Code != string(errs.Success) {
		return fmt.Errorf("response code=%s, message=%s", resp.Code, resp.Message)
	}

	return nil
}
