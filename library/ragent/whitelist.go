package ragent

import (
	"encoding/json"
	"errors"
	"fmt"

	"dt-common/errs"
)

// 重写白名单接口入参结构
type UpdateParams struct {
	Port    int      `json:"port"`
	BnsList []string `json:"bnsList"`
	IpList  []string `json:"ipList"`
	Action  string   `json:"action"`
}

// 重写白名单
func UpdateWhitelist(ip string, body *UpdateParams) error {
	if len(body.BnsList) == 0 && len(body.IpList) == 0 {
		return fmt.Errorf("bnsList and ipList can not be empty")
	}
	if body.Action != "add" && body.Action != "del" && body.Action != "cover" {
		return fmt.Errorf("action must be add or delete")
	}

	// 发送请求
	subUrl := "/whitelist/update"
	resp, err := post(ip, subUrl, &body)
	if err != nil {
		return fmt.Errorf("failed to call redis agent, ip=%s, error=(%v)", ip, err)
	}

	// 校验结果
	if resp.Code != string(errs.Success) {
		return fmt.Erro<PERSON>("failed to update whitelist, code=%s, message=(%s)", resp.Code, resp.Message)
	}

	return nil
}

// 回滚白名单
func RollbackWhitelist(ip string, body *OperateParams) error {
	// 发送请求
	subUrl := "/whitelist/rollback"
	resp, err := post(ip, subUrl, &body)
	if err != nil {
		return fmt.Errorf("failed to call redis agent, ip=%s, error=(%v)", ip, err)
	}

	// 校验结果
	if resp.Code != string(errs.Success) {
		return fmt.Errorf("response code=%s, message=(%s)", resp.Code, resp.Message)
	}

	return nil
}

// 查询进程白名单
func QueryWhitelist(ip string, port int) ([]string, []string, error) {
	// 发送请求
	subUrl := "/whitelist/query"
	resp, err := get(ip, subUrl, fmt.Sprintf("port=%d", port))
	if err != nil {
		return nil, nil, fmt.Errorf("failed to call redis agent, ip=%s, error=(%v)", ip, err)
	}

	// 执行失败返回错误信息
	if resp.Code != string(errs.Success) {
		return nil, nil, errors.New(resp.Message)
	}

	// 执行成功返回结果
	var result map[string][]string
	err = json.Unmarshal(resp.Data, &result)
	if err != nil {
		return nil, nil, err
	}

	return result["bns"], result["ip"], nil
}
