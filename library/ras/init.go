package ras

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"dt-common/logger"
	"dt-common/omodel"
)

type Project struct {
	URL   string `yaml:"url"`
	Token string `yaml:"token"`
}

// Config 配置结构
type Config struct {
	Manager  *Project      `yaml:"manager"`
	CManager *Project      `yaml:"cmanager"`
	Timeout  time.Duration `yaml:"timeout"` // 超时时间, 默认5秒
}

var (
	cfg *Config
)

type BasicParams struct {
	StageID     int64  `json:"stageId"`
	ClusterName string `json:"clusterName"`
}

// ras接口返回值结构
type response struct {
	Code    string          `json:"code"`
	Message string          `json:"msg,omitempty"`
	Data    json.RawMessage `json:"data"` // 返回结果可能是字符串，也可能是数组
}

// 向ras发送请求
func (p *Project) request(method string, subUrl string, reqBody interface{}) (*response, error) {
	if subUrl == "" {
		return nil, fmt.Errorf("subURL is empty")
	}
	url := p.URL + subUrl

	// 构造请求参数
	body, err := json.Marshal(reqBody)
	if err != nil {
		logger.Error("failed to convert parameter to JSON, error=(%v)", err)
		return nil, err
	}
	logger.Debug(string(body))

	req, err := http.NewRequest(method, url, strings.NewReader(string(body)))
	if err != nil {
		logger.Error("create http request failed, error=(%v)", err)
		return nil, err
	}

	req.Header.Set("Authorization", p.Token)
	req.Header.Set("Content-Type", "application/json;charset=UTF-8")
	client := &http.Client{
		Timeout: cfg.Timeout * time.Second,
	}

	res, err := client.Do(req)
	if err != nil {
		logger.Error("request rsa failure, error=(%v)", err)
		return nil, err
	}

	resBody, err := io.ReadAll(res.Body)
	if err != nil {
		logger.Error("failed to get response body, error=(%v)", err)
		return nil, err
	}

	result := response{}
	err = json.Unmarshal(resBody, &result)
	if err != nil {
		logger.Error("parsing response body to JSON failed, error=(%v)", err)
		return nil, err
	}

	return &result, nil
}

// Init 初始化配置
func Init(conf *Config) error {
	if conf.Manager == nil {
		return fmt.Errorf("missing redis-manager config, url & token")
	}
	if conf.Manager.URL == "" {
		return fmt.Errorf("missing redis-manager url config")
	}
	if !strings.HasPrefix(conf.Manager.URL, "http://") {
		conf.Manager.URL = fmt.Sprintf("http://%s", conf.Manager.URL)
	}

	if conf.CManager == nil {
		return fmt.Errorf("missing redis-cmanager config, url & token")
	}
	if conf.CManager.URL == "" {
		return fmt.Errorf("missing redis-cmanager url config")
	}
	if !strings.HasPrefix(conf.CManager.URL, "http://") {
		conf.CManager.URL = fmt.Sprintf("http://%s", conf.CManager.URL)
	}

	if conf.Timeout == 0 {
		conf.Timeout = 5
	}

	cfg = conf
	return nil
}

// deploy: if cluster deploy in docker then 1. It determines which api to call.
func Deploy(env int) *Project {
	switch env {
	case omodel.DEPLOY_ENV_DOCKER:
		return cfg.CManager
	default:
		return cfg.Manager
	}
}
