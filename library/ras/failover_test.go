package ras

import (
	"regexp"
	"testing"

	"github.com/jarcoal/httpmock"

	"dt-common/omodel"
)

func TestFailover(t *testing.T) {
	initTest()
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		params *FailoverParams
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test: call failed",
			args: args{
				params: &FailoverParams{
					StageID:     1,
					ClusterName: "r3_test",
					ShardList: []*omodel.Shard{
						{Name: "r3-test_server1", Master: &omodel.Instance{IP: "************", Port: 7000}, Slaves: []*omodel.Instance{{IP: "************", Port: 7000}}},
						{Name: "r3-test_server2", Master: &omodel.Instance{IP: "************", Port: 7001}, Slaves: []*omodel.Instance{{IP: "************", Port: 7001}}},
						{Name: "r3-test_server3", Master: &omodel.Instance{IP: "************", Port: 7002}, Slaves: []*omodel.Instance{{IP: "************", Port: 7002}}},
					},
					MaxConcurnecy: 2,
				},
			},
			wantErr: true,
		},
		{
			name: "test: return value format error",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/cluster/redis/failover`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": []}`))
			},
			args: args{
				params: &FailoverParams{
					StageID:     1,
					ClusterName: "r3_test",
					ShardList: []*omodel.Shard{
						{Name: "r3-test_server1", Master: &omodel.Instance{IP: "************", Port: 7000}, Slaves: []*omodel.Instance{{IP: "************", Port: 7000}}},
						{Name: "r3-test_server2", Master: &omodel.Instance{IP: "************", Port: 7001}, Slaves: []*omodel.Instance{{IP: "************", Port: 7001}}},
						{Name: "r3-test_server3", Master: &omodel.Instance{IP: "************", Port: 7002}, Slaves: []*omodel.Instance{{IP: "************", Port: 7002}}},
					},
					MaxConcurnecy: 2,
				},
			},
			wantErr: true,
		},
		{
			name: "test: success",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/cluster/redis/failover`),
					httpmock.NewStringResponder(200, `{"code": "00000", "message": "", "data": "ok"}`))
			},
			args: args{
				params: &FailoverParams{
					StageID:     1,
					ClusterName: "r3_test",
					ShardList: []*omodel.Shard{
						{Name: "r3-test_server1", Master: &omodel.Instance{IP: "************", Port: 7000}, Slaves: []*omodel.Instance{{IP: "************", Port: 7000}}},
						{Name: "r3-test_server2", Master: &omodel.Instance{IP: "************", Port: 7001}, Slaves: []*omodel.Instance{{IP: "************", Port: 7001}}},
						{Name: "r3-test_server3", Master: &omodel.Instance{IP: "************", Port: 7002}, Slaves: []*omodel.Instance{{IP: "************", Port: 7002}}},
					},
					MaxConcurnecy: 2,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := Failover(tt.args.params)
			if (err != nil) != tt.wantErr {
				t.Errorf("Failover() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

func TestStopFailover(t *testing.T) {
	initTest()
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		params *BasicParams
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test: call failed",
			args: args{
				params: &BasicParams{
					StageID:     1,
					ClusterName: "r3_test",
				},
			},
			wantErr: true,
		},
		{
			name: "test: return value format error",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/cluster/redis/stopFailover`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": []}`))
			},
			args: args{
				params: &BasicParams{
					StageID:     1,
					ClusterName: "r3_test",
				},
			},
			wantErr: true,
		},
		{
			name: "test: success",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/cluster/redis/stopFailover`),
					httpmock.NewStringResponder(200, `{"code": "00000", "message": "", "data": "ok"}`))
			},
			args: args{
				params: &BasicParams{
					StageID:     1,
					ClusterName: "r3_test",
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := StopFailover(tt.args.params)
			if (err != nil) != tt.wantErr {
				t.Errorf("Failover() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}
