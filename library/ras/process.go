package ras

import (
	"fmt"

	"dt-common/errs"
	"dt-common/logger"
	"dt-common/omodel"
)

type InstanceOperateParams struct {
	StageId     int64    `json:"stageId"`
	ClusterName string   `json:"-"`
	BNS         string   `json:"bns"`
	IDC         string   `json:"idc"`
	IPList      []string `json:"ipList"`
}

// =======================================
// 				 sentinel
// =======================================

// manager: sentinel启动接口
func StartSentinel(params *InstanceOperateParams) error {
	subUrl := "/process/sentinel/start"

	res, err := Deploy(omodel.DEPLOY_ENV_DOCKER).request("POST", subUrl, params)
	if err != nil {
		logger.Warn("failed to start Sentinel, clusterName=%v, error=(%v)", params.ClusterName, err)
		return err
	}

	if res.Code != string(errs.Success) {
		return fmt.Errorf(res.Message)
	}

	return nil
}

// manager: sentinel关停接口
func StopSentinel(params *InstanceOperateParams) error {
	subUrl := "/process/sentinel/stop"

	res, err := Deploy(omodel.DEPLOY_ENV_DOCKER).request("POST", subUrl, params)
	if err != nil {
		logger.Warn("failed to stop Sentinel, clusterName=%v, error=(%v)", params.ClusterName, err)
		return err
	}

	if res.Code != string(errs.Success) {
		return fmt.Errorf(res.Message)
	}

	return nil
}

// =======================================
// 				  proxy
// =======================================

// 异步启动Proxy实例，不包括解屏蔽动作
func StartProxy(params *InstanceOperateParams) error {
	subUrl := "/process/proxy/start"

	res, err := Deploy(omodel.DEPLOY_ENV_DOCKER).request("POST", subUrl, params)
	if err != nil {
		logger.Warn("failed to delete instance, clusterName=%v, error=(%v)", params.ClusterName, err)
		return err
	}

	if res.Code != string(errs.Success) {
		return fmt.Errorf(res.Message)
	}

	return nil
}

// 异步关停Proxy实例，包括屏蔽动作
// manager: 检查实例屏蔽状态
// agent: 检查流量
func StopProxy(params *InstanceOperateParams) error {
	subUrl := "/process/proxy/stop"

	res, err := Deploy(omodel.DEPLOY_ENV_DOCKER).request("POST", subUrl, params)
	if err != nil {
		logger.Warn("failed to delete instance, clusterName=%v, error=(%v)", params.ClusterName, err)
		return err
	}

	if res.Code != string(errs.Success) {
		return fmt.Errorf(res.Message)
	}

	return nil
}

// =======================================
// 				  redis
// =======================================

type RedisOperateParams struct {
	StageId     int64              `json:"stageId"`
	ClusterName string             `json:"-"`
	BNS         string             `json:"bns"`
	Instances   []*omodel.Instance `json:"instances"`
}

// manager: 启动集群从库
func StartSlaves(params *RedisOperateParams) error {
	subUrl := "/process/slave/start"

	res, err := Deploy(omodel.DEPLOY_ENV_BBC).request("POST", subUrl, params)
	if err != nil {
		logger.Warn("failed to delete instance, clusterName=%v, error=(%v)", params.ClusterName, err)
		return err
	}

	if res.Code != string(errs.Success) {
		return fmt.Errorf(res.Message)
	}

	return nil
}

// manager: 关停集群从库
func StopSlaves(params *RedisOperateParams) error {
	subUrl := "/process/slave/stop"

	res, err := Deploy(omodel.DEPLOY_ENV_BBC).request("POST", subUrl, params)
	if err != nil {
		logger.Warn("failed to delete instance, clusterName=%v, error=(%v)", params.ClusterName, err)
		return err
	}

	if res.Code != string(errs.Success) {
		return fmt.Errorf(res.Message)
	}

	return nil
}
