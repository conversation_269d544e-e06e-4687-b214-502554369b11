package ras

import (
	"fmt"
	"strings"

	"dt-common/errs"
	"dt-common/logger"
	"dt-common/omodel"
)

// =====================================
// 				slave of
// =====================================

// SlaveOfParams 主从切换请求结构体
type SlaveOfParams struct {
	StageID   int64           `json:"stageId"`
	ShardList []*omodel.Shard `json:"shardList"`
}

// RedisSlaveOf 容器集群redis实例slaveof操作
func SlaveOf(params *SlaveOfParams) error {
	subUrl := "/migration/slaveOf"
	res, err := Deploy(omodel.DEPLOY_ENV_DOCKER).request("POST", subUrl, params)
	if err != nil {
		logger.Warn("failed to call cmanager to exec slaveof, error=(%v)", err)
		return err
	}

	if res.Code != string(errs.Success) {
		return fmt.Errorf(res.Message)
	}

	return nil
}

// 取消slaveof，用于特殊情况下的止损。
// - 例如流量高峰期导致aof无法跟上，重复rdb的情况
func StopSlaveOf(params *BasicParams) error {
	subUrl := "/migration/stopSlaveOf"
	res, err := Deploy(omodel.DEPLOY_ENV_DOCKER).request("POST", subUrl, params)
	if err != nil {
		logger.Warn("failed to call cmanager to stop slaveof, error=(%v)", err)
		return err
	}

	if res.Code != string(errs.Success) {
		return fmt.Errorf(res.Message)
	}

	return nil
}

//
//
//

type ChangeEnabledAZParams struct {
	StageID     int64    `json:"stageId"`
	ClusterName string   `json:"clusterName"`
	EnabledAZ   []string `json:"enabledAZ"`
}

// cmanager: 将容器集群扩容完整
func ChangeClusterEnabledAZ(params *ChangeEnabledAZParams) error {
	if params.ClusterName == "" {
		return fmt.Errorf("cluster name should not be empty")
	}
	if len(params.EnabledAZ) == 0 {
		return fmt.Errorf("enabledAZ should not be empty")
	}

	subUrl := "/migration/changeEnabledAZ"
	res, err := Deploy(omodel.DEPLOY_ENV_DOCKER).request("POST", subUrl, params)
	if err != nil {
		logger.Warn("failed to change cluster enabledAZ to %v, error=(%v)", strings.Join(params.EnabledAZ, ","), err)
		return err
	}

	if res.Code != string(errs.Success) {
		return fmt.Errorf(res.Message)
	}

	return nil
}

//
//
//

type ChangeModeParams struct {
	StageID     int64  `json:"stageId"`
	ClusterName string `json:"clusterName"`
	Target      string `json:"target"`
}

// cmanager: 更新容器集群是否由operator托管
func ChangeMode(params *ChangeModeParams) error {
	subUrl := "/migration/changeMode"
	res, err := Deploy(omodel.DEPLOY_ENV_DOCKER).request("POST", subUrl, params)
	if err != nil {
		logger.Warn("failed to change cluster mode to %s, error=(%v)", params.Target, err)
		return err
	}

	if res.Code != string(errs.Success) {
		return fmt.Errorf(res.Message)
	}

	return nil
}

type QuorumParams struct {
	StageID int64  `json:"stageId"`
	BNS     string `json:"bns"`
	Quorum  int    `json:"quorum"`
}

func UpdateQuorum(params *QuorumParams) error {
	subUrl := "/migration/updateQuorum"
	res, err := Deploy(omodel.DEPLOY_ENV_DOCKER).request("POST", subUrl, params)
	if err != nil {
		logger.Warn("failed to call redis-camanger to update quorum, error=(%v)", err)
		return err
	}

	if res.Code != string(errs.Success) {
		return fmt.Errorf(res.Message)
	}

	return nil
}

// =====================================
//                解挂载
// =====================================

// 物理集群实例下线请求结构体
type InstanceOfflineRequest struct {
	StageId     int64  `json:"stageId"`
	ClusterName string `json:"clusterName"`
	IDC         string `json:"idc"`
}

// manager: 解挂载实例
func UnmountInstance(params *InstanceOfflineRequest) error {
	subUrl := "/migration/unmount"

	res, err := Deploy(omodel.DEPLOY_ENV_DOCKER).request("POST", subUrl, params)
	if err != nil {
		logger.Warn("failed to delete instance, clusterName=%v, error=(%v)", params.ClusterName, err)
		return err
	}

	if res.Code != string(errs.Success) {
		return fmt.Errorf(res.Message)
	}

	return nil
}
