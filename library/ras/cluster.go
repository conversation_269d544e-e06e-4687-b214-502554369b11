package ras

import (
	"fmt"

	"dt-common/errs"
	"dt-common/logger"
	"dt-common/omodel"
)

// 集群部署
func CreateCluster(params *omodel.DeployOptions) error {
	subUrl := "/cluster/deploy"
	res, err := Deploy(omodel.DEPLOY_ENV_DOCKER).request("POST", subUrl, params)
	if err != nil {
		logger.Warn("failed to create new cluster, error=(%v)", err)
		return err
	}

	if res.Code != string(errs.Success) {
		return fmt.Errorf(res.Message)
	}

	return nil
}

type ProxyScalingParams struct {
	*BasicParams
	ProxyNum int `json:"proxyNum"`
}

// proxy扩缩容，调整配置
func ProxyScaling(params *ProxyScalingParams) error {
	subUrl := "/cluster/proxy/scaling"
	res, err := Deploy(omodel.DEPLOY_ENV_DOCKER).request("POST", subUrl, params)
	if err != nil {
		logger.Warn("failed to call redis-cmanager to scale num of proxy, error=(%v)", err)
		return err
	}

	if res.Code != string(errs.Success) {
		return fmt.Errorf(res.Message)
	}

	return nil
}

// 删除污点Proxy（容器）
func RemoveTaintProxy(params *BasicParams) error {
	subUrl := "/cluster/proxy/remove"

	res, err := Deploy(omodel.DEPLOY_ENV_DOCKER).request("POST", subUrl, params)
	if err != nil {
		logger.Warn("failed to call redis-cmanager to delete tainted proxy, clusterName=%v, error=(%v)", params.ClusterName, err)
		return err
	}

	if res.Code != string(errs.Success) {
		return fmt.Errorf(res.Message)
	}

	return nil
}
