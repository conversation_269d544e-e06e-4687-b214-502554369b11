package ras

import (
	"log"
	"testing"

	"dt-common/logger"
	"redis-xweb/config"
)

func initTest() {
	// 初始化配置
	err := config.Init("../../config/config.yaml")
	if err != nil {
		log.Panicf("failed to init config, error=(%v)", err)
	}

	// 初始化 Logger
	var logConfig logger.Config
	err = config.Get("logger", &logConfig)
	if err != nil {
		log.Panicf("failed to init logger, error=(%v)", err)
	}
	logger.Init(&logConfig)

	// 初始化 ras
	var rasConfig Config
	config.Get("ras", &rasConfig)
	logger.Debug("ras config: %+v", rasConfig)
	if err != nil {
		log.Panicf("failed to init ras sdk, error=(%v)", err)
	}
	Init(&rasConfig)
}

func TestInit(t *testing.T) {
	type args struct {
		conf *Config
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test: manager nil",
			args: args{
				conf: &Config{
					CManager: &Project{URL: "http://127.0.0.1:8080", Token: "test"},
					Timeout:  5,
				},
			},
			wantErr: true,
		},
		{
			name: "test: manager url empty",
			args: args{
				conf: &Config{
					Manager:  &Project{URL: "", Token: "test"},
					CManager: &Project{URL: "http://127.0.0.1:8080", Token: "test"},
					Timeout:  5,
				},
			},
			wantErr: true,
		},
		{
			name: "test: cmanager nil",
			args: args{
				conf: &Config{
					Manager: &Project{URL: "http://127.0.0.1:8080", Token: "test"},
					Timeout: 5,
				},
			},
			wantErr: true,
		},
		{
			name: "test: cmanager url empty",
			args: args{
				conf: &Config{
					Manager:  &Project{URL: "http://127.0.0.1:8080", Token: "test"},
					CManager: &Project{URL: "", Token: "test"},
					Timeout:  5,
				},
			},
			wantErr: true,
		},
		{
			name: "test: success",
			args: args{
				conf: &Config{
					Manager:  &Project{URL: "127.0.0.1:8080", Token: "test"},
					CManager: &Project{URL: "127.0.0.1:8080", Token: "test"},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := Init(tt.args.conf)
			if (err != nil) != tt.wantErr {
				t.Errorf("Init() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

func TestDeploy(t *testing.T) {
	initTest()

	type args struct {
		env int
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, *Project)
	}{
		{
			name: "test: env 0 get default",
			args: args{
				env: 0,
			},
		},
		{
			name: "test: env 1 get 1",
			args: args{
				env: 1,
			},
		},
		{
			name: "test: env 2 get default",
			args: args{
				env: 2,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			p := Deploy(tt.args.env)
			// if (err != nil) != tt.wantErr {
			// 	t.Errorf("Deploy() error = %v, wantErr %v", err, tt.wantErr)
			// }
			if tt.expect != nil {
				tt.expect(t, p)
			}
		})
	}
}
