package ras

import (
	"fmt"

	"dt-common/errs"
	"dt-common/logger"
	"dt-common/omodel"
)

// FailoverParams 主从切换请求结构体
type FailoverParams struct {
	StageID       int64           `json:"stageId"`
	ClusterName   string          `json:"clusterName"`
	ShardList     []*omodel.Shard `json:"shardList"`
	MaxConcurnecy int             `json:"maxConcurnecy"`
	IntervalTime  int             `json:"intervalTime"`
}

// Failover 主从切换
// - 仅CManager实现
func Failover(params *FailoverParams) error {
	subUrl := "/cluster/redis/failover"

	res, err := Deploy(omodel.DEPLOY_ENV_DOCKER).request("POST", subUrl, params)
	if err != nil {
		logger.Warn("failed to failover, clusterName=%v, error=(%v)", params.ClusterName, err)
		return err
	}

	if res.Code != string(errs.Success) {
		return fmt.Errorf(res.Message)
	}

	return nil
}

// StopFailover 停止主从切换
// - 仅CManager实现
func StopFailover(params *BasicParams) error {
	subUrl := "/cluster/redis/stopFailover"
	res, err := Deploy(omodel.DEPLOY_ENV_DOCKER).request("POST", subUrl, params)
	if err != nil {
		logger.Warn("failed to stop failover, clusterName=%v, error=(%v)", params.ClusterName, err)
		return err
	}

	if res.Code != string(errs.Success) {
		return fmt.Errorf(res.Message)
	}

	return nil
}
