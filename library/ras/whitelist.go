package ras

import (
	"fmt"

	"dt-common/errs"
)

// WhiteListRequest 修改白名单请求结构体
type WhiteListRequest struct {
	StageID      int64    `json:"stageId"`
	ClusterName  string   `json:"clusterName"`
	Action       string   `json:"action"`
	WhiteListBns []string `json:"whiteListBns"`
	WhiteListIp  []string `json:"whiteListIp,omitempty"`
}

// UpdateWhitelist 更新白名单
func (p *Project) UpdateWhitelist(params *WhiteListRequest) error {
	subUrl := "/whitelist/updateWhitelist"
	res, err := p.request("POST", subUrl, params)
	if err != nil {
		return err
	}

	if res.Code != string(errs.Success) {
		return fmt.Errorf(res.Message)
	}

	return nil
}

// UpdateManageWhitelist 更新白名单
func (p *Project) UpdateManageWhitelist(params *WhiteListRequest) error {
	subUrl := "/whitelist/updateManageWhitelist"
	res, err := p.request("POST", subUrl, params)
	if err != nil {
		return err
	}

	if res.Code != string(errs.Success) {
		return fmt.Errorf(res.Message)
	}

	return nil
}
