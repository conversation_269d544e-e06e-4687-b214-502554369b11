package ras

import (
	"regexp"
	"testing"

	"github.com/jarcoal/httpmock"

	"dt-common/omodel"
)

func TestSlaveOf(t *testing.T) {
	initTest()
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		params *SlaveOfParams
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test: call failed",
			args: args{
				params: &SlaveOfParams{
					StageID:   1,
					ShardList: []*omodel.Shard{},
				},
			},
			wantErr: true,
		},
		{
			name: "test: return value format error",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/migration/slaveOf`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": ""}`))
			},
			args: args{
				params: &SlaveOfParams{
					StageID:   1,
					ShardList: []*omodel.Shard{},
				},
			},
			wantErr: true,
		},
		{
			name: "test: success",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/migration/slaveOf`),
					httpmock.NewStringResponder(200, `{"code": "00000", "message": "", "data": "ok"}`))
			},
			args: args{
				params: &SlaveOfParams{
					StageID:   1,
					ShardList: []*omodel.Shard{},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := SlaveOf(tt.args.params)
			if (err != nil) != tt.wantErr {
				t.Errorf("SlaveOf() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

func TestStopSlaveOf(t *testing.T) {
	initTest()
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	tests := []struct {
		name    string
		before  func()
		args    BasicParams
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test: call failed",
			args: BasicParams{
				StageID:     1,
				ClusterName: "r3_test",
			},
			wantErr: true,
		},
		{
			name: "test: return value format error",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/migration/stopSlaveOf`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": ""}`))
			},
			args: BasicParams{
				StageID:     1,
				ClusterName: "r3_test",
			},
			wantErr: true,
		},
		{
			name: "test: success",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/migration/stopSlaveOf`),
					httpmock.NewStringResponder(200, `{"code": "00000", "message": "", "data": "ok"}`))
			},
			args: BasicParams{
				StageID:     1,
				ClusterName: "r3_test",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := StopSlaveOf(&tt.args)
			if (err != nil) != tt.wantErr {
				t.Errorf("StopSlaveOf() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

func TestUnmountInstance(t *testing.T) {
	initTest()
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		params *InstanceOfflineRequest
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test: call failed",
			args: args{
				params: &InstanceOfflineRequest{
					StageId:     1,
					ClusterName: "r3_test",
					IDC:         "hba",
				},
			},
			wantErr: true,
		},
		{
			name: "test: return value format error",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/cluster/migration/unmount`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": ""}`))
			},
			args: args{
				params: &InstanceOfflineRequest{
					StageId:     1,
					ClusterName: "r3_test",
					IDC:         "hba",
				},
			},
			wantErr: true,
		},
		{
			name: "test: success",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/cluster/migration/unmount`),
					httpmock.NewStringResponder(200, `{"code": "00000", "message": "", "data": "ok"}`))
			},
			args: args{
				params: &InstanceOfflineRequest{
					StageId:     1,
					ClusterName: "r3_test",
					IDC:         "hba",
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}

			err := UnmountInstance(tt.args.params)
			if (err != nil) != tt.wantErr {
				t.Errorf("UnmountInstance() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

// 单测：hba、hbc扩容
func TestChangeClusterEnabledAZ(t *testing.T) {
	initTest()
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	tests := []struct {
		name    string
		before  func()
		args    ChangeEnabledAZParams
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test: empty value",
			args: ChangeEnabledAZParams{
				StageID:     1,
				ClusterName: "",
				EnabledAZ:   []string{},
			},
			wantErr: true,
		},
		{
			name: "test: return value format error",
			args: ChangeEnabledAZParams{
				StageID:     1,
				ClusterName: "r3_test",
				EnabledAZ:   []string{"hba", "hbb"},
			},
			wantErr: true,
		},
		{
			name: "test: success",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/migration/changeEnabledAZ`),
					httpmock.NewStringResponder(200, `{"code": "00000", "message": "", "data": "ok"}`))
			},
			args: ChangeEnabledAZParams{
				StageID:     1,
				ClusterName: "r3_test",
				EnabledAZ:   []string{"hba", "hbb"},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := ChangeClusterEnabledAZ(&tt.args)
			if (err != nil) != tt.wantErr {
				t.Errorf("ChangeClusterEnabledAZ() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

func TestChangeMode(t *testing.T) {
	initTest()
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		params ChangeModeParams
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test: call failed",
			args: args{
				params: ChangeModeParams{
					StageID: 1,
					Target:  omodel.MODE_FULL_CARE,
				},
			},
			wantErr: true,
		},
		{
			name: "test: return value format error",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/migration/changeMode`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": []}`))
			},
			args: args{
				params: ChangeModeParams{
					StageID: 1,
					Target:  omodel.MODE_FULL_CARE,
				},
			},
			wantErr: true,
		},
		{
			name: "test: success",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/migration/changeMode`),
					httpmock.NewStringResponder(200, `{"code": "00000", "message": "", "data": "ok"}`))
			},
			args: args{
				params: ChangeModeParams{
					StageID: 1,
					Target:  omodel.MODE_FULL_CARE,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := ChangeMode(&tt.args.params)
			if (err != nil) != tt.wantErr {
				t.Errorf("ChangeMode() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}
