package ras

import (
	"regexp"
	"testing"

	"github.com/jarcoal/httpmock"

	"dt-common/omodel"
)

func TestStartProxy(t *testing.T) {
	initTest()
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		params *InstanceOperateParams
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test: call failed",
			args: args{
				params: &InstanceOperateParams{
					StageId:     1,
					ClusterName: "r3_test",
					BNS:         "r3-test-router.siod-redis",
					IPList:      []string{"127.0.0.1", "************"},
				},
			},
			wantErr: true,
		},
		{
			name: "test: return value format error",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/process/proxy/start`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": ""}`))
			},
			args: args{
				params: &InstanceOperateParams{
					StageId:     1,
					ClusterName: "r3_test",
					BNS:         "r3-test-router.siod-redis",
					IPList:      []string{"127.0.0.1", "************"},
				},
			},
			wantErr: true,
		},
		{
			name: "test: success",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/process/proxy/start`),
					httpmock.NewStringResponder(200, `{"code": "00000", "message": "", "data": "ok"}`))
			},
			args: args{
				params: &InstanceOperateParams{
					StageId:     1,
					ClusterName: "r3_test",
					BNS:         "r3-test-router.siod-redis",
					IPList:      []string{"127.0.0.1", "************"},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := StartProxy(tt.args.params)
			if (err != nil) != tt.wantErr {
				t.Errorf("StartProxy() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

func TestStopProxy(t *testing.T) {
	initTest()
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		params *InstanceOperateParams
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test: call failed",
			args: args{
				params: &InstanceOperateParams{
					StageId:     1,
					ClusterName: "r3_test",
					BNS:         "r3-test-router.siod-redis",
					IPList:      []string{"127.0.0.1", "************"},
				},
			},
			wantErr: true,
		},
		{
			name: "test: return value format error",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/process/proxy/stop`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": ""}`))
			},
			args: args{
				params: &InstanceOperateParams{
					StageId:     1,
					ClusterName: "r3_test",
					BNS:         "r3-test-router.siod-redis",
					IPList:      []string{"127.0.0.1", "************"},
				},
			},
			wantErr: true,
		},
		{
			name: "test: success",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/process/proxy/stop`),
					httpmock.NewStringResponder(200, `{"code": "00000", "message": "", "data": "ok"}`))
			},
			args: args{
				params: &InstanceOperateParams{
					StageId:     1,
					ClusterName: "r3_test",
					BNS:         "r3-test-router.siod-redis",
					IPList:      []string{"127.0.0.1", "************"},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := StopProxy(tt.args.params)
			if (err != nil) != tt.wantErr {
				t.Errorf("StartProxy() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

func TestStartSentinel(t *testing.T) {
	initTest()
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		params *InstanceOperateParams
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test: call failed",
			args: args{
				params: &InstanceOperateParams{
					StageId:     1,
					ClusterName: "r3_test",
					BNS:         "r3-test-router.siod-redis",
					IPList:      []string{"127.0.0.1", "************"},
				},
			},
			wantErr: true,
		},
		{
			name: "test: return value format error",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/process/sentinel/start`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": ""}`))
			},
			args: args{
				params: &InstanceOperateParams{
					StageId:     1,
					ClusterName: "r3_test",
					BNS:         "r3-test-router.siod-redis",
					IPList:      []string{"127.0.0.1", "************"},
				},
			},
			wantErr: true,
		},
		{
			name: "test: success",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/process/sentinel/start`),
					httpmock.NewStringResponder(200, `{"code": "00000", "message": "", "data": "ok"}`))
			},
			args: args{
				params: &InstanceOperateParams{
					StageId:     1,
					ClusterName: "r3_test",
					BNS:         "r3-test-router.siod-redis",
					IPList:      []string{"127.0.0.1", "************"},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := StartSentinel(tt.args.params)
			if (err != nil) != tt.wantErr {
				t.Errorf("StartSentinel() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

func TestStopSentinel(t *testing.T) {
	initTest()
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		params *InstanceOperateParams
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test: call failed",
			args: args{
				params: &InstanceOperateParams{
					StageId:     1,
					ClusterName: "r3_test",
					BNS:         "r3-test-router.siod-redis",
					IPList:      []string{"127.0.0.1", "************"},
				},
			},
			wantErr: true,
		},
		{
			name: "test: return value format error",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/process/sentinel/stop`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": ""}`))
			},
			args: args{
				params: &InstanceOperateParams{
					StageId:     1,
					ClusterName: "r3_test",
					BNS:         "r3-test-router.siod-redis",
					IPList:      []string{"127.0.0.1", "************"},
				},
			},
			wantErr: true,
		},
		{
			name: "test: success",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/process/sentinel/stop`),
					httpmock.NewStringResponder(200, `{"code": "00000", "message": "", "data": "ok"}`))
			},
			args: args{
				params: &InstanceOperateParams{
					StageId:     1,
					ClusterName: "r3_test",
					BNS:         "r3-test-router.siod-redis",
					IPList:      []string{"127.0.0.1", "************"},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := StopSentinel(tt.args.params)
			if (err != nil) != tt.wantErr {
				t.Errorf("StopSentinel() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

func TestStartSlaves(t *testing.T) {
	initTest()
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		params *RedisOperateParams
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test: call failed",
			args: args{
				params: &RedisOperateParams{
					StageId:     1,
					ClusterName: "r3_test",
					BNS:         "r3-test-router.siod-redis",
					Instances:   []*omodel.Instance{},
				},
			},
			wantErr: true,
		},
		{
			name: "test: return value format error",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/process/slave/start`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": ""}`))
			},
			args: args{
				params: &RedisOperateParams{
					StageId:     1,
					ClusterName: "r3_test",
					BNS:         "r3-test-router.siod-redis",
					Instances:   []*omodel.Instance{},
				},
			},
			wantErr: true,
		},
		{
			name: "test: success",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/process/slave/start`),
					httpmock.NewStringResponder(200, `{"code": "00000", "message": "", "data": "ok"}`))
			},
			args: args{
				params: &RedisOperateParams{
					StageId:     1,
					ClusterName: "r3_test",
					BNS:         "r3-test-router.siod-redis",
					Instances:   []*omodel.Instance{},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := StartSlaves(tt.args.params)
			if (err != nil) != tt.wantErr {
				t.Errorf("StartSlaves() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

func TestStopSlaves(t *testing.T) {
	initTest()
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		params *RedisOperateParams
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test: call failed",
			args: args{
				params: &RedisOperateParams{
					StageId:     1,
					ClusterName: "r3_test",
					BNS:         "r3-test-router.siod-redis",
					Instances:   []*omodel.Instance{},
				},
			},
			wantErr: true,
		},
		{
			name: "test: return value format error",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/process/slave/stop`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": ""}`))
			},
			args: args{
				params: &RedisOperateParams{
					StageId:     1,
					ClusterName: "r3_test",
					BNS:         "r3-test-router.siod-redis",
					Instances:   []*omodel.Instance{},
				},
			},
			wantErr: true,
		},
		{
			name: "test: success",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/process/slave/stop`),
					httpmock.NewStringResponder(200, `{"code": "00000", "message": "", "data": "ok"}`))
			},
			args: args{
				params: &RedisOperateParams{
					StageId:     1,
					ClusterName: "r3_test",
					BNS:         "r3-test-router.siod-redis",
					Instances:   []*omodel.Instance{},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := StopSlaves(tt.args.params)
			if (err != nil) != tt.wantErr {
				t.Errorf("StopSlaves() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}
