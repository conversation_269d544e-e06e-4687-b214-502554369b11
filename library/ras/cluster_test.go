package ras

import (
	"regexp"
	"testing"

	"github.com/jarcoal/httpmock"

	"dt-common/omodel"
)

func TestCreateCluster(t *testing.T) {
	initTest()
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	// type args struct {}
	tests := []struct {
		name    string
		before  func()
		args    *omodel.DeployOptions
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name:    "test: empty value",
			args:    &omodel.DeployOptions{},
			wantErr: true,
		},
		{
			name: "test: success",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/cluster/deploy`),
					httpmock.NewStringResponder(200, `{"code": "00000", "message": "", "data": "ok"}`))
			},
			args:    &omodel.DeployOptions{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := CreateCluster(tt.args)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateCluster() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

func TestProxyScaling(t *testing.T) {
	initTest()
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	tests := []struct {
		name    string
		before  func()
		args    *ProxyScalingParams
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name:    "test: empty value",
			args:    &ProxyScalingParams{},
			wantErr: true,
		},
		{
			name: "test: success",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/cluster/proxy/scaling`),
					httpmock.NewStringResponder(200, `{"code": "00000", "message": "", "data": "ok"}`))
			},
			args:    &ProxyScalingParams{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := ProxyScaling(tt.args)
			if (err != nil) != tt.wantErr {
				t.Errorf("ProxyScaling() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}


func TestRemoveTaintProxy(t *testing.T) {
	initTest()
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		params *BasicParams
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test: call failed",
			args: args{
				params: &BasicParams{
					StageID:     1,
					ClusterName: "r3_test",
				},
			},
			wantErr: true,
		},
		{
			name: "test: return value format error",
			before: func() {
				httpmock.Reset()
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/cluster/proxy/remove`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": []}`))
			},
			args: args{
				params: &BasicParams{
					StageID:     1,
					ClusterName: "r3_test",
				},
			},
			wantErr: true,
		},
		{
			name: "test: success",
			before: func() {
				httpmock.Reset()
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/cluster/proxy/remove`),
					httpmock.NewStringResponder(200, `{"code": "00000", "message": "", "data": "ok"}`))
			},
			args: args{
				params: &BasicParams{
					StageID:     1,
					ClusterName: "r3_test",
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := RemoveTaintProxy(tt.args.params)
			if (err != nil) != tt.wantErr {
				t.Errorf("RemoveTaintProxy() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}
