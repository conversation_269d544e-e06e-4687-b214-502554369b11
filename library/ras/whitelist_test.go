package ras

import (
	"regexp"
	"testing"

	"github.com/jarcoal/httpmock"

	"dt-common/omodel"
)

func TestUpdateWhitelist(t *testing.T) {
	initTest()
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		params *WhiteListRequest
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test: call failed",
			args: args{
				params: &WhiteListRequest{},
			},
			wantErr: true,
		},
		{
			name: "test: return value format error",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/whitelist/updateWhitelist`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": ""}`))
			},
			args: args{
				params: &WhiteListRequest{},
			},
			wantErr: true,
		},
		{
			name: "test: success",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/whitelist/updateWhitelist`),
					httpmock.NewStringResponder(200, `{"code": "00000", "message": "", "data": "ok"}`))
			},
			args: args{
				params: &WhiteListRequest{},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := Deploy(omodel.DEPLOY_ENV_BBC).UpdateWhitelist(tt.args.params)
			if (err != nil) != tt.wantErr {
				t.Errorf("UpdateWhitelist() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

func TestUpdateManageWhitelist(t *testing.T) {
	initTest()
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		params *WhiteListRequest
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test: call failed",
			args: args{
				params: &WhiteListRequest{},
			},
			wantErr: true,
		},
		{
			name: "test: return value format error",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/whitelist/updateManageWhitelist`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": ""}`))
			},
			args: args{
				params: &WhiteListRequest{},
			},
			wantErr: true,
		},
		{
			name: "test: success",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/whitelist/updateManageWhitelist`),
					httpmock.NewStringResponder(200, `{"code": "00000", "message": "", "data": "ok"}`))
			},
			args: args{
				params: &WhiteListRequest{},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := Deploy(omodel.DEPLOY_ENV_BBC).UpdateManageWhitelist(tt.args.params)
			if (err != nil) != tt.wantErr {
				t.Errorf("UpdateManageWhitelist() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

// func TestRollbackWhitelist(t *testing.T) {
// 	initTest()
// 	httpmock.Activate()
// 	defer httpmock.DeactivateAndReset()

// 	type args struct {
// 		params *WhitelistRollbackParams
// 	}
// 	tests := []struct {
// 		name    string
// 		before  func()
// 		args    args
// 		wantErr bool
// 		expect  func(*testing.T)
// 	}{
// 		{
// 			name: "test: call failed",
// 			args: args{
// 				params: &WhitelistRollbackParams{},
// 			},
// 			wantErr: true,
// 		},
// 		{
// 			name: "test: return value format error",
// 			before: func() {
// 				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/whitelist/updateWhitelist`),
// 					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": ""}`))
// 			},
// 			args: args{
// 				params: &WhitelistRollbackParams{},
// 			},
// 			wantErr: true,
// 		},
// 		{
// 			name: "test: success",
// 			before: func() {
// 				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/whitelist/updateWhitelist`),
// 					httpmock.NewStringResponder(200, `{"code": "00000", "message": "", "data": "ok"}`))
// 			},
// 			args: args{
// 				params: &WhitelistRollbackParams{},
// 			},
// 			wantErr: false,
// 		},
// 	}
// 	for _, tt := range tests {
// 		t.Run(tt.name, func(t *testing.T) {
// 			if tt.before != nil {
// 				tt.before()
// 			}
// 			err := Deploy(omodel.DEPLOY_ENV_BBC).RollbackWhitelist(tt.args.params)
// 			if (err != nil) != tt.wantErr {
// 				t.Errorf("RollbackWhitelist() error = %v, wantErr %v", err, tt.wantErr)
// 			}
// 			if tt.expect != nil {
// 				tt.expect(t)
// 			}
// 		})
// 	}
// }
// func TestRollbackManageWhitelist(t *testing.T) {
// 	initTest()
// 	httpmock.Activate()
// 	defer httpmock.DeactivateAndReset()

// 	type args struct {
// 		params *WhitelistRollbackParams
// 	}
// 	tests := []struct {
// 		name    string
// 		before  func()
// 		args    args
// 		wantErr bool
// 		expect  func(*testing.T)
// 	}{
// 		{
// 			name: "test: call failed",
// 			args: args{
// 				params: &WhitelistRollbackParams{},
// 			},
// 			wantErr: true,
// 		},
// 		{
// 			name: "test: return value format error",
// 			before: func() {
// 				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/whitelist/updateManageWhitelist`),
// 					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": ""}`))
// 			},
// 			args: args{
// 				params: &WhitelistRollbackParams{},
// 			},
// 			wantErr: true,
// 		},
// 		{
// 			name: "test: success",
// 			before: func() {
// 				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/whitelist/updateManageWhitelist`),
// 					httpmock.NewStringResponder(200, `{"code": "00000", "message": "", "data": "ok"}`))
// 			},
// 			args: args{
// 				params: &WhitelistRollbackParams{},
// 			},
// 			wantErr: false,
// 		},
// 	}
// 	for _, tt := range tests {
// 		t.Run(tt.name, func(t *testing.T) {
// 			if tt.before != nil {
// 				tt.before()
// 			}
// 			err := Deploy(omodel.DEPLOY_ENV_BBC).RollbackManageWhitelist(tt.args.params)
// 			if (err != nil) != tt.wantErr {
// 				t.Errorf("RollbackManageWhitelist() error = %v, wantErr %v", err, tt.wantErr)
// 			}
// 			if tt.expect != nil {
// 				tt.expect(t)
// 			}
// 		})
// 	}
// }
