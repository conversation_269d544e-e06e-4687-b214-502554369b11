package stage

import (
	"fmt"
	"log"
	"reflect"

	"github.com/gin-gonic/gin"

	"dt-common/ent/stage"
	"dt-common/mysql"
	"dt-common/omodel"
	"dt-common/utils"
	"dt-common/xweb"
	"redis-cmanager/library/errc"
)

var (
	localIP string
)

func init() {
	ip, err := utils.GetLocalIP()
	if err != nil {
		log.Panicf("failed to get local ip")
		return
	}
	log.Printf("local ip is %s\n", ip)
	localIP = ip
}

// 后台执行时，panic不会被gin.Recovery捕获
func AsyncRecovery(params *xweb.CallBackParam) {
	if params.StageID == 0 {
		return
	}

	if err := recover(); err != nil {
		params.Success = false
		params.Message = fmt.Sprintf("%v", err)
	}
	xweb.StageCallBack(params)
}

// 将localIP记录数据库
func mark(stageId int64) error {
	if stageId == 0 {
		return nil
	}

	db, err := mysql.Database()
	if err != nil {
		return err
	}

	ctx, cancel := mysql.ContextWithTimeout()
	stageData, err := db.Stage.Query().Where(stage.ID(stageId)).Only(ctx)
	cancel()
	if err != nil {
		return err
	}

	// 已经打了标记的不重复打
	if stageData.ServerIP == localIP {
		return nil
	}
	// 已经被其他节点执行的不打标记
	if stageData.ServerIP != "" && stageData.Status != string(omodel.STAGE_STATUS_RUNNING) && stageData.Status != string(omodel.STAGE_STATUS_ROLLBACK_RUNNING) {
		return fmt.Errorf("stage already exec by another cmanager %s", stageData.ServerIP)
	}
	ctx, cancel = mysql.ContextWithTimeout()
	_, err = db.Stage.Update().SetServerIP(localIP).Where(stage.ID(stageId)).Save(ctx)
	cancel()

	return err
}

// 后台执行变更函数，结束回调xweb
func Async(stageId int64, syncFun any, args ...reflect.Value) error {
	// 判断syncFunc类型是否符合要求
	ft := reflect.TypeOf(syncFun)
	if ft.Kind() != reflect.Func || ft.NumOut() != 1 {
		return errc.CodeUndefinedValue.Detail("syncFunc should be a function with an error return value")
	}

	// 遇到单测退出
	if gin.Mode() == gin.TestMode {
		return nil
	}

	// 后台执行变更逻辑，defer callback
	go func() {
		cbParams := xweb.CallBackParam{
			StageID: stageId,
		}
		defer AsyncRecovery(&cbParams)

		// 打标记
		err := mark(stageId)
		if err != nil {
			cbParams.Success = false
			cbParams.Message = err.Error()
			return
		}

		f := reflect.ValueOf(syncFun)
		result := f.Call(args)
		// return nil表示执行成功，否则表示执行失败
		if result[0].IsNil() {
			cbParams.Success = true
			return
		}
		// 执行失败后上报错误信息给xweb
		err = result[0].Interface().(error)
		if err != nil {
			cbParams.Success = false
			cbParams.Message = err.Error()
		}
	}()

	return nil
}
