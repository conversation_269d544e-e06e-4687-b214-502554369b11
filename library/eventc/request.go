package eventc

import (
	"fmt"
)

type StageInfo struct {
	StageName string `json:"stage_name"`
	StartTime string `json:"start_time"`
	EndTime   string `json:"end_time"`
	Desc      string `json:"desc"`
}

// EventInfoMsg 发送到下游的数据模型
type EventInfoMsg struct {
	EventId       int64        `json:"event_id" validate:"required"`        //  事件ID
	Name          string       `json:"name"`                                //  名称
	EventType     int          `json:"event_type" validate:"required"`      //  事件类型 ，可枚举。目前存在，上线类型，变更，用户推送，流程单类型
	UpStreamKey   string       `json:"up_stream_Key" validate:"required"`   //  数据源标识
	BusinessKey   string       `json:"business_key"`                        //  业务线
	ProductKey    string       `json:"product_key"`                         //  产品线
	SubSystem     string       `json:"sub_system"`                          //  子系统
	AppName       string       `json:"app_name"`                            //  应用
	StageInfo     []*StageInfo `json:"stage_info" `                         //  事件的阶段信息，描述事件阶段的操作状态
	EventLink     string       `json:"event_link"`                          //  事件详情 url
	EventInfo     string       `json:"event_info"`                          //  事件详情，存储事件源事件信息，json结构，不同事件源存储内容可能不同
	Description   string       `json:"description"`                         //  事件描述 也可以当做标题使用
	StartTime     string       `json:"start_time" validate:"required"`      //  开始时间
	EndTime       string       `json:"end_time" validate:"required"`        //  结束时间
	IsClosed      bool         `json:"is_closed" validate:"required"`       //  事件是否关闭
	Status        string       `json:"status" validate:"required"`          //  状态，表示整个变更操作的状态。举例，cloud流程，有运行中，结束状态
	RelatedPerson string       `json:"related_person"  validate:"required"` //  相关人
	EventCategory string       `json:"event_category"`                      //  事件具体细分，不同事件源不同，无法枚举。可以理解为事件类型为一级分类，此字段为二级分类
}

// 创建流程
func Push(msg *EventInfoMsg) error {

	subUrl := "/collector/event_push"

	var resp Response
	err := post(subUrl, msg, &resp)
	if err != nil {
		return fmt.Errorf("create flow instance error, %s", err.Error())
	}

	// var ret FlowCreateResult
	// err = json.Unmarshal(resp.Data, &ret)
	// if err != nil {
	// 	return nil, err
	// }

	return nil
}
