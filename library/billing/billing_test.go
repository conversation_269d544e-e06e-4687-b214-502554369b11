package billing

import (
	"testing"

	"github.com/jarcoal/httpmock"
	"gitlab.duxiaoman-int.com/siod-open/billing-sdk-golang/billing"

	"redis-cmanager/config"
)

// 无法使用env.Mock 会循环引用
func initTest() {
	// 初始化配置
	config.Init("../../config/config.yaml")

	// 初始化 billing
	var billingConfig Config
	config.Get("billing", &billingConfig)
	Init(&billingConfig)
}

func TestInitFecComboList(t *testing.T) {
	initTest()

	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	tests := []struct {
		name    string
		before  func()
		expect  func(*testing.T, []billing.ComboInfo)
		wantErr bool
	}{
		{
			name: "test",
			before: func() {
				httpmock.RegisterResponder("POST", "http://10.32.140.215:8700/v2/api/billing/budget/getResourceCombos",
					httpmock.NewStringResponder(200, `{"errno":0,"errmsg":"","data":[{"id":37,"packageId":"BBC-K8KT1Pu2","parentResourceId":"r-W89zIydp","name":"均衡类-BBC1","desc":"均衡类-BBC1-2023【CPU:1核 内存:4G 数据盘:50G  】","resourceId":100056,"isAble":2,"creator":"yangpengyi_dxm","createdAt":"2022-12-10T13:59:50+08:00","updatedAt":"2023-12-07T10:41:33+08:00","unitInfo":"[{\"key\":\"cpu\",\"unit\":\"核\",\"value\":\"1\"},{\"key\":\"内存\",\"unit\":\"G\",\"value\":\"4\"},{\"key\":\"磁盘\",\"unit\":\"G\",\"value\":\"50\"}]","priceType":1,"price":68.56,"priceNoTax":0,"code":100056199037,"abilites":0,"dynamicComboId":"","idc":"","cost_price":0,"machineNums":0,"resourceName":"BBC"}]}`))
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			comboList, err := GetFecComboList()
			if (err != nil) != tt.wantErr {
				t.Errorf("GetFecComboList() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, comboList)
			}
		})
	}
}
