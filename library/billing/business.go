package billing

import (
	"errors"
	"fmt"

	"github.com/shopspring/decimal"
	"gitlab.duxiaoman-int.com/siod-open/billing-sdk-golang/billing"
)

// ======================
// 		  Product
// ======================

// 获取所有资源名称
func GetAllProducts() ([]string, error) {
	comboList, r := client.ListAllResource()
	if !r.IsOk() {
		return nil, errors.New(r.ErrMsg)
	}

	result := []string{}
	for _, combo := range comboList {
		result = append(result, combo.Name)
	}

	return result, nil
}

// 校验业务属性字段，各属性加和需要100%
func CheckProducts(products map[string]float64) (*[]billing.ProductParam, error) {
	resources, err := GetAllProducts()
	if err != nil {
		return nil, err
	}

	// slice转map
	productMap := make(map[string]int)
	for _, name := range resources {
		productMap[name] = 1
	}

	pp := []billing.ProductParam{}
	var totalPercent decimal.Decimal
	for name, percent := range products {
		if _, exist := productMap[name]; !exist {
			return nil, fmt.Errorf("product '%s' not vaild", name)
		}

		decimalPercent := decimal.NewFromFloat(percent)
		totalPercent = totalPercent.Add(decimalPercent)
		pp = append(pp, billing.ProductParam{
			Name:    name,
			Percent: percent,
		})
	}

	tp, _ := totalPercent.Float64()
	if tp != 100 {
		return nil, fmt.Errorf("business total percent not 100")
	}
	return &pp, nil
}

// ======================
// 		  Business
// ======================

// 获取所有业务属性
func GetAllBusinesses() ([]string, error) {
	r := client.GetAllBusinesses()
	if !r.IsOk() {
		return nil, errors.New(r.ErrMsg)
	}

	biz, ok := r.Data.([]interface{})
	if !ok {
		return nil, errors.New("cannot convert to []interface{}")
	}

	result := []string{}
	for _, name := range biz {
		result = append(result, name.(string))
	}

	return result, nil
}

// 校验业务属性字段，各属性加和需要100%
func CheckBusinesses(business map[string]float64) (*[]billing.BusinessParam, error) {
	bizs, err := GetAllBusinesses()
	if err != nil {
		return nil, err
	}

	// slice转map
	bizMap := make(map[string]int)
	for _, biz := range bizs {
		bizMap[biz] = 1
	}

	bp := []billing.BusinessParam{}
	var totalPercent decimal.Decimal
	for name, percent := range business {
		if _, exist := bizMap[name]; !exist {
			return nil, fmt.Errorf("business '%s' not vaild", name)
		}

		decimalPercent := decimal.NewFromFloat(percent)
		totalPercent = totalPercent.Add(decimalPercent)
		bp = append(bp, billing.BusinessParam{
			Name:    name,
			Percent: percent,
		})
	}

	tp, _ := totalPercent.Float64()
	if tp != 100 {
		return nil, fmt.Errorf("business total percent not 100")
	}
	return &bp, nil
}
