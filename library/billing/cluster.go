package billing

import (
	"errors"
	"fmt"

	"gitlab.duxiaoman-int.com/siod-open/billing-sdk-golang/billing"

	"dt-common/logger"
)

// ====================================
// 			创建容器集群订单
// ====================================

// 创建订单函数参数
type CreateOrderParams struct {
	ClusterName string             // 集群名，成本中心uniqID
	ProxyNum    int                // proxy实例数量
	ShardNum    float64            // 分片数量(半边进容器后，可能会出现.5个分片账单的情况，所以用float)
	StorageSize float64            // 内存大小,单位GB(半边进容器后，可能会出现.5个GB账单的情况，所以用float)
	SentinelNum float64            // sentinel数量
	DepartCode  int                // 部门代码
	Department  string             // 部门名称
	Creator     string             // 资源归属人
	Business    map[string]float64 // 业务属性
	Product     map[string]float64 // 产品属性
	InMigration bool               // 是否在迁移中
}

// 创建订单
// 因成本中心暂不支持组合套餐，临时方案为一个集群创建4个订单，以集群名为前缀，编号__1到__4
// 当成本中心支持组合套餐后，再改造为标准方案
func CreateClusterOrder(params *CreateOrderParams) error {
	if params == nil {
		return fmt.Errorf("parameter cannot be null")
	}
	if params.Business == nil && params.Product == nil {
		return fmt.Errorf("business and product can not be nil at the same time")
	}
	if params.Business != nil && params.Product != nil {
		return fmt.Errorf("business and product can not be empty at the same time")
	}

	var err error
	// 业务属性与产品属性二选一
	var businessParams *[]billing.BusinessParam
	if params.Business != nil {
		businessParams, err = CheckBusinesses(params.Business)
		if err != nil {
			return err
		}
	}
	// 业务属性与产品属性二选一
	var productParams *[]billing.ProductParam
	if params.Product != nil {
		productParams, err = CheckProducts(params.Product)
		if err != nil {
			return err
		}
	}

	// 组合套餐
	comboGroups := []billing.ComboInfos{
		{ComboCode: proxyComboCode, ComboNum: float64(params.ProxyNum)}, //
		{ComboCode: redisComboCode, ComboNum: params.ShardNum},          //
		{ComboCode: memoryComboCode, ComboNum: params.StorageSize},      //
		{ComboCode: sentinelComboCode, ComboNum: params.SentinelNum},    // 3个sentinel
	}

	// 组装参数
	requestParams := billing.OrderParam{
		DepartCode: params.DepartCode,
		Department: params.Department,
		Creator:    params.Creator,
		ResourceId: resourceID,
		ComboCode:  memoryComboCode,
		Num:        1,
		Desc:       fmt.Sprintf("redis: %s", params.ClusterName),
		SnapInfo: map[string]interface{}{
			"num": params.StorageSize,
		},
		InstanceList: []billing.OrderInstanceParam{
			{UniqId: params.ClusterName},
		},
		DynamicComboGroup: comboGroups,
	}
	if businessParams != nil {
		requestParams.Business = *businessParams
	}
	if productParams != nil {
		requestParams.Product = *productParams
	}
	// 创建订单
	if r := client.CreateOrder(requestParams); !r.IsOk() {
		return errors.New(r.ErrMsg)
	}

	return nil
}

// ====================================
//        更新订单：转让/扩缩容
// ====================================

// 获取实例详情
func GetInstanceInfos(uniqIDs []string) (*[]billing.InstanceInfo, error) {
	instanceList, r := client.GetInstanceInfos(resourceID, uniqIDs, 1)
	if !r.IsOk() {
		return nil, errors.New(r.ErrMsg)
	}

	return &instanceList, nil
}

// 实例转让
func Transfer(clusterName string, receiver string, departID int, departName string) error {
	// 获取原订单
	orders, err := GetInstanceInfos([]string{clusterName})
	if err != nil {
		return err
	}
	if len(*orders) != 1 {
		return fmt.Errorf("invalid billing order num of cluster %s, num=%d", clusterName, len(*orders))
	}

	// 不重复修改
	curOrder := (*orders)[0]
	if curOrder.Owner == receiver {
		return nil
	}

	// 转换类型
	business := make([]billing.BusinessParam, len(curOrder.Business))
	for i, biz := range curOrder.Business {
		business[i] = billing.BusinessParam(biz)
	}
	product := make([]billing.ProductParam, len(curOrder.Product))
	for i, pro := range curOrder.Product {
		product[i] = billing.ProductParam(pro)
	}

	//初始化参数
	r := client.ModifyOrder(billing.ModifyOrderParam{
		DepartCode: departID,
		Department: departName,
		Creator:    receiver,
		ResourceId: resourceID,
		ComboCode:  curOrder.ComboCode,
		Num:        1,
		Desc:       curOrder.Desc,
		Business:   business,
		Product:    product,
		SnapInfo:   curOrder.SnapInfo,
		InstanceList: []billing.OrderInstanceParam{
			{UniqId: curOrder.InstanceId},
		},
		DynamicComboGroup: curOrder.DynamicComboGroup,
	})
	if !r.IsOk() {
		logger.Warn("failed to transfer order, order id=%s, error=(%v)", curOrder.InstanceId, r.ErrMsg)
		return errors.New(r.ErrMsg)
	}

	return nil
}

// 套餐变更: proxy扩缩容
// 针对容器实例
func ProxyNumChange(clusterName string, proxyNum int) error {
	// 获取原订单
	orders, err := GetInstanceInfos([]string{clusterName})
	if err != nil {
		return err
	}
	if len(*orders) != 1 {
		return fmt.Errorf("invalid billing order num of cluster %s, num=%d", clusterName, len(*orders))
	}

	// 不重复修改
	curOrder := (*orders)[0]
	changed := false
	for i, combo := range curOrder.DynamicComboGroup {
		if combo.ComboCode != proxyComboCode {
			continue
		}

		if combo.ComboNum == float64(proxyNum) {
			logger.Info("order of cluster %s proxy num has already changed to %d", clusterName, proxyNum)
			return nil
		}

		changed = true
		curOrder.DynamicComboGroup[i].ComboNum = float64(proxyNum)
	}
	if !changed {
		return fmt.Errorf("missing dynamic combo of proxy, cluster=%s", clusterName)
	}

	// 转换类型
	business := make([]billing.BusinessParam, len(curOrder.Business))
	for i, biz := range curOrder.Business {
		business[i] = billing.BusinessParam(biz)
	}
	product := make([]billing.ProductParam, len(curOrder.Product))
	for i, pro := range curOrder.Product {
		product[i] = billing.ProductParam(pro)
	}

	//初始化参数
	params := billing.ModifyOrderParam{
		DepartCode: curOrder.DepartCode,
		Department: curOrder.Department,
		Creator:    curOrder.Owner,
		ResourceId: resourceID,
		ComboCode:  curOrder.ComboCode,
		Num:        1,
		Desc:       curOrder.Desc,
		Business:   business,
		Product:    product,
		SnapInfo:   curOrder.SnapInfo,
		InstanceList: []billing.OrderInstanceParam{
			{UniqId: curOrder.InstanceId},
		},
		DynamicComboGroup: curOrder.DynamicComboGroup,
	}
	r := client.ModifyOrder(params)
	if !r.IsOk() {
		logger.Warn("failed to update dynamic combo proxy num, cluster=%s, error=(%v)", clusterName, r.ErrMsg)
		return errors.New(r.ErrMsg)
	}

	return nil
}

// 套餐变更: 内存扩缩容
func MemorySizeChange(clusterName string, storageSize int) error {
	// 获取原订单
	orders, err := GetInstanceInfos([]string{clusterName})
	if err != nil {
		return err
	}
	if len(*orders) != 1 {
		return fmt.Errorf("invalid billing order num of cluster %s, num=%d", clusterName, len(*orders))
	}

	// 不重复修改
	curOrder := (*orders)[0]

	comboNum := float64(storageSize)
	// 组合套餐列表不为0，为物理订单
	if len(curOrder.DynamicComboGroup) != 0 {
		changed := false
		for i, combo := range curOrder.DynamicComboGroup {
			if combo.ComboCode != memoryComboCode {
				continue
			}

			if combo.ComboNum == float64(storageSize) {
				logger.Info("order of cluster %s memory size has already changed to %d", clusterName, storageSize)
				return nil
			}

			changed = true
			curOrder.DynamicComboGroup[i].ComboNum = float64(storageSize)
		}
		if !changed {
			return fmt.Errorf("missing dynamic combo of memory, cluster=%s", clusterName)
		}
	} else {
		comboNum = float64(storageSize) / 4
		if curOrder.SnapInfo["num"] == comboNum {
			return nil
		}
	}

	// 转换类型
	business := make([]billing.BusinessParam, len(curOrder.Business))
	for i, biz := range curOrder.Business {
		business[i] = billing.BusinessParam(biz)
	}
	product := make([]billing.ProductParam, len(curOrder.Product))
	for i, pro := range curOrder.Product {
		product[i] = billing.ProductParam(pro)
	}

	// 更新订单
	r := client.ModifyOrder(billing.ModifyOrderParam{
		DepartCode: curOrder.DepartCode,
		Department: curOrder.Department,
		Creator:    curOrder.Owner,
		ResourceId: resourceID,
		ComboCode:  curOrder.ComboCode,
		Num:        1,
		Desc:       curOrder.Desc,
		Business:   business,
		Product:    product,
		SnapInfo: map[string]interface{}{
			"num": comboNum,
		},
		InstanceList: []billing.OrderInstanceParam{
			{UniqId: curOrder.InstanceId},
		},
		DynamicComboGroup: curOrder.DynamicComboGroup,
	})
	if !r.IsOk() {
		logger.Warn("failed to update order of a redis memory, cluster=%s, error=(%v)", clusterName, r.ErrMsg)
		return errors.New(r.ErrMsg)
	}

	return nil
}

// ====================================
// 			转化容器集群订单
// ====================================

// 转换订单，从物理订单的基础上生成容器订单
// 必填参数：clusterName, proxyNum, shardNum, storageSize
func DockerTransform(params *CreateOrderParams) error {
	if params == nil {
		return errors.New("parameter cannot be null")
	}

	// 查询原订单
	orders, err := GetInstanceInfos([]string{params.ClusterName})
	if err != nil {
		return err
	}
	if len(*orders) != 1 {
		return fmt.Errorf("invalid billing order num of cluster %s, num=%d", params.ClusterName, len(*orders))
	}

	curOrder := (*orders)[0]

	// 转换类型
	businessParams := make([]billing.BusinessParam, len(curOrder.Business))
	for i, biz := range curOrder.Business {
		businessParams[i] = billing.BusinessParam(biz)
	}
	productParams := make([]billing.ProductParam, len(curOrder.Product))
	for i, pro := range curOrder.Product {
		productParams[i] = billing.ProductParam(pro)
	}

	// 组合套餐
	comboGroups := []billing.ComboInfos{
		{ComboCode: proxyComboCode, ComboNum: float64(params.ProxyNum)},
		{ComboCode: redisComboCode, ComboNum: params.ShardNum},
		{ComboCode: memoryComboCode, ComboNum: params.StorageSize},
		{ComboCode: sentinelComboCode, ComboNum: params.SentinelNum},
	}
	// 迁移中的账单需要加一半旧套餐进组
	if params.InMigration {
		comboGroups = append(comboGroups, billing.ComboInfos{
			ComboCode: OldComboCode,
			ComboNum:  curOrder.SnapInfo["num"].(float64) / 2,
		})
	}

	//初始化参数
	r := client.ModifyOrder(billing.ModifyOrderParam{
		DepartCode: curOrder.DepartCode,
		Department: curOrder.Department,
		Creator:    curOrder.Owner,
		ResourceId: curOrder.ResourceId,
		ComboCode:  memoryComboCode,
		Num:        1,
		Desc:       fmt.Sprintf("容器redis: %s", params.ClusterName),
		Business:   businessParams,
		Product:    productParams,
		SnapInfo: map[string]interface{}{
			"num": params.StorageSize,
		},
		InstanceList: []billing.OrderInstanceParam{
			{UniqId: params.ClusterName},
		},
		DynamicComboGroup: comboGroups,
	})
	if !r.IsOk() {
		return errors.New(r.ErrMsg)
	}

	return nil
}
