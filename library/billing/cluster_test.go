package billing

import (
	"regexp"
	"testing"

	"github.com/jarcoal/httpmock"
)

func TestCreateClusterOrder(t *testing.T) {
	initTest()
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		params *CreateOrderParams
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name:    "test1: check params",
			args:    args{},
			wantErr: true,
		},
		{
			name: "test2: check params",
			args: args{
				params: &CreateOrderParams{
					ClusterName: "create_test",
					ProxyNum:    4,
					ShardNum:    4,
					StorageSize: 20,
					DepartCode:  1000000037,
					Department:  "系统运维部",
					Creator:     "jiayiming_dxm",
				},
			},
			wantErr: true,
		},
		{
			name: "test3: business failed",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/api/billing/business/list`),
					httpmock.ResponderFromResponse(httpmock.NewStringResponse(200, "{\"errno\":0,\"errmsg\":\"success\",\"data\":[\"信贷\",\"金科\",\"理财\",\"支付\",\"保险\",\"供应链金融\",\"RPA\",\"GAI\"]}")))
			},
			args: args{
				params: &CreateOrderParams{
					ClusterName: "create_test",
					ProxyNum:    4,
					ShardNum:    4,
					StorageSize: 20,
					DepartCode:  1000000037,
					Department:  "系统运维部",
					Creator:     "jiayiming_dxm",
					Business: map[string]float64{
						"理财": 90,
					},
				},
			},
			wantErr: true,
		},
		{
			name: "test4: product failed",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/v2/api/billing/resource/list`),
					httpmock.ResponderFromResponse(httpmock.NewStringResponse(200, "{\"errno\":0,\"errmsg\":\"success\",\"data\":[{\"id\":100065,\"resourceId\":\"r-y1HRcES8\",\"name\":\"BCC\",\"nameCh\":\"虚拟云服务器\",\"desc\":\"线上虚拟机\",\"isAble\":2,\"creator\":\"jiafusong_dxm\",\"createdAt\":\"2021-08-19T13:15:19+08:00\",\"updatedAt\":\"2024-01-30T20:45:47+08:00\",\"approveRoleId\":57,\"approveRole\":\"bcc_admin\",\"unitFormat\":\"[{\"key\":\"CPU\",\"unit\":\"核\"},{\"key\":\"内存\",\"unit\":\"G\"},{\"key\":\"磁盘\",\"unit\":\"G\"}]\",\"ak\":\"\",\"sk\":\"\",\"rd\":\"\",\"op\":\"\",\"isInBilling\":1,\"billingMode\":4,\"code\":100065,\"isCloudBase\":0,\"region\":0,\"pid\":0,\"isProduct\":1,\"manager\":\"chenyu02_dxm\"}]}")))
			},
			args: args{
				params: &CreateOrderParams{
					ClusterName: "create_test",
					ProxyNum:    4,
					ShardNum:    4,
					StorageSize: 20,
					DepartCode:  1000000037,
					Department:  "系统运维部",
					Creator:     "jiayiming_dxm",
					Product: map[string]float64{
						"BFE": 90,
					},
				},
			},
			wantErr: true,
		},
		{
			name: "test5: request failed",
			args: args{
				params: &CreateOrderParams{
					ClusterName: "create_test",
					ProxyNum:    4,
					ShardNum:    4,
					StorageSize: 20,
					DepartCode:  1000000037,
					Department:  "系统运维部",
					Creator:     "jiayiming_dxm",
					Business: map[string]float64{
						"信贷": 100,
					},
				},
			},
			wantErr: true,
		},
		{
			name: "test6: success",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/api/billing/order/create`),
					httpmock.ResponderFromResponse(httpmock.NewStringResponse(200, "{\"errno\":0,\"errmsg\":\"success\",\"data\":{}}")))
			},
			args: args{
				params: &CreateOrderParams{
					ClusterName: "redis_dynamic_test2",
					ProxyNum:    4,
					ShardNum:    4,
					StorageSize: 20,
					DepartCode:  1000000037,
					Department:  "系统运维部",
					Creator:     "jiayiming_dxm",
					Business: map[string]float64{
						"信贷": 100,
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := CreateClusterOrder(tt.args.params)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateClusterOrder() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

// ====================================
//        更新订单：转让/扩缩容
// ====================================

func TestGetInstanceInfos(t *testing.T) {
	initTest()
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		uniqIDs []string
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test1: get instance failed",
			args: args{
				uniqIDs: []string{"return_test"},
			},
			wantErr: true,
		},
		{
			name: "test2: success",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/api/billing/order/info`),
					httpmock.ResponderFromResponse(httpmock.NewStringResponse(200, "{\"errno\":0,\"errmsg\":\"success\",\"data\":[]}")))
			},
			args: args{
				uniqIDs: []string{"r3_rd_test"},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			_, err := GetInstanceInfos(tt.args.uniqIDs)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetInstanceInfos() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

func TestTransfer(t *testing.T) {
	initTest()

	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		clusterName string
		receiver    string
		departID    int
		departName  string
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test1: request failed",
			args: args{
				clusterName: "transfer_test",
			},
			wantErr: true,
		},
		{
			name: "test2: order len = 0",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/api/billing/order/info`),
					httpmock.ResponderFromResponse(httpmock.NewStringResponse(200, "{\"errno\":0,\"errmsg\":\"success\",\"data\":[]}")))
			},
			args: args{
				clusterName: "transfer_test",
			},
			wantErr: true,
		},
		{
			name: "test: already transferd",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/api/billing/order/info`),
					httpmock.ResponderFromResponse(httpmock.NewStringResponse(200, "{\"errno\":0,\"errmsg\":\"success\",\"data\":[{\"resourceName\":\"REDIS\",\"resourceNameCh\":\"缓存数据库\",\"resourceId\":100049,\"resourceType\":\"\",\"comboName\":\"redis-科技云-单分片\",\"comboId\":21,\"comboCode\":100049985621,\"comboDesc\":\"\",\"comboUnitInfo\":\"\",\"unitPrice\":0,\"departCode\":1000000037,\"department\":\"系统运维部\",\"team\":\"\",\"useType\":0,\"business\":[{\"name\":\"理财\",\"percent\":100}],\"product\":[],\"isAckPercentMonthly\":false,\"percentDesc\":\"\",\"isUseAuthPercent\":false,\"authPercentName\":\"\",\"authPercentCode\":\"\",\"busReview\":null,\"tag\":null,\"cloudName\":\"\",\"cloudLabel\":\"dev\",\"desc\":\"redis: billing_desc_test\",\"RDOwner\":\"\",\"OPOwner\":\"\",\"zoneType\":\"\",\"idc\":\"\",\"useTypeName\":\"普通服务\",\"useForProduct\":\"\",\"staticQuantity\":null,\"id\":\"65f94c5bff1b3ecb5fcb7d0e\",\"instanceId\":\"billing_desc_test\",\"snapInfo\":{\"num\":1},\"costId\":\"\",\"monthPrice\":230,\"orderCode\":\"1000492168276591\",\"owner\":\"jiayiming_dxm\",\"startTime\":\"2024-03-19T16:27:07.653+08:00\",\"endTime\":\"0001-01-01T08:05:43+08:05\",\"createdAt\":\"2024-03-19T16:27:07.684+08:00\",\"updateAt\":\"2024-03-19T16:27:07.684+08:00\",\"status\":1,\"parentCode\":\"\",\"busPercentStatus\":0,\"instanceName\":\"\",\"shortId\":\"redis-57im2gwy4ab\"}]}")))
			},
			args: args{
				clusterName: "transfer_test",
				receiver:    "jiayiming_dxm",
			},
			wantErr: false,
		},
		{
			name: "test3: request failed",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/api/billing/order/info`),
					httpmock.ResponderFromResponse(httpmock.NewStringResponse(200, "{\"errno\":0,\"errmsg\":\"success\",\"data\":[{\"resourceName\":\"REDIS\",\"resourceNameCh\":\"缓存数据库\",\"resourceId\":100049,\"resourceType\":\"\",\"comboName\":\"redis-科技云-单分片\",\"comboId\":21,\"comboCode\":100049985621,\"comboDesc\":\"\",\"comboUnitInfo\":\"\",\"unitPrice\":0,\"departCode\":1000000037,\"department\":\"系统运维部\",\"team\":\"\",\"useType\":0,\"business\":[{\"name\":\"理财\",\"percent\":100}],\"product\":[],\"isAckPercentMonthly\":false,\"percentDesc\":\"\",\"isUseAuthPercent\":false,\"authPercentName\":\"\",\"authPercentCode\":\"\",\"busReview\":null,\"tag\":null,\"cloudName\":\"\",\"cloudLabel\":\"dev\",\"desc\":\"redis: billing_desc_test\",\"RDOwner\":\"\",\"OPOwner\":\"\",\"zoneType\":\"\",\"idc\":\"\",\"useTypeName\":\"普通服务\",\"useForProduct\":\"\",\"staticQuantity\":null,\"id\":\"65f94c5bff1b3ecb5fcb7d0e\",\"instanceId\":\"billing_desc_test\",\"snapInfo\":{\"num\":1},\"costId\":\"\",\"monthPrice\":230,\"orderCode\":\"1000492168276591\",\"owner\":\"jiayiming_dxm\",\"startTime\":\"2024-03-19T16:27:07.653+08:00\",\"endTime\":\"0001-01-01T08:05:43+08:05\",\"createdAt\":\"2024-03-19T16:27:07.684+08:00\",\"updateAt\":\"2024-03-19T16:27:07.684+08:00\",\"status\":1,\"parentCode\":\"\",\"busPercentStatus\":0,\"instanceName\":\"\",\"shortId\":\"redis-57im2gwy4ab\"}]}")))
			},
			args: args{
				clusterName: "transfer_test",
				receiver:    "huzhaoyun_dxm",
				departID:    1000000037,
				departName:  "系统运维部",
			},
			wantErr: true,
		},
		{
			name: "test5: success",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/api/billing/order/info`),
					httpmock.ResponderFromResponse(httpmock.NewStringResponse(200, "{\"errno\":0,\"errmsg\":\"success\",\"data\":[{\"resourceName\":\"REDIS\",\"resourceNameCh\":\"缓存数据库\",\"resourceId\":100049,\"resourceType\":\"\",\"comboName\":\"redis-科技云-单分片\",\"comboId\":21,\"comboCode\":100049985621,\"comboDesc\":\"\",\"comboUnitInfo\":\"\",\"unitPrice\":0,\"departCode\":1000000037,\"department\":\"系统运维部\",\"team\":\"\",\"useType\":0,\"business\":[{\"name\":\"理财\",\"percent\":100}],\"product\":[],\"isAckPercentMonthly\":false,\"percentDesc\":\"\",\"isUseAuthPercent\":false,\"authPercentName\":\"\",\"authPercentCode\":\"\",\"busReview\":null,\"tag\":null,\"cloudName\":\"\",\"cloudLabel\":\"dev\",\"desc\":\"redis: billing_desc_test\",\"RDOwner\":\"\",\"OPOwner\":\"\",\"zoneType\":\"\",\"idc\":\"\",\"useTypeName\":\"普通服务\",\"useForProduct\":\"\",\"staticQuantity\":null,\"id\":\"65f94c5bff1b3ecb5fcb7d0e\",\"instanceId\":\"billing_desc_test\",\"snapInfo\":{\"num\":1},\"costId\":\"\",\"monthPrice\":230,\"orderCode\":\"1000492168276591\",\"owner\":\"jiayiming_dxm\",\"startTime\":\"2024-03-19T16:27:07.653+08:00\",\"endTime\":\"0001-01-01T08:05:43+08:05\",\"createdAt\":\"2024-03-19T16:27:07.684+08:00\",\"updateAt\":\"2024-03-19T16:27:07.684+08:00\",\"status\":1,\"parentCode\":\"\",\"busPercentStatus\":0,\"instanceName\":\"\",\"shortId\":\"redis-57im2gwy4ab\"}]}")))

				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/api/billing/order/modify`),
					httpmock.ResponderFromResponse(httpmock.NewStringResponse(200, "{\"errno\":0,\"errmsg\":\"success\",\"data\":{}}")))
			},
			args: args{
				clusterName: "redis_dynamic_trans",
				receiver:    "huzhaoyun_dxm",
				departID:    1000000037,
				departName:  "系统运维部",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := Transfer(tt.args.clusterName, tt.args.receiver, tt.args.departID, tt.args.departName)
			if (err != nil) != tt.wantErr {
				t.Errorf("Transfer() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

func TestProxyNumChange(t *testing.T) {
	initTest()
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		clusterName string
		proxyNum    int
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(t *testing.T)
	}{
		{
			name: "test1: get instance failed",
			args: args{
				clusterName: "modify_test",
				proxyNum:    4,
			},
			wantErr: true,
		},
		{
			name: "test2: order len != 1",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/api/billing/order/info`),
					httpmock.ResponderFromResponse(httpmock.NewStringResponse(200, "{\"errno\":0,\"errmsg\":\"success\",\"data\":[]}")))
			},
			args: args{
				clusterName: "modify_test",
				proxyNum:    4,
			},
			wantErr: true,
		},
		{
			name: "test3: same combo num",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/api/billing/order/info`),
					httpmock.ResponderFromResponse(httpmock.NewStringResponse(200, "{\"errno\":0,\"errmsg\":\"success\",\"data\":[{\"resourceName\":\"REDIS\",\"resourceNameCh\":\"缓存数据库\",\"resourceId\":100049,\"resourceType\":\"\",\"comboName\":\"redis-科技云-单分片\",\"comboId\":21,\"comboCode\":100049985621,\"comboDesc\":\"\",\"comboUnitInfo\":\"\",\"unitPrice\":0,\"departCode\":1000000037,\"department\":\"系统运维部\",\"team\":\"\",\"useType\":0,\"business\":[{\"name\":\"理财\",\"percent\":100}],\"product\":[],\"isAckPercentMonthly\":false,\"percentDesc\":\"\",\"isUseAuthPercent\":false,\"authPercentName\":\"\",\"authPercentCode\":\"\",\"busReview\":null,\"tag\":null,\"cloudName\":\"\",\"cloudLabel\":\"dev\",\"desc\":\"redis: billing_desc_test\",\"RDOwner\":\"\",\"OPOwner\":\"\",\"zoneType\":\"\",\"idc\":\"\",\"useTypeName\":\"普通服务\",\"useForProduct\":\"\",\"staticQuantity\":null,\"id\":\"65f94c5bff1b3ecb5fcb7d0e\",\"instanceId\":\"billing_desc_test\",\"snapInfo\":{\"num\":1},\"costId\":\"\",\"monthPrice\":230,\"orderCode\":\"1000492168276591\",\"owner\":\"jiayiming_dxm\",\"startTime\":\"2024-03-19T16:27:07.653+08:00\",\"endTime\":\"0001-01-01T08:05:43+08:05\",\"createdAt\":\"2024-03-19T16:27:07.684+08:00\",\"updateAt\":\"2024-03-19T16:27:07.684+08:00\",\"dynamicComboGroup\": [{\"comboName\": \"\",\"comboId\":0,\"comboCode\":491488382,\"comboDesc\":\"\",\"comboUnitInfo\":\"\",\"unitPrice\":0,\"comboNum\":2},{\"comboName\": \"\",\"comboId\":0,\"comboCode\":491488383,\"comboDesc\":\"\",\"comboUnitInfo\":\"\",\"unitPrice\":0,\"comboNum\":2},{\"comboName\": \"\",\"comboId\":0,\"comboCode\":491488384,\"comboDesc\":\"\",\"comboUnitInfo\":\"\",\"unitPrice\":0,\"comboNum\":10},{\"comboName\": \"\",\"comboId\":0,\"comboCode\":491488385,\"comboDesc\":\"\",\"comboUnitInfo\":\"\",\"unitPrice\":0,\"comboNum\":1}],\"status\":1,\"parentCode\":\"\",\"busPercentStatus\":0,\"instanceName\":\"\",\"shortId\":\"redis-57im2gwy4ab\"}]}")))
			},
			args: args{
				clusterName: "modify_test",
				proxyNum:    2,
			},
			wantErr: false,
		},
		{
			name: "test: no dynamicComboGroup",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/api/billing/order/info`),
					httpmock.ResponderFromResponse(httpmock.NewStringResponse(200, "{\"errno\":0,\"errmsg\":\"success\",\"data\":[{\"resourceName\":\"REDIS\",\"resourceNameCh\":\"缓存数据库\",\"resourceId\":100049,\"resourceType\":\"\",\"comboName\":\"redis-科技云-单分片\",\"comboId\":21,\"comboCode\":100049985621,\"comboDesc\":\"\",\"comboUnitInfo\":\"\",\"unitPrice\":0,\"departCode\":1000000037,\"department\":\"系统运维部\",\"team\":\"\",\"useType\":0,\"business\":[{\"name\":\"理财\",\"percent\":100}],\"product\":[],\"isAckPercentMonthly\":false,\"percentDesc\":\"\",\"isUseAuthPercent\":false,\"authPercentName\":\"\",\"authPercentCode\":\"\",\"busReview\":null,\"tag\":null,\"cloudName\":\"\",\"cloudLabel\":\"dev\",\"desc\":\"redis: billing_desc_test\",\"RDOwner\":\"\",\"OPOwner\":\"\",\"zoneType\":\"\",\"idc\":\"\",\"useTypeName\":\"普通服务\",\"useForProduct\":\"\",\"staticQuantity\":null,\"id\":\"65f94c5bff1b3ecb5fcb7d0e\",\"instanceId\":\"billing_desc_test\",\"snapInfo\":{\"num\":1},\"costId\":\"\",\"monthPrice\":230,\"orderCode\":\"1000492168276591\",\"owner\":\"jiayiming_dxm\",\"startTime\":\"2024-03-19T16:27:07.653+08:00\",\"endTime\":\"0001-01-01T08:05:43+08:05\",\"createdAt\":\"2024-03-19T16:27:07.684+08:00\",\"updateAt\":\"2024-03-19T16:27:07.684+08:00\",\"dynamicComboGroup\": [],\"status\":1,\"parentCode\":\"\",\"busPercentStatus\":0,\"instanceName\":\"\",\"shortId\":\"redis-57im2gwy4ab\"}]}")))
			},
			args: args{
				clusterName: "modify_test",
				proxyNum:    2,
			},
			wantErr: true,
		},
		{
			name: "test: modify failed",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/api/billing/order/info`),
					httpmock.ResponderFromResponse(httpmock.NewStringResponse(200, "{\"errno\":0,\"errmsg\":\"success\",\"data\":[{\"resourceName\":\"REDIS\",\"resourceNameCh\":\"缓存数据库\",\"resourceId\":100049,\"resourceType\":\"\",\"comboName\":\"redis-科技云-单分片\",\"comboId\":21,\"comboCode\":100049985621,\"comboDesc\":\"\",\"comboUnitInfo\":\"\",\"unitPrice\":0,\"departCode\":1000000037,\"department\":\"系统运维部\",\"team\":\"\",\"useType\":0,\"business\":[{\"name\":\"理财\",\"percent\":100}],\"product\":[],\"isAckPercentMonthly\":false,\"percentDesc\":\"\",\"isUseAuthPercent\":false,\"authPercentName\":\"\",\"authPercentCode\":\"\",\"busReview\":null,\"tag\":null,\"cloudName\":\"\",\"cloudLabel\":\"dev\",\"desc\":\"redis: billing_desc_test\",\"RDOwner\":\"\",\"OPOwner\":\"\",\"zoneType\":\"\",\"idc\":\"\",\"useTypeName\":\"普通服务\",\"useForProduct\":\"\",\"staticQuantity\":null,\"id\":\"65f94c5bff1b3ecb5fcb7d0e\",\"instanceId\":\"billing_desc_test\",\"snapInfo\":{\"num\":1},\"costId\":\"\",\"monthPrice\":230,\"orderCode\":\"1000492168276591\",\"owner\":\"jiayiming_dxm\",\"startTime\":\"2024-03-19T16:27:07.653+08:00\",\"endTime\":\"0001-01-01T08:05:43+08:05\",\"createdAt\":\"2024-03-19T16:27:07.684+08:00\",\"updateAt\":\"2024-03-19T16:27:07.684+08:00\",\"dynamicComboGroup\": [{\"comboName\": \"\",\"comboId\":0,\"comboCode\":491488382,\"comboDesc\":\"\",\"comboUnitInfo\":\"\",\"unitPrice\":0,\"comboNum\":2},{\"comboName\": \"\",\"comboId\":0,\"comboCode\":491488383,\"comboDesc\":\"\",\"comboUnitInfo\":\"\",\"unitPrice\":0,\"comboNum\":2},{\"comboName\": \"\",\"comboId\":0,\"comboCode\":491488384,\"comboDesc\":\"\",\"comboUnitInfo\":\"\",\"unitPrice\":0,\"comboNum\":10},{\"comboName\": \"\",\"comboId\":0,\"comboCode\":491488385,\"comboDesc\":\"\",\"comboUnitInfo\":\"\",\"unitPrice\":0,\"comboNum\":1}],\"status\":1,\"parentCode\":\"\",\"busPercentStatus\":0,\"instanceName\":\"\",\"shortId\":\"redis-57im2gwy4ab\"}]}")))
			},
			args: args{
				clusterName: "modify_test",
				proxyNum:    4,
			},
			wantErr: true,
		},
		{
			name: "test5: success",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/api/billing/order/modify`),
					httpmock.ResponderFromResponse(httpmock.NewStringResponse(200, "{\"errno\":0,\"errmsg\":\"success\",\"data\":{}}")))
			},
			args: args{
				clusterName: "modify_test",
				proxyNum:    4,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := ProxyNumChange(tt.args.clusterName, tt.args.proxyNum)
			if (err != nil) != tt.wantErr {
				t.Errorf("ProxyNumChange() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

func TestMemorySizeChange(t *testing.T) {
	initTest()
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		clusterName string
		storageSize int
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(t *testing.T)
	}{
		{
			name: "test1: get instance failed",
			args: args{
				clusterName: "modify_test",
				storageSize: 20,
			},
			wantErr: true,
		},
		{
			name: "test2: order len != 1",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/api/billing/order/info`),
					httpmock.ResponderFromResponse(httpmock.NewStringResponse(200, "{\"errno\":0,\"errmsg\":\"success\",\"data\":[]}")))
			},
			args: args{
				clusterName: "modify_test",
				storageSize: 20,
			},
			wantErr: true,
		},
		{
			name: "test3: same combo num docker",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/api/billing/order/info`),
					httpmock.ResponderFromResponse(httpmock.NewStringResponse(200, "{\"errno\":0,\"errmsg\":\"success\",\"data\":[{\"resourceName\":\"REDIS\",\"resourceNameCh\":\"缓存数据库\",\"resourceId\":100049,\"resourceType\":\"\",\"comboName\":\"redis-科技云-单分片\",\"comboId\":21,\"comboCode\":100049985621,\"comboDesc\":\"\",\"comboUnitInfo\":\"\",\"unitPrice\":0,\"departCode\":1000000037,\"department\":\"系统运维部\",\"team\":\"\",\"useType\":0,\"business\":[{\"name\":\"理财\",\"percent\":100}],\"product\":[],\"isAckPercentMonthly\":false,\"percentDesc\":\"\",\"isUseAuthPercent\":false,\"authPercentName\":\"\",\"authPercentCode\":\"\",\"busReview\":null,\"tag\":null,\"cloudName\":\"\",\"cloudLabel\":\"dev\",\"desc\":\"redis: billing_desc_test\",\"RDOwner\":\"\",\"OPOwner\":\"\",\"zoneType\":\"\",\"idc\":\"\",\"useTypeName\":\"普通服务\",\"useForProduct\":\"\",\"staticQuantity\":null,\"id\":\"65f94c5bff1b3ecb5fcb7d0e\",\"instanceId\":\"billing_desc_test\",\"snapInfo\":{\"num\":1},\"costId\":\"\",\"monthPrice\":230,\"orderCode\":\"1000492168276591\",\"owner\":\"jiayiming_dxm\",\"startTime\":\"2024-03-19T16:27:07.653+08:00\",\"endTime\":\"0001-01-01T08:05:43+08:05\",\"createdAt\":\"2024-03-19T16:27:07.684+08:00\",\"updateAt\":\"2024-03-19T16:27:07.684+08:00\",\"dynamicComboGroup\": [{\"comboName\": \"\",\"comboId\":0,\"comboCode\":491488382,\"comboDesc\":\"\",\"comboUnitInfo\":\"\",\"unitPrice\":0,\"comboNum\":2},{\"comboName\": \"\",\"comboId\":0,\"comboCode\":491488383,\"comboDesc\":\"\",\"comboUnitInfo\":\"\",\"unitPrice\":0,\"comboNum\":2},{\"comboName\": \"\",\"comboId\":0,\"comboCode\":491488384,\"comboDesc\":\"\",\"comboUnitInfo\":\"\",\"unitPrice\":0,\"comboNum\":10},{\"comboName\": \"\",\"comboId\":0,\"comboCode\":491488385,\"comboDesc\":\"\",\"comboUnitInfo\":\"\",\"unitPrice\":0,\"comboNum\":1}],\"status\":1,\"parentCode\":\"\",\"busPercentStatus\":0,\"instanceName\":\"\",\"shortId\":\"redis-57im2gwy4ab\"}]}")))
			},
			args: args{
				clusterName: "modify_test",
				storageSize: 10,
			},
			wantErr: false,
		},
		{
			name: "test: no memory combo in group",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/api/billing/order/info`),
					httpmock.ResponderFromResponse(httpmock.NewStringResponse(200, "{\"errno\":0,\"errmsg\":\"success\",\"data\":[{\"resourceName\":\"REDIS\",\"resourceNameCh\":\"缓存数据库\",\"resourceId\":100049,\"resourceType\":\"\",\"comboName\":\"redis-科技云-单分片\",\"comboId\":21,\"comboCode\":100049985621,\"comboDesc\":\"\",\"comboUnitInfo\":\"\",\"unitPrice\":0,\"departCode\":1000000037,\"department\":\"系统运维部\",\"team\":\"\",\"useType\":0,\"business\":[{\"name\":\"理财\",\"percent\":100}],\"product\":[],\"isAckPercentMonthly\":false,\"percentDesc\":\"\",\"isUseAuthPercent\":false,\"authPercentName\":\"\",\"authPercentCode\":\"\",\"busReview\":null,\"tag\":null,\"cloudName\":\"\",\"cloudLabel\":\"dev\",\"desc\":\"redis: billing_desc_test\",\"RDOwner\":\"\",\"OPOwner\":\"\",\"zoneType\":\"\",\"idc\":\"\",\"useTypeName\":\"普通服务\",\"useForProduct\":\"\",\"staticQuantity\":null,\"id\":\"65f94c5bff1b3ecb5fcb7d0e\",\"instanceId\":\"billing_desc_test\",\"snapInfo\":{\"num\":1},\"costId\":\"\",\"monthPrice\":230,\"orderCode\":\"1000492168276591\",\"owner\":\"jiayiming_dxm\",\"startTime\":\"2024-03-19T16:27:07.653+08:00\",\"endTime\":\"0001-01-01T08:05:43+08:05\",\"createdAt\":\"2024-03-19T16:27:07.684+08:00\",\"updateAt\":\"2024-03-19T16:27:07.684+08:00\",\"dynamicComboGroup\": [{\"comboName\": \"\",\"comboId\":0,\"comboCode\":491488382,\"comboDesc\":\"\",\"comboUnitInfo\":\"\",\"unitPrice\":0,\"comboNum\":2}],\"status\":1,\"parentCode\":\"\",\"busPercentStatus\":0,\"instanceName\":\"\",\"shortId\":\"redis-57im2gwy4ab\"}]}")))
			},
			args: args{
				clusterName: "modify_test",
				storageSize: 20,
			},
			wantErr: true,
		},
		{
			name: "test3: same combo num bbc",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/api/billing/order/info`),
					httpmock.ResponderFromResponse(httpmock.NewStringResponse(200, "{\"errno\":0,\"errmsg\":\"success\",\"data\":[{\"resourceName\":\"REDIS\",\"resourceNameCh\":\"缓存数据库\",\"resourceId\":100049,\"resourceType\":\"\",\"comboName\":\"redis-科技云-单分片\",\"comboId\":21,\"comboCode\":100049985621,\"comboDesc\":\"\",\"comboUnitInfo\":\"\",\"unitPrice\":0,\"departCode\":1000000037,\"department\":\"系统运维部\",\"team\":\"\",\"useType\":0,\"business\":[{\"name\":\"理财\",\"percent\":100}],\"product\":[],\"isAckPercentMonthly\":false,\"percentDesc\":\"\",\"isUseAuthPercent\":false,\"authPercentName\":\"\",\"authPercentCode\":\"\",\"busReview\":null,\"tag\":null,\"cloudName\":\"\",\"cloudLabel\":\"dev\",\"desc\":\"redis: billing_desc_test\",\"RDOwner\":\"\",\"OPOwner\":\"\",\"zoneType\":\"\",\"idc\":\"\",\"useTypeName\":\"普通服务\",\"useForProduct\":\"\",\"staticQuantity\":null,\"id\":\"65f94c5bff1b3ecb5fcb7d0e\",\"instanceId\":\"billing_desc_test\",\"snapInfo\":{\"num\":1},\"costId\":\"\",\"monthPrice\":230,\"orderCode\":\"1000492168276591\",\"owner\":\"jiayiming_dxm\",\"startTime\":\"2024-03-19T16:27:07.653+08:00\",\"endTime\":\"0001-01-01T08:05:43+08:05\",\"createdAt\":\"2024-03-19T16:27:07.684+08:00\",\"updateAt\":\"2024-03-19T16:27:07.684+08:00\",\"dynamicComboGroup\": [],\"status\":1,\"parentCode\":\"\",\"busPercentStatus\":0,\"instanceName\":\"\",\"shortId\":\"redis-57im2gwy4ab\"}]}")))
			},
			args: args{
				clusterName: "modify_test",
				storageSize: 4,
			},
			wantErr: false,
		},
		{
			name: "test: request failed",
			args: args{
				clusterName: "modify_test",
				storageSize: 20,
			},
			wantErr: true,
		},
		{
			name: "test5: success",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/api/billing/order/modify`),
					httpmock.ResponderFromResponse(httpmock.NewStringResponse(200, "{\"errno\":0,\"errmsg\":\"success\",\"data\":{}}")))
			},
			args: args{
				clusterName: "modify_test",

				storageSize: 2,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := MemorySizeChange(tt.args.clusterName, tt.args.storageSize)
			if (err != nil) != tt.wantErr {
				t.Errorf("MemorySizeChange() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

// ====================================
// 			转化容器集群订单
// ====================================

func TestDockerTransform(t *testing.T) {
	initTest()
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		params *CreateOrderParams
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name:    "test1: check params",
			args:    args{},
			wantErr: true,
		},
		{
			name: "test2: get instances failed",
			args: args{
				params: &CreateOrderParams{
					ClusterName: "redis_dynamic_trans",
				},
			},
			wantErr: true,
		},
		{
			name: "test: order len != 1",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/api/billing/order/info`),
					httpmock.ResponderFromResponse(httpmock.NewStringResponse(200, "{\"errno\":0,\"errmsg\":\"success\",\"data\":[]}")))
			},
			args: args{
				params: &CreateOrderParams{
					ClusterName: "redis_dynamic_trans",
				},
			},
			wantErr: true,
		},
		{
			name: "test3: request failed",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/api/billing/order/info`),
					httpmock.ResponderFromResponse(httpmock.NewStringResponse(200, "{\"errno\":0,\"errmsg\":\"success\",\"data\":[{\"resourceName\":\"REDIS\",\"resourceNameCh\":\"缓存数据库\",\"resourceId\":100049,\"resourceType\":\"\",\"comboName\":\"redis-科技云-单分片\",\"comboId\":21,\"comboCode\":100049985621,\"comboDesc\":\"\",\"comboUnitInfo\":\"\",\"unitPrice\":0,\"departCode\":1000000037,\"department\":\"系统运维部\",\"team\":\"\",\"useType\":0,\"business\":[{\"name\":\"理财\",\"percent\":100}],\"product\":[],\"isAckPercentMonthly\":false,\"percentDesc\":\"\",\"isUseAuthPercent\":false,\"authPercentName\":\"\",\"authPercentCode\":\"\",\"busReview\":null,\"tag\":null,\"cloudName\":\"\",\"cloudLabel\":\"dev\",\"desc\":\"redis: billing_desc_test\",\"RDOwner\":\"\",\"OPOwner\":\"\",\"zoneType\":\"\",\"idc\":\"\",\"useTypeName\":\"普通服务\",\"useForProduct\":\"\",\"staticQuantity\":null,\"id\":\"65f94c5bff1b3ecb5fcb7d0e\",\"instanceId\":\"billing_desc_test\",\"snapInfo\":{\"num\":1},\"costId\":\"\",\"monthPrice\":230,\"orderCode\":\"1000492168276591\",\"owner\":\"jiayiming_dxm\",\"startTime\":\"2024-03-19T16:27:07.653+08:00\",\"endTime\":\"0001-01-01T08:05:43+08:05\",\"createdAt\":\"2024-03-19T16:27:07.684+08:00\",\"updateAt\":\"2024-03-19T16:27:07.684+08:00\",\"status\":1,\"parentCode\":\"\",\"busPercentStatus\":0,\"instanceName\":\"\",\"shortId\":\"redis-57im2gwy4ab\"}]}")))
			},
			args: args{
				params: &CreateOrderParams{
					ClusterName: "redis_dynamic_trans",
					ProxyNum:    2,
					ShardNum:    2,
					StorageSize: 10,
					SentinelNum: 1,
				},
			},
			wantErr: true,
		},
		{
			name: "test4: success",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/api/billing/order/info`),
					httpmock.ResponderFromResponse(httpmock.NewStringResponse(200, "{\"errno\":0,\"errmsg\":\"success\",\"data\":[{\"resourceName\":\"REDIS\",\"resourceNameCh\":\"缓存数据库\",\"resourceId\":100049,\"resourceType\":\"\",\"comboName\":\"redis-科技云-单分片\",\"comboId\":21,\"comboCode\":100049985621,\"comboDesc\":\"\",\"comboUnitInfo\":\"\",\"unitPrice\":0,\"departCode\":1000000037,\"department\":\"系统运维部\",\"team\":\"\",\"useType\":0,\"business\":[{\"name\":\"理财\",\"percent\":100}],\"product\":[],\"isAckPercentMonthly\":false,\"percentDesc\":\"\",\"isUseAuthPercent\":false,\"authPercentName\":\"\",\"authPercentCode\":\"\",\"busReview\":null,\"tag\":null,\"cloudName\":\"\",\"cloudLabel\":\"dev\",\"desc\":\"redis: billing_desc_test\",\"RDOwner\":\"\",\"OPOwner\":\"\",\"zoneType\":\"\",\"idc\":\"\",\"useTypeName\":\"普通服务\",\"useForProduct\":\"\",\"staticQuantity\":null,\"id\":\"65f94c5bff1b3ecb5fcb7d0e\",\"instanceId\":\"billing_desc_test\",\"snapInfo\":{\"num\":1},\"costId\":\"\",\"monthPrice\":230,\"orderCode\":\"1000492168276591\",\"owner\":\"jiayiming_dxm\",\"startTime\":\"2024-03-19T16:27:07.653+08:00\",\"endTime\":\"0001-01-01T08:05:43+08:05\",\"createdAt\":\"2024-03-19T16:27:07.684+08:00\",\"updateAt\":\"2024-03-19T16:27:07.684+08:00\",\"status\":1,\"parentCode\":\"\",\"busPercentStatus\":0,\"instanceName\":\"\",\"shortId\":\"redis-57im2gwy4ab\"}]}")))
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/api/billing/order/modify`),
					httpmock.ResponderFromResponse(httpmock.NewStringResponse(200, "{\"errno\":0,\"errmsg\":\"success\",\"data\":{}}")))
			},
			args: args{
				params: &CreateOrderParams{
					ClusterName: "redis_dynamic_trans",
					ProxyNum:    4,
					ShardNum:    4,
					StorageSize: 20,
					SentinelNum: 1,
					InMigration: true,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := DockerTransform(tt.args.params)
			if (err != nil) != tt.wantErr {
				t.Errorf("DockerTransform() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}
