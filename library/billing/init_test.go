package billing

import (
	"testing"
)

func TestInit(t *testing.T) {
	type args struct {
		cfg *Config
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test1: url empty",
			args: args{
				cfg: &Config{},
			},
			wantErr: true,
		},
		{
			name: "test2: success",
			args: args{
				cfg: &Config{
					URL:               "123",
					AK:                "",
					SK:                "",
					ResourceID:        1,
					ProxyComboCode:    1,
					RedisComboCode:    1,
					MemoryComboCode:   1,
					SentinelComboCode: 1,
					OldComboCode:      1,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := Init(tt.args.cfg)
			if (err != nil) != tt.wantErr {
				t.Errorf("Init() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}
