package billing

import (
	"regexp"
	"testing"

	"github.com/jarcoal/httpmock"
	"gitlab.duxiaoman-int.com/siod-open/billing-sdk-golang/billing"

	"dt-common/logger"
	"redis-xweb/config"
)

// 无法使用env.Mock 会循环引用
func initTest() {
	// 初始化配置
	config.Init("../../config/config.yaml")

	// 初始化 billing
	var billingConfig Config
	config.Get("billing", &billingConfig)
	Init(&billingConfig)

	// 初始化 Logger
	var logConfig logger.Config
	config.Get("logger", &logConfig)
	logger.Init(&logConfig)
}

func TestGetAllProducts(t *testing.T) {
	initTest()

	type args struct{}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, []string)
	}{
		{
			name: "test1: call failed",
			before: func() {
				httpmock.Activate()
			},
			args:    args{},
			wantErr: true,
		},
		{
			name: "test2: success",
			before: func() {
				httpmock.Deactivate()
			},
			args:    args{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			products, err := GetAllProducts()
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAllProducts() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, products)
			}
		})
	}
}

func TestCheckProducts(t *testing.T) {
	initTest()

	type args struct {
		products map[string]float64
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test1: call failed",
			before: func() {
				httpmock.Activate()
			},
			args: args{
				products: map[string]float64{
					"BFE":   10.5,
					"REDIS": 20,
					"KAFKA": 69.5,
				},
			},
			wantErr: true,
		},
		{
			name: "test2: percent not 100",
			before: func() {
				httpmock.Deactivate()
			},
			args: args{
				products: map[string]float64{
					"BFE":   10.5,
					"REDIS": 20,
					"KAFKA": 69.5,
					"BBC":   4.2,
				},
			},
			wantErr: true,
		},
		{
			name: "test3: undefined product",
			args: args{
				products: map[string]float64{
					"BFE":    10.5,
					"REDIS":  20,
					"KAFK2A": 69.5,
				},
			},
			wantErr: true,
		},
		{
			name: "test4: success",
			args: args{
				products: map[string]float64{
					"BFE":   10.5,
					"REDIS": 20,
					"KAFKA": 69.5,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			_, err := CheckProducts(tt.args.products)
			if (err != nil) != tt.wantErr {
				t.Errorf("CheckProducts() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

// 单测：获取业务属性
func TestGetAllBusinesses(t *testing.T) {
	initTest()

	type args struct{}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, []string)
	}{
		{
			name: "test1: call failed",
			before: func() {
				httpmock.Activate()
			},
			args:    args{},
			wantErr: true,
		},
		{
			name: "test2: undefined return value",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/api/billing/business/list`),
					httpmock.ResponderFromResponse(httpmock.NewStringResponse(200, `{"errno": 0, "data": "ok"}`)))
			},
			args:    args{},
			wantErr: true,
		},
		{
			name: "test2: success",
			before: func() {
				httpmock.Deactivate()
			},
			args:    args{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			products, err := GetAllBusinesses()
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAllBusinesses() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, products)
			}
		})
	}
}

// 校验业务属性字段，各属性加和需要100%
func TestCheckBusinesses(t *testing.T) {
	initTest()

	type args struct {
		business map[string]float64
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, *[]billing.BusinessParam)
	}{
		{
			name: "test1: call failed",
			before: func() {
				httpmock.Activate()
			},
			args:    args{},
			wantErr: true,
		},
		{
			name: "test2: undefined business value",
			before: func() {
				httpmock.Deactivate()
			},
			args: args{
				business: map[string]float64{
					"信贷01": 100,
				},
			},
			wantErr: true,
		},
		{
			name: "test3: percent not 100",
			args: args{
				business: map[string]float64{
					"信贷": 80.4,
					"支付": 21.2,
				},
			},
			wantErr: true,
		},
		{
			name: "test4: success",
			args: args{
				business: map[string]float64{
					"信贷": 80.4,
					"支付": 19.6,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			bizs, err := CheckBusinesses(tt.args.business)
			if (err != nil) != tt.wantErr {
				t.Errorf("CheckBusinesses() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, bizs)
			}
		})
	}
}
