package billing

import (
	"fmt"

	"gitlab.duxiaoman-int.com/siod-open/billing-sdk-golang/billing"
)

const (
	PROXY_ORDER_NO    int = 1 // proxy订单，编号__1
	REDIS_ORDER_NO    int = 2 // redis分片订单，编号__2
	MEMORY_ORDER_NO   int = 3 // redis内存订单，编号__3
	SENTINEL_ORDER_NO int = 4 // sentinel订单，编号__4
)

var (
	client            *billing.BillingClient
	resourceID        int   // redis资源ID
	proxyComboCode    int64 // Proxy套餐
	redisComboCode    int64 // 分片套餐
	memoryComboCode   int64 // 内存套餐
	sentinelComboCode int64 // 哨兵套餐
	// 物理集群4G内存订单  TODO: 容器集群上了后可以删掉
	OldComboCode int64
)

// 成本中心初始化配置
type Config struct {
	URL               string `yaml:"url"`
	AK                string `yaml:"access_key"`
	SK                string `yaml:"secret_key"`
	ResourceID        int    `yaml:"resource_id"`
	ProxyComboCode    int64  `yaml:"proxy_combo_code"`
	RedisComboCode    int64  `yaml:"redis_combo_code"`
	MemoryComboCode   int64  `yaml:"memory_combo_code"`
	SentinelComboCode int64  `yaml:"sentinel_combo_code"`
	OldComboCode      int64  `yaml:"old_combo_code"`
}

// client 初始化
func Init(cfg *Config) error {
	if cfg.URL == "" {
		return fmt.Errorf("config url not found")
	}

	client = &billing.BillingClient{}
	client.Host = cfg.URL
	client.Ak = cfg.AK
	client.Sk = cfg.SK

	resourceID = cfg.ResourceID
	proxyComboCode = cfg.ProxyComboCode
	redisComboCode = cfg.RedisComboCode
	memoryComboCode = cfg.MemoryComboCode
	sentinelComboCode = cfg.SentinelComboCode

	OldComboCode = cfg.OldComboCode

	return nil
}
