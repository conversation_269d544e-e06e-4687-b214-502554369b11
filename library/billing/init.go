package billing

import (
	"fmt"

	"gitlab.duxiaoman-int.com/siod-open/billing-sdk-golang/billing"
)

var (
	client     *billing.BillingClient
	resourceID int64 // fec资源ID
)

// 成本中心初始化配置
type Config struct {
	URL        string `yaml:"url"`
	AK         string `yaml:"access_key"`
	SK         string `yaml:"secret_key"`
	ResourceID int64  `yaml:"resource_id"`
}

// client 初始化
func Init(cfg *Config) error {
	if cfg.URL == "" {
		return fmt.Errorf("config url not found")
	}

	client = &billing.BillingClient{}
	client.Host = cfg.URL
	client.Ak = cfg.AK
	client.Sk = cfg.SK

	resourceID = cfg.ResourceID

	return nil
}
