package userc

import (
	"encoding/json"
	"errors"
	"net/http"
	"regexp"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/jarcoal/httpmock"

	"dt-common/errs"
	"redis-xweb/env"
)

func TestSetFlowResultIfNeeded(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")
	gin.SetMode(gin.TestMode)

	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		flowInstanceId uint32
		nodeInstanceId uint32
		finish         bool
		result         error
	}
	tests := []struct {
		name   string
		before func()
		args   args
		expect func(*testing.T)
	}{
		{
			name: "test1: success",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/v1/flow/ProcessCallBack`),
					func(req *http.Request) (*http.Response, error) {
						params := make(map[string]interface{})
						if err := json.NewDecoder(req.Body).Decode(&params); err != nil {
							return httpmock.NewStringResponse(400, ""), nil
						}
						result := params["result"].(map[string]interface{})
						if result["errno"] != 0.0 {
							t.Errorf("code is not 0")
						}

						return httpmock.NewStringResponse(200, "{\"errno\":0,\"errmsg\":\"\",\"data\":{\"flowInstanceId\":123}}"), nil
					})
			},
			args: args{
				flowInstanceId: 1,
				nodeInstanceId: 1,
				finish:         true,
				result:         errs.Success,
			},
			expect: func(t *testing.T) {
				m := httpmock.GetCallCountInfo()
				if m["POST =~://[\\w\\W]+/v1/flow/ProcessCallBack"] != 1 {
					t.Errorf("expect 1 POST, but %v", m)
				}
				httpmock.Reset()
			},
		},
		{
			name: "test2: failed",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/v1/flow/ProcessCallBack`),
					func(req *http.Request) (*http.Response, error) {
						params := make(map[string]interface{})
						if err := json.NewDecoder(req.Body).Decode(&params); err != nil {
							return httpmock.NewStringResponse(400, ""), nil
						}
						result := params["result"].(map[string]interface{})
						if result["errno"] == 0.0 {
							t.Errorf("code is 0")
						}

						return httpmock.NewStringResponse(200, "{\"errno\":0,\"errmsg\":\"\",\"data\":{\"flowInstanceId\":123}}"), nil
					})
			},
			args: args{
				flowInstanceId: 1,
				nodeInstanceId: 1,
				finish:         true,
				result:         errors.New("test"),
			},
			expect: func(t *testing.T) {
				m := httpmock.GetCallCountInfo()
				if m["POST =~://[\\w\\W]+/v1/flow/ProcessCallBack"] != 1 {
					t.Errorf("expect 1 POST, but %v", m)
				}
				httpmock.Reset()
			},
		},
		{
			name: "test3: not send request",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/v1/flow/ProcessCallBack`),
					func(req *http.Request) (*http.Response, error) {
						params := make(map[string]interface{})
						if err := json.NewDecoder(req.Body).Decode(&params); err != nil {
							return httpmock.NewStringResponse(400, ""), nil
						}
						result := params["result"].(map[string]interface{})
						if result["errno"] == 0.0 {
							t.Errorf("code is 0")
						}

						return httpmock.NewStringResponse(200, "{\"errno\":0,\"errmsg\":\"\",\"data\":{\"flowInstanceId\":123}}"), nil
					})
			},
			args: args{
				flowInstanceId: 1,
				nodeInstanceId: 1,
				finish:         false,
				result:         errors.New("test"),
			},
			expect: func(t *testing.T) {
				m := httpmock.GetCallCountInfo()
				if m["POST =~://[\\w\\W]+/v1/flow/ProcessCallBack"] != 0 {
					t.Errorf("expect 0 POST, but %v", m)
				}
				httpmock.Reset()
			},
		},
		{
			name: "test4: not send request 2",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/v1/flow/ProcessCallBack`),
					func(req *http.Request) (*http.Response, error) {
						params := make(map[string]interface{})
						if err := json.NewDecoder(req.Body).Decode(&params); err != nil {
							return httpmock.NewStringResponse(400, ""), nil
						}
						result := params["result"].(map[string]interface{})
						if result["errno"] == 0.0 {
							t.Errorf("code is 0")
						}

						return httpmock.NewStringResponse(200, "{\"errno\":0,\"errmsg\":\"\",\"data\":{\"flowInstanceId\":123}}"), nil
					})
			},
			args: args{
				flowInstanceId: 1,
				nodeInstanceId: 1,
				finish:         true,
				result:         nil,
			},
			expect: func(t *testing.T) {
				m := httpmock.GetCallCountInfo()
				if m["POST =~://[\\w\\W]+/v1/flow/ProcessCallBack"] != 0 {
					t.Errorf("expect 0 POST, but %v", m)
				}
				httpmock.Reset()
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			SetFlowResultIfNeeded(tt.args.flowInstanceId, tt.args.nodeInstanceId, &tt.args.finish, &tt.args.result)
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}
