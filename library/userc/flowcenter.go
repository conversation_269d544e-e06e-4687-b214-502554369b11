package userc

import (
	"dt-common/errs"
	"dt-common/logger"
	"dt-common/user-center/flowc"
)

// 如需要，回调流程中心结束流程
// finish 为 false，不影响工单结果
// result 为 nil，不影响工单结果
func SetFlowResultIfNeeded(flowInstanceId, nodeInstanceId uint32, finish *bool, result *error) {
	var code int
	var msg string
	if flowInstanceId == 0 || nodeInstanceId == 0 || !(*finish) || *result == nil {
		return
	}

	if (*result).Error() == errs.Success.Error() {
		code = 0
		msg = "任务结束"
	} else {
		code = -1
		msg = (*result).Error()
	}

	callErr := flowc.AddProcessResult(&flowc.AddResult{
		FlowInstanceId: flowInstanceId,
		NodeInstanceId: nodeInstanceId,
		Code:           code,
		Message:        msg,
	})
	if callErr != nil {
		logger.Error("[FLOW %d] failed to finish flow, error=(%v)", flowInstanceId, callErr)
	}
	logger.Info("[FLOW %d] succeed to finish flow, code=%d, message=(%s) ", flowInstanceId, code, msg)
}
