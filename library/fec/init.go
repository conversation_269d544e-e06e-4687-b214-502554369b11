package fec

import (
	"bytes"
	"crypto/hmac"
	"crypto/md5"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	netURL "net/url"
	"time"

	"dt-common/logger"
)

var (
	platform = "redis"
	config   = &Config{}
)

type Config struct {
	Host  string `yaml:"host"`
	Token string `yaml:"token"`
	Ak    string `yaml:"ak"`
	Sk    string `yaml:"sk"`
}

func Init(cfg *Config) {
	config = cfg
}

type Response struct {
	ErrNo  int    `json:"errno"`
	ErrMsg string `json:"errmsg"`
	Data   any    `json:"data"`
}

func APIEncryptPostRequstV2(url string, requestBody any) (*Response, error) {
	url = config.Host + url
	jsonBody, err := json.Marshal(requestBody)
	if err != nil {
		return nil, fmt.Errorf("error encoding request body, error=(%v)", err)
	}

	// 计算请求体的哈希值
	contentMd5 := md5Hash([]byte(jsonBody))
	u, err := netURL.Parse(url)
	if err != nil {
		return nil, fmt.Errorf("error parse url, error=(%v)", err)
	}

	req, err := http.NewRequest("POST", url, bytes.NewReader(jsonBody))
	if err != nil {
		return nil, fmt.Errorf("error creating request, error=(%v)", err)
	}
	req.Header = SetPostHeaders(config.Ak, config.Sk, contentMd5, fmt.Sprintf("%d", time.Now().Unix()), u.Path)

	client := http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		logger.Warn("failed to request fec, error=(%v)", err)
		return nil, err
	}
	defer resp.Body.Close()
	resBody, err := io.ReadAll(resp.Body)
	if err != nil {
		logger.Warn("failed to read from stream, error=(%v)", err)
		return nil, err
	}

	r := &Response{}
	err = json.Unmarshal(resBody, &r)
	if err != nil {
		logger.Warn("failed to unmarshal response from fec, content=(%s), error=(%v)", string(resBody), err)
		return nil, err
	}

	return r, nil
}

func SetPostHeaders(accessKeyId, secretAccessKey, contentMd5, timestamp, url string) (headers http.Header) {
	headers = make(http.Header)
	headers.Set("AccessKeyId", accessKeyId)
	headers.Set("Content-MD5", contentMd5)
	headers.Set("Date", timestamp)

	stringToSign := fmt.Sprintf("%s\n%s\n%s\n%s%s", "POST", accessKeyId, contentMd5, timestamp, url)
	// 计算签名
	hmacSha256 := hmac.New(sha256.New, []byte(secretAccessKey))
	hmacSha256.Write([]byte(stringToSign))
	signature := base64.StdEncoding.EncodeToString(hmacSha256.Sum(nil))
	headers.Set("Authorization", signature)
	return
}

// 计算字符串的 MD5 哈希值，并返回 base64 编码的字符串
func md5Hash(data []byte) string {
	md5Hash := md5.Sum(data)
	return base64.StdEncoding.EncodeToString(md5Hash[:])
}
