package fec

import (
	"net/http"
	"testing"
	"time"

	"github.com/jarcoal/httpmock"

	"dt-common/logger"
)

func initTest() {
	Init(&Config{
		Ak:    "siod-sc-0ZeI4iDTF2mnJ5L9",
		Host:  "http://*************:8536",
		Sk:    "S3g2LoXPOqjsyWzcGyjFsYAw5ngEf5iM",
		Token: "fec-manager_yujiazheng_dxm_4e73a1f4",
	})
	logger.Init(&logger.Config{})
}

func TestListPod(t *testing.T) {
	initTest()

	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		param *PodListRequstParam
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		expect  func(*testing.T, []*PodInfo)
		wantErr bool
	}{
		{
			name: "test: failed",
			args: args{
				param: &PodListRequstParam{
					LabelSelector: map[string]string{
						"dxm-redis/name":      "r-r3-rd-test",
						"dxm-redis/component": "redis",
					},
					Platform:      "redis",
					PodNameList:   []string{},
					PodNamePrefix: "",
					TimeoutSecond: 10,
				},
			},
			wantErr: true,
		},
		{
			name: "test: success",
			before: func() {
				httpmock.RegisterResponder("POST", "http://*************:8536/fec-manager/api/v1/fec/listPod",
					httpmock.NewStringResponder(200, "{\"errno\": 0,\"errmsg\": \"\",\"data\": [{\"id\": \"\",\"podName\": \"r-r3-rd-test-0-1bj0uqn4\",\"productName\": \"siod-kafka\",\"appName\": \"r3-rd-test-redis\",\"podIp\": \"************\",\"idc\": \"BDDX\",\"comboDesc\": \"\",\"safeRegion\": \"test_internal\",\"owner\": \"jiayiming_dxm\",\"applicant\": \"\",\"status\": \"Running\",\"labels\": {\"avoid_delete_by_accident\": \"true\",\"dxm-redis/component\": \"redis\",\"dxm-redis/idc\": \"hbb\",\"dxm-redis/index\": \"0\",\"dxm-redis/name\": \"r-r3-rd-test\",\"fec2/app_name\": \"r3-rd-test-redis\",\"fec2/bnsMount\": \"false\",\"fec2/budget_execution_id\": \"\",\"fec2/budget_number\": \"\",\"fec2/buget-checked\": \"pass\",\"fec2/cni-safe-region\": \"test_internal\",\"fec2/combo_code\": \"567756428\",\"fec2/device-id\": \"fec-PMOYhZDwP\",\"fec2/disable-inject-sidecar\": \"false\",\"fec2/fec-id\": \"fid-XxqWgKwoo\",\"fec2/idc\": \"BDDX\",\"fec2/over_budget\": \"true\",\"fec2/owner\": \"jiayiming_dxm\",\"fec2/product_name\": \"siod-kafka\",\"fec2/retain\": \"true\",\"fec2/saas\": \"redis\",\"fec2/service_level\": \"\",\"fec2/version\": \"v0.0.2\",\"fec2/white\": \"false\"},\"hostname\": \"r-r3-rd-test-0-1bj0uqn4.redis.fec.kj01.bddx.dxm-int.com\",\"scheduleLimit\": null,\"containerInfo\": [{\"containerName\": \"r-r3-rd-test-0\",\"imageName\": \"r.duxiaoman-int.com/siod_redis/redis-docker\",\"imageVersion\": \"v2024072402\",\"imagePullPolicy\": \"IfNotPresent\",\"cpu\": 1,\"mem\": 1,\"port\": null,\"entryCommand\": \"\",\"execArgs\": null,\"envArgs\": {\"CLOUD_TYPE\": \"kj\",\"CONTAINER_NAME\": \"r-r3-rd-test-0\",\"CONTAINER_TYPE\": \"main\",\"DEVICE_TYPE\": \"pod\",\"FECID\": \"fid-XxqWgKwoo\",\"FEC_OPERATOR_URL\": \"https://************:11443/fec2\",\"HAWKING_ENV\": \"container\",\"INIT_NOAH_USER\": \"redis\",\"MAX_MEMORY_POLICY\": \"volatile-lru\",\"NODE_IP\": \"\",\"POD_IP\": \"\",\"POD_NAME\": \"\",\"REDIS_PORT\": \"7000\"},\"diskApplication\": 0,\"mountType\": \"\",\"volumeMounts\": null,\"status\": \"Running\",\"privileged\": false}]}]}"))
			},
			args: args{
				param: &PodListRequstParam{
					LabelSelector: map[string]string{
						"dxm-redis/name":      "r-r3-rd-test",
						"dxm-redis/component": "redis",
					},
					Platform:      "redis",
					PodNameList:   []string{},
					PodNamePrefix: "",
					TimeoutSecond: 10,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			podList, err := ListPods(tt.args.param)
			if (err != nil) != tt.wantErr {
				t.Errorf("Client.ListPod() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.expect != nil {
				tt.expect(t, podList)
			}
		})
	}
}

func TestApplyPod(t *testing.T) {
	initTest()

	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		param *ApplyPodRequstParam
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
	}{
		{
			name: "test: failed",
			args: args{
				param: &ApplyPodRequstParam{
					IsBindingBns:    false,
					NoahProduct:     "siod-redis",
					NoahApp:         "public-test",
					Owner:           "jiayiming_dxm",
					OwnerDepartment: "系统运维部",
					Comment:         "redis",
					PodName:         "public-test-redis2",
					RestartPolicy:   "Always",
					ComboCPU:        1,
					ComboMem:        512,
					SafeRegion:      "生产Internal",
					IdcDetail: []*IdcDetail{{
						Idc: "BDDX",
						Num: 1,
					}},
					Containers: []*Container{{
						ContainerName:   "public-test",
						ImageName:       "r.duxiaoman-int.com/siod_redis/redis-docker",
						ImagePullPolicy: "IfNotPresent",
						ImageVersion:    "v20240102",
						EntryCommand:    "",
						ExecArgs:        []string{},
						DiskInfos: []VolumeInfo{
							{
								IsEphemeral: true,
								VolumeType:  PV_TYPE_SHARE,
								DiskType:    NVME_SSD,
								Capacity:    10,
							},
							{
								IsEphemeral: false,
								Path:        []string{"/home/<USER>"},
								VolumeType:  PV_TYPE_SHARE,
								DiskType:    NVME_SSD,
								Capacity:    15,
							},
						},
					}},
					ScheduleLimit: []*ScheduleLimit{
						{
							LimitType:     "label",
							LabelKey:      "dxm-redis/name",
							LabelValue:    "public-test",
							MaxNumOneNode: 1,
						},
					},
					Label: map[string]string{
						"dxm-redis/name": "public-test",
					},
				},
			},
			wantErr: true,
		},
		{
			name: "test: success",
			before: func() {
				httpmock.RegisterResponder("POST", "http://*************:8536/fec-manager/api/v1/fec/applyPod",
					httpmock.NewStringResponder(200, `{"errno": 0,"errmsg": "","data": "ok"}`))
			},
			args: args{
				param: &ApplyPodRequstParam{
					IsBindingBns:    false,
					NoahProduct:     "siod-redis",
					NoahApp:         "bos-platform-kj-test",
					Owner:           "jiayiming_dxm",
					OwnerDepartment: "系统运维部",
					Comment:         "redis",
					PodNamePrefix:   "r-public-test",
					PodName:         "r-public-test-1-ac",
					RestartPolicy:   "Always",
					ComboCPU:        1,
					ComboMem:        512,
					SafeRegion:      "测试Internal",
					IdcDetail: []*IdcDetail{{
						Idc: "ZZJG",
						Num: 1,
					}},
					Containers: []*Container{{
						ContainerName:   "public-test",
						ImageName:       "r.duxiaoman-int.com/siod_redis/redis-docker",
						ImagePullPolicy: "Always",
						ImageVersion:    "v1.0.0-test",
						EntryCommand:    "",
						EnvArgs: map[string]string{
							"REDIS_PORT":        "7000",
							"MAX_MEMORY_POLICY": "noeviction",
							"CMANAGER_URL":      "redis-cmanager.siod-kafka.serv:8811",
							"CMANAGER_TOKEN":    "dxm_cmanager_123456",
						},
						DiskInfos: []VolumeInfo{
							{
								IsEphemeral: true,
								VolumeType:  PV_TYPE_SHARE,
								DiskType:    NVME_SSD,
								Capacity:    10,
							},
							{
								IsEphemeral: false,
								Path:        []string{"/home/<USER>"},
								VolumeType:  PV_TYPE_SHARE,
								DiskType:    NVME_SSD,
								Capacity:    15,
							},
						},
					}},
					Label: map[string]string{
						"dxm-redis/name":      "public-test",
						"dxm-redis/idc":       "hbb",
						"dxm-redis/component": "redis",
						"dxm-redis/index":     "0",
					},
					PreStartSleep: 60,
					SidecarContainerResource: []*SidecarContainerResource{
						{
							ContainerName: "noah-agent",
							CpuLimit:      0.3,
							MemLimit:      0.2,
						},
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			if err := ApplyPods(tt.args.param); (err != nil) != tt.wantErr {
				t.Errorf("Client.ApplyPod() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestReturnPod(t *testing.T) {
	initTest()

	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		param *ReturnPodRequstParam
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
	}{
		{
			name: "test: failed",
			args: args{
				param: &ReturnPodRequstParam{
					Platform: "redis",
					Comment:  "redis",
					PodList: []*ReturnPod{
						{
							PodName:  "public-test-redis-xf8gl1n",
							IsRetain: false,
							Idc:      "hba",
						},
					},
				},
			},
			wantErr: true,
		},
		{
			name: "test: timeout",
			before: func() {
				httpmock.RegisterResponder("POST", "http://*************:8536/fec-manager/api/v1/fec/returnPod",
					func(req *http.Request) (*http.Response, error) {
						time.Sleep(6 * time.Second)
						return httpmock.NewStringResponse(408, "<html>"), nil
					})
			},
			args: args{
				param: &ReturnPodRequstParam{
					Platform: "redis",
					Comment:  "redis",
					PodList: []*ReturnPod{
						{
							PodName:  "public-test-redis-xf8gl1n",
							IsRetain: false,
							Idc:      "hba",
						},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "test: success",
			before: func() {
				httpmock.RegisterResponder("POST", "http://*************:8536/fec-manager/api/v1/fec/returnPod",
					httpmock.NewStringResponder(200, `{"errno": 0,"errmsg": "","data": "ok"}`))
			},
			args: args{
				param: &ReturnPodRequstParam{
					Platform: "redis",
					Comment:  "redis",
					PodList: []*ReturnPod{
						{
							PodName:  "public-test-redis-xf8gl1n",
							IsRetain: false,
							Idc:      "hba",
						},
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			if err := ReturnPods(tt.args.param); (err != nil) != tt.wantErr {
				t.Errorf("Client.ReturnPod() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestLabelPod(t *testing.T) {
	initTest()

	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		param *LabelPodRequstParam
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
	}{
		{
			name: "test: success",
			before: func() {
				httpmock.RegisterResponder("POST", "http://*************:8536/fec-manager/api/v1/fec/labelPod",
					httpmock.NewStringResponder(200, `{"errno": 0,"errmsg": "","data": {"platform": "redis", "podName": "s-r3-rd-test-o3", "labels": {"dxm-redis/taint":"true"}}}`))
			},
			args: args{
				param: &LabelPodRequstParam{
					Platform: "redis",
					PodName:  "s-r3-rd-test-o3",
					Labels: map[string]string{
						"dxm-redis/component": "sentinel",
						"dxm-redis/taint":     "true",
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			if err := LabelPod(tt.args.param); (err != nil) != tt.wantErr {
				t.Errorf("LabelPod() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
