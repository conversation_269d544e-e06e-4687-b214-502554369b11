package fec

import (
	"context"
	"encoding/json"
	"fmt"
	"time"
)

const (
	APPLY_URL    = "/fec-manager/api/v1/fec/applyPod"
	RETURN_URL   = "/fec-manager/api/v1/fec/returnPod"
	GET_RESULT   = "/fec-manager/api/v1/fec/getResult"
	UPDATE_LABEL = "/fec-manager/api/v1/fec/labelPod"
	LIST_POD     = "/fec-manager/api/v1/fec/listPod"
	LABEL_POD    = "/fec-manager/api/v1/fec/labelPod"

	NVME_SSD = "NVME_SSD"
	SATA_SSD = "SATA_SSD"
	SATA_HDD = "SATA_HDD"

	PV_TYPE_SHARE     = "share"     // 共享PV
	PV_TYPE_EXCLUSIVE = "exclusive" // 独占PV
)

// ===================================================
// 					 APPLY POD
// ===================================================

type IdcDetail struct {
	Idc string `json:"idc"`
	Num int    `json:"num"`
}

type Container struct {
	ContainerName   string            `json:"containerName"`
	ImageName       string            `json:"imageName"`
	ImagePullPolicy string            `json:"imagePullPolicy"`
	ImageVersion    string            `json:"imageVersion"`
	Cpu             float64           `json:"cpu"`
	Mem             float64           `json:"mem"`
	EntryCommand    string            `json:"entryCommand"`
	ExecArgs        []string          `json:"execArgs"`
	EnvArgs         map[string]string `json:"envArgs"`
	DiskInfos       []VolumeInfo      `json:"diskInfos"` // 挂载的持久化路径 可以填多个
}

type VolumeInfo struct {
	IsEphemeral bool     `json:"isEphemeral"` // 标识是否是临时存储
	Path        []string `json:"path"`        // 挂载路径
	VolumeType  string   `json:"volumeType"`  // 标识是否独占 share共享盘 exclusive 独占盘
	DiskType    string   `json:"diskType"`    // 标识使用使用NVME_SSD或者SATA_SSD 或者SATA_HDD
	Capacity    float64  `json:"capacity"`    // 磁盘容量
}

type CostInfo struct {
	UseType     int        `json:"useType"`
	Product     []*Product `json:"product"`
	PercentNote string     `json:"percentNote"`
	Owner       string     `json:"owner"`
}

type SidecarContainerResource struct {
	ContainerName string  `json:"containerName"`
	CpuLimit      float64 `json:"cpuLimit"`
	MemLimit      float64 `json:"memLimit"`
}

type Product struct {
	Name    string  `json:"name"`
	Percent float32 `json:"percent"`
}

// 结构体打印
func (p *Product) String() string {
	return fmt.Sprintf("{name=%s, percent=%f}", p.Name, p.Percent)
}

type ApplyPodRequstParam struct {
	Token                    string                      `json:"token"`
	Platform                 string                      `json:"platform"`
	IsBindingBns             bool                        `json:"isBindingBns"`
	NoahProduct              string                      `json:"noahProduct"`
	NoahApp                  string                      `json:"noahApp"`
	Owner                    string                      `json:"owner"`
	OwnerDepartment          string                      `json:"ownerDepartment"`
	Comment                  string                      `json:"comment"`
	PodNamePrefix            string                      `json:"podNamePrefix"`
	PodName                  string                      `json:"podName"`
	RestartPolicy            string                      `json:"restartPolicy"`
	ComboCode                int                         `json:"comboCode"`
	SafeRegion               string                      `json:"safeRegion"`
	ComboCPU                 float64                     `json:"comboCPU"`
	ComboMem                 float64                     `json:"comboMem"`
	IdcDetail                []*IdcDetail                `json:"idcDetail"`
	Containers               []*Container                `json:"containers"`
	CostInfo                 *CostInfo                   `json:"costInfo"`
	Label                    map[string]string           `json:"label"`
	ScheduleLimit            []*ScheduleLimit            `json:"scheduleLimit"`
	PreStartSleep            int                         `json:"preStartSleep"`
	SidecarContainerResource []*SidecarContainerResource `json:"sidecarContainerResource"`
}

// 结构体打印
func (p *ApplyPodRequstParam) String() string {
	str, _ := json.Marshal(p)
	return string(str)
}

// 申请POD
func ApplyPods(param *ApplyPodRequstParam) error {
	param.Token = config.Token
	param.Platform = platform
	result, err := APIEncryptPostRequstV2(APPLY_URL, param)
	if err != nil {
		return err
	}
	if result.ErrNo != 0 || result.ErrMsg != "" {
		return fmt.Errorf("apply pod failed, errNo=%v, errMsg=(%v)", result.ErrNo, result.ErrMsg)
	}
	return nil
}

// ===================================================
// 					 RETURN POD
// ===================================================

type ReturnPod struct {
	PodName  string `json:"podName"`
	IsRetain bool   `json:"isRetain"`
	Idc      string `json:"idc"`
}

type ReturnPodRequstParam struct {
	Platform string       `json:"platform"`
	Comment  string       `json:"comment"`
	PodList  []*ReturnPod `json:"podList"`
}

// 退还POD, FEC的退还接口底层是个异步流程，但是接口是个同步接口，且退还的pod数量一多可能会引发超时
// 此处等待20s，若请求超时未返回则认为请求成功
func ReturnPods(param *ReturnPodRequstParam) error {
	ctx, cancel := context.WithDeadline(context.Background(), time.Now().Add(20*time.Second))
	defer cancel()

	// 异步调接口，将结果添加到chan中
	ech := make(chan error, 1)
	go func() {
		defer close(ech)
		param.Platform = platform
		result, err := APIEncryptPostRequstV2(RETURN_URL, param)
		if err != nil {
			ech <- err
			return
		}
		if result.ErrNo != 0 || result.ErrMsg != "" {
			ech <- fmt.Errorf("return pod failed, errNo=%v, errMsg=(%v)", result.ErrNo, result.ErrMsg)
			return
		}
		ech <- nil
	}()

	// 返回结果，若超时则认为调用成功
	select {
	case <-ctx.Done():
		return nil
	case err := <-ech:
		return err
	}
}

// ===================================================
// 					  LIST POD
// ===================================================

type PodInfo struct {
	Id            string            `json:"id"`
	PodName       string            `json:"podName"`
	PodIp         string            `json:"podIp"`
	HostName      string            `json:"hostName"` // 确认增加
	Idc           string            `json:"idc"`
	ProductName   string            `json:"productName"`
	AppName       string            `json:"appName"`
	ComboDesc     string            `json:"comboDesc"`
	SafeRegion    string            `json:"safeRegion"`
	Owner         string            `json:"owner"`
	Applicant     string            `json:"applicant"`
	Status        string            `json:"status"`
	Labels        map[string]string `json:"labels"`
	ScheduleLimit []*ScheduleLimit  `json:"scheduleLimit"`
	ContainerInfo []*ContainerInfo  `json:"containerInfo"`
}

type ScheduleLimit struct {
	LimitType     string `json:"limitType"`
	LabelKey      string `json:"labelKey,omitempty"`
	LabelValue    string `json:"labelValue,omitempty"`
	MaxNumOneNode int    `json:"maxNumOneNode"`
	Bns           string `json:"bns,omitempty"`
}

type ContainerInfo struct {
	ContainerName   string            `json:"containerName"`
	ImageName       string            `json:"imageName"`
	ImageVersion    string            `json:"imageVersion"`
	ImagePullPolicy string            `json:"imagePullPolicy"`
	Cpu             float64           `json:"cpu"`
	Mem             float64           `json:"mem"`
	Port            string            `json:"port"`
	EntryCommand    string            `json:"entryCommand"`
	ExecArgs        []string          `json:"execArgs"`
	EnvArgs         map[string]string `json:"envArgs"`
	DiskApplication float64           `json:"diskApplication"`
	IsManual        bool              `json:"isManual"`
	MountType       string            `json:"mountType"`
	VolumeMounts    []string          `json:"volumeMounts"`
	Status          string            `json:"status"`
	Privileged      bool              `json:"privileged"`
}

type PodListRequstParam struct {
	Platform      string            `json:"platform"`
	TimeoutSecond int               `json:"timeoutSecond"`
	LabelSelector map[string]string `json:"labelSelector"`
	PodNamePrefix string            `json:"podNamePrefix"`
	PodNameList   []string          `json:"podNameList"`
}

type PodListResponse struct {
	PodList []*PodInfo `json:"podList"`
}

// 查询POD列表
func ListPods(param *PodListRequstParam) ([]*PodInfo, error) {
	param.Platform = platform
	param.TimeoutSecond = 10
	result, err := APIEncryptPostRequstV2(LIST_POD, param)
	if err != nil {
		return nil, err
	}
	if result.ErrNo != 0 || result.ErrMsg != "" {
		return nil, fmt.Errorf("list pod failed, errNo=%v, errMsg=(%v)", result.ErrNo, result.ErrMsg)
	}
	js, err := json.Marshal(result.Data)
	if err != nil {
		return nil, fmt.Errorf("list pod failed, data can not transfer to json, data=[%v]", result.Data)
	}
	podInfoList := make([]*PodInfo, 0)
	err = json.Unmarshal(js, &podInfoList)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal to []*podInfo, data=[%v], error=(%v)", string(js), err)
	}
	return podInfoList, nil
}

// ===================================================
// 					  LABEL POD
// ===================================================

type LabelPodRequstParam struct {
	Platform string            `json:"platform"`
	PodName  string            `json:"podName"`
	Labels   map[string]string `json:"labels"`
}

// 向pod打tag
func LabelPod(params *LabelPodRequstParam) error {
	if params.PodName == "" {
		return fmt.Errorf("podName can not be empty")
	}
	if len(params.Labels) == 0 {
		return fmt.Errorf("labels can not be empty")
	}

	params.Platform = platform
	result, err := APIEncryptPostRequstV2(LABEL_POD, params)
	if err != nil {
		return err
	}
	if result.ErrNo != 0 || result.ErrMsg != "" {
		return fmt.Errorf("label pod failed, errNo=%v, errMsg=%v", result.ErrNo, result.ErrMsg)
	}

	return nil
}
