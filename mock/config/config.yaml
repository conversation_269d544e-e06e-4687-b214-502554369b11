application:
  # debug:开发环境 test:测试环境 release:线上环境
  mode: test
  # 服务端口号
  port: 8433
  # 接口鉴权token
  token: dxm_test_123456

logger:
  # 日志等级 [error, warn, info, debug] 默认info
  level: debug
  # 是否打印到控制台 [true, false]
  log_console: true
  # 日志存放路径
  path: log/redis-agent.log
  # 在进行切割之前，日志文件的最大大小（以MB为单位）
  max_size: 100
  # 保留旧文件的最大天数
  max_age: 30
  # 保留旧文件的最大个数
  max_backups: 10

connection:
  # 连接redis超时时间，单位秒
  conn_timeout: 3
  # 读写redis超时时间，单位秒
  rw_timeout: 5
  # 禁止执行的命令
  forbidden_cmds: [shutdown]

redis:
  dialTimeout: 3
  readTimeout: 1
  writeTimeout: 2
  autoCloseAfterSeconds: 30

env:
  local_ip: 127.0.0.1
  # 进程根路径
  proxy_root: ../../mock/router_server${port}
  redis_root: ../../mock/redis${port}
  sentinel_root: ../../mock/sentinel${port}
  # 白名单路径
  proxy_whitelist: ../../mock/router_server${port}/conf
  redis_whitelist: ../../mock/redis${port}/conf
  sentinel_whitelist: ../../mock/sentinel${port}/conf
  # 控制脚本路径
  proxy_script: ../../mock/script/nutcracker_service${port}.sh
  redis_script: ../../mock/script/redis_service.sh
  sentinel_script: ../../mock/script/sentinel_service.sh
