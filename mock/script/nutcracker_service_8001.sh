#!/bin/bash

NUTCRACKER_PORT=8001
SENTINEL_BNS="redis-public-test-sentinel.siod-kafka"
SENTINEL_PORT=9001
STATUS_PORT=9001
time=$(date "+%Y-%m-%d %H:%M:%S")
cd /home/<USER>/local/router_server_8001

case "$1" in
status)
    echo "(Not all processes could be identified, non-owned process info
 will not be shown, you would have to be root to see it all.)
tcp        0      0 0.0.0.0:8001            0.0.0.0:*               LISTEN      471055/nutcracker"
    ;;
start)
    echo "${time} starting..router_server_${NUTCRACKER_PORT}"
    echo "${time} stopped successfully."
    ;;
stop)
    echo "${time} stoping..router_server_${NUTCRACKER_PORT}"
    echo "${time} stopped successfully."
    ;;
# restart)
#     stop
#     start
#     ;;
*)
    echo "Usage: $0 {status|start|stop|restart}"
    ;;
esac
