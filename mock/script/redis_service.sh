#!/bin/bash

case "$1" in
status)
    case "$2" in
    7000)
        echo "port 7000 is runing"
        ;;
    7001)
        echo "port 7002 is not runing"
        ;;
    7002)
        exit 2
        ;;
    esac
    ;;
start)
    case "$2" in
    7000)
        echo "port 7000 is runing"
        ;;
    7001)
        echo "port 7002 is not runing"
        ;;
    esac
    ;;
stop)
    echo "${time} stoping..router_server_${NUTCRACKER_PORT}"
    echo "${time} stopped successfully."
    ;;
*)
    echo "Usage: $0 {status|start|stop|restart}"
    ;;
esac
