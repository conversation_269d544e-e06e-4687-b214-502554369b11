#!/bin/bash

case "$1" in
status)
    case "$2" in
    9001)
        echo "(Not all processes could be identified, non-owned process info
 will not be shown, you would have to be root to see it all.)
 port 9001 is runing"
        ;;
    9002)
        echo "(Not all processes could be identified, non-owned process info
 will not be shown, you would have to be root to see it all.)
 port 9002 is not runing"
        ;;
    9003)
        exit 2
        ;;
    esac
    ;;
start)
    case "$2" in
    9001)
        echo "port 7000 is runing"
        ;;
    9002)
        echo "port 7002 is not runing"
        ;;
    esac
    ;;
stop)
    echo "${time} stoping..router_server_${NUTCRACKER_PORT}"
    echo "${time} stopped successfully."
    ;;
*)
    echo "Usage: $0 {status|start|stop|restart}"
    ;;
esac
