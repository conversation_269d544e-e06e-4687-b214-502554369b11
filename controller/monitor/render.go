package monitor

import (
	"github.com/gin-gonic/gin"

	"dt-common/errs"
	"dt-common/gintool"
	"dt-common/logger"
	"redis-cmanager/library/renderer"
)

// 获取渲染进度
func RenderProgress(c *gin.Context) {
	type schema struct {
		ClusterName string `json:"clusterName"`
	}

	var args schema
	err := c.ShouldBind<PERSON>(&args)
	if err != nil {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}

	result, err := renderer.GetInspectionProgress(args.ClusterName)
	if err != nil {
		logger.Error("%v", err)
		gintool.JSON(c, nil, errs.CodeNotFound.Detail(err.Error()))
		return
	}

	gintool.JSON(c, result, nil)
}
