package monitor

import (
	"context"
	"encoding/json"
	"io"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/gin-gonic/gin"

	"dt-common/mysql"
	"redis-cmanager/env"
)

// 单测：回调
func TestProxyProber(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")
	gin.SetMode(gin.TestMode)
	objC := env.MockCluster("public_test")

	type args struct {
		c *gin.Context
		w *httptest.ResponseRecorder
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, *httptest.ResponseRecorder)
	}{
		{
			name: "test1: check args",
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Request = &http.Request{
					Body: io.NopCloser(strings.NewReader("-1")),
				}
				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] == "00000" {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
		{
			name: "test2: not success",
			before: func() {
				db, _ := mysql.Database()
				db.Proxy.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetBns("redis-test-router.siod-redis").SetIP("************").SetPort(8001).SetIdc("hbb").Save(context.Background())
			},
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				params := &map[string]interface{}{
					"ip":       "************",
					"port":     8001,
					"password": "123",
				}
				b, _ := json.Marshal(params)
				c.Request = &http.Request{
					Body: io.NopCloser(strings.NewReader(string(b))),
				}
				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] == "00000" {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			ProxyProber(tt.args.c)
			// if (err != nil) != tt.wantErr {
			// 	t.Errorf("CreateProxySlowlog() error = %v, wantErr %v", err, tt.wantErr)
			// }
			if tt.expect != nil {
				tt.expect(t, tt.args.w)
			}
		})
	}
}
