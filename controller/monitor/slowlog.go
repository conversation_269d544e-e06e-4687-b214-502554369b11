package monitor

import (
	"time"

	"github.com/gin-gonic/gin"

	"dt-common/errs"
	"dt-common/gintool"
	"dt-common/logger"
	"dt-common/mysql"
)

// ==============================
//  		Proxy 慢查
// ==============================

type ProxySlowlog struct {
	IP        string    `json:"ip"`
	Port      int       `json:"port"`
	Cmd       string    `json:"cmd"`
	Duration  int       `json:"duration"`
	Server    string    `json:"server"`
	ReqBytes  int       `json:"reqBytes"`
	RspBytes  int       `json:"rspBytes"`
	HappendAt time.Time `json:"happendAt"`
}

// proxy慢查记录落库
func ReportProxySlowlog(c *gin.Context) {
	var args ProxySlowlog
	err := c.ShouldBindJSON(&args)
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}

	db, err := mysql.Database()
	if err != nil {
		logger.Error("failed to get mysql connection, error=(%v)", err)
		gintool.JSON(c, nil, errs.CodeDatabase.Append(err.Error()))
		return
	}

	ctx, cancel := mysql.ContextWithTimeout()
	_, err = db.ProxySlowlog.Create().
		SetIP(args.IP).
		SetPort(args.Port).
		SetCmd(args.Cmd).
		SetDuration(args.Duration).
		SetServer(args.Server).
		SetReqBytes(args.ReqBytes).
		SetRspBytes(args.RspBytes).
		SetHappendAt(args.HappendAt).
		Save(ctx)
	cancel()
	if err != nil {
		logger.Error("failed to create ProxySlowlog, error=(%v)", err)
		gintool.JSON(c, nil, errs.CodeDatabase.Append(err.Error()))
		return
	}

	gintool.JSON(c, "ok", nil)
}

// ==============================
//  		Redis 慢查
// ==============================

type RedisSlowlog struct {
	LogId     int64     `json:"logId"`
	IP        string    `json:"ip"`
	Port      int       `json:"port"`
	Cmd       string    `json:"cmd"`
	Duration  int       `json:"duration"`
	Client    string    `json:"client"`
	HappendAt time.Time `json:"happendAt"`
}

// redis慢查记录落库
func ReportRedisSlowlog(c *gin.Context) {
	var args RedisSlowlog
	err := c.ShouldBindJSON(&args)
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}

	db, err := mysql.Database()
	if err != nil {
		logger.Error("failed to get mysql connection, error=(%v)", err)
		gintool.JSON(c, nil, errs.CodeDatabase.Append(err.Error()))
	}

	ctx, cancel := mysql.ContextWithTimeout()
	err = db.RedisSlowlog.Create().
		SetLogID(args.LogId).
		SetIP(args.IP).
		SetPort(args.Port).
		SetCmd(args.Cmd).
		SetDuration(args.Duration).
		SetClient(args.Client).
		SetHappendAt(args.HappendAt).
		OnConflict().DoNothing().Exec(ctx)
	cancel()
	if err != nil {
		logger.Error("failed to create RedisSlowlog, error=(%v)", err)
		gintool.JSON(c, nil, errs.CodeDatabase.Append(err.Error()))
	}

	gintool.JSON(c, "ok", nil)
}
