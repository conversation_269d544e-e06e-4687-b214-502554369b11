package monitor

import (
	"github.com/gin-gonic/gin"

	"dt-common/errs"
	"dt-common/gintool"
	"dt-common/logger"
	"redis-cmanager/model/inspection"
)

// proxy探测，判断是否存活
func ProxyProber(c *gin.Context) {
	type schema struct {
		IP   string `json:"ip"`
		Port int    `json:"port"`
	}

	var args schema
	err := c.ShouldBindJSON(&args)
	if err != nil {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}

	// 获取该proxy最新的探测结果，不存在时报错
	alive, err := inspection.GetLastProberResult(args.IP, args.Port)
	if err != nil {
		logger.Error("%v", err)
		gintool.JSON(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}

	gintool.JSON(c, alive, nil)
}
