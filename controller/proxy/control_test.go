package proxy

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/http/httptest"
	"os"
	"strconv"
	"strings"
	"testing"

	"github.com/gin-gonic/gin"

	"dt-common/errs"
	"dt-common/utils"
	"redis-agent/env"
)

func TestExecCommandHelper(t *testing.T) {
	if os.Getenv("GO_WANT_HELPER_PROCESS") != "1" {
		return
	}

	// println("Mocked stdout:", os.Getenv("STDOUT"))
	fmt.Fprint(os.Stdout, os.Getenv("STDOUT"))
	i, _ := strconv.Atoi(os.Getenv("EXIT_STATUS"))
	os.Exit(i)
}

// 单侧：启动proxy
func TestStart(t *testing.T) {
	env.Mock(t, "../../mock/config/config.yaml")
	gin.SetMode(gin.TestMode)

	utils.ActivateExecMock()
	defer utils.DeactivateExecMock()

	type args struct {
		c *gin.Context
		w *httptest.ResponseRecorder
	}
	tests := []struct {
		name   string
		before func()
		args   args
		expect func(*testing.T, *httptest.ResponseRecorder)
	}{
		{
			name: "test1: check args",
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Request = &http.Request{
					Body: io.NopCloser(strings.NewReader("-1")),
				}

				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] == string(errs.Success) {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
		{
			name: "test2: start failed",
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)

				params := InstanceRequest{
					Port: 8001,
				}
				b, _ := json.Marshal(params)
				c.Request = &http.Request{
					Body: io.NopCloser(strings.NewReader(string(b))),
				}

				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] == string(errs.Success) {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
		{
			name: "test3: is alive",
			before: func() {
				utils.ExpectExec(0, "")
			},
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)

				params := InstanceRequest{
					Port: 8001,
				}
				b, _ := json.Marshal(params)
				c.Request = &http.Request{
					Body: io.NopCloser(strings.NewReader(string(b))),
				}

				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] != string(errs.Success) {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			Start(tt.args.c)
			if tt.expect != nil {
				tt.expect(t, tt.args.w)
			}
		})
	}
}

// 单侧：关停proxy
func TestStop(t *testing.T) {
	env.Mock(t, "../../mock/config/config.yaml")
	gin.SetMode(gin.TestMode)

	utils.ActivateExecMock()
	defer utils.DeactivateExecMock()

	type args struct {
		c *gin.Context
		w *httptest.ResponseRecorder
	}
	tests := []struct {
		name   string
		before func()
		args   args
		expect func(*testing.T, *httptest.ResponseRecorder)
	}{
		{
			name: "test1: check args",
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Request = &http.Request{
					Body: io.NopCloser(strings.NewReader("-1")),
				}

				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] == string(errs.Success) {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
		{
			name: "test2: already stopped",
			before: func() {
				utils.ExpectExec(-1, "")
			},
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)

				params := InstanceRequest{
					Port: 8002,
				}
				b, _ := json.Marshal(params)
				c.Request = &http.Request{
					Body: io.NopCloser(strings.NewReader(string(b))),
				}

				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] != string(errs.Success) {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
		{
			name: "test3: get proxy monitor failed",
			before: func() {
				utils.ExpectExec(0, "")
				utils.ExpectExec(-1, "")
			},
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)

				params := InstanceRequest{
					Port: 8001,
				}
				b, _ := json.Marshal(params)
				c.Request = &http.Request{
					Body: io.NopCloser(strings.NewReader(string(b))),
				}

				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] == string(errs.Success) {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
		{
			name: "test4: still has qps",
			before: func() {
				utils.ExpectExec(0, "")
				utils.ExpectExec(0, `{"service":"dxm-nutcracker", "source":"kafka0003.kj01.bddx.dxm-int.com","version":"2.0.0.2", "uptime":263854, "timestamp":1722228664, "proxy_qps":0, "proxy_read_qps":0, "proxy_write_qps":0, "r3_test_3": {"total_qps":0, "real_read_qps":10, "real_write_qps":10, "server1": {},"server2": {}}}`)
			},
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)

				params := InstanceRequest{
					Port: 8001,
				}
				b, _ := json.Marshal(params)
				c.Request = &http.Request{
					Body: io.NopCloser(strings.NewReader(string(b))),
				}

				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] == string(errs.Success) {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
		{
			name: "test5: get connections failed",
			before: func() {
				utils.ExpectExec(0, "")
				utils.ExpectExec(0, `{"service":"dxm-nutcracker", "source":"kafka0003.kj01.bddx.dxm-int.com","version":"2.0.0.2", "uptime":263854, "timestamp":1722228664, "proxy_qps":0, "proxy_read_qps":0, "proxy_write_qps":0, "r3_test_3": {"total_qps":0, "real_read_qps":0, "real_write_qps":0, "server1": {},"server2": {}}}`)
				utils.ExpectExec(1, "10.32.140.127\n10.32.140.183\n10.32.140.73")
			},
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)

				params := InstanceRequest{
					Port: 8001,
				}
				b, _ := json.Marshal(params)
				c.Request = &http.Request{
					Body: io.NopCloser(strings.NewReader(string(b))),
				}

				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] == string(errs.Success) {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
		{
			name: "test6: has connections",
			before: func() {
				utils.ExpectExec(0, "")
				utils.ExpectExec(0, `{"service":"dxm-nutcracker", "source":"kafka0003.kj01.bddx.dxm-int.com", "version":"2.0.0.2", "uptime":263854, "timestamp":1722228664, "proxy_qps":0, "proxy_read_qps":0, "proxy_write_qps":0, "r3_test_3": {"client_eof":105447, "client_err":2, "client_connections":1, "total_qps":0, "real_read_qps":0, "real_write_qps":0, "unknown_cmd_qps":0, "del_cmd_qps":0, "exists_cmd_qps":0, "expire_cmd_qps":0, "expireat_cmd_qps":0, "pexpire_cmd_qps":0, "pexpireat_cmd_qps":0, "persist_cmd_qps":0, "pttl_cmd_qps":0, "ttl_cmd_qps":0, "type_cmd_qps":0, "append_cmd_qps":0, "bitcount_cmd_qps":0, "decr_cmd_qps":0, "decrby_cmd_qps":0, "dump_cmd_qps":0, "get_cmd_qps":0, "getbit_cmd_qps":0, "getrange_cmd_qps":0, "getset_cmd_qps":0, "incr_cmd_qps":0, "incrby_cmd_qps":0, "incrbyfloat_cmd_qps":0, "mget_cmd_qps":0, "psetex_cmd_qps":0, "restore_cmd_qps":0, "set_cmd_qps":0, "setbit_cmd_qps":0, "setex_cmd_qps":0, "setnx_cmd_qps":0, "setrange_cmd_qps":0, "strlen_cmd_qps":0, "hdel_cmd_qps":0, "hexists_cmd_qps":0, "hget_cmd_qps":0, "hgetall_cmd_qps":0, "hincrby_cmd_qps":0, "hincrbyfloat_cmd_qps":0, "hkeys_cmd_qps":0, "hlen_cmd_qps":0, "hmget_cmd_qps":0, "hmset_cmd_qps":0, "hset_cmd_qps":0, "hsetnx_cmd_qps":0, "hvals_cmd_qps":0, "lindex_cmd_qps":0, "linsert_cmd_qps":0, "llen_cmd_qps":0, "lpop_cmd_qps":0, "lpush_cmd_qps":0, "lpushx_cmd_qps":0, "lrange_cmd_qps":0, "lrem_cmd_qps":0, "lset_cmd_qps":0, "ltrim_qps":0, "rpop_cmd_qps":0, "rpoplpush_cmd_qps":0, "rpush_cmd_qps":0, "rpushx_cmd_qps":0, "sadd_cmd_qps":0, "scard_cmd_qps":0, "sdiff_cmd_qps":0, "sdiffstore_cmd_qps":0, "sinter_cmd_qps":0, "sinterstore_cmd_qps":0, "sismember_cmd_qps":0, "smembers_cmd_qps":0, "smove_cmd_qps":0, "spop_cmd_qps":0, "srandmember_cmd_qps":0, "srem_cmd_qps":0, "sunion_cmd_qps":0, "sunionstore_cmd_qps":0, "zadd_cmd_qps":0, "zcard_cmd_qps":0, "zcount_cmd_qps":0, "zincrby_cmd_qps":0, "zinterstore_cmd_qps":0, "zrange_cmd_qps":0, "zrangebyscore_cmd_qps":0, "zrank_cmd_qps":0, "zrem_cmd_qps":0, "zremrangebyrank_cmd_qps":0, "zremrangebyscore_cmd_qps":0, "zrevrange_cmd_qps":0, "zrevrangebyscore_cmd_qps":0, "zrevrank_cmd_qps":0, "zscore_cmd_qps":0, "zunionstore_cmd_qps":0, "eval_cmd_qps":0, "evalsha_cmd_qps":0, "mset_cmd_qps":0, "ping_cmd_qps":0, "quit_cmd_qps":0, "sort_cmd_qps":0, "pfcount_cmd_qps":0, "zlexcount_cmd_qps":0, "zremrangebylex_cmd_qps":0, "hscan_cmd_qps":0, "sscan_cmd_qps":0, "pfadd_cmd_qps":0, "pfmerge_cmd_qps":0, "zrangebylex_cmd_qps":0, "zscan_cmd_qps":0, "flushdb_cmd_qps":0, "flushall_cmd_qps":0, "total_rsp_time":0, "real_rsp_time":0, "unknown_cmd_rsp_time":0, "del_cmd_rsp_time":0, "exists_cmd_rsp_time":0, "expire_cmd_rsp_time":0, "expireat_cmd_rsp_time":0, "pexpire_cmd_rsp_time":0, "pexpireat_cmd_rsp_time":0, "persist_cmd_rsp_time":0, "pttl_cmd_rsp_time":0, "ttl_cmd_rsp_time":0, "type_cmd_rsp_time":0, "append_cmd_rsp_time":0, "bitcount_cmd_rsp_time":0, "decr_cmd_rsp_time":0, "decrby_cmd_rsp_time":0, "dump_cmd_rsp_time":0, "get_cmd_rsp_time":0, "getbit_cmd_rsp_time":0, "getrange_cmd_rsp_time":0, "getset_cmd_rsp_time":0, "incr_cmd_rsp_time":0, "incrby_cmd_rsp_time":0, "incrbyfloat_cmd_rsp_time":0, "mget_cmd_rsp_time":0, "psetex_cmd_rsp_time":0, "restore_cmd_rsp_time":0, "set_cmd_rsp_time":0, "setbit_cmd_rsp_time":0, "setex_cmd_rsp_time":0, "setnx_cmd_rsp_time":0, "setrange_cmd_rsp_time":0, "strlen_cmd_rsp_time":0, "hdel_cmd_rsp_time":0, "hexists_cmd_rsp_time":0, "hget_cmd_rsp_time":0, "hgetall_cmd_rsp_time":0, "hincrby_cmd_rsp_time":0, "hincrbyfloat_cmd_rsp_time":0, "hkeys_cmd_rsp_time":0, "hlen_cmd_rsp_time":0, "hmget_cmd_rsp_time":0, "hmset_cmd_rsp_time":0, "hset_cmd_rsp_time":0, "hsetnx_cmd_rsp_time":0, "hvals_cmd_rsp_time":0, "lindex_cmd_rsp_time":0, "linsert_cmd_rsp_time":0, "llen_cmd_rsp_time":0, "lpop_cmd_rsp_time":0, "lpush_cmd_rsp_time":0, "lpushx_cmd_rsp_time":0, "lrange_cmd_rsp_time":0, "lrem_cmd_rsp_time":0, "lset_cmd_rsp_time":0, "ltrim_rsp_time":0, "rpop_cmd_rsp_time":0, "rpoplpush_cmd_rsp_time":0, "rpush_cmd_rsp_time":0, "rpushx_cmd_rsp_time":0, "sadd_cmd_rsp_time":0, "scard_cmd_rsp_time":0, "sdiff_cmd_rsp_time":0, "sdiffstore_cmd_rsp_time":0, "sinter_cmd_rsp_time":0, "sinterstore_cmd_rsp_time":0, "sismember_cmd_rsp_time":0, "smembers_cmd_rsp_time":0, "smove_cmd_rsp_time":0, "spop_cmd_rsp_time":0, "srandmember_cmd_rsp_time":0, "srem_cmd_rsp_time":0, "sunion_cmd_rsp_time":0, "sunionstore_cmd_rsp_time":0, "zadd_cmd_rsp_time":0, "zcard_cmd_rsp_time":0, "zcount_cmd_rsp_time":0, "zincrby_cmd_rsp_time":0, "zinterstore_cmd_rsp_time":0, "zrange_cmd_rsp_time":0, "zrangebyscore_cmd_rsp_time":0, "zrank_cmd_rsp_time":0, "zrem_cmd_rsp_time":0, "zremrangebyrank_cmd_rsp_time":0, "zremrangebyscore_cmd_rsp_time":0, "zrevrange_cmd_rsp_time":0, "zrevrangebyscore_cmd_rsp_time":0, "zrevrank_cmd_rsp_time":0, "zscore_cmd_rsp_time":0, "zunionstore_cmd_rsp_time":0, "eval_cmd_rsp_time":0, "evalsha_cmd_rsp_time":0, "mset_cmd_rsp_time":0, "ping_cmd_rsp_time":0, "quit_cmd_rsp_time":0, "sort_cmd_rsp_time":0, "pfcount_cmd_rsp_time":0, "zlexcount_cmd_rsp_time":0, "zremrangebylex_cmd_rsp_time":0, "hscan_cmd_rsp_time":0, "sscan_cmd_rsp_time":0, "pfadd_cmd_rsp_time":0, "pfmerge_cmd_rsp_time":0, "zrangebylex_cmd_rsp_time":0, "zscan_cmd_rsp_time":0, "flushdb_cmd_rsp_time":0, "flushall_cmd_rsp_time":0, "server_ejects":0, "forward_error":0, "fragments":0, "slowlog_cnt":0, "server1": {"server_eof":0, "server_err":1, "server_timedout":0, "server_connections":0, "requests":0, "request_bytes":0, "responses":0, "response_bytes":0, "in_queue":0, "in_queue_bytes":0, "out_queue":0, "out_queue_bytes":0, "slow_cmd_count":0},"server2": {"server_eof":0, "server_err":1, "server_timedout":0, "server_connections":0, "requests":0, "request_bytes":0, "responses":0, "response_bytes":0, "in_queue":0, "in_queue_bytes":0, "out_queue":0, "out_queue_bytes":0, "slow_cmd_count":0}}}`)
				utils.ExpectExec(0, "10.32.140.127\n10.32.140.183\n10.32.140.73")
			},
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)

				params := InstanceRequest{
					Port: 8001,
				}
				b, _ := json.Marshal(params)
				c.Request = &http.Request{
					Body: io.NopCloser(strings.NewReader(string(b))),
				}

				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] == string(errs.Success) {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
		{
			name: "test7: stop failed",
			before: func() {
				utils.ExpectExec(0, "")
				utils.ExpectExec(0, `{"service":"dxm-nutcracker", "source":"kafka0003.kj01.bddx.dxm-int.com", "version":"2.0.0.2", "uptime":263854, "timestamp":1722228664, "proxy_qps":0, "proxy_read_qps":0, "proxy_write_qps":0, "r3_test_3": {"client_eof":105447, "client_err":2, "client_connections":1, "total_qps":0, "real_read_qps":0, "real_write_qps":0, "unknown_cmd_qps":0, "del_cmd_qps":0, "exists_cmd_qps":0, "expire_cmd_qps":0, "expireat_cmd_qps":0, "pexpire_cmd_qps":0, "pexpireat_cmd_qps":0, "persist_cmd_qps":0, "pttl_cmd_qps":0, "ttl_cmd_qps":0, "type_cmd_qps":0, "append_cmd_qps":0, "bitcount_cmd_qps":0, "decr_cmd_qps":0, "decrby_cmd_qps":0, "dump_cmd_qps":0, "get_cmd_qps":0, "getbit_cmd_qps":0, "getrange_cmd_qps":0, "getset_cmd_qps":0, "incr_cmd_qps":0, "incrby_cmd_qps":0, "incrbyfloat_cmd_qps":0, "mget_cmd_qps":0, "psetex_cmd_qps":0, "restore_cmd_qps":0, "set_cmd_qps":0, "setbit_cmd_qps":0, "setex_cmd_qps":0, "setnx_cmd_qps":0, "setrange_cmd_qps":0, "strlen_cmd_qps":0, "hdel_cmd_qps":0, "hexists_cmd_qps":0, "hget_cmd_qps":0, "hgetall_cmd_qps":0, "hincrby_cmd_qps":0, "hincrbyfloat_cmd_qps":0, "hkeys_cmd_qps":0, "hlen_cmd_qps":0, "hmget_cmd_qps":0, "hmset_cmd_qps":0, "hset_cmd_qps":0, "hsetnx_cmd_qps":0, "hvals_cmd_qps":0, "lindex_cmd_qps":0, "linsert_cmd_qps":0, "llen_cmd_qps":0, "lpop_cmd_qps":0, "lpush_cmd_qps":0, "lpushx_cmd_qps":0, "lrange_cmd_qps":0, "lrem_cmd_qps":0, "lset_cmd_qps":0, "ltrim_qps":0, "rpop_cmd_qps":0, "rpoplpush_cmd_qps":0, "rpush_cmd_qps":0, "rpushx_cmd_qps":0, "sadd_cmd_qps":0, "scard_cmd_qps":0, "sdiff_cmd_qps":0, "sdiffstore_cmd_qps":0, "sinter_cmd_qps":0, "sinterstore_cmd_qps":0, "sismember_cmd_qps":0, "smembers_cmd_qps":0, "smove_cmd_qps":0, "spop_cmd_qps":0, "srandmember_cmd_qps":0, "srem_cmd_qps":0, "sunion_cmd_qps":0, "sunionstore_cmd_qps":0, "zadd_cmd_qps":0, "zcard_cmd_qps":0, "zcount_cmd_qps":0, "zincrby_cmd_qps":0, "zinterstore_cmd_qps":0, "zrange_cmd_qps":0, "zrangebyscore_cmd_qps":0, "zrank_cmd_qps":0, "zrem_cmd_qps":0, "zremrangebyrank_cmd_qps":0, "zremrangebyscore_cmd_qps":0, "zrevrange_cmd_qps":0, "zrevrangebyscore_cmd_qps":0, "zrevrank_cmd_qps":0, "zscore_cmd_qps":0, "zunionstore_cmd_qps":0, "eval_cmd_qps":0, "evalsha_cmd_qps":0, "mset_cmd_qps":0, "ping_cmd_qps":0, "quit_cmd_qps":0, "sort_cmd_qps":0, "pfcount_cmd_qps":0, "zlexcount_cmd_qps":0, "zremrangebylex_cmd_qps":0, "hscan_cmd_qps":0, "sscan_cmd_qps":0, "pfadd_cmd_qps":0, "pfmerge_cmd_qps":0, "zrangebylex_cmd_qps":0, "zscan_cmd_qps":0, "flushdb_cmd_qps":0, "flushall_cmd_qps":0, "total_rsp_time":0, "real_rsp_time":0, "unknown_cmd_rsp_time":0, "del_cmd_rsp_time":0, "exists_cmd_rsp_time":0, "expire_cmd_rsp_time":0, "expireat_cmd_rsp_time":0, "pexpire_cmd_rsp_time":0, "pexpireat_cmd_rsp_time":0, "persist_cmd_rsp_time":0, "pttl_cmd_rsp_time":0, "ttl_cmd_rsp_time":0, "type_cmd_rsp_time":0, "append_cmd_rsp_time":0, "bitcount_cmd_rsp_time":0, "decr_cmd_rsp_time":0, "decrby_cmd_rsp_time":0, "dump_cmd_rsp_time":0, "get_cmd_rsp_time":0, "getbit_cmd_rsp_time":0, "getrange_cmd_rsp_time":0, "getset_cmd_rsp_time":0, "incr_cmd_rsp_time":0, "incrby_cmd_rsp_time":0, "incrbyfloat_cmd_rsp_time":0, "mget_cmd_rsp_time":0, "psetex_cmd_rsp_time":0, "restore_cmd_rsp_time":0, "set_cmd_rsp_time":0, "setbit_cmd_rsp_time":0, "setex_cmd_rsp_time":0, "setnx_cmd_rsp_time":0, "setrange_cmd_rsp_time":0, "strlen_cmd_rsp_time":0, "hdel_cmd_rsp_time":0, "hexists_cmd_rsp_time":0, "hget_cmd_rsp_time":0, "hgetall_cmd_rsp_time":0, "hincrby_cmd_rsp_time":0, "hincrbyfloat_cmd_rsp_time":0, "hkeys_cmd_rsp_time":0, "hlen_cmd_rsp_time":0, "hmget_cmd_rsp_time":0, "hmset_cmd_rsp_time":0, "hset_cmd_rsp_time":0, "hsetnx_cmd_rsp_time":0, "hvals_cmd_rsp_time":0, "lindex_cmd_rsp_time":0, "linsert_cmd_rsp_time":0, "llen_cmd_rsp_time":0, "lpop_cmd_rsp_time":0, "lpush_cmd_rsp_time":0, "lpushx_cmd_rsp_time":0, "lrange_cmd_rsp_time":0, "lrem_cmd_rsp_time":0, "lset_cmd_rsp_time":0, "ltrim_rsp_time":0, "rpop_cmd_rsp_time":0, "rpoplpush_cmd_rsp_time":0, "rpush_cmd_rsp_time":0, "rpushx_cmd_rsp_time":0, "sadd_cmd_rsp_time":0, "scard_cmd_rsp_time":0, "sdiff_cmd_rsp_time":0, "sdiffstore_cmd_rsp_time":0, "sinter_cmd_rsp_time":0, "sinterstore_cmd_rsp_time":0, "sismember_cmd_rsp_time":0, "smembers_cmd_rsp_time":0, "smove_cmd_rsp_time":0, "spop_cmd_rsp_time":0, "srandmember_cmd_rsp_time":0, "srem_cmd_rsp_time":0, "sunion_cmd_rsp_time":0, "sunionstore_cmd_rsp_time":0, "zadd_cmd_rsp_time":0, "zcard_cmd_rsp_time":0, "zcount_cmd_rsp_time":0, "zincrby_cmd_rsp_time":0, "zinterstore_cmd_rsp_time":0, "zrange_cmd_rsp_time":0, "zrangebyscore_cmd_rsp_time":0, "zrank_cmd_rsp_time":0, "zrem_cmd_rsp_time":0, "zremrangebyrank_cmd_rsp_time":0, "zremrangebyscore_cmd_rsp_time":0, "zrevrange_cmd_rsp_time":0, "zrevrangebyscore_cmd_rsp_time":0, "zrevrank_cmd_rsp_time":0, "zscore_cmd_rsp_time":0, "zunionstore_cmd_rsp_time":0, "eval_cmd_rsp_time":0, "evalsha_cmd_rsp_time":0, "mset_cmd_rsp_time":0, "ping_cmd_rsp_time":0, "quit_cmd_rsp_time":0, "sort_cmd_rsp_time":0, "pfcount_cmd_rsp_time":0, "zlexcount_cmd_rsp_time":0, "zremrangebylex_cmd_rsp_time":0, "hscan_cmd_rsp_time":0, "sscan_cmd_rsp_time":0, "pfadd_cmd_rsp_time":0, "pfmerge_cmd_rsp_time":0, "zrangebylex_cmd_rsp_time":0, "zscan_cmd_rsp_time":0, "flushdb_cmd_rsp_time":0, "flushall_cmd_rsp_time":0, "server_ejects":0, "forward_error":0, "fragments":0, "slowlog_cnt":0, "server1": {"server_eof":0, "server_err":1, "server_timedout":0, "server_connections":0, "requests":0, "request_bytes":0, "responses":0, "response_bytes":0, "in_queue":0, "in_queue_bytes":0, "out_queue":0, "out_queue_bytes":0, "slow_cmd_count":0},"server2": {"server_eof":0, "server_err":1, "server_timedout":0, "server_connections":0, "requests":0, "request_bytes":0, "responses":0, "response_bytes":0, "in_queue":0, "in_queue_bytes":0, "out_queue":0, "out_queue_bytes":0, "slow_cmd_count":0}}}`)
				utils.ExpectExec(0, "")
			},
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)

				params := InstanceRequest{
					Port:  8001,
					Force: true,
				}
				b, _ := json.Marshal(params)
				c.Request = &http.Request{
					Body: io.NopCloser(strings.NewReader(string(b))),
				}

				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] != string(errs.Success) {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			Stop(tt.args.c)
			if tt.expect != nil {
				tt.expect(t, tt.args.w)
			}
		})
	}
}
