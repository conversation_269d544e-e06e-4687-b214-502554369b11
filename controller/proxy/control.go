package proxy

import (
	"github.com/gin-gonic/gin"

	"dt-common/errs"
	"dt-common/gintool"
	"dt-common/logger"
	"redis-agent/library/errc"
	"redis-agent/model/worker"
)

type InstanceRequest struct {
	Port  int  `form:"port" json:"port" binding:"required"`
	Force bool `form:"force" json:"force,omitempty"` // 强制关停
}

// proxy 启动
func Start(c *gin.Context) {
	// 解析路由参数
	var params InstanceRequest
	err := c.ShouldBindJSON(&params)
	if err != nil {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Append(err.Error()))
		return
	}

	// 判断进程存活状态
	alive := worker.IsProxyAlive(params.Port)
	if alive {
		logger.Info("proxy with port %d already started", params.Port)
		gintool.JSON(c, "success", nil)
		return
	}

	// 启动proxy
	err = worker.ProxyControl(params.Port, worker.OperationStart)
	if err != nil {
		logger.Error("failed to start proxy, port=%d, error=%v", params.Port, err)
		gintool.JSON(c, nil, errc.CodeOperationError.Append(err.Error()))
		return
	}

	gintool.JSON(c, "success", nil)
}

// proxy 关停
func Stop(c *gin.Context) {
	// 解析路由参数
	var params InstanceRequest
	err := c.ShouldBindJSON(&params)
	if err != nil {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Append(err.Error()))
		return
	}

	// 判断进程存活状态
	alive := worker.IsProxyAlive(params.Port)
	if !alive {
		logger.Info("proxy with port %d already stopped", params.Port)
		gintool.JSON(c, "success", nil)
		return
	}

	// 检查流量
	monitor, err := worker.GetProxyMonitor(params.Port)
	if err != nil {
		logger.Error("failed to get proxy qps, port=%d, error=%v", params.Port, err)
		gintool.JSON(c, nil, errs.CodeRequestFailed.Append(err.Error()))
		return
	}
	if monitor.Pool.RealWriteQPS != 0 || monitor.Pool.RealReadQPS != 0 {
		gintool.JSON(c, nil, errc.CodeOperationError.Append("proxy still has qps, can not stop"))
		return
	}

	// 有客户端（如BDRP）即便没有请求qps，proxy屏蔽后还是会连上来，所以增加一个强制关停选项，跳过长连接检查
	if !params.Force {
		conns, err := worker.GetConnections(params.Port)
		if err != nil {
			logger.Error("failed to get connections, port=%d, error=%v", params.Port, err)
			gintool.JSON(c, nil, errs.CodeRequestFailed.Append(err.Error()))
			return
		}
		if len(conns) != 0 {
			logger.Warn("proxy %d still has connections, can not stop, connections=%v", params.Port, conns)
			gintool.JSON(c, nil, errs.CodeRequestFailed.Append("proxy still has connections, can not stop"))
			return
		}
	}

	// 关停proxy
	err = worker.ProxyControl(params.Port, worker.OperationStop)
	if err != nil {
		logger.Error("failed to stop proxy, port=%d, error=%v", params.Port, err)
		gintool.JSON(c, nil, errc.CodeOperationError.Append(err.Error()))
		return
	}

	gintool.JSON(c, "success", nil)
}

// 获取proxy的存活状态
func Status(c *gin.Context) {
	// 解析路由参数
	var params InstanceRequest
	err := c.ShouldBindQuery(&params)
	if err != nil {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Append(err.Error()))
		return
	}

	gintool.JSON(c, worker.IsProxyAlive(params.Port), nil)
}

// 获取proxy拓扑结构
func Topo(c *gin.Context) {
	// 解析路由参数
	var params InstanceRequest
	err := c.ShouldBindQuery(&params)
	if err != nil {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Append(err.Error()))
		return
	}

	servers, err := worker.GetProxyTopo(params.Port)
	if err != nil {
		gintool.JSON(c, nil, errc.CodeOperationError.Append(err.Error()))
		return
	}

	gintool.JSON(c, servers, nil)
}

// 获取proxy的qps和长连接
func Connections(c *gin.Context) {
	// 解析路由参数
	var params InstanceRequest
	err := c.ShouldBindQuery(&params)
	if err != nil {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Append(err.Error()))
		return
	}

	// 检查QPS
	monitor, err := worker.GetProxyMonitor(params.Port)
	if err != nil {
		logger.Error("failed to get proxy qps, port=%d, error=%v", params.Port, err)
		gintool.JSON(c, nil, errs.CodeRequestFailed.Append(err.Error()))
		return
	}

	// 检查长链接
	conns, err := worker.GetConnections(params.Port)
	if err != nil {
		logger.Error("failed to get connections, port=%d, error=%v", params.Port, err)
		gintool.JSON(c, nil, errs.CodeRequestFailed.Append(err.Error()))
		return
	}

	gintool.JSON(c, gin.H{
		"qps":   monitor.Pool.RealReadQPS + monitor.Pool.RealWriteQPS,
		"conns": conns,
	}, nil)
}
