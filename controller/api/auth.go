package api

import (
	"strings"

	"github.com/gin-gonic/gin"

	"dt-common/errs"
	"dt-common/gintool"
	"dt-common/logger"
	"dt-common/user-center/authc"
	"redis-xweb/model"
)

// 创建角色
func CreateRole(c *gin.Context) {
	type schema struct {
		Name string `json:"role" binding:"required"`
		Type string `json:"type" binding:"required"`
	}

	// 解析路由参数
	var args schema
	err := c.ShouldBindJSON(&args)
	if err != nil {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail(err.<PERSON>rror()))
		return
	}

	roleID, err := authc.CreateRoleV2(&authc.Role{
		Name:   args.Name,
		Type:   args.Type,
		Alias:  args.Name,
		Params: map[string]string{},
	})
	if err != nil && !strings.Contains(err.<PERSON>rror(), "Duplicate") {
		gintool.JSON(c, nil, errs.CodeAuthCenterRequestFailed.Detail(err.Error()))
		return
	}

	gintool.JSON(c, roleID, nil)
}

// 为用户添加角色
func AddUserToRole(c *gin.Context) {
	type schema struct {
		Role  string   `json:"role" binding:"required"`
		Users []string `json:"users" binding:"required"`
	}

	// 解析路由参数
	var args schema
	err := c.ShouldBindJSON(&args)
	if err != nil {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}

	// role角色校验
	roleType := model.ROLE_TYPE_FLOW
	if args.Role != model.ROLE_FLOW_ENGINEER && args.Role != model.ROLE_FLOW_MANAGER {
		roleType = model.ROLE_TYPE_REDIS
	}

	roleData, err := authc.GetRoleByName(args.Role, roleType)
	if err != nil {
		logger.Warn("failed to get role detail, roleName=%s, error=(%v)", args.Role, err)
		gintool.JSON(c, nil, errs.CodeAuthCenterRequestFailed.Detail("角色详情获取失败", err.Error()))
		return
	}

	for _, username := range args.Users {
		err = authc.BindRoleToUserV2(username, roleType, &[]int{roleData.ID})
		if err != nil {
			logger.Warn("failed to add user to role, username=%s, roleName=%s, error=(%v) ", username, args.Role, err)
			gintool.JSON(c, nil, errs.CodeAuthCenterRequestFailed.Detail("用户添加失败", err.Error()))
			return
		}
	}

	gintool.JSON(c, "ok", nil)
}
