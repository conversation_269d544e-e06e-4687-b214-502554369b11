package api

import (
	"github.com/gin-gonic/gin"

	"dt-common/errs"
	"dt-common/gintool"
	"dt-common/logger"
	"redis-xweb/library/billing"
)

//!
//! 物理订单的调整可以在成本中心计费实例直接操作
//! 组合套餐的订单还只能用接口调整
//!

// 获取计费实例
func GetOrderInfo(c *gin.Context) {
	type schema struct {
		ClusterName string `form:"clusterName" binding:"required"`
	}

	// 解析路由参数
	var args schema
	err := c.ShouldBindQuery(&args)
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}

	// 获取订单信息
	instances, err := billing.GetInstanceInfos([]string{args.ClusterName})
	if err != nil {
		logger.Warn("failed to update order data, error=(%v)", err)
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}

	gintool.JSON2FE(c, instances, nil)
}

// 内存扩缩容
func MemorySizeChange(c *gin.Context) {
	type schema struct {
		ClusterName string `json:"clusterName" binding:"required"`
		StorageSize int    `json:"storageSize" binding:"required"`
	}

	// 解析路由参数
	var args schema
	err := c.ShouldBindJSON(&args)
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}

	// 更新订单
	err = billing.MemorySizeChange(args.ClusterName, args.StorageSize)
	if err != nil {
		logger.Warn("failed to update order data, error=(%v)", err)
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}

	gintool.JSON2FE(c, "ok", nil)
}

// 内存扩缩容
func ProxyNumChange(c *gin.Context) {
	type schema struct {
		ClusterName string `json:"clusterName" binding:"required"`
		ProxyNum    int    `json:"proxyNum" binding:"required"`
	}

	// 解析路由参数
	var args schema
	err := c.ShouldBindJSON(&args)
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}

	// 更新订单
	err = billing.ProxyNumChange(args.ClusterName, args.ProxyNum)
	if err != nil {
		logger.Warn("failed to update order data, error=(%v)", err)
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}

	gintool.JSON2FE(c, "ok", nil)
}
