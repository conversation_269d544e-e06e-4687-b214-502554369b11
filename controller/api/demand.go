package api

import (
	"fmt"

	"github.com/gin-gonic/gin"

	"dt-common/ent/cluster"
	"dt-common/ent/predicate"
	"dt-common/logger"
	"dt-common/mysql"
	"dt-common/omodel"
	"redis-xweb/config"
)

type ResponsibleItem struct {
	Title string `json:"title"`
	URL   string `json:"url"`
}

type ResponsibleDetail struct {
	Num         int                `json:"num"`
	Description string             `json:"desc"`
	Items       *[]ResponsibleItem `json:"items"`
}

type ResponsibleResponse struct {
	Result string               `json:"result"`
	Detail *[]ResponsibleDetail `json:"detail,omitempty"`
}

// 获取某个用户负责的集群，提供给离职资产排查用
func GetUserResponsibleClusters(c *gin.Context) {
	type schema struct {
		UserName string `form:"username" binding:"required"`
	}

	// 参数校验
	var args schema
	err := c.BindQuery(&args)
	if err != nil {
		c.JSON(200, gin.H{"code": "FAIL", "msg": "缺少参数username"})
		return
	}

	// 获取mysql连接
	db, err := mysql.Database()
	if err != nil {
		logger.Error("failed to get mysql connection, error=(%v)", err)
		c.JSON(200, gin.H{"code": "FAIL", "msg": "数据库连接失败"})
		return
	}

	// 整理查询条件
	predicates := []predicate.Cluster{
		cluster.StatusEQ(omodel.CLUSTER_STATUS_NORMAL),
		cluster.OwnerEQ(args.UserName),
	}

	// 查询符合条件的集群
	rows, err := db.Cluster.Query().Where(predicates...).All(c)
	if err != nil {
		logger.Error("failed to query cluster, error=(%v)", err)
		c.JSON(200, gin.H{"code": "FAIL", "msg": "查询失败"})
		return
	}

	// 如果没有负责的集群，返回pass
	if len(rows) == 0 {
		res := ResponsibleResponse{
			Result: "PASS",
		}
		c.JSON(200, gin.H{"code": "SUCCESS", "msg": "成功", "data": &res})
		return
	}

	host, err := config.GetString("application/fe_host")
	if err != nil {
		logger.Error("missing config application/fe_host")
		c.JSON(200, gin.H{"code": "FAIL", "msg": "查询失败"})
		return
	}

	items := make([]ResponsibleItem, len(rows))
	for i, row := range rows {
		items[i] = ResponsibleItem{
			Title: row.Alias,
			URL:   fmt.Sprintf("%s/redisClusterDetail?clusterId=%d", host, row.ID),
		}
	}

	res := ResponsibleResponse{
		Result: "FAIL",
		Detail: &[]ResponsibleDetail{{
			Num:         len(rows),
			Description: "负责的Redis集群",
			Items:       &items,
		}},
	}

	c.JSON(200, gin.H{"code": "SUCCESS", "msg": "成功", "data": &res})
}
