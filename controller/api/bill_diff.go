package api

import (
	"fmt"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"golang.org/x/sync/errgroup"

	"dt-common/ent"
	"dt-common/ent/cluster"
	"dt-common/ent/redis"
	"dt-common/ent/stage"
	"dt-common/errs"
	"dt-common/logger"
	"dt-common/mysql"
	"dt-common/omodel"
	"redis-xweb/config"
	"redis-xweb/library/billing"
)

// 分页结构
type paging struct {
	Total    int         `json:"total"`
	Page     int         `json:"page"`
	PageSize int         `json:"pageSize"`
	Rows     interface{} `json:"list"`
}

// 分页返回格式化
func formatPaging(rows []*BillingCluster, page int, pageSize int) *paging {
	sI := pageSize * (page - 1)
	eI := pageSize * page
	if eI > len(rows) {
		eI = len(rows)
	}

	currentPageRows := rows[sI:eI]

	return &paging{
		Total:    len(rows),
		Page:     page,
		PageSize: pageSize,
		Rows:     currentPageRows,
	}
}

type Response struct {
	ErrNo  int    `json:"errno"`
	ErrMsg string `json:"errmsg"`
	Data   any    `json:"data"` // 改为使用接口接收数据，因为返回结果可能是字符串
}

// 适用于与前端对接的返回值格式化方法
func JSON(c *gin.Context, data interface{}, err error) {
	var b Response
	// b.ReqID = c.MustGet("ReqID").(string)

	if err != nil {
		switch e := err.(type) {
		case *errs.HttpError:
			b.ErrNo = e.Code.Int()
			b.ErrMsg = e.Error()
			c.Writer.WriteHeader(e.Status)
		case errs.Code:
			b.ErrNo = e.Int()
			b.ErrMsg = e.Error()
			c.Writer.WriteHeader(e.Status())
		default:
			b.ErrNo = 1
			b.ErrMsg = e.Error()
			c.Writer.WriteHeader(http.StatusInternalServerError)
		}
	} else if data == nil {
		b.ErrNo = errs.CodeVoidReturn.Int()
		b.ErrMsg = "no return value"
		c.Writer.WriteHeader(http.StatusInternalServerError)
	} else {
		b.ErrNo = 0
		b.Data = data
	}

	c.JSON(http.StatusOK, b)
	c.Abort()
}

type ComboInfo struct {
	ComboName     string  `json:"comboName,omitempty" bson:"comboName,omitempty" description:"套餐名称"`
	ComboId       int     `json:"comboId,omitempty" bson:"comboId,omitempty" description:"套餐id"`
	ComboCode     int64   `json:"comboCode" bson:"comboCode,omitempty" description:"套餐code"`
	ComboDesc     string  `json:"comboDesc,omitempty" bson:"comboDesc" description:"套餐描述"`
	ComboUnitInfo string  `json:"comboUnitInfo,omitempty" bson:"comboUnitInfo" description:"套餐单元信息"`
	UnitPrice     float64 `json:"unitPrice,omitempty" bson:"unitPrice" description:"单位价格"`
	ComboNum      float64 `json:"comboNum" bson:"comboNum" description:"套餐数量"`
}

type BillingCluster struct {
	InstanceId        string         `json:"instanceId"`
	Owner             string         `json:"owner"`
	DepartCode        string         `json:"departCode"`
	Department        string         `json:"department"`
	StartTime         time.Time      `json:"startTime"`
	EndTime           time.Time      `json:"endTime"`
	ResourceId        int            `json:"resourceId"`
	ComboCode         string         `json:"comboCode"`
	SnapInfo          map[string]any `json:"snapInfo"`
	DynamicComboGroup []ComboInfo    `json:"dynamicComboGroup" bson:"dynamicComboGroup,omitempty" description:"动态套餐组"`
}

// 纯物理集群的订单
func formatBbcBill(cfg *billing.Config, clusterData *ent.Cluster) *BillingCluster {
	maxmemory := 0
	uniq := map[string]int{}
	for _, instance := range clusterData.Edges.Redis {
		if _, exist := uniq[instance.Name]; !exist {
			uniq[instance.Name] = 1
			maxmemory += instance.Maxmemory
		}
	}
	storageSize := int(float64(maxmemory) * 0.6 / 1024 / 1024 / 1024)

	return &BillingCluster{
		InstanceId: clusterData.Name,
		Owner:      clusterData.Owner,
		DepartCode: fmt.Sprintf("%d", clusterData.DepartmentID),
		Department: clusterData.Department,
		StartTime:  clusterData.CreatedAt,
		ResourceId: cfg.ResourceID,
		ComboCode:  fmt.Sprintf("%d", cfg.OldComboCode),
		SnapInfo: map[string]any{
			"num": float64(storageSize) / 4,
		},
	}
}

// 纯容器集群的订单
func formatDockerBills(cfg *billing.Config, clusterData *ent.Cluster, startTime time.Time) *BillingCluster {
	return &BillingCluster{
		InstanceId: clusterData.Name,
		Owner:      clusterData.Owner,
		DepartCode: fmt.Sprintf("%d", clusterData.DepartmentID),
		Department: clusterData.Department,
		StartTime:  startTime,
		ResourceId: cfg.ResourceID,
		ComboCode:  fmt.Sprintf("%d", cfg.MemoryComboCode),
		SnapInfo:   map[string]any{"num": clusterData.StorageSize},
		DynamicComboGroup: []ComboInfo{
			{ComboCode: cfg.ProxyComboCode, ComboNum: float64(clusterData.ProxyNum)},     // 代理订单
			{ComboCode: cfg.RedisComboCode, ComboNum: float64(clusterData.ShardNum)},     // 分片订单
			{ComboCode: cfg.MemoryComboCode, ComboNum: float64(clusterData.StorageSize)}, // 内存订单
			{ComboCode: cfg.SentinelComboCode, ComboNum: 3},                              // 哨兵订单
		},
	}
}

// 迁移中的订单
func formatMigrationBills(cfg *billing.Config, clusterData *ent.Cluster, startTime time.Time) *BillingCluster {
	maxmemory := 0
	uniq := map[string]int{}
	for _, instance := range clusterData.Edges.Redis {
		if _, exist := uniq[instance.Name]; !exist {
			uniq[instance.Name] = 1
			maxmemory += instance.Maxmemory
		}
	}
	storageSize := int(float64(maxmemory) * 0.6 / 1024 / 1024 / 1024)

	return &BillingCluster{
		InstanceId: clusterData.Name,
		Owner:      clusterData.Owner,
		DepartCode: fmt.Sprintf("%d", clusterData.DepartmentID),
		Department: clusterData.Department,
		StartTime:  startTime,
		ResourceId: cfg.ResourceID,
		ComboCode:  fmt.Sprintf("%d", cfg.MemoryComboCode),
		SnapInfo:   map[string]any{"num": clusterData.StorageSize},
		DynamicComboGroup: []ComboInfo{
			{ComboCode: cfg.OldComboCode, ComboNum: float64(storageSize) / 4},                // 物理订单
			{ComboCode: cfg.ProxyComboCode, ComboNum: float64(clusterData.ProxyNum) / 2},     // 代理订单
			{ComboCode: cfg.RedisComboCode, ComboNum: float64(clusterData.ShardNum) / 2},     // 分片订单
			{ComboCode: cfg.MemoryComboCode, ComboNum: float64(clusterData.StorageSize) / 2}, // 内存订单
			{ComboCode: cfg.SentinelComboCode, ComboNum: 1},                                  // 哨兵订单
		},
	}
}

// 获取计费实例列表
// 用于前端的统一账单diff
func GetBillingCluster(c *gin.Context) {
	type schema struct {
		Search       string `json:"search"`
		ResourceName string `json:"resourceName"`
		PageNum      int    `json:"pageNum"`
		PageSize     int    `json:"pageSize"`
	}

	// 参数校验
	var args schema
	err := c.ShouldBindJSON(&args)
	if err != nil {
		JSON(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}

	// 获取计费配置
	var billingConfig billing.Config
	err = config.Get("billing", &billingConfig)
	if err != nil {
		logger.Error("failed to query Cluster, error=(%v)", err)
		JSON(c, nil, errs.CodeDatabase.Detail("未找到该集群"))
		return
	}

	db, err := mysql.Database()
	if err != nil {
		logger.Error("failed to get mysql connection, error=(%v)", err)
		JSON(c, nil, errs.CodeDatabase.Detail("数据库连接获取失败"))
		return
	}
	// 并行查询
	g := &errgroup.Group{}
	var clusterList []*ent.Cluster
	var stageList []*ent.Stage
	g.Go(func() error {
		ctx, cancel := mysql.ContextWithTimeout()
		clusterList, err = db.Cluster.Query().Where(
			cluster.StatusNEQ(omodel.CLUSTER_STATUS_DELETED),
			cluster.AreaNEQ("credit"), // 过滤掉征信区的集群
		).WithRedis(func(q *ent.RedisQuery) {
			q.Select(redis.FieldName, redis.FieldMaxmemory).Where(redis.Docker(omodel.DEPLOY_ENV_BBC))
		}).All(ctx)
		cancel()
		if err != nil {
			logger.Error("failed to query clusters, error=(%v)", err)
			return err
		}
		return nil
	})
	g.Go(func() error {
		// 查询迁移阶段
		ctx, cancel := mysql.ContextWithTimeout()
		stageList, err = db.Stage.Query().Where(
			stage.Or(
				stage.Type(omodel.STAGE_TYPE_TRANSFORM_BILL_HALF),
				stage.Type(omodel.STAGE_TYPE_TRANSFORM_BILL_DOCKER),
			),
		).All(ctx)
		cancel()
		if err != nil {
			logger.Error("failed to query stages for billing, error=(%v)", err)
			return err
		}
		return nil
	})
	if err = g.Wait(); err != nil {
		JSON(c, nil, errs.CodeDatabase.Detail("数据查询失败"))
		return
	}

	// slice 转 map 方便查询
	stageMap := map[string]*ent.Stage{}
	for _, stageData := range stageList {
		stageMap[stageData.ClusterName+"--"+stageData.Type] = stageData
	}

	// 实例转换成订单
	// docker=0, dynamicComboGroup=0
	// docker=1，dynamicComboGroup=4
	// docker=2，dynamicComboGroup=5
	billResult := make([]*BillingCluster, len(clusterList))
	for i, clusterData := range clusterList {
		switch clusterData.Docker {
		case omodel.DEPLOY_ENV_BBC:
			billResult[i] = formatBbcBill(&billingConfig, clusterData)
		case omodel.DEPLOY_ENV_DOCKER:
			billResult[i] = formatDockerBills(&billingConfig, clusterData, clusterData.CreatedAt)
		case omodel.DEPLOY_ENV_MIGRATING:
			s1, exist := stageMap[clusterData.Name+"--"+omodel.STAGE_TYPE_TRANSFORM_BILL_HALF]
			if !exist {
				logger.Error("migrating cluster has no billing stage, cluster=%s", clusterData.Name)
				continue
			}
			// 按照物理订单算
			if s1.Status != string(omodel.STAGE_STATUS_DONE) {
				billResult[i] = formatBbcBill(&billingConfig, clusterData)
				continue
			}

			s2, exist := stageMap[clusterData.Name+"--"+omodel.STAGE_TYPE_TRANSFORM_BILL_DOCKER]
			if !exist {
				logger.Error("migrating cluster has no billing stage, cluster=%s", clusterData.Name)
				continue
			}
			// 按照容器订单算
			if s2.Status == string(omodel.STAGE_STATUS_DONE) {
				billResult[i] = formatDockerBills(&billingConfig, clusterData, s2.UpdatedAt)
				continue
			}

			// 按照迁移订单算
			billResult[i] = formatMigrationBills(&billingConfig, clusterData, s1.UpdatedAt)
		default:
			logger.Error("unknown cluster docker env, cluster=%s", clusterData.Name)
		}
	}

	JSON(c, formatPaging(billResult, args.PageNum, args.PageSize), nil)
}
