package api

import (
	"strings"

	"github.com/gin-gonic/gin"

	"dt-common/errs"
	"dt-common/gintool"
	"redis-cmanager/library/renderer"
)

// Pods打污点
func TaintPods(c *gin.Context) {
	type schema struct {
		ClusterName string   `json:"clusterName" binding:"required"`
		PodNames    []string `json:"podNames" binding:"required"`
	}
	var params schema
	err := c.ShouldBindJSON(&params)
	if err != nil {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}
	params.ClusterName = strings.ReplaceAll(params.ClusterName, "-", "_")

	// 打标签
	err = renderer.TaintPods(params.ClusterName, params.PodNames, true)
	if err != nil {
		gintool.JSON(c, nil, errs.CodeRequestFailed.Detail(err.<PERSON><PERSON>r()))
		return
	}

	gintool.JSON(c, "ok", nil)
}

func UnTaintPods(c *gin.Context) {
	type schema struct {
		ClusterName string   `json:"clusterName" binding:"required"`
		PodNames    []string `json:"podNames" binding:"required"`
	}
	var params schema
	err := c.ShouldBindJSON(&params)
	if err != nil {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}
	params.ClusterName = strings.ReplaceAll(params.ClusterName, "-", "_")

	// 打标签
	err = renderer.TaintPods(params.ClusterName, params.PodNames, false)
	if err != nil {
		gintool.JSON(c, nil, errs.CodeRequestFailed.Detail(err.Error()))
		return
	}

	gintool.JSON(c, "ok", nil)
}

// 删除PODS
func DeletePods(c *gin.Context) {
	type schema struct {
		ClusterName string   `json:"clusterName" binding:"required"`
		PodNames    []string `json:"podNames" binding:"required"`
		Force       bool     `json:"force" `
	}
	var params schema
	err := c.ShouldBindJSON(&params)
	if err != nil {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}
	params.ClusterName = strings.ReplaceAll(params.ClusterName, "-", "_")

	err = renderer.DeletePods(params.ClusterName, params.PodNames, params.Force)
	if err != nil {
		gintool.JSON(c, nil, errs.CodeRequestFailed.Detail(err.Error()))
		return
	}

	gintool.JSON(c, "ok", nil)
}
