package api

import (
	"encoding/json"

	"github.com/gin-gonic/gin"

	"dt-common/ent/cluster"
	"dt-common/errs"
	"dt-common/gintool"
	"dt-common/mysql"
	"redis-xweb/library/billing"
	"redis-xweb/model/autoflow"
)

// 集群内存扩缩容
// 用于在迁移前标准化集群分片maxmeory
// 分片quota取值范围 1-16GB，maxmeory取值范围 2-32GB
// storageSize一定是整数
func ClusterStorageChange(c *gin.Context) {
	type schema struct {
		ClusterName string `json:"clusterName"`
		StorageSize int    `json:"storageSize"`
	}

	//解析路由参数
	var args schema
	err := c.ShouldBindJSON(&args)
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}

	// 更新成本中心
	err = billing.MemorySizeChange(args.ClusterName, args.StorageSize)
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeBillCenterRequestFailed.Append(err.Error()))
		return
	}

	// 更新数据库
	db, err := mysql.Database()
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}
	ctx, cancel := mysql.ContextWithTimeout()
	err = db.Cluster.Update().SetStorageSize(args.StorageSize).Where(cluster.Name(args.ClusterName)).Exec(ctx)
	cancel()
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}

	gintool.JSON2FE(c, "ok", nil)
}

// proxy扩缩容
func ProxyScaling(c *gin.Context) {
	type schema struct {
		ClusterName string `json:"clusterName"`
		ProxyNum    int    `json:"proxyNum"` // 扩缩容后的proxy总数
		Description string `json:"description"`
	}

	// 解析路由参数
	var args schema
	err := c.ShouldBindJSON(&args)
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}

	// 查询集群
	db, err := mysql.Database()
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}
	ctx, cancel := mysql.ContextWithTimeout()
	clusterData, err := db.Cluster.Query().Where(cluster.Name(args.ClusterName)).Only(ctx)
	cancel()
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}

	// step2.2.获取Task&Stages参数
	var taskArgs *autoflow.TaskArgs
	paramStr, err := json.Marshal(args)
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}
	taskArgs, err = autoflow.ProxyScaling(args.ClusterName, string(paramStr))
	if err != nil {
		return
	}
	// step2.3.创建Task&Stages
	taskArgs.FlowArgs = &autoflow.FlowArgs{
		FlowInstanceID: 0,
		NodeInstanceID: 0,
		Applicant:      "jiayiming_dxm",
		Description:    args.Description,
		Operator:       "jiayiming_dxm",
		ClusterID:      clusterData.ID,
		ClusterName:    clusterData.Name,
	}

	_, err = autoflow.CreateTaskAndStages(taskArgs)
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}

	gintool.JSON2FE(c, "ok", nil)
}
