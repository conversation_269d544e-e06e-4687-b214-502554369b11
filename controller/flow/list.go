package flow

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"

	"dt-common/ent/flowapplycluster"
	"dt-common/ent/flowcluster"
	"dt-common/ent/flowtransfer"
	"dt-common/ent/flowwhitelist"
	"dt-common/mysql"
	"redis-xweb/model"
)

// 获取部署申请工单详情
func GetListDeploy(ctx context.Context, listID int) (map[string]any, error) {
	// 获取db client
	db, err := mysql.Database()
	if err != nil {
		return nil, err
	}

	// 获取工单记录
	flow, err := db.FlowCluster.Query().
		Where(flowcluster.ID(int64(listID))).
		Only(ctx)
	if err != nil {
		return nil, err
	}

	// 区域
	areaName, _ := model.GetAreaName(flow.Area)

	// 业务属性
	biz := make(map[string]float64)
	err = json.Unmarshal([]byte(flow.Business), &biz)
	if err != nil {
		return nil, err
	}

	// proxy分布
	proxyMap := make(map[string]int)
	err = json.Unmarshal([]byte(flow.ProxyMap), &proxyMap)
	if err != nil {
		return nil, err
	}

	// 白名单
	whitelist := []string{}
	err = json.Unmarshal([]byte(flow.Whitelist), &whitelist)
	if err != nil {
		return nil, err
	}

	// 判断是否有密码
	hasPassword := false
	if flow.Password != "" {
		hasPassword = true
	}

	// 单分片大小
	shardSize := float64(flow.StorageSize*1073741824) / float64(flow.ShardNum*1073741824)
	shardSize, _ = strconv.ParseFloat(fmt.Sprintf("%.2f", shardSize), 64)

	listDetail := map[string]any{
		"OperationArgs": map[string]any{
			"applicant":   flow.Applicant,
			"department":  flow.Department,
			"description": flow.Description,

			"name":            flow.Name,
			"area":            flow.Area,
			"areaName":        areaName,
			"type":            flow.Type,
			"redisVersion":    flow.RedisVersion,
			"maxmemoryPolicy": flow.MaxmemoryPolicy,
			"storageSize":     flow.StorageSize,
			"shardNum":        flow.ShardNum,
			"shardSize":       shardSize,
			"proxyMap":        proxyMap,
			"hasPassword":     hasPassword,
			"whitelist":       whitelist,
			"level":           flow.Level,
			"business":        biz,
		},
		"Approve": flow.Applicant,
	}

	return listDetail, nil
}

// 获取角色申请工单详情
func GetListRole(ctx context.Context, listID int) (map[string]any, error) {
	// 获取db client
	db, err := mysql.Database()
	if err != nil {
		return nil, err
	}

	// 获取工单记录
	detail, err := db.FlowApplyCluster.
		Query().
		WithCluster().
		Where(flowapplycluster.ID(int64(listID))).
		Only(ctx)
	if err != nil {
		return nil, err
	}

	listDetail := map[string]any{
		"OperationArgs": map[string]any{
			"applicant":   detail.Applicant,
			"department":  detail.Department,
			"description": detail.Description,

			"clusterId":    detail.Edges.Cluster.ID,
			"clusterAlias": detail.Edges.Cluster.Alias,
			"role":         detail.Role,
		},
		"Approve": detail.Applicant,
	}

	return listDetail, nil
}

// 获取白名单工单详情
func GetListWhitelist(ctx context.Context, listID int) (map[string]any, error) {
	// 获取db client
	db, err := mysql.Database()
	if err != nil {
		return nil, err
	}

	// 获取工单记录
	flowDetail, err := db.FlowWhitelist.Query().
		WithCluster().
		Where(flowwhitelist.ID(int64(listID))).
		Only(ctx)
	if err != nil {
		return nil, err
	}

	listDetail := map[string]any{
		"OperationArgs": map[string]any{
			"applicant":   flowDetail.Applicant,
			"department":  flowDetail.Department,
			"description": flowDetail.Description,

			"clusterId":         flowDetail.Edges.Cluster.ID,
			"clusterAlias":      flowDetail.Edges.Cluster.Alias,
			"clusterDepartment": flowDetail.Edges.Cluster.Department,
			"clusterOwner":      flowDetail.Edges.Cluster.Owner,
			"type":              flowDetail.Type,
			"privilege":         flowDetail.Privilege,
			"bnsList":           flowDetail.Bns,
			"ipList":            flowDetail.IP,
		},
		"Approve": flowDetail.Applicant,
	}

	return listDetail, nil
}

// 获取转让工单详情
func GetListTransfer(ctx context.Context, listID int) (map[string]any, error) {
	// 获取db client
	db, err := mysql.Database()
	if err != nil {
		return nil, err
	}

	// 获取工单记录
	flowDetail, err := db.FlowTransfer.Query().
		Where(flowtransfer.ID(int64(listID))).
		Only(ctx)
	if err != nil {
		return nil, err
	}

	listDetail := map[string]any{
		"OperationArgs": map[string]any{
			"applicant":   flowDetail.Applicant,
			"department":  flowDetail.Department,
			"description": flowDetail.Description,

			"clusterId":    flowDetail.ClusterID,
			"clusterAlias": flowDetail.ClusterName,
			"receiver":     flowDetail.Receiver,
		},
		"Approve": flowDetail.Applicant,
	}

	return listDetail, nil
}
