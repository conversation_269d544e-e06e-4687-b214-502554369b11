package flow

import (
	"reflect"

	"github.com/gin-gonic/gin"

	"dt-common/errs"
	"dt-common/gintool"
	"dt-common/logger"
	"dt-common/user-center/flowc"
	"redis-xweb/model/autoflow"
)

var funcsMap map[string]any

func init() {
	funcsMap = map[string]any{
		"Run":             autoflow.Run,             // Flow
		"DeployCluster":   autoflow.DeployCluster,   // FlowCluster
		"ApplyRole":       autoflow.AddUserToRole,   // FlowApplyCluster
		"UpdateWhitelist": autoflow.UpdateWhitelist, // FlowWhitelist
		"Transfer":        autoflow.Transfer,        // FlowTransfer
	}
}

// 查询服务器资产信息API（获取动态角色）
func FuncAsyncCaller(c *gin.Context) {
	var params flowc.FuncCall
	err := c.ShouldBindJSON(&params)
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail(err.<PERSON>rror()))
		return
	}

	funcName, found := funcsMap[params.FuncName]
	if !found {
		logger.Error("Can't find funcName  with funcName:%+v while call function", params.FuncName)
		return
	}

	f := reflect.ValueOf(funcName)
	args := make([]reflect.Value, len(params.Args))
	for index, param := range params.Args {
		args[index] = reflect.ValueOf(param)
	}
	logger.Error("len:%+v;args:%+v", len(params.Args), args)
	go f.Call(args)

	c.JSON(flowc.Format("ok", nil))
	c.Abort()
}

// 查询服务器资产信息API（获取动态角色）
func FuncSyncCaller(c *gin.Context) {
	var params flowc.FuncCall
	err := c.ShouldBindJSON(&params)
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}

	funcName, found := funcsMap[params.FuncName]
	if !found {
		logger.Error("Can't find funcName  with funcName:%+v while call function", params.FuncName)
		return
	}

	f := reflect.ValueOf(funcName)
	args := make([]reflect.Value, len(params.Args))
	for index, param := range params.Args {
		args[index] = reflect.ValueOf(param)
	}
	logger.Error("len:%+v;args:%+v", len(params.Args), args)
	result := f.Call(args)
	httpStatus := result[0].Interface().(int)
	r := result[1].Interface().(*flowc.Response)

	c.JSON(httpStatus, r)
	c.Abort()
}

// 异步自动执行
func FlowFuncAsyncCaller(c *gin.Context) {
	var params flowc.FlowFuncCall
	err := c.ShouldBindJSON(&params)
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}

	funcName, found := funcsMap[params.FuncName]
	if !found {
		logger.Warn("Can't find funcName  with funcName:%+v while call function", params.FuncName)
		return
	}

	logger.Info("FlowFuncAsyncCaller Params: %+v", params)
	f := reflect.ValueOf(funcName)
	args := make([]reflect.Value, 6)
	args[0] = reflect.ValueOf(params.Args.FlowInstanceId)
	args[1] = reflect.ValueOf(params.Args.NodeInstanceId)
	args[2] = reflect.ValueOf(params.Args.ListId)
	args[3] = reflect.ValueOf(params.Args.ListType)
	args[4] = reflect.ValueOf(params.Args.NodeSubmitHistory)
	args[5] = reflect.ValueOf(params.Args.NodeReturnHistory)
	go f.Call(args)

	c.JSON(flowc.Format("ok", nil))
	c.Abort()
}

// 同步自动执行
func FlowFuncSyncCaller(c *gin.Context) {
	var params flowc.FlowFuncCall
	err := c.ShouldBindJSON(&params)
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}

	funcName, found := funcsMap[params.FuncName]
	if !found {
		logger.Warn("Can't find funcName  with funcName:%+v while call function", params.FuncName)
		return
	}

	f := reflect.ValueOf(funcName)
	//args:= []reflect.Value{input.Args.FlowInstanceId,input.Args.NodeInstanceId}
	args := make([]reflect.Value, 6)
	args[0] = reflect.ValueOf(params.Args.FlowInstanceId)
	args[1] = reflect.ValueOf(params.Args.NodeInstanceId)
	args[2] = reflect.ValueOf(params.Args.ListId)
	args[3] = reflect.ValueOf(params.Args.ListType)
	args[4] = reflect.ValueOf(params.Args.NodeSubmitHistory)
	args[5] = reflect.ValueOf(params.Args.NodeReturnHistory)

	result := f.Call(args)

	c.JSON(flowc.Format(result, nil))
	c.Abort()
}

// 获取流程表单信息
func GetListDetail(c *gin.Context) {
	var args flowc.ListInfo
	err := c.ShouldBindJSON(&args)
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}

	var listDetail map[string]any
	switch args.ListType {
	case "redis_cluster_apply":
		listDetail, _ = GetListDeploy(c, args.ListID)
	case "redis_role_apply":
		listDetail, _ = GetListRole(c, args.ListID)
	case "redis_whitelist_apply":
		listDetail, _ = GetListWhitelist(c, args.ListID)
	case "redis_cluster_transfer":
		listDetail, _ = GetListTransfer(c, args.ListID)
	}

	c.JSON(flowc.Format(listDetail, nil))
	c.Abort()
}
