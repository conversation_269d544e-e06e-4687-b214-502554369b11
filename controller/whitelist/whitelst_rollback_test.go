package whitelist

import (
	"encoding/json"
	"io"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/gin-gonic/gin"

	"dt-common/errs"
	"dt-common/utils"
	"redis-agent/env"
)

func TestReadFile(t *testing.T) {
	type schema struct {
		FilePath string `json:"filepath"`
	}
	tests := []struct {
		name    string
		args    schema
		wantErr bool
	}{
		{
			name: "test1",
			args: schema{
				FilePath: "",
			},
			wantErr: true,
		},
		{
			name: "test2",
			args: schema{
				FilePath: "../../go.mod1",
			},
			wantErr: true,
		},
		{
			name: "test3",
			args: schema{
				FilePath: "../../go.mod",
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if _, err := readFile(tt.args.FilePath); (err != nil) != tt.wantErr {
				t.Errorf("readFile() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestRevertFile(t *testing.T) {
	type schema struct {
		filePath string
		bakPath  string
	}
	tests := []struct {
		name    string
		args    schema
		wantErr bool
	}{
		{
			name: "test1: read file error",
			args: schema{
				filePath: "",
				bakPath:  "",
			},
			wantErr: true,
		},
		{
			name: "test2: read file error2",
			args: schema{
				filePath: "../../mock/redis_7000/conf/whitelist.bns",
				bakPath:  "",
			},
			wantErr: true,
		},
		{
			name: "test3: success",
			args: schema{
				filePath: "../../mock/redis_7000/conf/whitelist.bns",
				bakPath:  "../../mock/redis_7000/conf/whitelist.bns.bak",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if _, err := revertFile(tt.args.filePath, tt.args.bakPath); (err != nil) != tt.wantErr {
				t.Errorf("revertFile() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

// 单侧：回滚白名单
func TestRollback(t *testing.T) {
	env.Mock(t, "../../mock/config/config.yaml")

	utils.ActivateExecMock()
	defer utils.DeactivateExecMock()

	type args struct {
		c *gin.Context
		w *httptest.ResponseRecorder
	}
	tests := []struct {
		name   string
		before func()
		args   args
		expect func(*testing.T, *httptest.ResponseRecorder)
	}{
		{
			name: "test1: check args",
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Request = &http.Request{
					Body: io.NopCloser(strings.NewReader("-1")),
				}
				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] == string(errs.Success) {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
		{
			name: "test2: locked and get whitelist failed",
			before: func() {
				portMap.LoadOrStore(7001, "in use")
				go func() {
					time.Sleep(5 * time.Second)
					portMap.Delete(7001)
				}()
			},
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				params := struct {
					Port    int      `json:"port" binding:"required"`
					BnsList []string `json:"bnsList" binding:"required"`
					IpList  []string `json:"ipList" binding:"required"`
					Action  string   `json:"action" binding:"required" validate:"oneof=add del"`
				}{
					Port:    7001,
					BnsList: []string{"ceshi.test rw"},
					IpList:  []string{},
					Action:  "add",
				}
				b, _ := json.Marshal(params)
				c.Request = &http.Request{
					Body: io.NopCloser(strings.NewReader(string(b))),
				}
				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] == string(errs.Success) {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
		{
			name: "test3: success",
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				params := struct {
					Port int `json:"port" binding:"required"`
				}{
					Port: 7000,
				}
				b, _ := json.Marshal(params)
				c.Request = &http.Request{
					Body: io.NopCloser(strings.NewReader(string(b))),
				}
				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] != string(errs.Success) {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			Rollback(tt.args.c)
			if tt.expect != nil {
				tt.expect(t, tt.args.w)
			}
		})
	}
}
