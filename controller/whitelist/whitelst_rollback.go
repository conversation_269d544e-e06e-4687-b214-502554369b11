package whitelist

import (
	"bufio"
	"fmt"
	"os"
	"strings"
	"time"

	"github.com/gin-gonic/gin"

	"dt-common/errs"
	"dt-common/gintool"
	"dt-common/logger"
	"dt-common/utils"
	"redis-agent/model/worker"
)

// 读取白名单
func readFile(filePath string) ([]string, error) {
	var whitelist []string
	file, err := os.Open(filePath)
	if err != nil {
		return nil, err
	}
	defer file.Close()
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		parts := strings.Split(scanner.Text(), " ")
		if len(parts) != 2 {
			return nil, fmt.Errorf("content of current file is wrong, file=%s", filePath)
		}
		whitelist = append(whitelist, parts[0]+" "+parts[1])
	}
	return whitelist, nil
}

// 覆盖文件
func revertFile(filePath, bakFilePath string) ([]string, error) {
	oldList, err := readFile(filePath)
	if err != nil {
		return nil, err
	}
	newList, err := readFile(bakFilePath)
	if err != nil {
		return nil, err
	}
	bnsContent := strings.Join(newList, "\n")
	err = utils.RewriteFile(filePath, bnsContent)
	if err != nil {
		return nil, err
	}
	diffList := diffWhitelist(oldList, newList)
	return diffList, nil
}

// 白名单回滚
func Rollback(c *gin.Context) {
	type schema struct {
		Port int `json:"port" binding:"required"`
	}
	//解析路由参数
	var params schema
	err := c.ShouldBindJSON(&params)
	if err != nil {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Append(err.Error()))
		return
	}

	for {
		//检查是否已加锁
		if _, loaded := portMap.LoadOrStore(params.Port, "in use"); loaded {
			logger.Warn("failed to acquire lock for port=%d, retrying in 5 seconds...", params.Port)
			time.Sleep(5 * time.Second)
			continue
		}
		defer portMap.Delete(params.Port)

		// 获取白名单文件路径
		bnsFilePath, ipFilePath, err := worker.GetWhitelistPath(params.Port)
		if err != nil {
			logger.Error("failed to get whitelist file, error=(%v)", err)
			gintool.JSON(c, nil, errs.CodeFileNotExist.Detail(err.Error()))
			return
		}
		bnsBakFilePath := bnsFilePath + ".bak"
		ipBakFilePath := ipFilePath + ".bak"

		// 备份当前版本的白名单文件，后缀为.problem.timestamp
		bnsProblemFilePath := bnsFilePath + ".problem." + time.Now().Format("20060102150405")
		err = utils.CopyFile(bnsFilePath, bnsProblemFilePath, true)
		if err != nil {
			logger.Error("failed to backup problematic bns whitelist, port=%d, error=(%s)", params.Port, err.Error())
			gintool.JSON(c, nil, errs.CodeWriteFile.Detail(err.Error()))
			return
		}
		logger.Info("succeeded to backup problematic bns whitelist, file=%s", bnsProblemFilePath)
		ipProblemFilePath := ipFilePath + ".problem." + time.Now().Format("20060102150405")
		err = utils.CopyFile(ipFilePath, ipProblemFilePath, true)
		if err != nil {
			logger.Error("failed to backup problematic ip whitelist, port=%d, error=(%s)", params.Port, err.Error())
			gintool.JSON(c, nil, errs.CodeWriteFile.Detail(err.Error()))
			return
		}
		logger.Info("succeeded to backup problematic ip whitelist, file=%s", ipProblemFilePath)

		// 回滚BNS/IP白名单
		bnsDiffs, err := revertFile(bnsFilePath, bnsBakFilePath)
		if err != nil {
			logger.Error("failed to revert bns file, error=(%v)", err)
			gintool.JSON(c, nil, errs.CodeReadFile.Detail(err.Error()))
			return
		}
		logger.Info("succeeded to rollback bns whitelist, diff=%v", bnsDiffs)
		ipDiffs, err := revertFile(ipFilePath, ipBakFilePath)
		if err != nil {
			logger.Error("failed to revert ip file, error=(%v)", err)
			gintool.JSON(c, nil, errs.CodeReadFile.Detail(err.Error()))
			return
		}
		logger.Info("succeeded to rollback ip whitelist, diff=%v", ipDiffs)

		gintool.JSON(c, "ok", nil)
		break
	}
}
