package whitelist

import (
	"reflect"

	"github.com/gin-gonic/gin"

	"dt-common/errs"
	"dt-common/gintool"
	"redis-cmanager/library/stage"
	"redis-cmanager/model/whitelist"
)

type schema struct {
	StageID      int64    `json:"stageId" binding:"required"`
	ClusterName  string   `json:"clusterName" binding:"required"`
	Action       string   `json:"action" binding:"required" validate:"oneof=add del"`
	WhiteListBns []string `json:"whiteListBns"`
	WhiteListIp  []string `json:"whiteListIp"`
}

// 更新业务白名单（proxy）
func UpdateProxyWhitelist(c *gin.Context) {
	// 参数校验
	var params schema
	err := c.ShouldBindJ<PERSON>N(&params)
	if err != nil {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}

	args := make([]reflect.Value, 5)
	args[0] = reflect.ValueOf(params.StageID)
	args[1] = reflect.ValueOf(params.ClusterName)
	args[2] = reflect.ValueOf(params.Action)
	args[3] = reflect.ValueOf(params.WhiteListBns)
	args[4] = reflect.ValueOf(params.WhiteListIp)
	err = stage.Async(params.StageID, whitelist.UpdateProxyWhitelist, args...)
	if err != nil {
		gintool.JSON(c, nil, err)
		return
	}

	gintool.JSON(c, "ok", nil)
}
