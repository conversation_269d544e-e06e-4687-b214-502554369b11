package whitelist

import (
	"bufio"
	"fmt"
	"os"
	"slices"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"

	"dt-common/errs"
	"dt-common/gintool"
	"dt-common/logger"
	"dt-common/utils"
	"redis-agent/model/worker"
)

// 更新指定实例的白名单文件，更新失败不会重试，不会回滚，但返回错误
// TODO：版本控制，conf下写一个datetime，避免并发冲突
// 使用sync.Map作为锁，Go语言原生 map 是非线性安全的，对 map 进行并发读写操作时，需要加锁。Go 1.9引入sync.map，一种并发安全的map
var portMap sync.Map

// 读取当前白名单并根据更新动作来对传参白名单进行处理
func preTreatment(whitelistFilePath string, paramsList []string, action string) ([]string, []string, error) {
	data := make(map[string]string)
	var oldWhitelist []string
	var newWhitelist []string
	file, err := os.Open(whitelistFilePath)
	if err != nil {
		return nil, nil, err
	}
	defer file.Close()
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		if scanner.Text() == "" {
			continue
		}

		parts := strings.Split(scanner.Text(), " ")
		if len(parts) != 2 {
			return nil, nil, fmt.Errorf("error in current whitelist")
		}
		data[parts[0]] = parts[1]
		oldWhitelist = append(oldWhitelist, parts[0]+" "+parts[1])
	}
	//判断更新动作是添加还是删除
	switch action {
	case "add":
		for _, list := range paramsList {
			parts := strings.Split(list, " ")
			if len(parts) != 2 {
				return nil, nil, fmt.Errorf("error in incoming whitelist")
			}
			data[parts[0]] = parts[1]
		}
	case "del":
		for _, list := range paramsList {
			parts := strings.Split(list, " ")
			if len(parts) != 2 {
				return nil, nil, fmt.Errorf("error in incoming whitelist")
			}
			delete(data, parts[0])
		}
	case "cover":
		data = make(map[string]string) //清空原来的data
		for _, list := range paramsList {
			parts := strings.Split(list, " ")
			if len(parts) != 2 {
				return nil, nil, fmt.Errorf("error in incoming whitelist")
			}
			data[parts[0]] = parts[1]
		}
	default:
		return nil, nil, fmt.Errorf("action must be one of add/del/cover")
	}

	for key, value := range data {
		newWhitelist = append(newWhitelist, key+" "+value)
	}
	slices.Sort(oldWhitelist)
	slices.Sort(newWhitelist)
	return oldWhitelist, newWhitelist, nil
}

// diff出白名单更新前后的差异内容
func diffWhitelist(arr1, arr2 []string) []string {
	elementsMap := make(map[string]string)
	for _, element := range arr1 {
		elementsMap[element] = "-"
	}

	var diff []string
	for _, element := range arr2 {
		_, ok := elementsMap[element]
		if !ok {
			elementsMap[element] = "+"
		} else {
			elementsMap[element] = "|"
		}
	}

	for element, state := range elementsMap {
		if state != "|" {
			diff = append(diff, fmt.Sprintf("%s%s", state, element))
		}
	}
	return diff
}

func Update(c *gin.Context) {
	type schema struct {
		Port    int      `json:"port" binding:"required"`
		BnsList []string `json:"bnsList"`
		IpList  []string `json:"ipList"`
		Action  string   `json:"action" binding:"required" validate:"oneof=add del cover"`
	}
	//解析路由参数
	var params schema
	err := c.ShouldBindJSON(&params)
	if err != nil {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Append(err.Error()))
		return
	}
	if len(params.BnsList) == 0 && len(params.IpList) == 0 {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail("bnsList or ipList must be set"))
		return
	}

	for {
		//检查是否已加锁
		if _, loaded := portMap.LoadOrStore(params.Port, "in use"); loaded {
			logger.Warn("failed to acquire lock for port=%d, retrying in 5 seconds...", params.Port)
			time.Sleep(5 * time.Second)
			continue
		}
		defer portMap.Delete(params.Port)

		// 获取白名单文件路径
		bnsFilePath, ipFilePath, err := worker.GetWhitelistPath(params.Port)
		if err != nil {
			logger.Error("failed to get whitelist file, error=(%v)", err)
			gintool.JSON(c, nil, errs.CodeFileNotExist.Detail(err.Error()))
			return
		}

		// 预处理BNS白名单
		var oldBnsList []string
		var newBnsList []string
		var diffBnsList []string
		oldBnsList, newBnsList, err = preTreatment(bnsFilePath, params.BnsList, params.Action)
		if err != nil {
			logger.Error("failed to preprocess bns whitelist, bnsList=%v, error=(%v)", params.BnsList, err)
			gintool.JSON(c, nil, errs.CodeRequestFailed.Detail(err.Error()))
			return
		}

		// 预处理IP白名单
		var oldIpList []string
		var newIpList []string
		var diffIpList []string
		oldIpList, newIpList, err = preTreatment(ipFilePath, params.IpList, params.Action)
		if err != nil {
			logger.Error("failed to preprocess ip whitelist, ipList=%v, error=(%v)", params.IpList, err)
			gintool.JSON(c, nil, errs.CodeRequestFailed.Detail(err.Error()))
			return
		}

		// 如果有diff再更新bns白名单文件
		diffBnsList = diffWhitelist(oldBnsList, newBnsList)
		if len(diffBnsList) != 0 {
			// 备份
			err := utils.CopyFile(bnsFilePath, bnsFilePath+".bak", true)
			if err != nil {
				logger.Error("failed to backup bns whitelist, port=%d, error=(%v)", params.Port, err)
				gintool.JSON(c, nil, errs.CodeWriteFile.Detail(err.Error()))
				return
			}
			logger.Info("succeeded to backup the bns whitelist")
			// 重写
			bnsContent := strings.Join(newBnsList, "\n")
			err = utils.RewriteFile(bnsFilePath, bnsContent)
			if err != nil {
				logger.Error("failed to update bns whitelist, port=%d, error=(%v)", params.Port, err)
				gintool.JSON(c, nil, errs.CodeWriteFile.Detail(err.Error()))
				return
			}
			logger.Info("succeeded to update bns whitelist, action=%s, after update bns whitelist=%v, diff bns whitelist=%v", params.Action, newBnsList, diffBnsList)
		}

		// 如果有diff再更新ip白名单文件
		diffIpList = diffWhitelist(oldIpList, newIpList)
		if len(diffIpList) != 0 {
			// 备份
			err = utils.CopyFile(ipFilePath, ipFilePath+".bak", true)
			if err != nil {
				logger.Error("failed to backup ip whitelist, port=%d, error=(%v)", params.Port, err)
				gintool.JSON(c, nil, errs.CodeWriteFile.Detail(err.Error()))
				return
			}
			logger.Info("succeeded to backup the ip whitelist")
			// 重写
			ipContent := strings.Join(newIpList, "\n")
			err = utils.RewriteFile(ipFilePath, ipContent)
			if err != nil {
				logger.Error("failed to update ip whitelist, port=%d, error=(%v)", params.Port, err)
				gintool.JSON(c, nil, errs.CodeWriteFile.Detail(err.Error()))
				return
			}
			logger.Info("succeeded to update ip whitelist, action=%s, after update ip whitelist=%v, diff ip whitelist=%v", params.Action, newIpList, diffIpList)
		}

		gintool.JSON(c, gin.H{"bnsTotal": len(newBnsList), "ipTotal": len(newIpList)}, nil)
		break
	}
}
