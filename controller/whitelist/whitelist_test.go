package whitelist

import (
	"encoding/json"
	"io"
	"net/http"
	"net/http/httptest"
	"slices"
	"strings"
	"testing"
	"time"

	"github.com/gin-gonic/gin"

	"dt-common/errs"
	"redis-agent/env"
)

func TestPreTreatment(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")

	type schema struct {
		WhitelistFilePath string   `json:"whitelistFilePath"`
		ParamsList        []string `json:"paramsList"`
		Action            string   `json:"action"`
	}
	tests := []struct {
		name    string
		before  func()
		args    schema
		wantErr bool
		expect  func(*testing.T, []string, []string)
	}{
		{
			name: "test1: openfile error",
			args: schema{
				WhitelistFilePath: "",
				Action:            "add",
				ParamsList:        []string{"ceshi.bns r", "ceshi2.bns rw"},
			},
			wantErr: true,
		},
		{
			name: "test2: missing privilege",
			args: schema{
				WhitelistFilePath: "../../mock/redis_7000/conf/whitelist.bns",
				Action:            "add",
				ParamsList:        []string{"ceshi.bns", "ceshi2.bns rw"},
			},
			wantErr: true,
		},
		{
			name: "test3: missing privilege 2",
			args: schema{
				WhitelistFilePath: "../../mock/redis_7000/conf/whitelist.bns",
				Action:            "del",
				ParamsList:        []string{"ceshi.bns", "ceshi2.bns rw"},
			},
			wantErr: true,
		},
		{
			name: "test4: add success",
			args: schema{
				WhitelistFilePath: "../../mock/redis_7000/conf/whitelist.bns",
				Action:            "add",
				ParamsList:        []string{"ceshi.bns rw", "ceshi2.bns rw"},
			},
			wantErr: false,
			expect: func(t *testing.T, oldWhitelist, newWhitelist []string) {
				if !slices.Contains(newWhitelist, "ceshi2.bns rw") {
					t.Errorf("newWhitelist should contain ceshi2.bns rw")
				}
			},
		},
		{
			name: "test5: del success",
			args: schema{
				WhitelistFilePath: "../../mock/redis_7000/conf/whitelist.bns",
				Action:            "del",
				ParamsList:        []string{"ceshi.bns rw", "ceshi2.bns rw"},
			},
			wantErr: false,
			expect: func(t *testing.T, oldWhitelist, newWhitelist []string) {
				if slices.Contains(newWhitelist, "ceshi2.bns rw") {
					t.Errorf("newWhitelist should not contain ceshi2.bns rw")
				}
			},
		},
		{
			name: "test6: cover success",
			args: schema{
				WhitelistFilePath: "../../mock/redis_7000/conf/whitelist.bns",
				Action:            "cover",
				ParamsList:        []string{"ceshi2.bns rw", "ceshi.bns rw"},
			},
			wantErr: false,
			expect: func(t *testing.T, oldWhitelist, newWhitelist []string) {
				if newWhitelist[1] != "ceshi2.bns rw" {
					t.Errorf("newWhitelist should not contain ceshi2.bns rw")
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			oldWhitelist, newWhitelist, err := preTreatment(tt.args.WhitelistFilePath, tt.args.ParamsList, tt.args.Action)
			if (err != nil) != tt.wantErr {
				t.Errorf("Pretreatment() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, oldWhitelist, newWhitelist)
			}
		})
	}
}

func TestDiffWhitelist(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")

	type schema struct {
		arr1 []string
		arr2 []string
	}
	tests := []struct {
		name    string
		before  func()
		args    schema
		wantErr bool
		expect  func(*testing.T, []string)
	}{
		{
			name: "test1: add new item",
			args: schema{
				arr1: []string{"127.0.0.1", "192.168.3.11"},
				arr2: []string{"127.0.0.1", "192.168.3.11", "192.168.3.12"},
			},
			expect: func(t *testing.T, diff []string) {
				if !slices.Contains(diff, "+192.168.3.12") {
					t.Errorf("test1: expected +192.168.3.12, but not get")
				}
			},
		},
		{
			name: "test2: del item",
			args: schema{
				arr1: []string{"127.0.0.1", "192.168.3.11", "127.0.0.3"},
				arr2: []string{"127.0.0.1"},
			},
			expect: func(t *testing.T, diff []string) {
				if !slices.Contains(diff, "-192.168.3.11") {
					t.Errorf("test1: expected -192.168.3.11, but not get")
				}
				if !slices.Contains(diff, "-127.0.0.3") {
					t.Errorf("test1: expected -127.0.0.3, but not get")
				}
			},
		},
		{
			name: "test3: add and del item",
			args: schema{
				arr1: []string{"127.0.0.1", "192.168.3.11"},
				arr2: []string{"127.0.0.1", "127.0.0.3"},
			},
			expect: func(t *testing.T, diff []string) {
				if !slices.Contains(diff, "-192.168.3.11") {
					t.Errorf("test1: expected -192.168.3.11, but not get")
				}
				if !slices.Contains(diff, "+127.0.0.3") {
					t.Errorf("test1: expected +127.0.0.3, but not get")
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			diff := diffWhitelist(tt.args.arr1, tt.args.arr2)
			// if (err != nil) != tt.wantErr {
			// 	t.Errorf("diffWhitelist() error = %v, wantErr %v", err, tt.wantErr)
			// }
			if tt.expect != nil {
				tt.expect(t, diff)
			}
		})
	}
}

// 单侧：更新白名单
func TestUpdate(t *testing.T) {
	env.Mock(t, "../../mock/config/config.yaml")
	gin.SetMode(gin.TestMode)

	type args struct {
		c *gin.Context
		w *httptest.ResponseRecorder
	}
	tests := []struct {
		name   string
		before func()
		args   args
		expect func(*testing.T, *httptest.ResponseRecorder)
	}{
		{
			name: "test1: check args",
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Request = &http.Request{
					Body: io.NopCloser(strings.NewReader("-1")),
				}
				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] == string(errs.Success) {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
		{
			name: "test2: locked and get whitelist failed",
			before: func() {
				portMap.LoadOrStore(7001, "in use")
				go func() {
					time.Sleep(5 * time.Second)
					portMap.Delete(7001)
				}()
			},
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				params := struct {
					Port    int      `json:"port" binding:"required"`
					BnsList []string `json:"bnsList" binding:"required"`
					IpList  []string `json:"ipList" binding:"required"`
					Action  string   `json:"action" binding:"required" validate:"oneof=add del"`
				}{
					Port:    7001,
					BnsList: []string{"ceshi.test rw"},
					IpList:  []string{},
					Action:  "add",
				}
				b, _ := json.Marshal(params)
				c.Request = &http.Request{
					Body: io.NopCloser(strings.NewReader(string(b))),
				}
				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] == string(errs.Success) {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
		{
			name: "test3: success",
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				params := struct {
					Port    int      `json:"port" binding:"required"`
					BnsList []string `json:"bnsList" binding:"required"`
					IpList  []string `json:"ipList" binding:"required"`
					Action  string   `json:"action" binding:"required" validate:"oneof=add del"`
				}{
					Port:    7000,
					BnsList: []string{"ceshi.test rw"},
					IpList:  []string{},
					Action:  "add",
				}
				b, _ := json.Marshal(params)
				c.Request = &http.Request{
					Body: io.NopCloser(strings.NewReader(string(b))),
				}
				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] != string(errs.Success) {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			Update(tt.args.c)
			if tt.expect != nil {
				tt.expect(t, tt.args.w)
			}
		})
	}
}
