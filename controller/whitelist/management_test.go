package whitelist

import (
	"encoding/json"
	"io"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/gin-gonic/gin"
)

func TestUpdateManageWhitelist(t *testing.T) {
	gin.SetMode(gin.TestMode)

	type args struct {
		c *gin.Context
		w *httptest.ResponseRecorder
	}
	tests := []struct {
		name   string
		args   args
		expect func(*testing.T, *httptest.ResponseRecorder)
	}{
		{
			name: "test1",
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)

				c.Request = &http.Request{
					Body: io.NopCloser(strings.NewReader(`{"stageId":1,"clusterName":"123","action":"add","isRollback":false,"whiteListBns":["123.123"]}`)),
				}

				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] != "00000" {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
		{
			name: "test2",
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)

				c.Request = &http.Request{
					Body: io.NopCloser(strings.NewReader(`{"stageId":1,"clusterName":"123","action":"add","isRollback":true,"whiteListBns":["123.123"]}`)),
				}

				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] != "00000" {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			UpdateManageWhitelist(tt.args.c)
			if tt.expect != nil {
				tt.expect(t, tt.args.w)
			}
		})
	}
}
