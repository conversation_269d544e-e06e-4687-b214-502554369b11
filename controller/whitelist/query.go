package whitelist

import (
	"github.com/gin-gonic/gin"

	"dt-common/errs"
	"dt-common/gintool"
	"redis-agent/model/worker"
)

// 获取实际白名单
func ListAll(c *gin.Context) {
	type schema struct {
		Port int `form:"port" binding:"required"`
	}

	//解析路由参数
	var params schema
	err := c.Should<PERSON>ind<PERSON>uery(&params)
	if err != nil {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}

	// 获取文件路径
	whitelistBns, whitelistIp, err := worker.GetWhitelistPath(params.Port)
	if err != nil {
		gintool.JSON(c, nil, errs.CodeMissingConfig.Detail(err.Error()))
		return
	}

	bnsList, err := readFile(whitelistBns)
	if err != nil {
		gintool.JSON(c, nil, errs.CodeShellExecFailed.Detail(err.<PERSON>rror()))
		return
	}
	ipList, err := readFile(whitelistIp)
	if err != nil {
		gintool.JSON(c, nil, errs.CodeShellExecFailed.Detail(err.Error()))
		return
	}

	gintool.JSON(c, gin.H{"bns": bnsList, "ip": ipList}, nil)
}
