package whitelist

import (
	"encoding/json"
	"io"
	"net/http"
	"net/http/httptest"
	"net/url"
	"testing"

	"github.com/gin-gonic/gin"

	"dt-common/errs"
	"redis-agent/env"
)

// 单侧：更新白名单
func TestListAll(t *testing.T) {
	env.Mock(t, "../../mock/config/config.yaml")
	gin.SetMode(gin.TestMode)

	type args struct {
		c *gin.Context
		w *httptest.ResponseRecorder
	}
	tests := []struct {
		name   string
		before func()
		args   args
		expect func(*testing.T, *httptest.ResponseRecorder)
	}{
		{
			name: "test1: check args",
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Request = &http.Request{
					URL: &url.URL{},
				}
				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] == string(errs.Success) {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
		{
			name: "test2: get whitelist failed",
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Request = &http.Request{
					URL: &url.URL{
						RawQuery: "port=7001",
					},
				}
				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] == string(errs.Success) {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
		{
			name: "test3: success",
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Request = &http.Request{
					URL: &url.URL{
						RawQuery: "port=7000",
					},
				}
				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] != string(errs.Success) {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			ListAll(tt.args.c)
			if tt.expect != nil {
				tt.expect(t, tt.args.w)
			}
		})
	}
}
