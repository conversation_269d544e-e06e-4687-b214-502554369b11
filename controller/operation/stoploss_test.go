package operation

import (
	"encoding/json"
	"io"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"

	"github.com/gin-gonic/gin"

	"redis-xweb/env"
)

// 单测：实例解挂载
func TestCreateFlowCutTask(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")

	type args struct {
		filter *FlowCutFilter
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, error)
	}{
		{
			name: "test1: check idc",
			args: args{
				filter: &FlowCutFilter{
					IDC: "",
				},
			},
			wantErr: true,
		},
		{
			name: "test2: no cluster",
			args: args{
				filter: &FlowCutFilter{
					IDC: "hba",
				},
			},
			wantErr: true,
		},
		{
			name: "test3: success",
			before: func() {
				env.MockCluster("dev1")
				env.MockCluster("dev2")
				env.MockCluster("dev3")
			},
			args: args{
				filter: &FlowCutFilter{
					IDC: "hba",
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			_, err := createFlowCutTask(tt.args.filter)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateFlowCutTask() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, err)
			}
		})
	}
}

func TestEmergencyFlowCut(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")
	gin.SetMode(gin.TestMode)

	type args struct {
		c *gin.Context
		w *httptest.ResponseRecorder
	}
	tests := []struct {
		name   string
		before func()
		args   args
		expect func(*testing.T, *httptest.ResponseRecorder)
	}{
		{
			name: "test1",
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)

				params := map[string]interface{}{
					"idc": "hba",
				}
				b, _ := json.Marshal(params)
				c.Request = &http.Request{
					Body: io.NopCloser(strings.NewReader(string(b))),
				}

				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] != 0.0 {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			EmergencyFlowCut(tt.args.c)
			if tt.expect != nil {
				tt.expect(t, tt.args.w)
			}
		})
	}
}
