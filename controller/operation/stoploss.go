package operation

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/gin-gonic/gin"

	"dt-common/ent"
	"dt-common/ent/cluster"
	"dt-common/ent/predicate"
	"dt-common/ent/task"
	"dt-common/errs"
	"dt-common/gintool"
	"dt-common/logger"
	"dt-common/mysql"
	"dt-common/omodel"
)

// 切流过滤参数
type FlowCutFilter struct {
	IDC         string   `json:"idc" binding:"required"` // Filter: 切流机房
	ClusterName []string `json:"clusterName"`            // Filter: 按集群名过滤
	Desc        string   `json:"desc"`                   // 切流说明
}

// 一键机房切流
// 1、proxy屏蔽
// 2、主库切换
func EmergencyFlowCut(c *gin.Context) {
	//解析路由参数
	var args FlowCutFilter
	err := c.ShouldBindJSON(&args)
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail("参数解析失败"))
		return
	}

	// 获取数据库连接
	db, err := mysql.Database()
	if err != nil {
		logger.Error("failed to get mysql connection, error=(%v)", err)
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail("数据库连接获取失败"))
		return
	}

	// 5分钟内不重复创建
	ctx, cancel := mysql.ContextWithTimeout()
	taskData, err := db.Task.Query().Where(
		task.Type(omodel.TASK_TYPE_EMERGENCY_CUT),
		task.CreatedAtGT(time.Now().Add(-5*time.Minute)),
	).First(ctx)
	cancel()
	if err == nil && taskData != nil {
		gintool.JSON2FE(c, taskData.ID, nil)
		return
	}

	// 创建任务
	taskId, err := createFlowCutTask(&args)
	gintool.JSON2FE(c, taskId, err)
}

// 创建切流任务
func createFlowCutTask(filter *FlowCutFilter) (int64, error) {
	if filter.IDC == "" {
		return -1, fmt.Errorf("parameter idc can not be empty")
	}
	// 序列化stage parameters
	params := map[string]string{"idc": filter.IDC}
	paramBytes, err := json.Marshal(params)
	if err != nil {
		return -1, err
	}

	db, err := mysql.Database()
	if err != nil {
		logger.Error("failed to get mysql connection, error=(%v)", err)
		return -1, err
	}
	// 查询集群列表
	whereCond := []predicate.Cluster{cluster.StatusNEQ(omodel.CLUSTER_STATUS_DELETED)}
	if len(filter.ClusterName) != 0 {
		whereCond = append(whereCond, cluster.NameIn(filter.ClusterName...))
	}
	ctx, cancel := mysql.ContextWithTimeout()
	clusterList, err := db.Cluster.Query().Select(cluster.FieldName).Where(whereCond...).All(ctx)
	cancel()
	if err != nil {
		return -1, err
	}
	// 没有集群返回空
	if len(clusterList) == 0 {
		return -1, fmt.Errorf("there is no cluster should cut flow")
	}

	// 事务：创建task&stages
	var taskData *ent.Task
	err = db.Transaction(context.Background(), func(tx *ent.Tx) error {
		// 创建任务
		// TODO：前端就位后 jiayiming_dxm 改成 user.name
		ctx, cancel = mysql.ContextWithTimeout()
		taskData, err = tx.Task.Create().
			SetClusterID(0).SetClusterName("机房切流").
			SetName("机房切流").SetType(omodel.TASK_TYPE_EMERGENCY_CUT).
			SetApplicant("jiayiming_dxm").SetDescription(filter.Desc).
			Save(ctx)
		cancel()
		if err != nil {
			return err
		}

		// 批量创建stages
		bulkCreate := make([]*ent.StageCreate, len(clusterList))
		for i, objCluster := range clusterList {
			bulkCreate[i] = tx.Stage.Create().
				SetTaskID(taskData.ID).SetClusterName(objCluster.Name).
				SetName(objCluster.Name).SetType(omodel.STAGE_TYPE_EMERGENCY_CUT).
				SetSequence(i + 1).SetParameter(string(paramBytes))
		}
		ctx, cancel = mysql.ContextWithTimeout()
		_, err = tx.Stage.CreateBulk(bulkCreate...).Save(ctx)
		cancel()

		return err
	})
	if err != nil {
		return -1, err
	}

	return taskData.ID, nil
}
