package operation

import (
	"context"
	"fmt"
	"time"

	"github.com/gin-gonic/gin"

	"dt-common/ent"
	"dt-common/ent/cluster"
	"dt-common/ent/predicate"
	"dt-common/ent/task"
	"dt-common/errs"
	"dt-common/gintool"
	"dt-common/logger"
	"dt-common/mysql"
	"dt-common/omodel"
)

// 切流过滤参数
type MonitorUpgradeFilter struct {
	ClusterName []string `json:"clusterName"` // Filter: 按集群名过滤
	Desc        string   `json:"desc"`        // 切流说明
}

// 监控升级，每个集群创建一个监控Stage
func MonitorUpgrade(c *gin.Context) {
	//解析路由参数
	var args MonitorUpgradeFilter
	err := c.ShouldBindJSON(&args)
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail("参数解析失败"))
		return
	}

	// 获取数据库连接
	db, err := mysql.Database()
	if err != nil {
		logger.Error("failed to get mysql connection, error=(%v)", err)
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail("数据库连接获取失败"))
		return
	}

	// 5分钟内不重复创建
	ctx, cancel := mysql.ContextWithTimeout()
	taskData, err := db.Task.Query().Where(
		task.Type(omodel.TASK_TYPE_MONITOR_UPGRADE),
		task.CreatedAtGT(time.Now().Add(-5*time.Minute)),
	).First(ctx)
	cancel()
	if err == nil && taskData != nil {
		gintool.JSON2FE(c, taskData.ID, nil)
		return
	}

	// 查询集群列表
	whereCond := []predicate.Cluster{
		cluster.StatusNEQ(omodel.CLUSTER_STATUS_DELETED),
		cluster.DockerNEQ(omodel.DEPLOY_ENV_BBC),
	}
	if len(args.ClusterName) != 0 {
		whereCond = append(whereCond, cluster.NameIn(args.ClusterName...))
	}
	ctx, cancel = mysql.ContextWithTimeout()
	clusterList, err := db.Cluster.Query().Select(cluster.FieldName).Where(whereCond...).Order(cluster.ByName()).All(ctx)
	cancel()
	if err != nil {
		gintool.JSON2FE(c, nil, err)
	}
	// 没有集群返回空
	if len(clusterList) == 0 {
		gintool.JSON2FE(c, nil, fmt.Errorf("there is no cluster "))
		return
	}

	// 事务：创建task&stages
	err = db.Transaction(context.Background(), func(tx *ent.Tx) error {
		// 创建任务
		// TODO：前端就位后 jiayiming_dxm 改成 user.name
		ctx, cancel = mysql.ContextWithTimeout()
		taskData, err = tx.Task.Create().
			SetName("监控升级").SetType(omodel.TASK_TYPE_MONITOR_UPGRADE).
			SetApplicant("jiayiming_dxm").SetDescription(args.Desc).
			Save(ctx)
		cancel()
		if err != nil {
			return err
		}

		// 批量创建stages
		bulkCreate := make([]*ent.StageCreate, len(clusterList))
		for i, objCluster := range clusterList {
			bulkCreate[i] = tx.Stage.Create().
				SetTaskID(taskData.ID).SetClusterName(objCluster.Name).
				SetName(objCluster.Name).SetType(omodel.STAGE_TYPE_MONITOR_UPGRADE).
				SetSequence(i + 1).SetAutomate(true)
		}
		ctx, cancel = mysql.ContextWithTimeout()
		_, err = tx.Stage.CreateBulk(bulkCreate...).Save(ctx)
		cancel()

		return err
	})

	gintool.JSON2FE(c, taskData.ID, err)
}
