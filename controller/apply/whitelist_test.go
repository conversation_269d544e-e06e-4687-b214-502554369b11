package apply

import (
	"encoding/json"
	"io"
	"net/http"
	"net/http/httptest"
	"regexp"
	"strings"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/jarcoal/httpmock"

	"dt-common/user-center/authc"
	"redis-xweb/env"
)

func TestWhitelist(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")
	objC := env.MockCluster("unit_test_whitelist")
	gin.SetMode(gin.TestMode)
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		c *gin.Context
		w *httptest.ResponseRecorder
	}
	tests := []struct {
		name   string
		before func()
		args   args
		expect func(*testing.T, *httptest.ResponseRecorder)
	}{
		{
			name: "test1: check args",
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Request = &http.Request{
					Body: io.NopCloser(strings.NewReader("-1")),
				}

				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] == 0.0 {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
		{
			name: "test2: user not exist",
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				params := WhitelistSchema{
					ClusterID:   objC.ID,
					Type:        "add",
					BnsList:     []string{"unit.test"},
					Privilege:   "rw",
					Description: "111",
				}
				b, _ := json.Marshal(params)
				c.Request = &http.Request{
					Body: io.NopCloser(strings.NewReader(string(b))),
				}

				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] == 0.0 {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
		{
			name: "test3: get department failed",
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Set("user", &authc.User{ID: 1, Name: "admin"})
				params := WhitelistSchema{
					ClusterID:   objC.ID,
					Type:        "add",
					BnsList:     []string{"unit.test"},
					Privilege:   "rw",
					Description: "111",
				}
				b, _ := json.Marshal(params)
				c.Request = &http.Request{
					Body: io.NopCloser(strings.NewReader(string(b))),
				}

				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] == 0.0 {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
		{
			name: "test4: cluster not exist",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/auth/GetUserDepart`),
					httpmock.NewStringResponder(200, "{\"departmentID\":1000000037,\"departmentName\":\"系统运维部\"}"))
			},
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Set("user", &authc.User{ID: 1, Name: "admin"})
				params := WhitelistSchema{
					ClusterID:   99,
					Type:        "add",
					BnsList:     []string{"unit.test"},
					Privilege:   "rw",
					Description: "111",
				}
				b, _ := json.Marshal(params)
				c.Request = &http.Request{
					Body: io.NopCloser(strings.NewReader(string(b))),
				}
				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] == 0.0 {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
		{
			name: "test5: get_user_by_role failed",
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Set("user", &authc.User{ID: 1, Name: "admin"})
				params := WhitelistSchema{
					ClusterID:   objC.ID,
					Type:        "add",
					BnsList:     []string{"unit.test"},
					Privilege:   "rw",
					Description: "111",
				}
				b, _ := json.Marshal(params)
				c.Request = &http.Request{
					Body: io.NopCloser(strings.NewReader(string(b))),
				}
				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] == 0.0 {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
		{
			name: "test6: add flow failed",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/auth/GetUsersByRole`),
					httpmock.NewStringResponder(200, "{\"errno\":0,\"errmsg\":\"\",\"data\":\"jiayiming_dxm,huzhaoyun_dxm\"}"))
			},
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Set("user", &authc.User{ID: 1, Name: "admin"})
				params := WhitelistSchema{
					ClusterID:   objC.ID,
					Type:        "add",
					BnsList:     []string{"unit.test"},
					Privilege:   "rw",
					Description: "111",
				}
				b, _ := json.Marshal(params)
				c.Request = &http.Request{
					Body: io.NopCloser(strings.NewReader(string(b))),
				}
				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] == 0.0 {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
		{
			name: "test7: success",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/v1/flow/CreateFlowInstance`),
					httpmock.NewStringResponder(200, "{\"errno\":0,\"errmsg\":\"\",\"data\":{\"flowInstanceId\":123}}"))
			},
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Set("user", &authc.User{ID: 1, Name: "admin"})

				params := WhitelistSchema{
					ClusterID:   objC.ID,
					Type:        "add",
					BnsList:     []string{"unit.test"},
					Privilege:   "rw",
					Description: "111",
				}

				b, _ := json.Marshal(params)
				c.Request = &http.Request{
					Body: io.NopCloser(strings.NewReader(string(b))),
				}

				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] != 0.0 {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			Whitelist(tt.args.c)
			if tt.expect != nil {
				tt.expect(t, tt.args.w)
			}
		})
	}
}

func TestReWhitelist(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")
	objC := env.MockCluster("unit_test_rewhitelist")
	gin.SetMode(gin.TestMode)
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		c *gin.Context
		w *httptest.ResponseRecorder
	}
	tests := []struct {
		name   string
		before func()
		args   args
		expect func(*testing.T, *httptest.ResponseRecorder)
	}{
		{
			name: "test1: check args",
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Request = &http.Request{
					Body: io.NopCloser(strings.NewReader("-1")),
				}

				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] == 0.0 {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
		{
			name: "test2: user not exist",
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				params := struct {
					FlowInstanceID uint32 `json:"flowInstanceId" binding:"required"` // 流程ID
					WhitelistSchema
				}{
					FlowInstanceID: 1,
					WhitelistSchema: WhitelistSchema{
						ClusterID:   objC.ID,
						Type:        "add",
						BnsList:     []string{"unit.test"},
						Privilege:   "rw",
						Description: "111",
					},
				}
				b, _ := json.Marshal(params)
				c.Request = &http.Request{
					Body: io.NopCloser(strings.NewReader(string(b))),
				}

				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] == 0.0 {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
		{
			name: "test3: get department failed",
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Set("user", &authc.User{ID: 1, Name: "admin"})
				params := struct {
					FlowInstanceID uint32 `json:"flowInstanceId" binding:"required"` // 流程ID
					WhitelistSchema
				}{
					FlowInstanceID: 1,
					WhitelistSchema: WhitelistSchema{
						ClusterID:   objC.ID,
						Type:        "add",
						BnsList:     []string{"unit.test"},
						Privilege:   "rw",
						Description: "111",
					},
				}
				b, _ := json.Marshal(params)
				c.Request = &http.Request{
					Body: io.NopCloser(strings.NewReader(string(b))),
				}

				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] == 0.0 {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
		{
			name: "test4: resubmit flow failed",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/auth/GetUserDepart`),
					httpmock.NewStringResponder(200, "{\"departmentID\":1000000037,\"departmentName\":\"系统运维部\"}"))
			},
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Set("user", &authc.User{ID: 1, Name: "admin"})
				params := struct {
					FlowInstanceID uint32 `json:"flowInstanceId" binding:"required"` // 流程ID
					WhitelistSchema
				}{
					FlowInstanceID: 1,
					WhitelistSchema: WhitelistSchema{
						ClusterID:   objC.ID,
						Type:        "add",
						BnsList:     []string{"unit.test"},
						Privilege:   "rw",
						Description: "111",
					},
				}
				b, _ := json.Marshal(params)
				c.Request = &http.Request{
					Body: io.NopCloser(strings.NewReader(string(b))),
				}
				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] == 0.0 {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
		{
			name: "test5:success",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/v1/flow/ResubmitFlowInstance`),
					httpmock.NewStringResponder(200, "{\"errno\":0,\"errmsg\":\"\",\"data\":{\"flowInstanceId\":123}}"))
			},
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Set("user", &authc.User{ID: 1, Name: "admin"})

				params := struct {
					FlowInstanceID uint32 `json:"flowInstanceId" binding:"required"` // 流程ID
					WhitelistSchema
				}{
					FlowInstanceID: 1,
					WhitelistSchema: WhitelistSchema{
						ClusterID:   objC.ID,
						Type:        "add",
						BnsList:     []string{"unit.test"},
						Privilege:   "rw",
						Description: "111",
					},
				}

				b, _ := json.Marshal(params)
				c.Request = &http.Request{
					Body: io.NopCloser(strings.NewReader(string(b))),
				}

				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] != 0.0 {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			ReWhitelist(tt.args.c)
			if tt.expect != nil {
				tt.expect(t, tt.args.w)
			}
		})
	}
}
