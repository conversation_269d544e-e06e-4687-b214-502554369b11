package apply

import (
	"fmt"
	"strings"

	"github.com/gin-gonic/gin"
	"golang.org/x/sync/errgroup"

	"dt-common/ent/cluster"
	"dt-common/ent/flowapplycluster"
	"dt-common/errs"
	"dt-common/gintool"
	"dt-common/logger"
	"dt-common/mysql"
	"dt-common/user-center/authc"
	"dt-common/user-center/flowc"
	"redis-xweb/model"
)

// 集群访问权限申请
// 可一次申请多个集群的访问权限，每个集群一个单子
func Role(c *gin.Context) {
	type schema struct {
		ClusterIDList []int64 `json:"clusterIdList" binding:"required"`
		Role          string  `json:"role" binding:"required" validate:"oneof=user manager"`
		Description   string  `json:"description"`
	}

	//解析路由参数
	var args schema
	err := c.ShouldBindJSON(&args)
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}

	// 获取UserID
	user, err := gintool.GetUser(c)
	if err != nil {
		logger.Warn("cannot get user info, error=(%v)", err)
		gintool.JSON2FE(c, nil, errs.CodeAuthCenterRequestFailed.Detail("用户信息获取失败"))
		return
	}
	// 获取所属部门
	department, err := authc.GetUserDepartment(user.Name)
	if err != nil {
		logger.Warn("cannot get user department, username=%s, error=(%v)", user.Name, err)
		gintool.JSON2FE(c, nil, errs.CodeAuthCenterRequestFailed.Detail("获取部门信息失败"))
		return
	}

	db, err := mysql.Database()
	if err != nil {
		logger.Error("failed to get mysql connection, error=(%v)", err)
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail("数据库连接获取失败"))
		return
	}

	// 获取每一个集群的管理员，发单
	var firstNode *flowc.FlowCreateResult
	g, _ := errgroup.WithContext(c)
	for _, id := range args.ClusterIDList {
		clusterID := id
		g.Go(func() error {
			// 从认证中心获取集群管理员列表
			clusterData, err := db.Cluster.Query().
				Where(cluster.ID(clusterID)).Only(c)
			if err != nil {
				logger.Error("failed to select Cluster, clusterId: %d, error=(%v)", clusterID, err)
				return err
			}
			roleName := fmt.Sprintf("%s__%s", clusterData.Name, model.ROLE_MANAGER)
			users, err := authc.GetUsersByRole(roleName, model.ROLE_TYPE_REDIS)
			if err != nil {
				logger.Error("failed to get users, roleName: %s, roleTye: %s, error=(%v)", roleName, model.ROLE_TYPE_REDIS, err)
				return err
			}

			// step1.插入数据
			flowCluster, err := db.FlowApplyCluster.Create().
				SetClusterID(clusterID).
				SetDepartmentID(department.ID).
				SetDepartment(department.Name).
				SetApplicant(user.Name).
				SetRole(args.Role).
				SetDescription(args.Description).
				Save(c)
			if err != nil {
				logger.Error("failed to insert FlowApplyCluster, error=(%v)", err)
				return err
			}

			// step2.发单
			node, err := flowc.AddListToFlow(&flowc.FlowCreate{
				ListID:    int(flowCluster.ID),
				FlowName:  "redis_role_apply",
				ListType:  "redis_role_apply",
				Applicant: user.Name,
				FlowIndex: flowc.FlowIndexType{
					Department:    department.ID,
					FlowSubmitter: user.Name,
				},
				RoleUser: map[string]string{
					"cluster-manager": strings.Join(users, ","),
				},
			})
			if err != nil {
				logger.Warn("failed to request api AddListToFlow, flowName: redis_role_apply, error=(%v)", err)
				return err
			}
			firstNode = node

			// step3.更新单号
			_, err = flowCluster.Update().
				SetFlowInstanceID(node.FlowInstanceId).
				Save(c)
			if err != nil {
				logger.Warn("failed to update FlowCluster.FlowInstanceId, error=(%v)", err)
				return err
			}

			return nil
		})
	}
	if err = g.Wait(); err != nil {
		logger.Error("failed to create flow redis_role_apply, error=(%v)", err)
		gintool.JSON2FE(c, nil, errs.CodeCreateFlowFailed)
		return
	}

	gintool.JSON2FE(c, firstNode, nil)
}

// 重提交：集群访问权限申请
func ReRole(c *gin.Context) {
	type schema struct {
		FlowInstanceID uint32 `json:"flowInstanceId" binding:"required"` // 流程ID
		ClusterID      int64  `json:"clusterId" binding:"required"`
		Role           string `json:"role" binding:"required" validate:"oneof=user manager"`
		Description    string `json:"description"`
	}

	//解析路由参数
	var args schema
	err := c.ShouldBindJSON(&args)
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}

	// 获取UserID
	user, err := gintool.GetUser(c)
	if err != nil {
		logger.Warn("cannot get user info, error=(%v)", err)
		gintool.JSON2FE(c, nil, errs.CodeAuthCenterRequestFailed.Detail("用户信息获取失败"))
		return
	}
	// 获取所属部门
	department, err := authc.GetUserDepartment(user.Name)
	if err != nil {
		logger.Warn("cannot get user department, username=%s, error=(%v)", user.Name, err)
		gintool.JSON2FE(c, nil, errs.CodeAuthCenterRequestFailed.Detail("获取部门信息失败"))
		return
	}

	// 获取连接更新数据
	db, err := mysql.Database()
	if err != nil {
		logger.Error("failed to get mysql connection, error=(%v)", err)
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail("数据库连接获取失败"))
		return
	}
	_, err = db.FlowApplyCluster.Update().
		SetClusterID(args.ClusterID).
		SetDepartmentID(department.ID).
		SetDepartment(department.Name).
		SetApplicant(user.Name).
		SetRole(args.Role).
		SetDescription(args.Description).
		Where(flowapplycluster.FlowInstanceID(args.FlowInstanceID)).
		Save(c)
	if err != nil {
		logger.Error("failed to create flow_cluster_apply, error=(%v)", err)
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}

	// step2.重提流程
	node, err := flowc.ResubmitListToFlow(&flowc.FlowResubmit{
		FlowInstanceId: args.FlowInstanceID,
		Applicant:      user.Name,
	})
	if err != nil {
		logger.Warn("failed to resubmit flow, err:%s", err)
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}

	gintool.JSON2FE(c, node, nil)
}
