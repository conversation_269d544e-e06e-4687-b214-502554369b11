package apply

import (
	"encoding/json"
	"io"
	"net/http"
	"net/http/httptest"
	"regexp"
	"strings"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/jarcoal/httpmock"

	"dt-common/user-center/authc"
	"redis-xweb/env"
)

func TestCheckDeployArgs(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")

	type args struct {
		args *DeploySchema
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test1: area not exist",
			args: args{
				args: &DeploySchema{
					Name:            "unit_test",
					Area:            "publ2ic",
					Type:            "enterprise",
					RedisVersion:    "4.0.14",
					ShardNum:        2,
					ShardSize:       2,
					MaxmemoryPolicy: "noeviction",
					ProxyMap: map[string]int{
						"hba": 2,
						"hbb": 2,
					},
					Password:    "",
					Description: "unit_test",
					Level:       3,
					Business: map[string]float64{
						"金科": 100,
					},
					Whitelist: []string{"unit.test"},
				},
			},
			wantErr: true,
		},
		{
			name: "test2: shard size out of range",
			args: args{
				args: &DeploySchema{
					Name:            "unit_test",
					Area:            "public",
					Type:            "enterprise",
					RedisVersion:    "4.0.14",
					ShardNum:        2,
					ShardSize:       17,
					MaxmemoryPolicy: "noeviction",
					ProxyMap: map[string]int{
						"hba": 2,
						"hbb": 2,
					},
					Password:    "",
					Description: "unit_test",
					Level:       3,
					Business: map[string]float64{
						"金科": 100,
					},
					Whitelist: []string{"unit.test"},
				},
			},
			wantErr: true,
		},
		{
			name: "test3: proxy idc",
			args: args{
				args: &DeploySchema{
					Name:            "unit_test",
					Area:            "public",
					Type:            "enterprise",
					RedisVersion:    "4.0.14",
					ShardNum:        2,
					ShardSize:       16,
					MaxmemoryPolicy: "noeviction",
					ProxyMap: map[string]int{
						"hbb": 2,
					},
					Password:    "",
					Description: "unit_test",
					Level:       3,
					Business: map[string]float64{
						"金科": 100,
					},
					Whitelist: []string{"unit.test"},
				},
			},
			wantErr: true,
		},
		{
			name: "test4: proxy num = 0",
			args: args{
				args: &DeploySchema{
					Name:            "unit_test",
					Area:            "public",
					Type:            "enterprise",
					RedisVersion:    "4.0.14",
					ShardNum:        2,
					ShardSize:       16,
					MaxmemoryPolicy: "noeviction",
					ProxyMap: map[string]int{
						"hba": 0,
						"hbb": 2,
					},
					Password:    "",
					Description: "unit_test",
					Level:       3,
					Business: map[string]float64{
						"金科": 100,
					},
					Whitelist: []string{"unit.test"},
				},
			},
			wantErr: true,
		},
		{
			name: "test5: check business",
			args: args{
				args: &DeploySchema{
					Name:            "unit_test",
					Area:            "public",
					Type:            "enterprise",
					RedisVersion:    "4.0.14",
					ShardNum:        2,
					ShardSize:       16,
					MaxmemoryPolicy: "noeviction",
					ProxyMap: map[string]int{
						"hba": 2,
						"hbb": 2,
					},
					Password:    "",
					Description: "unit_test",
					Level:       3,
					Business: map[string]float64{
						"金2科": 100,
					},
					Whitelist: []string{"unit.test"},
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := CheckDeployArgs(tt.args.args)
			if (err != nil) != tt.wantErr {
				t.Errorf("CheckDeployArgs() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

func TestDeploy(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")
	gin.SetMode(gin.TestMode)
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		c *gin.Context
		w *httptest.ResponseRecorder
	}
	tests := []struct {
		name   string
		before func()
		args   args
		expect func(*testing.T, *httptest.ResponseRecorder)
	}{
		{
			name: "test1: check args",
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Request = &http.Request{
					Body: io.NopCloser(strings.NewReader("-1")),
				}

				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] == 0.0 {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
		{
			name: "test2: check deploy args",
			before: func() {
				// mock billcenter
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/api/billing/business/list`),
					httpmock.NewStringResponder(200, "{\"errno\":0,\"errmsg\":\"success\",\"data\":[\"信贷\",\"金科\",\"理财\",\"支付\",\"保险\",\"供应链金融\",\"RPA\",\"GAI\"]}"))
			},
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				params := DeploySchema{
					Name:            "unit_test",
					Area:            "publ2ic",
					Type:            "enterprise",
					RedisVersion:    "4.0.14",
					ShardNum:        2,
					ShardSize:       2,
					MaxmemoryPolicy: "noeviction",
					ProxyMap: map[string]int{
						"hba": 2,
						"hbb": 2,
					},
					Password:    "",
					Description: "unit_test",
					Level:       3,
					Business: map[string]float64{
						"金科": 100,
					},
					Whitelist: []string{"unit.test"},
				}
				b, _ := json.Marshal(params)
				c.Request = &http.Request{
					Body: io.NopCloser(strings.NewReader(string(b))),
				}
				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] == 0.0 {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
		{
			name: "test3: user not exist",
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				params := DeploySchema{
					Name:            "unit_test",
					Area:            "public",
					Type:            "enterprise",
					RedisVersion:    "4.0.14",
					ShardNum:        2,
					ShardSize:       2,
					MaxmemoryPolicy: "noeviction",
					ProxyMap: map[string]int{
						"hba": 2,
						"hbb": 2,
					},
					Password:    "",
					Description: "unit_test",
					Level:       3,
					Business: map[string]float64{
						"金科": 100,
					},
					Whitelist: []string{"unit.test"},
				}
				b, _ := json.Marshal(params)
				c.Request = &http.Request{
					Body: io.NopCloser(strings.NewReader(string(b))),
				}

				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] == 0.0 {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
		{
			name: "test4: get department failed",
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Set("user", &authc.User{ID: 1, Name: "admin"})
				params := DeploySchema{
					Name:            "unit_test",
					Area:            "public",
					Type:            "enterprise",
					RedisVersion:    "4.0.14",
					ShardNum:        2,
					ShardSize:       2,
					MaxmemoryPolicy: "noeviction",
					ProxyMap: map[string]int{
						"hba": 2,
						"hbb": 2,
					},
					Password:    "",
					Description: "unit_test",
					Level:       3,
					Business: map[string]float64{
						"金科": 100,
					},
					Whitelist: []string{"unit.test"},
				}
				b, _ := json.Marshal(params)
				c.Request = &http.Request{
					Body: io.NopCloser(strings.NewReader(string(b))),
				}

				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] == 0.0 {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
		{
			name: "test5: add flow failed",
			before: func() {
				// mock authcenter
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/auth/GetUserDepart`),
					httpmock.NewStringResponder(200, "{\"departmentID\":1000000037,\"departmentName\":\"系统运维部\"}"))
			},
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Set("user", &authc.User{ID: 1, Name: "admin"})
				params := DeploySchema{
					Name:            "unit_test",
					Area:            "public",
					Type:            "enterprise",
					RedisVersion:    "4.0.14",
					ShardNum:        2,
					ShardSize:       2,
					MaxmemoryPolicy: "noeviction",
					ProxyMap: map[string]int{
						"hba": 2,
						"hbb": 2,
					},
					Password:    "",
					Description: "unit_test",
					Level:       3,
					Business: map[string]float64{
						"金科": 100,
					},
					Whitelist: []string{"unit.test"},
				}
				b, _ := json.Marshal(params)
				c.Request = &http.Request{
					Body: io.NopCloser(strings.NewReader(string(b))),
				}

				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] == 0.0 {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
		{
			name: "test6: success",
			before: func() {
				// mock flowcenter
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/v1/flow/CreateFlowInstance`),
					httpmock.NewStringResponder(200, "{\"errno\":0,\"errmsg\":\"\",\"data\":{\"flowInstanceId\":123}}"))
			},
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Set("user", &authc.User{ID: 1, Name: "admin"})
				params := DeploySchema{
					Name:            "unit_test",
					Area:            "public",
					Type:            "enterprise",
					RedisVersion:    "4.0.14",
					ShardNum:        2,
					ShardSize:       2,
					MaxmemoryPolicy: "noeviction",
					ProxyMap: map[string]int{
						"hba": 2,
						"hbb": 2,
					},
					Password:    "",
					Description: "unit_test",
					Level:       3,
					Business: map[string]float64{
						"金科": 100,
					},
					Whitelist: []string{"unit.test"},
				}
				b, _ := json.Marshal(params)
				c.Request = &http.Request{
					Body: io.NopCloser(strings.NewReader(string(b))),
				}

				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] != 0.0 {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			Deploy(tt.args.c)
			if tt.expect != nil {
				tt.expect(t, tt.args.w)
			}
		})
	}
}

func TestReDeploy(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")
	gin.SetMode(gin.TestMode)
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		c *gin.Context
		w *httptest.ResponseRecorder
	}
	tests := []struct {
		name   string
		before func()
		args   args
		expect func(*testing.T, *httptest.ResponseRecorder)
	}{
		{
			name: "test1: check args",
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Request = &http.Request{
					Body: io.NopCloser(strings.NewReader("-1")),
				}

				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] == 0.0 {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
		{
			name: "test2: check deploy args",
			before: func() {
				// mock billcenter
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/api/billing/business/list`),
					httpmock.NewStringResponder(200, "{\"errno\":0,\"errmsg\":\"success\",\"data\":[\"信贷\",\"金科\",\"理财\",\"支付\",\"保险\",\"供应链金融\",\"RPA\",\"GAI\"]}"))
			},
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				params := struct {
					FlowInstanceID uint32
					DeploySchema
				}{
					FlowInstanceID: 1,
					DeploySchema: DeploySchema{
						Name:            "unit_test",
						Area:            "publ2ic",
						Type:            "enterprise",
						RedisVersion:    "4.0.14",
						ShardNum:        2,
						ShardSize:       2,
						MaxmemoryPolicy: "noeviction",
						ProxyMap: map[string]int{
							"hba": 2,
							"hbb": 2,
						},
						Password:    "",
						Description: "unit_test",
						Level:       3,
						Business: map[string]float64{
							"金科": 100,
						},
						Whitelist: []string{"unit.test"},
					},
				}
				b, _ := json.Marshal(params)
				c.Request = &http.Request{
					Body: io.NopCloser(strings.NewReader(string(b))),
				}
				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] == 0.0 {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
		{
			name: "test3: user not exist",
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				params := struct {
					FlowInstanceID uint32
					DeploySchema
				}{
					FlowInstanceID: 1,
					DeploySchema: DeploySchema{
						Name:            "unit_test",
						Area:            "public",
						Type:            "enterprise",
						RedisVersion:    "4.0.14",
						ShardNum:        2,
						ShardSize:       2,
						MaxmemoryPolicy: "noeviction",
						ProxyMap: map[string]int{
							"hba": 2,
							"hbb": 2,
						},
						Password:    "",
						Description: "unit_test",
						Level:       3,
						Business: map[string]float64{
							"金科": 100,
						},
						Whitelist: []string{"unit.test"},
					},
				}
				b, _ := json.Marshal(params)
				c.Request = &http.Request{
					Body: io.NopCloser(strings.NewReader(string(b))),
				}

				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] == 0.0 {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
		{
			name: "test4: get department failed",
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Set("user", &authc.User{ID: 1, Name: "admin"})
				params := struct {
					FlowInstanceID uint32
					DeploySchema
				}{
					FlowInstanceID: 1,
					DeploySchema: DeploySchema{
						Name:            "unit_test",
						Area:            "public",
						Type:            "enterprise",
						RedisVersion:    "4.0.14",
						ShardNum:        2,
						ShardSize:       2,
						MaxmemoryPolicy: "noeviction",
						ProxyMap: map[string]int{
							"hba": 2,
							"hbb": 2,
						},
						Password:    "",
						Description: "unit_test",
						Level:       3,
						Business: map[string]float64{
							"金科": 100,
						},
						Whitelist: []string{"unit.test"},
					},
				}
				b, _ := json.Marshal(params)
				c.Request = &http.Request{
					Body: io.NopCloser(strings.NewReader(string(b))),
				}

				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] == 0.0 {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
		{
			name: "test5: resubmit failed",
			before: func() {
				// mock authcenter
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/auth/GetUserDepart`),
					httpmock.NewStringResponder(200, "{\"departmentID\":1000000037,\"departmentName\":\"系统运维部\"}"))
			},
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Set("user", &authc.User{ID: 1, Name: "admin"})

				params := struct {
					FlowInstanceID uint32
					DeploySchema
				}{
					FlowInstanceID: 1,
					DeploySchema: DeploySchema{
						Name:            "unit_test",
						Area:            "public",
						Type:            "enterprise",
						RedisVersion:    "4.0.14",
						ShardNum:        2,
						ShardSize:       2,
						MaxmemoryPolicy: "noeviction",
						ProxyMap: map[string]int{
							"hba": 2,
							"hbb": 2,
						},
						Password:    "",
						Description: "unit_test",
						Level:       3,
						Business: map[string]float64{
							"金科": 100,
						},
						Whitelist: []string{"unit.test"},
					},
				}
				b, _ := json.Marshal(params)
				c.Request = &http.Request{
					Body: io.NopCloser(strings.NewReader(string(b))),
				}

				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] == 0.0 {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
		{
			name: "test6: success",
			before: func() {
				// mock flowcenter
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/v1/flow/ResubmitFlowInstance`),
					httpmock.NewStringResponder(200, "{\"errno\":0,\"errmsg\":\"\",\"data\":{\"flowInstanceId\":123}}"))
			},
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Set("user", &authc.User{ID: 1, Name: "admin"})

				params := struct {
					FlowInstanceID uint32
					DeploySchema
				}{
					FlowInstanceID: 1,
					DeploySchema: DeploySchema{
						Name:            "unit_test",
						Area:            "public",
						Type:            "enterprise",
						RedisVersion:    "4.0.14",
						ShardNum:        2,
						ShardSize:       2,
						MaxmemoryPolicy: "noeviction",
						ProxyMap: map[string]int{
							"hba": 2,
							"hbb": 2,
						},
						Password:    "",
						Description: "unit_test",
						Level:       3,
						Business: map[string]float64{
							"金科": 100,
						},
						Whitelist: []string{"unit.test"},
					},
				}
				b, _ := json.Marshal(params)
				c.Request = &http.Request{
					Body: io.NopCloser(strings.NewReader(string(b))),
				}

				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] != 0.0 {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			ReDeploy(tt.args.c)
			if tt.expect != nil {
				tt.expect(t, tt.args.w)
			}
		})
	}
}
