package apply

import (
	"github.com/gin-gonic/gin"

	"dt-common/ent/cluster"
	"dt-common/ent/flowtransfer"
	"dt-common/errs"
	"dt-common/gintool"
	"dt-common/logger"
	"dt-common/mysql"
	"dt-common/user-center/authc"
	"dt-common/user-center/flowc"
	"redis-xweb/library/errc"
	"redis-xweb/model"
)

type TransferSchema struct {
	ClusterName string `json:"clusterName" binding:"required"`
	Receiver    string `json:"receiver" binding:"required"`
	Description string `json:"description" binding:"required"`
}

// 集群转让，变更owner记录的同时还会重置成本中心订单
// 集群所属部门会改变成新owner的部门
func Transfer(c *gin.Context) {
	//解析路由参数
	var args TransferSchema
	err := c.ShouldBindJSON(&args)
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}

	// 获取当前用户的User Info
	user, err := gintool.GetUser(c)
	if err != nil {
		logger.Warn("cannot get user info, error=(%v)", err)
		gintool.JSON2FE(c, nil, errs.CodeAuthCenterRequestFailed.Detail("用户信息获取失败"))
		return
	}
	// 获取所属部门
	department, err := authc.GetUserDepartment(user.Name)
	if err != nil {
		logger.Warn("cannot get user department, username=%s, error=(%v)", user.Name, err)
		gintool.JSON2FE(c, nil, errs.CodeAuthCenterRequestFailed.Detail("部门信息获取失败"))
		return
	}

	// 接收人不能是自己
	if user.Name == args.Receiver {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail("接收人不能是自己"))
		return
	}

	// 获取集群owner
	db, err := mysql.Database()
	if err != nil {
		logger.Error("failed to get mysql connection, error=(%v)", err)
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail("数据库连接获取失败"))
		return
	}
	clusterData, err := db.Cluster.Query().Where(cluster.NameEQ(args.ClusterName)).Only(c)
	if err != nil {
		logger.Error("failed to query cluster, cluster=%s, error=(%v)", args.ClusterName, err)
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}

	// 发起者可以是集群owner 或 平台管理员
	if clusterData.Owner != user.Name {
		isAdmin, err := model.IsRedisSuperAdmin(user.Name)
		if err != nil {
			logger.Error("failed to get user roles, username=%d, cluster=%s, error=(%v)", user.Name, clusterData.Name, err)
			gintool.JSON2FE(c, nil, errs.CodeAuthCenterRequestFailed.Detail(err.Error()))
			return
		}
		if !isAdmin {
			gintool.JSON2FE(c, nil, errc.CodeHaveNoPermission)
			return
		}
	}

	// step1.插入数据
	flowData, err := db.FlowTransfer.Create().
		SetClusterID(clusterData.ID).
		SetClusterName(clusterData.Name).
		SetDepartmentID(department.ID).
		SetDepartment(department.Name).
		SetApplicant(user.Name).
		SetReceiver(args.Receiver).
		SetDescription(args.Description).
		Save(c)
	if err != nil {
		logger.Warn("failed to insert FlowWhitelist, error=(%v)", err)
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail("工单记录插入失败"))
		return
	}

	// step2.发单
	node, err := flowc.AddListToFlow(&flowc.FlowCreate{
		ListID:    int(flowData.ID),
		FlowName:  "redis_cluster_transfer",
		ListType:  "redis_cluster_transfer",
		Applicant: user.Name,
		FlowIndex: flowc.FlowIndexType{
			Department:    department.ID,
			FlowSubmitter: user.Name,
		},
		RoleUser: map[string]string{
			"cluster-receiver": args.Receiver,
		},
		FlowResources: []flowc.FlowResource{
			{
				ResourceType:  "redisCluster",
				ResourceID:    clusterData.ID,
				UniqueId:      clusterData.Name,
				ResourceLabel: "",
				Action:        "update",
			},
		},
	})
	if err != nil {
		logger.Warn("failed to create flow, flowName=redis_whitelist_apply, error=(%v)", err)
		gintool.JSON2FE(c, nil, errs.CodeFlowCenterRequestFailed.Detail("流程创建失败"))
		return
	}

	// step3.更新单号
	_, err = flowData.Update().SetFlowInstanceID(node.FlowInstanceId).Save(c)
	if err != nil {
		logger.Warn("failed to update FlowTransfer.FlowInstanceId, recordId=%d, flowInstanceID=%d, error=(%v)", flowData.ID, node.FlowInstanceId, err)
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail("工单信息更新失败"))
		return
	}

	gintool.JSON2FE(c, node, nil)
}

// 重新发单
func ReTransfer(c *gin.Context) {
	type schema struct {
		FlowInstanceID uint32 `json:"flowInstanceId" binding:"required"` // 流程ID
		TransferSchema
	}

	//解析路由参数
	var args schema
	err := c.ShouldBindJSON(&args)
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Append(err.Error()))
		return
	}

	// 获取当前用户的User Info
	user, err := gintool.GetUser(c)
	if err != nil {
		logger.Warn("cannot get user info, error=(%v)", err)
		gintool.JSON2FE(c, nil, errs.CodeAuthCenterRequestFailed.Detail("用户信息获取失败"))
		return
	}
	// 获取所属部门
	department, err := authc.GetUserDepartment(user.Name)
	if err != nil {
		logger.Warn("cannot get user department, username=%s, error=(%v)", user.Name, err)
		gintool.JSON2FE(c, nil, errs.CodeAuthCenterRequestFailed.Detail("部门信息获取失败"))
		return
	}

	// 接收人不能是自己
	if user.Name == args.Receiver {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail("接收人不能是自己"))
		return
	}

	// 获取集群owner
	db, err := mysql.Database()
	if err != nil {
		logger.Error("failed to get mysql connection, error=(%v)", err)
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail("数据库连接获取失败"))
		return
	}
	clusterData, err := db.Cluster.Query().Where(cluster.NameEQ(args.ClusterName)).Only(c)
	if err != nil {
		logger.Error("failed to query cluster, cluster=%s, error=(%v)", args.ClusterName, err)
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}

	// 发起者可以是集群owner 或 平台管理员
	if clusterData.Owner != user.Name {
		isAdmin, err := model.IsRedisSuperAdmin(user.Name)
		if err != nil {
			logger.Error("failed to get user roles, username=%d, cluster=%s, error=(%v)", user.Name, clusterData.Name, err)
			gintool.JSON2FE(c, nil, errs.CodeAuthCenterRequestFailed.Detail(err.Error()))
			return
		}
		if !isAdmin {
			gintool.JSON2FE(c, nil, errc.CodeHaveNoPermission)
			return
		}
	}

	// step1.更新数据
	_, err = db.FlowTransfer.Update().
		SetClusterID(clusterData.ID).
		SetClusterName(clusterData.Name).
		SetDepartmentID(department.ID).
		SetDepartment(department.Name).
		SetApplicant(user.Name).
		SetReceiver(args.Receiver).
		SetDescription(args.Description).
		Where(flowtransfer.FlowInstanceID(args.FlowInstanceID)).
		Save(c)
	if err != nil {
		logger.Warn("failed to update FlowTransfer, error=(%v)", err)
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail("工单记录更新失败"))
		return
	}

	// step2.重提流程
	node, err := flowc.ResubmitListToFlow(&flowc.FlowResubmit{
		FlowInstanceId: args.FlowInstanceID,
		Applicant:      user.Name,
	})
	if err != nil {
		logger.Warn("failed to resubmit flow, FlowInstanceID=%d, error=(%v)", err)
		gintool.JSON2FE(c, nil, errs.CodeFlowCenterRequestFailed.Detail("流程重提失败"))
		return
	}

	gintool.JSON2FE(c, node, nil)
}
