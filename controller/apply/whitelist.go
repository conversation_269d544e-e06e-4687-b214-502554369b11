package apply

import (
	"fmt"
	"strings"

	"github.com/gin-gonic/gin"

	"dt-common/ent/cluster"
	"dt-common/ent/flowwhitelist"
	"dt-common/errs"
	"dt-common/gintool"
	"dt-common/logger"
	"dt-common/mysql"
	"dt-common/user-center/authc"
	"dt-common/user-center/flowc"
	"redis-xweb/model"
)

type WhitelistSchema struct {
	ClusterID   int64    `json:"clusterId" binding:"required"`
	Type        string   `json:"type" binding:"required" validate:"oneof=add del"`
	BnsList     []string `json:"bnsList"`
	IpList      []string `json:"ipList"`
	Privilege   string   `json:"privilege" validate:"oneof=r rw"`
	Description string   `json:"description"`
}

// 白名单申请发单，落库、创建流程、更新ID
func Whitelist(c *gin.Context) {
	//解析路由参数
	var args WhitelistSchema
	err := c.ShouldBindJSON(&args)
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Append(err.Error()))
		return
	}

	// 获取UserID
	user, err := gintool.GetUser(c)
	if err != nil {
		logger.Warn("cannot get user info, error=(%v)", err)
		gintool.JSON2FE(c, nil, errs.CodeAuthCenterRequestFailed.Detail("用户信息获取失败"))
		return
	}
	// 获取所属部门
	department, err := authc.GetUserDepartment(user.Name)
	if err != nil {
		logger.Warn("cannot get user department, username=%s, error=(%v)", user.Name, err)
		gintool.JSON2FE(c, nil, errs.CodeAuthCenterRequestFailed.Detail("部门信息获取失败"))
		return
	}

	// 根据ClusterId查询集群信息
	db, err := mysql.Database()
	if err != nil {
		logger.Error("failed to get mysql connection, error=(%v)", err)
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail("数据库连接获取失败"))
		return
	}
	// 获取实例管理员用于流程审批
	clusterData, err := db.Cluster.Query().Where(cluster.ID(args.ClusterID)).Only(c)
	if err != nil {
		logger.Warn("failed to select Cluster, clusterId=%d, error=(%v)", args.ClusterID, err)
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail("查询出错，请稍后刷新重试"))
		return
	}
	roleName := fmt.Sprintf("%s__%s", clusterData.Name, model.ROLE_MANAGER)
	clusterManagers, err := authc.GetUsersByRole(roleName, model.ROLE_TYPE_REDIS)
	if err != nil {
		logger.Warn("failed to get users from role, roleName=%s, roleType=%s, error=(%v)", roleName, model.ROLE_TYPE_REDIS, err)
		gintool.JSON2FE(c, nil, errs.CodeAuthCenterRequestFailed.Detail("实例管理员获取失败"))
		return
	}

	// step1.插入数据
	flowData, err := db.FlowWhitelist.Create().
		SetClusterID(args.ClusterID).
		SetType(args.Type).
		SetDepartmentID(department.ID).
		SetDepartment(department.Name).
		SetApplicant(user.Name).
		SetBns(strings.Join(args.BnsList, ",")).
		SetIP(strings.Join(args.IpList, ",")).
		SetPrivilege(args.Privilege).
		SetDescription(args.Description).
		Save(c)
	if err != nil {
		logger.Warn("failed to insert FlowWhitelist, error=(%v)", err)
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail("工单记录插入失败"))
		return
	}

	// step2.发单
	node, err := flowc.AddListToFlow(&flowc.FlowCreate{
		ListID:    int(flowData.ID),
		FlowName:  "redis_whitelist_apply",
		ListType:  "redis_whitelist_apply",
		Applicant: user.Name,
		FlowIndex: flowc.FlowIndexType{
			Department:    department.ID,
			FlowSubmitter: user.Name,
		},
		RoleUser: map[string]string{
			"cluster-manager": strings.Join(clusterManagers, ","),
		},
		FlowResources: []flowc.FlowResource{
			{
				ResourceType:  "redisCluster",
				ResourceID:    clusterData.ID,
				UniqueId:      clusterData.Name,
				ResourceLabel: "",
				Action:        "update",
			},
		},
	})
	if err != nil {
		logger.Warn("failed to create flow, flowName=redis_whitelist_apply, error=(%v)", err)
		gintool.JSON2FE(c, nil, errs.CodeFlowCenterRequestFailed.Detail("流程创建失败"))
		return
	}

	// step3.更新单号
	_, err = flowData.Update().SetFlowInstanceID(node.FlowInstanceId).Save(c)
	if err != nil {
		logger.Warn("failed to update FlowWhitelist.FlowInstanceId, recordId=%d, flowInstanceID=%d, error=(%v)", flowData.ID, node.FlowInstanceId, err)
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail("工单信息更新失败"))
		return
	}

	gintool.JSON2FE(c, node, nil)
}

// 重发白名单申请，即工单信息的update
func ReWhitelist(c *gin.Context) {
	type schema struct {
		FlowInstanceID uint32 `json:"flowInstanceId" binding:"required"` // 流程ID
		WhitelistSchema
	}

	//解析路由参数
	var args schema
	err := c.ShouldBindJSON(&args)
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Append(err.Error()))
		return
	}

	// 获取UserID
	user, err := gintool.GetUser(c)
	if err != nil {
		logger.Warn("cannot get user info, error=(%v)", err)
		gintool.JSON2FE(c, nil, errs.CodeAuthCenterRequestFailed.Detail("用户信息获取失败"))
		return
	}
	// 获取所属部门
	department, err := authc.GetUserDepartment(user.Name)
	if err != nil {
		logger.Warn("cannot get user department, username=%s, error=(%v)", user.Name, err)
		gintool.JSON2FE(c, nil, errs.CodeAuthCenterRequestFailed.Detail("部门信息获取失败"))
		return
	}

	db, err := mysql.Database()
	if err != nil {
		logger.Error("failed to get mysql connection, error=(%v)", err)
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail("数据库连接获取失败"))
		return
	}

	// step1.更新数据
	_, err = db.FlowWhitelist.Update().
		SetClusterID(args.ClusterID).
		SetType(args.Type).
		SetDepartmentID(department.ID).
		SetDepartment(department.Name).
		SetApplicant(user.Name).
		SetBns(strings.Join(args.BnsList, ",")).
		SetIP(strings.Join(args.IpList, ",")).
		SetPrivilege(args.Privilege).
		SetDescription(args.Description).
		Where(flowwhitelist.FlowInstanceID(args.FlowInstanceID)).
		Save(c)
	if err != nil {
		logger.Warn("failed to update FlowWhitelist, error=(%v)", err)
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail("工单记录更新失败"))
		return
	}

	// step2.重提流程
	node, err := flowc.ResubmitListToFlow(&flowc.FlowResubmit{
		FlowInstanceId: args.FlowInstanceID,
		Applicant:      user.Name,
	})
	if err != nil {
		logger.Warn("failed to resubmit flow, FlowInstanceID=%d, error=(%v)", err)
		gintool.JSON2FE(c, nil, errs.CodeFlowCenterRequestFailed.Detail("流程重提失败"))
		return
	}

	gintool.JSON2FE(c, node, nil)
}
