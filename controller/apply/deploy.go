package apply

import (
	"encoding/json"
	"fmt"

	"github.com/gin-gonic/gin"

	"dt-common/ent/cluster"
	"dt-common/ent/flowcluster"
	"dt-common/errs"
	"dt-common/gintool"
	"dt-common/logger"
	"dt-common/mysql"
	"dt-common/user-center/authc"
	"dt-common/user-center/flowc"
	"redis-xweb/library/billing"
	"redis-xweb/model"
)

// 实例申请工单参数
type DeploySchema struct {
	Name            string             `json:"name" binding:"required"`                    // 集群名
	Area            string             `json:"area" binding:"required"`                    // 部署区域, ['public', 'fund', 'insurance', 'credit']
	Type            string             `json:"type" validate:"oneof=enterprise community"` // 引擎类型：企业版、社区版 ['enterprise', 'community']
	RedisVersion    string             `json:"redisVersion"`                               // 引擎版本
	ShardNum        int                `json:"shardNum"`                                   // 分片数量
	ShardSize       int                `json:"shardSize"`                                  // 单分片大小，单位: GB
	MaxmemoryPolicy string             `json:"maxmemoryPolicy"`                            // 内存淘汰策略
	ProxyMap        map[string]int     `json:"proxyMap"`                                   // PROXY数量, hba: int, hbb: int ...
	Password        string             `json:"password"`                                   // PROXY密码
	Description     string             `json:"description"`                                // 使用场景
	Level           int                `json:"level" binding:"required"`                   // 集群级别
	Business        map[string]float64 `json:"business"`                                   // 业务属性
	Whitelist       []string           `json:"whitelist"`                                  // BNS白名单，格式：xxx-xxx-x.xxx-xx rw
}

// 参数校验
func CheckDeployArgs(args *DeploySchema) error {
	// step1.1区域校验
	if !model.IsAreaExists(args.Area) {
		return fmt.Errorf("不存在的部署区域")
	}

	// step1.2单分片大小校验, 1 <= size <= 20
	if args.ShardSize < 1 || args.ShardSize > 16 {
		return fmt.Errorf("单分片大小应限制在[1, 16]GB之间")
	}

	// step1.3proxy数量校验
	if len(args.ProxyMap) < 2 {
		return fmt.Errorf("Proxy需要双机房部署")
	}

	for idc := range args.ProxyMap {
		if args.ProxyMap[idc] == 0 {
			return fmt.Errorf("单机房Proxy数量不能为0")
		}
	}

	// step1.4业务属性校验
	_, err := billing.CheckBusinesses(args.Business)
	if err != nil {
		return err
	}

	// step1.5集群名唯一性
	db, err := mysql.Database()
	if err != nil {
		return err
	}
	ctx, cancel := mysql.ContextWithTimeout()
	isExist, err := db.Cluster.Query().Where(cluster.Name(args.Name)).Exist(ctx)
	cancel()
	if err != nil {
		return err
	}
	if isExist {
		return fmt.Errorf("实例名已存在")
	}

	return nil
}

// 新集群申请
func Deploy(c *gin.Context) {
	// step1.1参数校验
	var args DeploySchema
	err := c.ShouldBindJSON(&args)
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}
	err = CheckDeployArgs(&args)
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}

	// step1.2 序列化
	proxy, err := json.Marshal(args.ProxyMap)
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeMarshalError.Append(err.Error()))
		return
	}
	biz, err := json.Marshal(args.Business)
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeMarshalError.Append(err.Error()))
		return
	}
	whitelist, err := json.Marshal(args.Whitelist)
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeMarshalError.Append(err.Error()))
		return
	}

	// step2.1获取username
	user, err := gintool.GetUser(c)
	if err != nil {
		logger.Warn("cannot get user info, error=(%v)", err)
		gintool.JSON2FE(c, nil, errs.CodeAuthCenterRequestFailed.Detail("用户信息获取失败"))
		return
	}
	// step2.2获取部门信息
	department, err := authc.GetUserDepartment(user.Name)
	if err != nil {
		logger.Warn("cannot get user department, username=%s, error=(%v)", user.Name, err)
		gintool.JSON2FE(c, nil, errs.CodeAuthCenterRequestFailed.Detail("获取部门信息失败"))
		return
	}

	// step3.插入数据
	db, err := mysql.Database()
	if err != nil {
		logger.Error("failed to get mysql connection, error=(%v)", err)
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail("数据库连接获取失败"))
		return
	}

	// step3.1 序列化
	flowClusterData, err := db.FlowCluster.Create().
		SetName(args.Name).
		SetType(args.Type).
		SetArea(args.Area).
		SetMaxmemoryPolicy(args.MaxmemoryPolicy).
		SetStorageSize(args.ShardNum * args.ShardSize).
		SetShardNum(args.ShardNum).
		SetShardSize(args.ShardSize).
		SetProxyMap(string(proxy)).
		SetPassword(args.Password).
		SetDepartmentID(department.ID).
		SetDepartment(department.Name).
		SetApplicant(user.Name).
		SetLevel(args.Level).
		SetBusiness(string(biz)).
		SetDescription(args.Description).
		SetWhitelist(string(whitelist)).
		Save(c)
	if err != nil {
		logger.Error("failed to insert FlowCluster, error=(%v)", err)
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}

	// step4.发起流程
	node, err := flowc.AddListToFlow(&flowc.FlowCreate{
		ListID:    int(flowClusterData.ID),
		FlowName:  "redis_cluster_apply",
		ListType:  "redis_cluster_apply",
		Applicant: user.Name,
		FlowIndex: flowc.FlowIndexType{
			Department:    department.ID,
			FlowSubmitter: user.Name,
		},
		FlowResources: []flowc.FlowResource{
			{
				ResourceType:  "redisCluster",
				ResourceID:    flowClusterData.ID,
				UniqueId:      flowClusterData.Name,
				ResourceLabel: "",
				Action:        "create",
			},
		},
	})
	if err != nil {
		logger.Error("failed to request api AddListToFlow, FlowName: redis_cluster_apply, error=(%v)", err)
		gintool.JSON2FE(c, nil, errs.CodeRequestFailed)
		return
	}

	// step5.更新flowid
	_, err = flowClusterData.Update().SetFlowInstanceID(node.FlowInstanceId).Save(c)
	if err != nil {
		logger.Error("failed to update FlowCluster.FlowInstanceId, error=(%v)", err)
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}

	gintool.JSON2FE(c, node, nil)
}

// 重提交：新集群申请
func ReDeploy(c *gin.Context) {
	type schema struct {
		FlowInstanceID uint32 `json:"flowInstanceId" binding:"required"` // 流程ID
		DeploySchema
	}

	// step1.参数校验
	var args schema
	err := c.ShouldBindJSON(&args)
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}
	err = CheckDeployArgs(&args.DeploySchema)
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
	}

	// step1.2 序列化
	proxy, err := json.Marshal(args.ProxyMap)
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeMarshalError.Append(err.Error()))
		return
	}
	biz, err := json.Marshal(args.Business)
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeMarshalError.Append(err.Error()))
		return
	}
	whitelist, err := json.Marshal(args.Whitelist)
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeMarshalError.Append(err.Error()))
		return
	}

	// step2.1获取username
	user, err := gintool.GetUser(c)
	if err != nil {
		logger.Warn("cannot get user info, error=(%v)", err)
		gintool.JSON2FE(c, nil, errs.CodeAuthCenterRequestFailed.Detail("用户信息获取失败"))
		return
	}
	// step2.2获取部门信息
	department, err := authc.GetUserDepartment(user.Name)
	if err != nil {
		logger.Warn("cannot get user department, username=%s, error=(%v)", user.Name, err)
		gintool.JSON2FE(c, nil, errs.CodeAuthCenterRequestFailed.Detail("获取部门信息失败"))
		return
	}

	// step3.更新数据
	db, err := mysql.Database()
	if err != nil {
		logger.Error("failed to get mysql connection, error=(%v)", err)
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail("数据库连接获取失败"))
		return
	}
	_, err = db.FlowCluster.Update().
		SetName(args.Name).
		SetType(args.Type).
		SetArea(args.Area).
		SetMaxmemoryPolicy(args.MaxmemoryPolicy).
		SetStorageSize(args.ShardNum * args.ShardSize).
		SetShardNum(args.ShardNum).
		SetShardSize(args.ShardSize).
		SetProxyMap(string(proxy)).
		SetPassword(args.Password).
		SetDepartmentID(department.ID).
		SetDepartment(department.Name).
		SetApplicant(user.Name).
		SetLevel(args.Level).
		SetBusiness(string(biz)).
		SetDescription(args.Description).
		SetWhitelist(string(whitelist)).
		Where(flowcluster.FlowInstanceID(args.FlowInstanceID)).
		Save(c)
	if err != nil {
		logger.Warn("failed to update FlowCluster, error=(%v)", err)
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}

	// 重提流程
	node, err := flowc.ResubmitListToFlow(&flowc.FlowResubmit{
		FlowInstanceId: args.FlowInstanceID,
		Applicant:      user.Name,
	})
	if err != nil {
		logger.Warn("failed to update FlowCluster.FlowInstanceId, error=(%v)", err)
		gintool.JSON2FE(c, nil, errs.CodeRequestFailed)
		return
	}

	gintool.JSON2FE(c, node, nil)
}
