package migration

import (
	"reflect"

	"github.com/gin-gonic/gin"

	"dt-common/errs"
	"dt-common/gintool"
	"redis-cmanager/library/stage"
	"redis-cmanager/model/component"
)

type OperateParams struct {
	StageId int64    `json:"stageId"`
	BNS     string   `json:"bns"`
	IDC     string   `json:"idc,omitempty"`
	IPList  []string `json:"ipList,omitempty"`
}

// proxy进程启动
func StartProxy(c *gin.Context) {
	var params OperateParams
	err := c.ShouldBindJSON(&params)
	if err != nil {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}
	// 入参校验
	if params.BNS == "" {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail("expect bns but got empty value"))
		return
	}
	if params.IDC == "" && len(params.IPList) <= 0 {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail("idc and iplist is empty"))
		return
	}

	args := make([]reflect.Value, 4)
	args[0] = reflect.ValueOf(params.StageId)
	args[1] = reflect.ValueOf(params.BNS)
	args[2] = reflect.ValueOf(params.IDC)
	args[3] = reflect.ValueOf(params.IPList)
	err = stage.Async(params.StageId, component.StartProxy, args...)
	if err != nil {
		gintool.JSON(c, nil, err)
		return
	}

	gintool.JSON(c, "ok", nil)
}

// proxy进程关停
func StopProxy(c *gin.Context) {
	var params OperateParams
	err := c.ShouldBindJSON(&params)
	if err != nil {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}
	// 入参校验
	if params.BNS == "" {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail("expect bns but got empty value"))
		return
	}
	if params.IDC == "" && len(params.IPList) <= 0 {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail("idc and iplist is empty"))
		return
	}

	args := make([]reflect.Value, 4)
	args[0] = reflect.ValueOf(params.StageId)
	args[1] = reflect.ValueOf(params.BNS)
	args[2] = reflect.ValueOf(params.IDC)
	args[3] = reflect.ValueOf(params.IPList)
	err = stage.Async(params.StageId, component.StopProxy, args...)
	if err != nil {
		gintool.JSON(c, nil, err)
		return
	}

	gintool.JSON(c, "ok", nil)
}
