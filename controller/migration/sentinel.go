package migration

import (
	"reflect"

	"github.com/gin-gonic/gin"

	"dt-common/errs"
	"dt-common/gintool"
	"redis-cmanager/library/stage"
	"redis-cmanager/model/component"
)

// sentinel实例启动(同步)
func StartSentinel(c *gin.Context) {
	// 1 参数解析
	var params OperateParams
	err := c.ShouldBindJSON(&params)
	if err != nil {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}
	if params.IDC == "" && len(params.IPList) <= 0 {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail("idc and iplist is empty"))
		return
	}

	args := make([]reflect.Value, 4)
	args[0] = reflect.ValueOf(params.StageId)
	args[1] = reflect.ValueOf(params.BNS)
	args[2] = reflect.ValueOf(params.IDC)
	args[3] = reflect.ValueOf(params.IPList)
	err = stage.Async(params.StageId, component.StartSentinel, args...)
	if err != nil {
		gintool.JSON(c, nil, err)
		return
	}

	gintool.JSON(c, "ok", nil)
}

// Sentinel实例关停(同步)
func StopSentinel(c *gin.Context) {
	// 1 参数解析
	var params OperateParams
	err := c.ShouldBindJSON(&params)
	if err != nil {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}
	if params.IDC == "" && len(params.IPList) <= 0 {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail("idc and iplist is empty"))
		return
	}

	args := make([]reflect.Value, 4)
	args[0] = reflect.ValueOf(params.StageId)
	args[1] = reflect.ValueOf(params.BNS)
	args[2] = reflect.ValueOf(params.IDC)
	args[3] = reflect.ValueOf(params.IPList)
	err = stage.Async(params.StageId, component.StopSentinel, args...)
	if err != nil {
		gintool.JSON(c, nil, err)
		return
	}

	gintool.JSON(c, "ok", nil)
}

type QuorumParams struct {
	StageId int64  `json:"stageId"`
	BNS     string `json:"bns"`
	Quorum  int    `json:"quorum"`
}

// 更新sentinel quorum
func SentinelQuorum(c *gin.Context) {
	// 1 参数解析
	var params QuorumParams
	err := c.ShouldBindJSON(&params)
	if err != nil {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}

	if params.Quorum <= 0 {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail("quorum should be greater than 0"))
		return
	}

	args := make([]reflect.Value, 3)
	args[0] = reflect.ValueOf(params.StageId)
	args[1] = reflect.ValueOf(params.BNS)
	args[2] = reflect.ValueOf(params.Quorum)
	err = stage.Async(params.StageId, component.UpdateQuorum, args...)
	if err != nil {
		gintool.JSON(c, nil, err)
		return
	}

	gintool.JSON(c, "ok", nil)
}
