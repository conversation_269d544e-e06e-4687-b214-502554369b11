package migration

import (
	"context"
	"encoding/json"
	"io"
	"net/http"
	"net/http/httptest"
	"regexp"
	"strings"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/jarcoal/httpmock"

	"dt-common/mysql"
	"dt-common/omodel"
	"dt-common/user-center/authc"
	"redis-xweb/env"
)

// 单测：获取迁移阶段
func TestGetStagesRawData(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")
	objC := env.MockCluster("migration")
	db, _ := mysql.Database()
	db.Sentinel.Create().SetCluster(objC).SetClusterName(objC.Name).SetIP("************").SetPort(9001).SetIdc("hba").SetBns("r3-test-bbc-sentinel.test").SetDocker(0).Save(context.Background())
	db.Sentinel.Create().SetCluster(objC).SetClusterName(objC.Name).SetIP("************").SetPort(9001).SetIdc("hba").SetBns("r3-test-bbc-sentinel.test").SetDocker(0).Save(context.Background())
	db.Sentinel.Create().SetCluster(objC).SetClusterName(objC.Name).SetIP("************").SetPort(9001).SetIdc("hbb").SetBns("r3-test-bbc-sentinel.test").SetDocker(0).Save(context.Background())

	type args struct {
		clusterId int64
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, error)
	}{
		{
			name: "test1: check params",
			args: args{
				clusterId: 99,
			},
			wantErr: true,
		},
		{
			name: "test2: check shard size",
			before: func() {
				objC.Update().SetStorageSize(16).SetShardNum(3).Save(context.Background())
			},
			args: args{
				clusterId: objC.ID,
			},
			wantErr: true,
		},
		{
			name: "test3: check shard size range",
			before: func() {
				objC.Update().SetStorageSize(17).SetShardNum(1).Save(context.Background())
			},
			args: args{
				clusterId: objC.ID,
			},
			wantErr: true,
		},
		{
			name: "test4: success",
			before: func() {
				objC.Update().SetStorageSize(16).SetShardNum(2).Save(context.Background())
			},
			args: args{
				clusterId: objC.ID,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			_, _, err := getStagesRawData(tt.args.clusterId)
			if (err != nil) != tt.wantErr {
				t.Errorf("getStagesRawData() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, err)
			}
		})
	}
}

func TestCreateTaskAndStages(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")
	objC := env.MockCluster("migration")

	type args struct {
		clusterId     int64
		clusterName   string
		stageRawDatas []*omodel.Stage
		applicant     string
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, error)
	}{
		{
			name: "test1: cluster not found",
			args: args{
				clusterId:     99,
				clusterName:   "test",
				stageRawDatas: StageList,
				applicant:     "jiayiming_dxm",
			},
			wantErr: true,
		},
		{
			name: "test2: success",
			args: args{
				clusterId:     objC.ID,
				clusterName:   objC.Name,
				stageRawDatas: StageList,
				applicant:     "jiayiming_dxm",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			_, err := createTaskAndStages(tt.args.clusterId, tt.args.clusterName, tt.args.stageRawDatas, tt.args.applicant)
			if (err != nil) != tt.wantErr {
				t.Errorf("createTaskAndStages() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, err)
			}
		})
	}
}

func TestStart(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")
	gin.SetMode(gin.TestMode)
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()
	objC := env.MockCluster("migration2")
	db, _ := mysql.Database()
	db.Sentinel.Create().SetCluster(objC).SetClusterName(objC.Name).SetIP("************").SetPort(9002).SetIdc("hba").SetBns("r3-test-bbc-sentinel.test").SetDocker(0).Save(context.Background())
	db.Sentinel.Create().SetCluster(objC).SetClusterName(objC.Name).SetIP("************").SetPort(9002).SetIdc("hba").SetBns("r3-test-bbc-sentinel.test").SetDocker(0).Save(context.Background())
	db.Sentinel.Create().SetCluster(objC).SetClusterName(objC.Name).SetIP("************").SetPort(9002).SetIdc("hbb").SetBns("r3-test-bbc-sentinel.test").SetDocker(0).Save(context.Background())

	// mock authcenter
	httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/auth/GetUsersByRole`),
		httpmock.NewStringResponder(200, "{\"errno\":0,\"errmsg\":\"\",\"data\":\"admin\"}"))

	type args struct {
		c *gin.Context
		w *httptest.ResponseRecorder
	}
	tests := []struct {
		name   string
		before func()
		args   args
		expect func(*testing.T, *httptest.ResponseRecorder)
	}{
		{
			name: "test1",
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Set("user", &authc.User{ID: 1, Name: "admin"})

				params := map[string]interface{}{
					"clusterId": objC.ID,
				}
				b, _ := json.Marshal(params)
				c.Request = &http.Request{
					Body: io.NopCloser(strings.NewReader(string(b))),
				}

				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] != 0.0 {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
		{
			name: "test2: no auth to opt",
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Set("user", &authc.User{ID: 2, Name: "user"})

				params := map[string]interface{}{
					"clusterId": objC.ID,
				}
				b, _ := json.Marshal(params)
				c.Request = &http.Request{
					Body: io.NopCloser(strings.NewReader(string(b))),
				}

				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] == 0.0 {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			Start(tt.args.c)
			if tt.expect != nil {
				tt.expect(t, tt.args.w)
			}
		})
	}
}
