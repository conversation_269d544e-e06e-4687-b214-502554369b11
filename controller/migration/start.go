package migration

import (
	"context"
	"encoding/json"

	"github.com/gin-gonic/gin"
	"golang.org/x/sync/errgroup"

	"dt-common/ent"
	"dt-common/ent/cluster"
	"dt-common/ent/sentinel"
	"dt-common/errs"
	"dt-common/gintool"
	"dt-common/logger"
	"dt-common/mysql"
	"dt-common/omodel"
	"redis-xweb/library/errc"
	"redis-xweb/model"
)

var (
	StageList = []*omodel.Stage{
		{Name: "HBB机房容器实例部署", Type: omodel.STAGE_TYPE_MIGRATION_DEPLOY},
		{Name: "添加监控", Type: omodel.STAGE_TYPE_ADD_MONITOR},
		{Name: "跨区、跨云服务发布", Type: omodel.STAGE_TYPE_EMPTY},
		{Name: "物理实例新增白名单", Type: omodel.STAGE_TYPE_UPDATE_WHITELIST},
		{Name: "容器实例新增白名单", Type: omodel.STAGE_TYPE_UPDATE_WHITELIST},
		{Name: "HBB机房容器Sentinel关停", Type: omodel.STAGE_TYPE_STOP_INSTANCES},
		{Name: "建立主从关系（物理<-容器）", Type: omodel.STAGE_TYPE_SLAVE_OF},
		{Name: "HBB机房物理从库关停", Type: omodel.STAGE_TYPE_STOP_NON_DOCKER_SLAVE},
		{Name: "主从切换", Type: omodel.STAGE_TYPE_FAILOVER},
		{Name: "HBB机房容器Sentinel启动", Type: omodel.STAGE_TYPE_START_INSTANCES},
		{Name: "HBB机房物理Sentinel关停", Type: omodel.STAGE_TYPE_STOP_INSTANCES},
		{Name: "Smart扩容", Type: omodel.STAGE_TYPE_UPDATE_GROUP_APPLIST},
		{Name: "HBB机房流量接入容器", Type: omodel.STAGE_TYPE_UNBLOCK_DOCKER_PROXY},
		{Name: "HBB机房物理Proxy关停", Type: omodel.STAGE_TYPE_STOP_INSTANCES},
		{Name: "HBB机器保留期1~3个月", Type: omodel.STAGE_TYPE_EMPTY},
		{Name: "BNS实例缩容(HBB)", Type: omodel.STAGE_TYPE_UNMOUNT_INSTANCE},
		{Name: "套餐转换(1/2)", Type: omodel.STAGE_TYPE_TRANSFORM_BILL_HALF},
		{Name: "HBB迁移已完成，请观察期结束后再执行后续阶段", Type: omodel.STAGE_TYPE_EMPTY},
		{Name: "HBA、HBC机房容器实例扩容", Type: omodel.STAGE_TYPE_COMPLETE_CLUSTER},
		{Name: "HBA机房物理Sentinel关停", Type: omodel.STAGE_TYPE_STOP_INSTANCES},
		{Name: "清理Sentinel物理监控", Type: omodel.STAGE_TYPE_CLEAN_BBC_SENTINEL_MONITOR},
		{Name: "HBA机房物理从库关停", Type: omodel.STAGE_TYPE_STOP_NON_DOCKER_SLAVE},
		{Name: "HBA机房流量接入容器", Type: omodel.STAGE_TYPE_UNBLOCK_DOCKER_PROXY},
		{Name: "HBA机房物理Proxy关停", Type: omodel.STAGE_TYPE_STOP_INSTANCES},
		{Name: "Smart缩容", Type: omodel.STAGE_TYPE_UPDATE_GROUP_APPLIST},
		{Name: "容器实例清理白名单", Type: omodel.STAGE_TYPE_UPDATE_WHITELIST},
		{Name: "HBA机器保留期1~3个月", Type: omodel.STAGE_TYPE_EMPTY},
		{Name: "BNS实例缩容(HBA)", Type: omodel.STAGE_TYPE_UNMOUNT_INSTANCE},
		{Name: "套餐转换(2/2)", Type: omodel.STAGE_TYPE_TRANSFORM_BILL_DOCKER},
	}

	StageListPay = []*omodel.Stage{
		{Name: "容器实例部署", Type: omodel.STAGE_TYPE_MIGRATION_DEPLOY},
		{Name: "添加监控", Type: omodel.STAGE_TYPE_ADD_MONITOR},
		{Name: "物理实例新增白名单", Type: omodel.STAGE_TYPE_UPDATE_WHITELIST},
		{Name: "容器实例新增白名单", Type: omodel.STAGE_TYPE_UPDATE_WHITELIST},
		{Name: "Smart扩容", Type: omodel.STAGE_TYPE_UPDATE_GROUP_APPLIST},
		{Name: "业务流量接入容器", Type: omodel.STAGE_TYPE_UNBLOCK_DOCKER_PROXY},
		{Name: "物理Proxy关停", Type: omodel.STAGE_TYPE_STOP_INSTANCES},
		{Name: "Smart缩容", Type: omodel.STAGE_TYPE_UPDATE_GROUP_APPLIST},
		{Name: "物理Sentinel关停(pay)", Type: omodel.STAGE_TYPE_STOP_INSTANCES},
		{Name: "更新SentinelQuorum", Type: omodel.STAGE_TYPE_UPDATE_SENTINEL_QUORUM},
		{Name: "容器Sentinel关停", Type: omodel.STAGE_TYPE_STOP_INSTANCES},
		{Name: "建立主从关系（物理<-容器）", Type: omodel.STAGE_TYPE_SLAVE_OF},
		{Name: "HBB机房物理从库关停", Type: omodel.STAGE_TYPE_STOP_NON_DOCKER_SLAVE},
		{Name: "主从切换", Type: omodel.STAGE_TYPE_FAILOVER},
		{Name: "容器Sentinel启动", Type: omodel.STAGE_TYPE_START_INSTANCES},
		{Name: "2/3物理Sentinel关停(siod-redis)", Type: omodel.STAGE_TYPE_STOP_INSTANCES},
		{Name: "BNS实例缩容(HBB)", Type: omodel.STAGE_TYPE_UNMOUNT_INSTANCE},
		{Name: "从库、Sentinel扩容", Type: omodel.STAGE_TYPE_COMPLETE_CLUSTER},
		{Name: "1/3物理Sentinel关停(siod-redis)", Type: omodel.STAGE_TYPE_STOP_INSTANCES},
		{Name: "清理Sentinel物理监控", Type: omodel.STAGE_TYPE_CLEAN_BBC_SENTINEL_MONITOR},
		{Name: "HBA机房物理从库关停", Type: omodel.STAGE_TYPE_STOP_NON_DOCKER_SLAVE},
		{Name: "容器实例清理白名单", Type: omodel.STAGE_TYPE_UPDATE_WHITELIST},
		{Name: "BNS实例缩容(HBA)", Type: omodel.STAGE_TYPE_UNMOUNT_INSTANCE},
		{Name: "套餐转换", Type: omodel.STAGE_TYPE_TRANSFORM_BILL_DOCKER},
	}
)

// 根据现有集群信息组装各阶段参数
// 后续阶段的绝大部分参数需要在容器集群部署完成之后才能得到
// 参数规则：object， value可以是简单类型、数组或object，但不能是 object 的数组
func getStagesRawData(clusterId int64) (string, []*omodel.Stage, error) {
	db, err := mysql.Database()
	if err != nil {
		logger.Error("failed to get mysql connection, error=(%v)", err)
		return "", nil, errs.CodeDatabase.Detail("数据库连接失败")
	}

	var clusterData *ent.Cluster
	var sentinelPort int

	ttlCtx, cancel := mysql.ContextWithTimeout()
	defer cancel()
	g, ctx := errgroup.WithContext(ttlCtx)
	// 查集群信息
	g.Go(func() error {
		clusterData, err = db.Cluster.Query().Where(cluster.ID(clusterId)).Only(ctx)
		return err
	})
	// 查sentinel port
	g.Go(func() error {
		sentinelPort, err = db.Sentinel.Query().Select(sentinel.FieldPort).
			Where(sentinel.ClusterID(clusterId)).
			GroupBy(sentinel.FieldPort).Int(ctx)
		return err
	})
	if err = g.Wait(); err != nil {
		logger.Error("failed to query database, error=(%v)", err)
		return "", nil, errs.CodeDatabase.Detail("查询出错，请稍后刷新重试")
	}

	// 配置校验:
	if clusterData.StorageSize%clusterData.ShardNum != 0 {
		return "", nil, errc.CodeClusterAbnormal.Detail("分片Quota内存必须是整数")
	}
	shardMemSize := clusterData.StorageSize / clusterData.ShardNum
	if shardMemSize < 1 || shardMemSize > 16 {
		return "", nil, errc.CodeClusterAbnormal.Detail("分片Quota内存超出范围，可选范围1~16G")
	}

	proxyNumPerIDC := clusterData.ProxyNum / 2
	var stages []*omodel.Stage

	// 集群配置写入阶段parameters
	if clusterData.ProductLine == "pay" {
		stages = append([]*omodel.Stage{}, StageListPay...)
		stages[0].Parameter = map[string]any{
			"clusterId":     clusterId,
			"name":          clusterData.Name,
			"proxyPassword": clusterData.Password,
			"proxyPort":     clusterData.Port,
			"proxyReplicas": map[string]int{
				"hba": proxyNumPerIDC,
				"hbb": proxyNumPerIDC,
			},
			"shardNum":  clusterData.ShardNum,
			"shardMem":  shardMemSize,
			"shardPort": 7000,
			"shardReplicas": map[string]int{
				"hba": 0,
				"hbb": 1,
			},
			"maxmemoryPolicy": clusterData.MaxmemoryPolicy,
			"sentinelPort":    sentinelPort,
			"sentinelReplicas": map[string]int{
				"hba": 1,
				"hbb": 1,
				"hbc": 1,
			},
			"enabledAZ": []string{"hba", "hbb"},
		}
	} else {
		stages = append([]*omodel.Stage{}, StageList...)
		stages[0].Parameter = map[string]any{
			"clusterId":     clusterId,
			"name":          clusterData.Name,
			"proxyPassword": clusterData.Password,
			"proxyPort":     clusterData.Port,
			"proxyReplicas": map[string]int{
				"hba": proxyNumPerIDC,
				"hbb": proxyNumPerIDC,
			},
			"shardNum":  clusterData.ShardNum,
			"shardMem":  shardMemSize,
			"shardPort": 7000,
			"shardReplicas": map[string]int{
				"hba": 1,
				"hbb": 1,
			},
			"maxmemoryPolicy": clusterData.MaxmemoryPolicy,
			"sentinelPort":    sentinelPort,
			"sentinelReplicas": map[string]int{
				"hba": 1,
				"hbb": 1,
				"hbc": 1,
			},
			"enabledAZ": []string{"hbb"},
		}
	}

	return clusterData.Name, stages, nil
}

// 创建任务和阶段，返回任务ID供前端跳转
func createTaskAndStages(clusterId int64, clusterName string, stageRawDatas []*omodel.Stage, applicant string) (int64, error) {
	db, err := mysql.Database()
	if err != nil {
		logger.Error("failed to get mysql connection, error=(%v)", err)
		return 0, errs.CodeDatabase.Detail("数据库连接失败")
	}

	// 开启事务，创建task和stages
	var taskId int64
	err = db.Transaction(context.Background(), func(tx *ent.Tx) error {
		// 更新cluster docker为迁移状态
		ctx, cancel := mysql.ContextWithTimeout()
		updateCount, err := tx.Cluster.Update().
			SetDocker(omodel.DEPLOY_ENV_MIGRATING).
			Where(cluster.ID(clusterId), cluster.Docker(omodel.DEPLOY_ENV_BBC)).
			Save(ctx)
		cancel()
		if err != nil {
			logger.Error("failed to update Cluster.Docker, cluster=%s, error=(%v)", clusterName, err)
			return errs.CodeDatabase.Detail("更新集群状态出错，请稍后刷新重试")
		}
		if updateCount == 0 {
			logger.Warn("failed to update Cluster.Docker, docker value may not be 1, cluster=%s, error=(%v)", clusterName, err)
			return errc.CodeClusterAbnormal.Detail("当前集群不是物理部署集群，不能发起迁移")
		}

		// 创建Task
		ctx, cancel = mysql.ContextWithTimeout()
		taskData, err := tx.Task.Create().
			SetClusterID(clusterId).SetClusterName(clusterName).SetApplicant(applicant).
			SetType(omodel.TASK_TYPE_DOCKER_MIGRATION).SetName("容器迁移").SetDescription("容器迁移").
			Save(ctx)
		cancel()
		if err != nil {
			logger.Error("failed to create migration Task, cluster=%s, error=(%v)", clusterName, err)
			return errs.CodeDatabase.Detail("迁移任务创建失败")
		}
		taskId = taskData.ID

		// 创建Stages
		bulkArray := make([]*ent.StageCreate, len(stageRawDatas))
		emptyParams := map[string]any{}
		for i, rawData := range stageRawDatas {
			if rawData.Parameter == nil {
				rawData.Parameter = emptyParams
			}
			params, err := json.Marshal(rawData.Parameter)
			if err != nil {
				logger.Warn("failed to marshal Stage.Parameter, parameter=%+v, error=(%v)", rawData.Parameter, err)
				return err
			}

			bulkArray[i] = tx.Stage.Create().
				SetTask(taskData).SetClusterName(clusterName).
				SetName(rawData.Name).SetType(rawData.Type).
				SetSequence(i + 1).SetParameter(string(params))
		}
		ctx, cancel = mysql.ContextWithTimeout()
		_, err = tx.Stage.CreateBulk(bulkArray...).Save(ctx)
		cancel()
		if err != nil {
			logger.Error("failed to bulk create Stages, cluster=%s, error=(%v)", clusterName, err)
			return errs.CodeDatabase.Append("阶段任务创建失败")
		}
		return nil
	})
	if err != nil {
		logger.Error("failed to create task, cluster=%s, error=(%v)", clusterName, err)
		return 0, err
	}

	return taskId, nil
}

// 创建迁移任务
// 条件：(1)管理员 (2)物理集群docker=0
func Start(c *gin.Context) {
	type schema struct {
		ClusterID int64 `json:"clusterId" binding:"required"`
	}

	// 解析路由参数
	var args schema
	err := c.ShouldBindJSON(&args)
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail("缺少参数clusterId"))
		return
	}

	// 判断是否为管理员发起的任务
	user, err := gintool.GetUser(c)
	if err != nil {
		logger.Warn("cannot get user info, error=(%v)", err)
		gintool.JSON2FE(c, nil, errs.CodeAuthCenterRequestFailed.Detail("用户信息获取失败"))
		return
	}
	isAdmin, err := model.IsRedisSuperAdmin(user.Name)
	if err != nil {
		logger.Warn("failed to get local role redis-manager, error=(%v)", err)
		gintool.JSON2FE(c, nil, errs.CodeAuthCenterRequestFailed.Detail("管理员鉴权失败"))
		return
	}
	if !isAdmin {
		gintool.JSON2FE(c, nil, errc.CodeHaveNoPermission.Detail("仅管理员可发起迁移任务"))
		return
	}

	// 获取阶段元数据
	clusterName, stages, err := getStagesRawData(args.ClusterID)
	if err != nil {
		gintool.JSON2FE(c, nil, err)
		return
	}

	// 创建任务
	taskId, err := createTaskAndStages(args.ClusterID, clusterName, stages, user.Name)
	if err != nil {
		gintool.JSON2FE(c, nil, err)
		return
	}
	logger.Info("succeed to create docker migration task, clusterId=%d, operator=%s", args.ClusterID, user.Name)

	// 返回taskId
	gintool.JSON2FE(c, taskId, nil)
}
