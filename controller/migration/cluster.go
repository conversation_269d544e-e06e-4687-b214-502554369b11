package migration

import (
	"reflect"

	"github.com/gin-gonic/gin"

	"dt-common/errs"
	"dt-common/gintool"
	"dt-common/omodel"
	"redis-cmanager/library/renderer"
	"redis-cmanager/library/stage"
	"redis-cmanager/model/cluster"
	"redis-cmanager/model/migration"
)

type ChangeEnabledAZParams struct {
	StageID     int64    `json:"stageId"`
	ClusterName string   `json:"clusterName"`
	EnabledAZ   []string `json:"enabledAZ"`
}

// 修改EnabledAZ
func ChangeEnabledAZ(c *gin.Context) {
	var params ChangeEnabledAZParams
	err := c.ShouldBindJSON(&params)
	if err != nil {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}

	if params.ClusterName == "" {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail("cluster name should not be empty"))
		return
	}
	if len(params.EnabledAZ) < 2 {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail("the number of enabledAZ should greater than 2"))
		return
	}

	args := make([]reflect.Value, 3)
	args[0] = reflect.ValueOf(params.StageID)
	args[1] = reflect.ValueOf(params.ClusterName)
	args[2] = reflect.ValueOf(params.EnabledAZ)
	err = stage.Async(params.StageID, cluster.ChangeEnabledAZ, args...)
	if err != nil {
		gintool.JSON(c, nil, err)
		return
	}

	gintool.JSON(c, "ok", nil)
}

// ==========================================
//			    修改集群托管模式
// ==========================================

type ChangeModeParams struct {
	StageID     int64  `json:"stageId"`
	ClusterName string `json:"clusterName"`
	Target      string `json:"target"`
}

// 修改集群的托管状态
func ChangeMode(c *gin.Context) {
	var params ChangeModeParams
	err := c.ShouldBindJSON(&params)
	if err != nil {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}

	if params.ClusterName == "" {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail("cluster name should not be empty"))
		return
	}

	switch params.Target {
	case omodel.MODE_FULL_CARE:
	case omodel.MODE_KEEP_POD_ALIVE:
	case omodel.MODE_NOT_CARE:
	default:
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail("target mode not valid"))
		return
	}

	args := make([]reflect.Value, 2)
	args[0] = reflect.ValueOf(params.ClusterName)
	args[1] = reflect.ValueOf(params.Target)
	err = stage.Async(params.StageID, renderer.UpdateDeploymentMode, args...)
	if err != nil {
		gintool.JSON(c, nil, err)
		return
	}

	gintool.JSON(c, "ok", nil)
}

// ==========================================
//			        解挂载
// ==========================================

type UnmountInstanceParams struct {
	StageId int64  `json:"stageId"`
	IDC     string `json:"idc"`
}

// 解挂载单边实例
// 1、从BNS中摘除实例
// 2、从数据库中清除记录
func UnmountInstance(c *gin.Context) {
	var params UnmountInstanceParams
	err := c.ShouldBindJSON(&params)
	if err != nil {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}
	// 入参校验
	if params.IDC == "" {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail("expect parameter idc but got empty value"))
		return
	}

	args := make([]reflect.Value, 2)
	args[0] = reflect.ValueOf(params.StageId)
	args[1] = reflect.ValueOf(params.IDC)
	err = stage.Async(params.StageId, migration.InstanceUnmount, args...)
	if err != nil {
		gintool.JSON(c, nil, err)
		return
	}

	gintool.JSON(c, "ok", nil)
}
