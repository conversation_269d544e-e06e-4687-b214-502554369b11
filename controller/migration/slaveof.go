package migration

import (
	"reflect"

	"github.com/gin-gonic/gin"

	"dt-common/errs"
	"dt-common/gintool"
	"dt-common/omodel"
	"redis-cmanager/library/stage"
	"redis-cmanager/model/component"
)

// SlaveOfParams 主从切换请求结构体
type SlaveOfParams struct {
	StageID   int64           `json:"stageId"`
	ShardList []*omodel.Shard `json:"shardList"`
}

// 建立主从
func SlaveOf(c *gin.Context) {
	var params SlaveOfParams
	err := c.ShouldBindJSON(&params)
	if err != nil {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}

	if len(params.ShardList) == 0 {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail("expect parameter shardList but got empty value"))
		return
	}

	args := make([]reflect.Value, 2)
	args[0] = reflect.ValueOf(params.StageID)
	args[1] = reflect.ValueOf(params.ShardList)
	err = stage.Async(params.StageID, component.SlaveOf, args...)
	if err != nil {
		gintool.JSON(c, nil, err)
		return
	}

	gintool.JSON(c, "ok", nil)
}

// 停止主从切换任务
func StopSlaveOf(c *gin.Context) {
	type schema struct {
		StageID int64 `json:"stageId"`
	}
	var params schema
	err := c.ShouldBindJSON(&params)
	if err != nil {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}

	args := make([]reflect.Value, 1)
	args[0] = reflect.ValueOf(params.StageID)
	err = stage.Async(params.StageID, component.StopSlaveOf, args...)
	if err != nil {
		gintool.JSON(c, nil, err)
		return
	}

	gintool.JSON(c, "ok", nil)
}
