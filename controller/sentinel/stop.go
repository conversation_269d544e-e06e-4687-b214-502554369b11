package sentinel

import (
	"github.com/gin-gonic/gin"

	"dt-common/errs"
	"dt-common/gintool"
	"dt-common/logger"
	"redis-agent/model/worker"
)

type InstanceRequest struct {
	Port  int  `json:"port" binding:"required"`
	Force bool `json:"force,omitempty"` // 强制关停
}

// sentinel启动
// agent对sentinel只做关停操作前的基础检查；
// 由xweb根据sentinel数量(容器实例+物理实例)，判断集群是否启动新物理sentinel；
func SentinelStop(c *gin.Context) {
	// 解析路由参数
	var params InstanceRequest
	err := c.ShouldBindJSON(&params)
	if err != nil {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Append(err.Error()))
		return
	}

	// 判断进程存活状态
	alive := worker.IsSentinelAlive(params.Port)
	if !alive {
		logger.Info("sentinel with port %d already stopped", params.Port)
		gintool.JSON(c, "success", nil)
		return
	}

	// 关停sentinel
	err = worker.SentinelControl(params.Port, worker.OperationStop)
	if err != nil {
		// 关停失败
		logger.Error("failed to stop sentinel on port %d, error=(%v)", params.Port, err)
		gintool.JSON(c, nil, errs.CodeShellExecFailed.Detail(err.Error()))
		return
	}

	// 返回关停成功
	logger.Info("sentinel with port %d stopped successfully", params.Port)
	gintool.JSON(c, "success", nil)
}
