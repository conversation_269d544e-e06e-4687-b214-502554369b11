package sentinel

import (
	"github.com/gin-gonic/gin"

	"dt-common/errs"
	"dt-common/gintool"
	"dt-common/logger"
	"redis-agent/model/worker"
)

// sentinel启动
// agent对sentinel只做启动操作前的基础检查；
// 由xweb根据sentinel数量(容器实例+物理实例)，判断集群是否启动新物理sentinel；
func SentinelStart(c *gin.Context) {
	type schema struct {
		Port int `json:"port" binding:"required"`
	}

	// 解析路由参数
	var params schema
	err := c.ShouldBindJSON(&params)
	if err != nil {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Append(err.Error()))
		return
	}

	// 判断进程存活状态
	alive := worker.IsSentinelAlive(params.Port)
	if alive {
		logger.Info("sentinel with port %d already started", params.Port)
		gintool.JSON(c, "success", nil)
		return
	}

	// 启动sentinel
	err = worker.SentinelControl(params.Port, worker.OperationStart)
	if err != nil {
		// 启动失败
		logger.Error("failed to start sentinel on port %d, error=(%v)", params.Port, err)
		gintool.JSON(c, nil, errs.CodeShellExecFailed.Detail(err.Error()))
		return
	}

	// 返回启动成功
	logger.Info("sentinel with port %d started successfully", params.Port)
	gintool.JSON(c, "success", nil)
}
