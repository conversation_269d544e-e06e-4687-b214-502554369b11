package sentinel

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/http/httptest"
	"os"
	"strconv"
	"strings"
	"testing"

	"github.com/gin-gonic/gin"

	"dt-common/errs"
	"dt-common/redisc"
	"dt-common/utils"
	"redis-agent/env"
)

func TestExecCommandHelper(t *testing.T) {
	if os.Getenv("GO_WANT_HELPER_PROCESS") != "1" {
		return
	}

	// println("Mocked stdout:", os.Getenv("STDOUT"))
	fmt.Fprint(os.Stdout, os.Getenv("STDOUT"))
	i, _ := strconv.Atoi(os.Getenv("EXIT_STATUS"))
	os.Exit(i)
}

// 单侧：启动sentinel
func TestStart(t *testing.T) {
	env.Mock(t, "../../mock/config/config.yaml")
	gin.SetMode(gin.TestMode)

	utils.ActivateExecMock()
	defer utils.DeactivateExecMock()

	type args struct {
		c *gin.Context
		w *httptest.ResponseRecorder
	}
	tests := []struct {
		name   string
		before func()
		args   args
		expect func(*testing.T, *httptest.ResponseRecorder)
	}{
		{
			name: "test1: check args",
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Request = &http.Request{
					Body: io.NopCloser(strings.NewReader("-1")),
				}

				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] == string(errs.Success) {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
		{
			name: "test2: check alive failed",
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				params := InstanceRequest{
					Port: 9005,
				}
				b, _ := json.Marshal(params)
				c.Request = &http.Request{
					Body: io.NopCloser(strings.NewReader(string(b))),
				}
				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] == string(errs.Success) {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
		{
			name: "test3: is still alive",
			before: func() {
				utils.ExpectExec(0, "")
			},
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				params := InstanceRequest{
					Port: 9001,
				}
				b, _ := json.Marshal(params)
				c.Request = &http.Request{
					Body: io.NopCloser(strings.NewReader(string(b))),
				}
				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] != string(errs.Success) {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
		{
			name: "test4: start failed",
			before: func() {
				utils.ExpectExec(-1, "")
				utils.ExpectExec(-1, "123123")
			},
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				params := InstanceRequest{
					Port: 9002,
				}
				b, _ := json.Marshal(params)
				c.Request = &http.Request{
					Body: io.NopCloser(strings.NewReader(string(b))),
				}
				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] == string(errs.Success) {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			SentinelStart(tt.args.c)
			if tt.expect != nil {
				tt.expect(t, tt.args.w)
			}
		})
	}
}

// 单侧：关停sentinel
func TestStop(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")
	gin.SetMode(gin.TestMode)

	utils.ActivateExecMock()
	defer utils.DeactivateExecMock()

	type args struct {
		c *gin.Context
		w *httptest.ResponseRecorder
	}
	tests := []struct {
		name   string
		before func()
		args   args
		expect func(*testing.T, *httptest.ResponseRecorder)
	}{
		{
			name: "test1: check args",
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Request = &http.Request{
					Body: io.NopCloser(strings.NewReader("-1")),
				}

				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] == string(errs.Success) {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
		{
			name: "test2: is already dead",
			before: func() {
				utils.ExpectExec(-1, "")
			},
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				params := InstanceRequest{
					Port: 9002,
				}
				b, _ := json.Marshal(params)
				c.Request = &http.Request{
					Body: io.NopCloser(strings.NewReader(string(b))),
				}
				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] != string(errs.Success) {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
		{
			name: "test3: ckquorum failed",
			before: func() {
				utils.ExpectExec(0, "")
			},
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				params := InstanceRequest{
					Port: 9001,
				}
				b, _ := json.Marshal(params)
				c.Request = &http.Request{
					Body: io.NopCloser(strings.NewReader(string(b))),
				}
				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] == string(errs.Success) {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
		{
			name: "test4: ckquorum less than sentinelNum",
			before: func() {
				utils.ExpectExec(0, "")
				rm := redisc.Mock()
				rm.ExpectDo("SENTINEL", "MASTERS").SetVal("SENTINEL MASTERS: [[name redis_public_test-server1 ip ************ port 7000 runid 403fddb743cd80a2ae393a23fe6e0bc37161820d flags master link-pending-commands 0 link-refcount 1 last-ping-sent 0 last-ok-ping-reply 639 last-ping-reply 639 down-after-milliseconds 30000 info-refresh 8540 role-reported master role-reported-time 2052594567 config-epoch 0 num-slaves 1 num-other-sentinels 0 quorum 2 failover-timeout 180000 parallel-syncs 1] [name redis_public_test-server2 ip ************ port 7001 runid 369fe0655a2be709fe957ca8a4ed6a628a961cf7 flags master link-pending-commands 0 link-refcount 1 last-ping-sent 0 last-ok-ping-reply 639 last-ping-reply 639 down-after-milliseconds 30000 info-refresh 8540 role-reported master role-reported-time 1111888057 config-epoch 0 num-slaves 1 num-other-sentinels 0 quorum 2 failover-timeout 180000 parallel-syncs 1]]")
				rm.ExpectDo("SENTINEL", "CKQUORUM", "redis_public_test-server1").SetVal("SENTINEL CKQUORUM redis_public_test-server1: NOQUORUM 1 usable Sentinels. Not enough available Sentinels to reach the specified quorum for this master")
			},
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				params := InstanceRequest{
					Port: 9001,
				}
				b, _ := json.Marshal(params)
				c.Request = &http.Request{
					Body: io.NopCloser(strings.NewReader(string(b))),
				}
				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] == string(errs.Success) {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
		{
			name: "test5: stop failed",
			before: func() {
				utils.ExpectExec(0, "")
				utils.ExpectExec(-1, "")
			},
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				params := InstanceRequest{
					Port:  9001,
					Force: true,
				}
				b, _ := json.Marshal(params)
				c.Request = &http.Request{
					Body: io.NopCloser(strings.NewReader(string(b))),
				}
				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] == string(errs.Success) {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			SentinelStop(tt.args.c)
			if tt.expect != nil {
				tt.expect(t, tt.args.w)
			}
		})
	}
}
