package cluster

import (
	"sort"
	"strings"

	"github.com/gin-gonic/gin"
	"golang.org/x/sync/errgroup"

	"dt-common/ent/cluster"
	"dt-common/ent/predicate"
	"dt-common/errs"
	"dt-common/gintool"
	"dt-common/logger"
	"dt-common/mysql"
	"dt-common/omodel"
	"dt-common/user-center/authc"
	"redis-xweb/model"
)

type ClusterApplyListResponse struct {
	ID         int64  `json:"id"`
	Name       string `json:"name"`
	<PERSON><PERSON>      string `json:"alias"`
	Department string `json:"department"`
	Owner      string `json:"owner"`
	Status     string `json:"status"`         //
	Role       string `json:"role,omitempty"` // user、manager、owner
}

// 获取用来申请权限的实例列表
func GetClusterListForApply(c *gin.Context) {
	type schema struct {
		ClusterID int64  `form:"clusterId,omitempty"`
		Keyword   string `form:"keyword,omitempty"`
		Page      int    `form:"page"`
		PageSize  int    `form:"pageSize"`
	}

	//解析路由参数
	var args schema
	err := c.Bind<PERSON>y(&args)
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}

	if args.Page == 0 {
		args.Page = 1
	}
	if args.PageSize == 0 {
		args.PageSize = 10
	}
	if args.Keyword != "" {
		args.Keyword = strings.ToLower(args.Keyword)
	}

	// 判断是不是REDIS的管理员，如果是，不需要再判断集群角色了
	user, err := gintool.GetUser(c)
	if err != nil {
		logger.Warn("cannot get user info, error=(%v)", err)
		gintool.JSON2FE(c, nil, errs.CodeAuthCenterRequestFailed.Detail("用户信息获取失败"))
		return
	}
	isAdmin, err := model.IsRedisSuperAdmin(user.Name)
	if err != nil {
		logger.Warn("failed to get local role redis-manager, error=(%v)", err)
		gintool.JSON2FE(c, nil, errs.CodeAuthCenterRequestFailed.Detail("管理员鉴权失败"))
		return
	}

	// 如果不是管理员，查一下对哪些集群有权限
	idRoleMap := make(map[string]string) // 用于记录角色
	if !isAdmin {
		// 请求获得当前用户的所有角色
		roles, err := authc.GetRoleByUserID(user.ID, model.ROLE_TYPE_REDIS)
		if err != nil {
			logger.Warn("failed to get user's redis roles, username=%s, error=(%v)", user.Name, err)
			gintool.JSON2FE(c, nil, errs.CodeAuthCenterRequestFailed.Detail())
			return
		}
		for _, role := range *roles {
			items := strings.Split(role.Name, "__")
			// 集群Name
			clusterName := items[0]
			// 真实角色
			realRole := items[1]

			if realRole != model.ROLE_MANAGER && realRole != model.ROLE_USER {
				continue
			}

			_, exists := idRoleMap[clusterName]
			if realRole == model.ROLE_MANAGER || !exists {
				idRoleMap[clusterName] = realRole
				continue
			}
		}
	}

	db, err := mysql.Database()
	if err != nil {
		logger.Error("failed to get mysql connection, error=(%v)", err)
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail("数据库连接获取失败"))
		return
	}

	// 整理查询条件, clusterID和keyword二选一
	predicates := []predicate.Cluster{
		cluster.StatusNEQ(omodel.CLUSTER_STATUS_DELETED),
	}
	if args.ClusterID != 0 {
		predicates = append(predicates, cluster.ID(args.ClusterID))
	} else {
		predicates = append(predicates, cluster.Or(
			cluster.NameContains(args.Keyword),
			cluster.SmartContains(args.Keyword),
			cluster.OwnerContains(args.Keyword),
			cluster.Department(args.Keyword),
		))
	}

	// select column
	fields := []string{
		cluster.FieldID,
		cluster.FieldName,
		cluster.FieldAlias,
		cluster.FieldDepartment,
		cluster.FieldOwner,
	}

	// 分别查询list和count
	var total int
	rows := []ClusterApplyListResponse{}
	g, _ := errgroup.WithContext(c)
	g.Go(func() error {
		return db.Cluster.Query().Where(predicates...).
			Limit(args.PageSize).Offset((args.Page-1)*args.PageSize).
			Select(fields...).Scan(c, &rows)
	})
	g.Go(func() error {
		total, err = db.Cluster.Query().Where(predicates...).Count(c)
		return err
	})
	if err := g.Wait(); err != nil {
		logger.Warn("failed to select Cluster, error=(%v)", err)
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail("查询出错，请稍后刷新重试"))
		return
	}

	// 整理数据
	for i, row := range rows {
		// 超级管理员
		if isAdmin {
			rows[i].Status = model.APPLY_STATUS_AUTHORIZED
			rows[i].Role = model.ROLE_LOCAL_MANAGER
			continue
		}

		// 集群负责人
		if row.Owner == user.Name {
			rows[i].Status = model.APPLY_STATUS_AUTHORIZED
			rows[i].Role = model.ROLE_OWNER
			continue
		}

		// 已授权角色
		if role, exist := idRoleMap[row.Name]; exist {
			rows[i].Status = model.APPLY_STATUS_AUTHORIZED
			rows[i].Role = role[3:]
			continue
		}

		// 未被授权
		rows[i].Status = model.APPLY_STATUS_UNAUTHORIZED
	}

	// 对结果进行排序，为授权的在前
	sort.SliceStable(rows, func(i, j int) bool {
		return rows[i].Status > rows[j].Status
	})

	gintool.JSON2FE(c, gintool.FormatPaging(total, rows, args.Page, args.PageSize), nil)
}
