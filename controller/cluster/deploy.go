package cluster

import (
	"reflect"

	"github.com/gin-gonic/gin"

	"dt-common/errs"
	"dt-common/gintool"
	"dt-common/logger"
	"dt-common/omodel"
	"redis-cmanager/library/renderer"
	"redis-cmanager/library/stage"
	"redis-cmanager/model/cluster"
	"redis-cmanager/model/inspection"
)

// 新集群部署
func Deploy(c *gin.Context) {
	var params omodel.DeployOptions
	err := c.ShouldBindJSON(&params)
	if err != nil {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}

	// 入参校验
	if params.ClusterID == 0 {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail("parameter ClusterID must not be 0"))
		return
	}
	if params.Name == "" {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail("parameter Name must not be \"\""))
		return
	}
	if params.ProxyPort < 1 {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail("parameter ProxyPort must > 1"))
		return
	}
	if len(params.ProxyReplicas) == 0 {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail("parameter ProxyReplicas must not be empty"))
		return
	}
	if params.ShardNum < 1 {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail("parameter ShardNum must > 1"))
		return
	}
	if params.ShardMem < 1 {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail("parameter ShardMem must > 1"))
		return
	}
	if params.ShardPort < 1 {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail("parameter ShardPort must > 1"))
		return
	}
	if len(params.ShardReplicas) == 0 {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail("parameter ShardReplicas must not be empty"))
		return
	}
	if params.SentinelPort < 1 {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail("parameter SentinelPort must > 1"))
		return
	}
	if len(params.SentinelReplicas) == 0 {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail("parameter SentinelReplicas must not be empty"))
		return
	}
	if len(params.EnabledAZ) == 0 {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail("parameter EnabledAZ must not be empty"))
		return
	}

	args := make([]reflect.Value, 1)
	args[0] = reflect.ValueOf(&params)
	err = stage.Async(params.StageID, cluster.ClusterCreate, args...)
	if err != nil {
		gintool.JSON(c, nil, err)
		return
	}

	gintool.JSON(c, "ok", nil)
}

// 获取集群渲染进度
func RealtimeRenderProgress(c *gin.Context) {
	// 参数解析
	clusterName := c.Query("clusterName")
	if clusterName == "" {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail("缺少参数clusterName"))
		return
	}

	// 获取该proxy最新的探测结果，不存在时报错
	result, err := inspection.GetRealtimeProgress(clusterName)
	if err != nil {
		logger.Error("%v", err)
		gintool.JSON(c, nil, errs.CodeNotFound.Detail(err.Error()))
		return
	}

	gintool.JSON(c, renderer.ResultInHuman(result), nil)
}
