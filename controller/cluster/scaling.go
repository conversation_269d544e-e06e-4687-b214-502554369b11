package cluster

import (
	"reflect"

	"github.com/gin-gonic/gin"

	"dt-common/errs"
	"dt-common/gintool"
	"redis-cmanager/library/stage"
	"redis-cmanager/model/cluster"
)

// 代理扩缩容
func ProxyScaling(c *gin.Context) {
	type schema struct {
		StageID     int64  `json:"stageId"`
		ClusterName string `json:"clusterName" binding:"required"`
		ProxyNum    int    `json:"proxyNum" binding:"required"`
	}
	var params schema
	err := c.ShouldBindJSON(&params)
	if err != nil {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}
	if params.ProxyNum <= 0 {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail("parameter proxyNum must be an integer greater than 0"))
		return
	}

	args := make([]reflect.Value, 3)
	args[0] = reflect.ValueOf(params.StageID)
	args[1] = reflect.ValueOf(params.ClusterName)
	args[2] = reflect.ValueOf(params.ProxyNum)
	err = stage.Async(params.StageID, cluster.ProxyScaling, args...)
	if err != nil {
		gintool.JSON(c, nil, err)
		return
	}

	gintool.JSON(c, "ok", nil)
}
