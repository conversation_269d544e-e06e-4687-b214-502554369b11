package cluster

import (
	"context"
	"strings"
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/gin-gonic/gin"
	"golang.org/x/sync/errgroup"

	"dt-common/ent"
	"dt-common/ent/cluster"
	"dt-common/ent/predicate"
	"dt-common/ent/redis"
	"dt-common/errs"
	"dt-common/gintool"
	"dt-common/logger"
	"dt-common/mysql"
	"dt-common/omodel"
	"dt-common/user-center/authc"
	"redis-xweb/model"
)

// 集群列表返回值结构
type ClusterListResponse struct {
	ID             int64      `json:"id"`             // 主键ID
	Alias          string     `json:"alias"`          // 别名
	Level          int        `json:"level"`          // 级别
	Department     string     `json:"department"`     // 归属部门
	Owner          string     `json:"owner"`          // 归属人
	StorageSize    int        `json:"storageSize"`    // Quota内存大小，单位GB
	UsedMemorySize int        `json:"usedMemorySize"` // 已用内存
	Docker         int        `json:"docker"`         // 是否容器内部署
	ProductLine    string     `json:"productLine"`    // 产品线，用于跳转noah监控
	Subsystem      string     `json:"subsystem"`      // 子系统，用于跳转noah监控
	Status         string     `json:"status"`         // 状态
	CreatedAt      *time.Time `json:"createdAt"`      // 创建时间
	IsAdmin        bool       `json:"isAdmin"`        // 是否是管理员，决定要不要展示部分信息和按钮
}

// 获取用户可见redis集群详细信息列表，分页返回
func GetClusterList(c *gin.Context) {
	type schema struct {
		Keyword  string `form:"keyword"`
		Page     int    `form:"page"`
		PageSize int    `form:"pageSize"`
	}

	// 参数校验
	var args schema
	err := c.BindQuery(&args)
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}

	if args.Page == 0 {
		args.Page = 1
	}
	if args.PageSize == 0 {
		args.PageSize = 10
	}
	if args.Keyword != "" {
		args.Keyword = strings.ToLower(args.Keyword)
	}

	// 判断是不是REDIS的管理员，如果是，不需要再判断集群角色了
	user, err := gintool.GetUser(c)
	if err != nil {
		logger.Warn("cannot get user info, error=(%v)", err)
		gintool.JSON2FE(c, nil, errs.CodeAuthCenterRequestFailed.Detail("用户信息获取失败"))
		return
	}
	isAdmin, err := model.IsRedisSuperAdmin(user.Name)
	if err != nil {
		logger.Warn("failed to get local role redis-manager, error=(%v)", err)
		gintool.JSON2FE(c, nil, errs.CodeAuthCenterRequestFailed.Detail("管理员鉴权失败"))
		return
	}
	// 如果不是admin，需要筛选clusterName用于sql查询
	clusterNames := []string{}
	if !isAdmin {
		// 请求获得当前用户的所有集群角色
		roles, err := authc.GetRoleByUserID(user.ID, model.ROLE_TYPE_REDIS)
		if err != nil {
			logger.Warn("failed to get user's redis roles, username=%s, error=(%v)", user.Name, err)
			gintool.JSON2FE(c, nil, errs.CodeAuthCenterRequestFailed.Detail())
			return
		}
		idMap := make(map[string]string)
		for _, role := range *roles {
			items := strings.Split(role.Name, "__")
			clusterName := items[0]
			if _, exists := idMap[clusterName]; !exists {
				idMap[clusterName] = items[1]
				clusterNames = append(clusterNames, clusterName)
			}
		}
	}

	db, err := mysql.Database()
	if err != nil {
		logger.Error("failed to get mysql connection, error=(%v)", err)
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail("数据库连接获取失败"))
		return
	}

	// 整理查询条件
	predicates := []predicate.Cluster{cluster.StatusNEQ(omodel.CLUSTER_STATUS_DELETED)}
	if !isAdmin {
		predicates = append(predicates, cluster.NameIn(clusterNames...))
	}
	if args.Keyword != "" {
		predicates = append(predicates, cluster.Or(
			cluster.NameContains(strings.ReplaceAll(args.Keyword, "-", "_")),
			cluster.SmartContains(args.Keyword),
			cluster.OwnerContains(args.Keyword),
			cluster.Department(args.Keyword),
		))
	}

	// select column
	fields := []string{
		cluster.FieldID,
		cluster.FieldAlias,
		cluster.FieldLevel,
		cluster.FieldDepartment,
		cluster.FieldOwner,
		cluster.FieldStorageSize,
		cluster.FieldDocker,
		cluster.FieldProductLine,
		cluster.FieldSubsystem,
		cluster.FieldStatus,
		cluster.FieldCreatedAt,
	}

	// 分别查询rows和count
	var total int
	var clusters []*ent.Cluster
	g, _ := errgroup.WithContext(context.Background())
	g.Go(func() error {
		clusters, err = db.Cluster.Query().
			Where(predicates...).
			WithRedis(func(q *ent.RedisQuery) {
				q.Select(redis.FieldUsedMemory).Where(
					redis.Role(omodel.REDIS_ROLE_MASTER),
				)
			}).
			Limit(args.PageSize).Offset((args.Page - 1) * args.PageSize).
			Order(cluster.ByID(sql.OrderDesc())).
			Select(fields...).All(c)
		return err
	})
	g.Go(func() error {
		total, err = db.Cluster.Query().Where(predicates...).Count(c)
		return err
	})
	if err := g.Wait(); err != nil {
		logger.Warn("failed to select Cluster, error=(%v)", err)
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail("查询出错，请稍后刷新重试"))
		return
	}

	// 整理数据
	rows := make([]ClusterListResponse, len(clusters))
	for i, c := range clusters {
		// 计算used_memory
		totalUsedMemory := 0
		for _, r := range c.Edges.Redis {
			totalUsedMemory += r.UsedMemory
		}

		rows[i].ID = c.ID
		rows[i].Alias = c.Alias
		rows[i].Level = c.Level
		rows[i].Department = c.Department
		rows[i].Owner = c.Owner
		rows[i].StorageSize = c.StorageSize
		rows[i].UsedMemorySize = totalUsedMemory / 1024 / 1024
		rows[i].Docker = c.Docker
		rows[i].ProductLine = c.ProductLine
		rows[i].Subsystem = c.Subsystem
		rows[i].Status = c.Status
		rows[i].CreatedAt = &c.CreatedAt
		rows[i].IsAdmin = isAdmin
	}

	gintool.JSON2FE(c, gintool.FormatPaging(total, rows, args.Page, args.PageSize), nil)
}

// 获取用户可见redis集群详细信息列表，分页返回
func GetClusterSelectList(c *gin.Context) {
	// 获取UserID
	user, err := gintool.GetUser(c)
	if err != nil {
		logger.Warn("cannot get user info, error=(%v)", err)
		gintool.JSON2FE(c, nil, errs.CodeAuthCenterRequestFailed.Detail("用户信息获取失败"))
		return
	}

	// 判断是不是REDIS的管理员，如果是，不需要再判断集群角色了
	isAdmin, err := model.IsRedisSuperAdmin(user.Name)
	if err != nil {
		logger.Warn("failed to get local role redis-manager, error=(%v)", err)
		gintool.JSON2FE(c, nil, errs.CodeAuthCenterRequestFailed.Detail("管理员鉴权失败"))
		return
	}

	// 如果不是admin，需要筛选clusterID用于sql查询
	clusterNames := []string{}
	if !isAdmin {
		// 请求获得当前用户的所有集群角色
		roles, err := authc.GetRoleByUserID(user.ID, model.ROLE_TYPE_REDIS)
		if err != nil {
			logger.Warn("failed to get user's redis roles, username=%s, error=(%v)", user.Name, err)
			gintool.JSON2FE(c, nil, errs.CodeAuthCenterRequestFailed.Detail())
			return
		}
		nameMap := make(map[string]string)
		for _, role := range *roles {
			// 集群Name
			items := strings.Split(role.Name, "__")
			clusterName := items[0]

			if _, exists := nameMap[clusterName]; !exists {
				nameMap[clusterName] = items[1]
				clusterNames = append(clusterNames, clusterName)
			}
		}
	}

	db, err := mysql.Database()
	if err != nil {
		logger.Error("failed to get mysql connection, error=(%v)", err)
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail("数据库连接获取失败"))
		return
	}

	// 整理查询条件
	predicates := []predicate.Cluster{cluster.StatusEQ(omodel.CLUSTER_STATUS_NORMAL)}
	if !isAdmin {
		predicates = append(predicates, cluster.NameIn(clusterNames...))
	}

	clusters, err := db.Cluster.Query().
		Where(predicates...).
		Select(cluster.FieldID, cluster.FieldAlias).All(c)
	if err != nil {
		logger.Error("failed to select Cluster, error=(%v)", err)
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail("查询出错，请稍后刷新重试"))
		return
	}

	// 整理数据
	rows := make([]ClusterListResponse, len(clusters))
	for i, c := range clusters {
		rows[i].ID = c.ID
		rows[i].Alias = c.Alias
	}

	gintool.JSON2FE(c, rows, nil)
}
