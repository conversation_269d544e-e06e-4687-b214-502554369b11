package cluster

import (
	"fmt"

	"github.com/gin-gonic/gin"

	"dt-common/ent/cluster"
	"dt-common/errs"
	"dt-common/gintool"
	"dt-common/logger"
	"dt-common/mysql"
	"dt-common/user-center/authc"
	"redis-xweb/library/billing"
	"redis-xweb/library/errc"
	"redis-xweb/model"
)

// 集群转让，变更owner记录的同时还会重置成本中心订单
func Transfer(c *gin.Context) {
	type schema struct {
		ClusterID int64  `json:"clusterId" binding:"required"`
		ToOwner   string `json:"toOwner" binding:"required"`
	}

	//解析路由参数
	var args schema
	err := c.ShouldBindJSON(&args)
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}

	// 修改mysql记录，将owner改为指定用户
	db, err := mysql.Database()
	if err != nil {
		logger.Error("failed to get mysql connection, error=(%v)", err)
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail("数据库连接获取失败"))
		return
	}
	// 获取实例信息
	clusterInfo, err := db.Cluster.Query().Where(cluster.IDEQ(args.ClusterID)).Only(c)
	if err != nil {
		logger.Error("failed to query Cluster, clusterID=%d, error=(%v)", args.ClusterID, err)
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}

	// 获取当前用户的User Info
	user, err := gintool.GetUser(c)
	if err != nil {
		logger.Warn("cannot get user info, error=(%v)", err)
		gintool.JSON2FE(c, nil, errs.CodeAuthCenterRequestFailed.Detail("用户信息获取失败"))
		return
	}
	hasPermission, err := model.HasWritePermission(user, clusterInfo.Name)
	if err != nil {
		logger.Error("failed to get user roles, username=%d, cluster=%s, error=(%v)", user.Name, clusterInfo.Name, err)
		gintool.JSON2FE(c, nil, errs.CodeAuthCenterRequestFailed.Detail(err.Error()))
		return
	}
	// 判断是否有权限访问
	if !hasPermission {
		gintool.JSON2FE(c, nil, errc.CodeHaveNoPermission)
		return
	}

	// 获取NewOwner的部门信息
	department, err := authc.GetUserDepartment(args.ToOwner)
	if err != nil {
		logger.Warn("cannot get user department, username=%s, error=(%v)", args.ToOwner, err)
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail("转让目标部门信息获取失败"))
		return
	}

	// 修改成本中心订单
	uniqID := clusterInfo.Name
	err = billing.Transfer(uniqID, args.ToOwner, department.ID, department.Name)
	if err != nil {
		logger.Error("failed to update billing order, error=(%v)", err)
		gintool.JSON2FE(c, nil, errs.CodeRequestFailed.Detail("转让失败，成本订单请求失败"))
		return
	}

	// 绑定角色
	managerRole := fmt.Sprintf("%s__%s", clusterInfo.Name, model.ROLE_MANAGER)
	roleData, err := authc.GetRoleByName(managerRole, model.ROLE_TYPE_REDIS)
	if err != nil {
		logger.Warn("failed to get role detail, roleName=%s, error=(%v)", managerRole, err)
		gintool.JSON(c, nil, errs.CodeAuthCenterRequestFailed.Detail("角色详情获取失败", err.Error()))
		return
	}
	err = authc.BindRoleToUserV2(args.ToOwner, model.ROLE_TYPE_REDIS, &[]int{roleData.ID})
	if err != nil {
		logger.Error("failed to bind role to user, user=%s, roleId=%d, error=(%v)", args.ToOwner, roleData.ID, err)
		err = errs.CodeAuthCenterRequestFailed.Detail("角色绑定失败")
		return
	}

	// 修改数据库记录
	ctx, cancel := mysql.ContextWithTimeout()
	_, err = db.Cluster.Update().
		SetOwner(args.ToOwner).
		Where(cluster.IDEQ(args.ClusterID)).
		Save(ctx)
	cancel()
	if err != nil {
		logger.Error("failed to update owner, clusterId=%d, error=(%v)", args.ClusterID, err)
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}

	gintool.JSON2FE(c, errs.Success, nil)
}
