package cluster

import (
	"fmt"
	"strconv"

	"github.com/gin-gonic/gin"

	"dt-common/ent"
	"dt-common/ent/cluster"
	"dt-common/ent/redis"
	"dt-common/errs"
	"dt-common/gintool"
	"dt-common/logger"
	"dt-common/mysql"
	"dt-common/omodel"
	"dt-common/user-center/authc"
	"redis-xweb/library/errc"
	"redis-xweb/model"
)

type ClusterDetail struct {
	Alias          string `json:"alias"`          // 名称
	RedisVersion   string `json:"redisVersion"`   // 版本
	Type           string `json:"type"`           // 类型
	Level          int    `json:"level"`          // 级别
	StorageSize    int    `json:"storageSize"`    // 配额内存，单位 GB
	UsedMemorySize int    `json:"usedMemorySize"` // 已用内存，单位 MB
	ShardNum       int    `json:"shardNum"`       // 分片数
	ProxyNum       int    `json:"proxyNum"`       // proxy数
	Smart          string `json:"smart"`          // smart地址
	Port           int    `json:"port"`           // 端口
	Password       string `json:"password"`       // 密码
	Department     string `json:"department"`     // 所属部门
	Owner          string `json:"owner"`          // 所属者
	CreatedAt      string `json:"createdAt"`      // 创建时间
	Status         string `json:"status"`         // 状态
}

// 获取实例详情
func GetClusterDetail(c *gin.Context) {
	// 参数解析
	clusterIdStr := c.Query("clusterId")
	if clusterIdStr == "" {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail("缺少参数clusterId"))
		return
	}

	clusterId, err := strconv.ParseInt(clusterIdStr, 10, 64)
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail("参数clusterId格式错误"))
		return
	}

	// 查询集群记录
	db, err := mysql.Database()
	if err != nil {
		logger.Error("failed to get mysql connection, error=(%v)", err)
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail("数据库连接获取失败"))
		return
	}

	// 根据ClusterId查询集群信息
	clusterData, err := db.Cluster.Query().
		Where(cluster.ID(clusterId)).
		WithRedis(func(q *ent.RedisQuery) {
			q.Select(redis.FieldUsedMemory).Where(
				redis.Role(omodel.REDIS_ROLE_MASTER),
			)
		}).
		Only(c)
	if err != nil {
		logger.Warn("failed to select Cluster, clusterId=%d, error=(%v)", clusterId, err)
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail("查询出错，请稍后刷新重试"))
		return
	}

	// 判断是否有读权限
	user, err := gintool.GetUser(c)
	if err != nil {
		logger.Warn("cannot get user info, error=(%v)", err)
		gintool.JSON2FE(c, nil, errs.CodeAuthCenterRequestFailed.Detail("用户信息获取失败"))
		return
	}
	hasPermission, err := model.HasReadPermission(user, clusterData.Name)
	if err != nil || !hasPermission {
		gintool.JSON2FE(c, nil, errc.CodeHaveNoPermission)
		return
	}

	// 判断有密码/无密码集群
	var passwordType string
	if len(clusterData.Password) != 0 {
		passwordType = "true"
	} else {
		passwordType = "false"
	}

	// 计算used_memory
	totalUsedMemory := 0
	for _, r := range clusterData.Edges.Redis {
		totalUsedMemory += r.UsedMemory
	}

	clusterDetail := ClusterDetail{
		Alias:          clusterData.Alias,
		RedisVersion:   clusterData.RedisVersion,
		Type:           clusterData.Type,
		Level:          clusterData.Level,
		StorageSize:    clusterData.StorageSize,
		UsedMemorySize: totalUsedMemory / 1024 / 1024,
		ShardNum:       clusterData.ShardNum,
		ProxyNum:       clusterData.ProxyNum,
		Smart:          clusterData.Smart,
		Port:           clusterData.Port,
		Password:       passwordType,
		Department:     clusterData.Department,
		Owner:          clusterData.Owner,
		CreatedAt:      clusterData.CreatedAt.String(),
		Status:         clusterData.Status,
	}

	gintool.JSON2FE(c, clusterDetail, nil)
}

// 获取集群管理员
func GetClusterManagers(c *gin.Context) {
	// 参数解析
	clusterIdStr := c.Query("clusterId")
	if clusterIdStr == "" {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail("缺少参数clusterId"))
		return
	}

	clusterId, err := strconv.ParseInt(clusterIdStr, 10, 64)
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail("参数clusterId格式错误"))
		return
	}

	db, err := mysql.Database()
	if err != nil {
		logger.Error("failed to get mysql connection, error=(%v)", err)
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail("数据库连接获取失败"))
		return
	}

	// 根据ClusterId查询集群信息
	clusterData, err := db.Cluster.Query().Where(cluster.ID(clusterId)).Only(c)
	if err != nil {
		logger.Warn("failed to select Cluster, clusterId=%d, error=(%v)", clusterId, err)
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail("查询出错，请稍后刷新重试"))
		return
	}

	// 查询管理员列表
	managerRole := fmt.Sprintf("%s__%s", clusterData.Name, model.ROLE_MANAGER)
	managers, err := authc.GetUsersByRole(managerRole, model.ROLE_TYPE_REDIS)
	if err != nil {
		logger.Error("failed to get users from role, roleName=%s, roleType=%s, error=(%v)", managerRole, model.ROLE_TYPE_REDIS, err)
		gintool.JSON2FE(c, nil, errs.CodeAuthCenterRequestFailed.Detail("查询出错，请稍后刷新重试"))
		return
	}

	gintool.JSON2FE(c, managers, nil)
}
