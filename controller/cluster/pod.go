package cluster

import (
	"net/http"

	"entgo.io/ent/dialect/sql"
	"github.com/gin-gonic/gin"

	"dt-common/ent/configuration"
	"dt-common/logger"
	"dt-common/mysql"
)

// 获取Agent压缩包地址
func GetAgentPackage(c *gin.Context) {
	db, err := mysql.Database()
	if err != nil {
		logger.Error("failed to get mysql connection, error=(%v)", err)
		c.String(http.StatusInternalServerError, "")
		return
	}

	ctx, cancel := mysql.ContextWithTimeout()
	row, err := db.Configuration.Query().
		Where(configuration.NameEQ("agent_package_url")).
		Order(configuration.ByVersion(sql.OrderDesc())).
		First(ctx)
	cancel()
	if err != nil {
		logger.Error("failed to query configuration agent_package_url, error=(%v)", err)
		c.String(http.StatusInternalServerError, "")
		return
	}

	// 返回结果
	c.String(http.StatusOK, row.Value)
}

// 获取Agent配置文件
func GetAgentConfig(c *gin.Context) {
	db, err := mysql.Database()
	if err != nil {
		logger.Error("failed to get mysql connection, error=(%v)", err)
		c.String(http.StatusInternalServerError, "")
		return
	}

	ctx, cancel := mysql.ContextWithTimeout()
	row, err := db.Configuration.Query().
		Where(configuration.NameEQ("agent_yaml_config")).
		Order(configuration.ByVersion(sql.OrderDesc())).
		First(ctx)
	cancel()
	if err != nil {
		logger.Error("failed to query configuration agent_yaml_config, error=(%v)", err)
		c.String(http.StatusInternalServerError, "")
		return
	}

	// 返回结果
	c.String(http.StatusOK, row.Value)
}

// 获取监控脚本压缩包地址
func GetRedisMonitorPackage(c *gin.Context) {
	db, err := mysql.Database()
	if err != nil {
		logger.Error("failed to get mysql connection, error=(%v)", err)
		c.String(http.StatusInternalServerError, "")
		return
	}

	ctx, cancel := mysql.ContextWithTimeout()
	row, err := db.Configuration.Query().
		Where(configuration.NameEQ("redis_monitor_package_url")).
		Order(configuration.ByVersion(sql.OrderDesc())).
		First(ctx)
	cancel()
	if err != nil {
		logger.Error("failed to query configuration redis_monitor_package_url, error=(%v)", err)
		c.String(http.StatusInternalServerError, "")
		return
	}

	// 返回结果
	c.String(http.StatusOK, row.Value)
}
