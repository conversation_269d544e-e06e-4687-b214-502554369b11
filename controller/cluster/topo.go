package cluster

import (
	"strconv"

	"github.com/gin-gonic/gin"

	"dt-common/ent/cluster"
	"dt-common/errs"
	"dt-common/gintool"
	"dt-common/logger"
	"dt-common/mysql"
	"redis-xweb/model"
)

type Instance struct {
	IP     string `json:"ip"`
	IDC    string `json:"idc"`
	Port   int    `json:"port"`
	BNS    string `json:"bns"`
	Name   string `json:"name,omitempty"`
	Role   string `json:"role,omitempty"`
	Docker int    `json:"docker"`
}

type Topology struct {
	Proxy    []Instance `json:"proxy"`
	Redis    []Instance `json:"redis"`
	Sentinel []Instance `json:"sentinel"`
}

// GetTopology 获取集群拓扑结构
func GetTopology(c *gin.Context) {
	// 参数解析
	clusterIdStr := c.Query("clusterId")
	if clusterIdStr == "" {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail("缺少参数clusterId"))
		return
	}

	clusterID, err := strconv.ParseInt(clusterIdStr, 10, 64)
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail("参数clusterId格式错误"))
		return
	}

	// 获取数据库连接
	db, err := mysql.Database()
	if err != nil {
		logger.Error("failed to get mysql connection, error=(%v)", err)
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail("数据库连接获取失败"))
		return
	}

	// 从数据库中获取拓扑
	clusterData, err := db.Cluster.Query().WithRedis().WithProxy().WithSentinel().Where(
		cluster.ID(clusterID),
	).Only(c)
	if err != nil {
		logger.Error("failed to query Cluster with Topology, clusterId=%d, error=(%v)", clusterID, err)
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}

	// 判断是否有读权限
	user, err := gintool.GetUser(c)
	if err != nil {
		logger.Warn("cannot get user info, error=(%v)", err)
		gintool.JSON2FE(c, nil, errs.CodeAuthCenterRequestFailed.Detail("用户信息获取失败"))
		return
	}
	hasPermission, err := model.HasReadPermission(user, clusterData.Name)
	if err != nil || !hasPermission {
		gintool.JSON2FE(c, nil, errs.CodeAuthCenterRequestFailed)
		return
	}

	redisInstances := make([]Instance, len(clusterData.Edges.Redis))
	for i, instance := range clusterData.Edges.Redis {
		redisInstances[i].BNS = instance.Bns
		redisInstances[i].IP = instance.IP
		redisInstances[i].IDC = instance.Idc
		redisInstances[i].Port = instance.Port
		redisInstances[i].Role = instance.Role
		redisInstances[i].Name = instance.Name
		redisInstances[i].Docker = instance.Docker
	}

	proxyInstances := make([]Instance, len(clusterData.Edges.Proxy))
	for i, instance := range clusterData.Edges.Proxy {
		proxyInstances[i].BNS = instance.Bns
		proxyInstances[i].IP = instance.IP
		proxyInstances[i].IDC = instance.Idc
		proxyInstances[i].Port = instance.Port
		proxyInstances[i].Docker = instance.Docker
	}

	sentinelInstances := make([]Instance, len(clusterData.Edges.Sentinel))
	for i, instance := range clusterData.Edges.Sentinel {
		sentinelInstances[i].BNS = instance.Bns
		sentinelInstances[i].IP = instance.IP
		sentinelInstances[i].IDC = instance.Idc
		sentinelInstances[i].Port = instance.Port
		sentinelInstances[i].Docker = instance.Docker
	}

	gintool.JSON2FE(c, &Topology{
		Proxy:    proxyInstances,
		Redis:    redisInstances,
		Sentinel: sentinelInstances,
	}, nil)
}
