package cluster

import (
	"github.com/gin-gonic/gin"

	"dt-common/ent/cluster"
	"dt-common/errs"
	"dt-common/gintool"
	"dt-common/logger"
	"dt-common/mysql"
	"dt-common/noah"
	"redis-xweb/library/errc"
	"redis-xweb/model"
)

// 白名单列表返回值结构1
type WhitelistResponse struct {
	BNS []WhitelistResponseItem `json:"bns"`
	IP  []WhitelistResponseItem `json:"ip"`
}

// 白名单列表返回值结构2
type WhitelistResponseItem struct {
	ID        int64  `json:"id"`
	Name      string `json:"name"`
	Privilege string `json:"privilege"`
	Health    string `json:"health,omitempty"`
}

// 获取集群白名单列表
func GetWhitelist(c *gin.Context) {
	type schema struct {
		ClusterID int64 `form:"clusterId" binding:"required"`
	}

	//解析路由参数
	var args schema
	err := c.Should<PERSON>ind<PERSON>y(&args)
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Append(err.Error()))
		return
	}

	db, err := mysql.Database()
	if err != nil {
		logger.Error("failed to get mysql connection, error=(%v)", err)
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail("数据库连接获取失败"))
		return
	}

	// 根据ClusterId查询集群信息
	clusterData, err := db.Cluster.Query().
		Where(cluster.ID(args.ClusterID)).
		WithWhitelist().
		Only(c)
	if err != nil {
		logger.Error("failed to query Cluster, clusterId=%d, error=(%v)", args.ClusterID, err)
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail("查询出错，请稍后刷新重试"))
		return
	}

	// 检查用户是否具备访问权限
	user, err := gintool.GetUser(c)
	if err != nil {
		logger.Warn("cannot get user info, error=(%v)", err)
		gintool.JSON2FE(c, nil, errs.CodeAuthCenterRequestFailed.Detail("用户信息获取失败"))
		return
	}
	hasPermission, err := model.HasReadPermission(user, clusterData.Name)
	if err != nil || !hasPermission {
		gintool.JSON2FE(c, nil, errc.CodeHaveNoPermission)
		return
	}

	// format response
	ret := WhitelistResponse{
		BNS: []WhitelistResponseItem{},
		IP:  []WhitelistResponseItem{},
	}
	for _, whitelist := range clusterData.Edges.Whitelist {
		item := WhitelistResponseItem{
			ID:        whitelist.ID,
			Name:      whitelist.Value,
			Privilege: whitelist.Privilege,
		}
		switch whitelist.Type {
		case "bns":
			_, err := noah.GetInstancesByBns(whitelist.Value)
			if err != nil {
				logger.Debug("failed to get bns instances, bns=%s, error=(%v)", whitelist.Value, err)
				item.Health = "false"
			} else {
				item.Health = "true"
			}

			ret.BNS = append(ret.BNS, item)
		case "ip":
			ret.IP = append(ret.IP, item)
		}
	}

	gintool.JSON2FE(c, ret, nil)
}
