package cluster

import (
	"github.com/gin-gonic/gin"

	"dt-common/ent/cluster"
	"dt-common/errs"
	"dt-common/gintool"
	"dt-common/logger"
	"dt-common/mysql"
	"dt-common/omodel"
	"redis-xweb/library/billing"
	"redis-xweb/library/errc"
	"redis-xweb/model"
)

// 账单返回值结构，一个集群会关联不同数量的redis和proxy套餐
type ClusterBill struct {
	ID    int     `json:"comboId,omitempty"`
	Name  string  `json:"comboName,omitempty"`
	Price float64 `json:"price"`
	Num   float64 `json:"num"`
}

// 获取集群账单
func GetClusterBill(c *gin.Context) {
	type schema struct {
		ClusterID int64 `form:"clusterId"`
	}

	// 参数校验
	var args schema
	err := c.BindQuery(&args)
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}

	// 检查集群合法性
	db, err := mysql.Database()
	if err != nil {
		logger.Error("failed to get mysql connection, error=(%v)", err)
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail("数据库连接获取失败"))
		return
	}
	clusterData, err := db.Cluster.Query().Where(
		cluster.StatusEQ(omodel.CLUSTER_STATUS_NORMAL),
		cluster.IDEQ(args.ClusterID),
	).Only(c)
	if err != nil {
		logger.Error("failed to query Cluster, id=%d, error=(%v)", args.ClusterID, err)
		gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail("未找到该集群"))
		return
	}

	// 检查用户是否具备访问权限
	user, err := gintool.GetUser(c)
	if err != nil {
		logger.Warn("cannot get user info, error=(%v)", err)
		gintool.JSON2FE(c, nil, errs.CodeAuthCenterRequestFailed.Detail("用户信息获取失败"))
		return
	}
	hasPermission, err := model.HasReadPermission(user, clusterData.Name)
	if err != nil || !hasPermission {
		gintool.JSON2FE(c, nil, errc.CodeHaveNoPermission)
		return
	}

	// 根据集群的docker值，获取账单信息
	var result ClusterBill
	// 获取账单信息，账单的uniqId用cluster.Name字段
	orderInfos, err := billing.GetInstanceInfos([]string{clusterData.Name})
	if err != nil {
		logger.Error("failed to get cluster bill info, cluster=%s, error=(%v)", clusterData.Name, err)
		gintool.JSON2FE(c, nil, errs.CodeRequestFailed.Detail("成本中心请求失败"))
		return
	}
	if orderInfos == nil || len(*orderInfos) == 0 {
		logger.Error("Billing instance not found, uniqId=%s", clusterData.Name)
		gintool.JSON2FE(c, nil, errc.CodeBillNotFound)
		return
	}

	order := (*orderInfos)[0]
	switch len(order.DynamicComboGroup) {
	case 0:
		result.ID = order.ComboId
		result.Name = order.ComboName
		result.Price = order.MonthPrice
		result.Num = order.SnapInfo["num"].(float64)
	case 4:
		result.Name = "容器实例组合套餐"
		result.Price = 0
		result.Num = 1
		for _, comboInfo := range order.DynamicComboGroup {
			result.Price += comboInfo.UnitPrice * comboInfo.ComboNum
		}
	case 5:
		result.Name = "容器化过渡套餐"
		result.Price = 0
		result.Num = 1
		for _, comboInfo := range order.DynamicComboGroup {
			result.Price += comboInfo.UnitPrice * comboInfo.ComboNum
		}
	default:
		gintool.JSON2FE(c, nil, errc.CodeBillNotFound.Detail("套餐组合解析失败"))
		return
	}

	gintool.JSON2FE(c, &result, nil)
}

// 获取所有业务属性
func GetAllBusinesses(c *gin.Context) {
	bizs, err := billing.GetAllBusinesses()
	gintool.JSON2FE(c, bizs, err)
}
