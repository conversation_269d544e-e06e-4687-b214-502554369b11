package cluster

import (
	"sort"

	"github.com/gin-gonic/gin"

	"dt-common/errs"
	"dt-common/gintool"
	"dt-common/logger"
	"redis-xweb/model"
)

// 获取可用部署区域，从配置文件里获取
func GetAreas(c *gin.Context) {
	areas, err := model.GetAreaList()
	if err != nil {
		logger.Warn("failed to get cluster area, error=(%v)", err)
		gintool.JSON2FE(c, nil, errs.CodeMissingConfig.Detail("可选地区获取失败"))
		return
	}

	// 把公共区域放最前
	sort.Slice(*areas, func(i, j int) bool {
		return (*areas)[i].Name == "public"
	})

	gintool.JSON2FE(c, areas, nil)
}
