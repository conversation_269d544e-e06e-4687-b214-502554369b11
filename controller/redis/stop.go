package redis

import (
	"fmt"
	"strconv"

	"github.com/gin-gonic/gin"

	"dt-common/errs"
	"dt-common/gintool"
	"dt-common/logger"
	"dt-common/omodel"
	"dt-common/redisc"
	"redis-agent/config"
	"redis-agent/library/errc"
	"redis-agent/model/worker"
)

type InstanceRequest struct {
	Port  int  `json:"port" binding:"required"`
	Force bool `json:"force,omitempty"` // 强制关停
}

// redis 关停
func RedisStop(c *gin.Context) {
	// 解析路由参数
	var params InstanceRequest
	err := c.ShouldBindJSON(&params)
	if err != nil {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Append(err.Error()))
		return
	}

	// 判断进程存活状态
	alive := worker.IsRedisAlive(params.Port)
	if !alive {
		logger.Info("redis with port %d already stopped", params.Port)
		gintool.JSON(c, "success", nil)
		return
	}

	// 此处修改为使用127.0.0.1 因为使用10开头的ip在初始部署的时候 ip会有不在白名单中的情况，导致执行命令没权限
	localIP, err := config.GetString("env/local_ip")
	if err != nil {
		localIP = "127.0.0.1"
	}
	info, err := redisc.Info(localIP, params.Port, "Replication")
	if err != nil {
		logger.Error("failed to get the information of redis with port %d, error=(%v)", params.Port, err)
		gintool.JSON(c, nil, errc.CodeRedisExecFailed.Append(err.Error()))
		return
	}

	// 1、检查实例是否符合角色
	if info["role"] != omodel.REDIS_ROLE_SLAVE {
		errMsg := fmt.Sprintf("the redis with port %d is not a slave", params.Port)
		logger.Error(errMsg)
		gintool.JSON(c, nil, errc.CodeOperationError.Append(errMsg))
		return
	}

	// 2、检查该分片是否还有其他从库，如果强制关停则不检查
	if params.Force {
		masterPort, err := strconv.Atoi(info["master_port"])
		if err != nil {
			errMsg := fmt.Sprintf("failed to convert info[master_host] to int, value=%s", info["master_port"])
			logger.Error(errMsg)
			gintool.JSON(c, nil, errc.CodeOperationError.Append(errMsg))
			return
		}
		slaveNum, err := getSlaveNum(info["master_host"], masterPort)
		if err != nil {
			errMsg := fmt.Sprintf("failed to get slave num, error=(%v)", err)
			logger.Warn(errMsg)
			gintool.JSON(c, nil, errc.CodeOperationError.Append(errMsg))
			return
		}
		// 至少有1个从库才能关停
		if slaveNum < 2 {
			errMsg := fmt.Sprintf("shard has less than 2 slaves, can not shutdown a slave, port=%d", params.Port)
			logger.Warn(errMsg)
			gintool.JSON(c, nil, fmt.Errorf("not enough slaves"))
			return
		}
	}

	// 关停redis
	err = worker.RedisControl(params.Port, worker.OperationStop)
	if err != nil {
		logger.Error("failed to stop redis on port %d, error=(%v)", params.Port, err)
		gintool.JSON(c, nil, errc.CodeRedisExecFailed.Append(err.Error()))
		return
	}

	// 关停成功，执行成功日志会在Stop方法中打印
	gintool.JSON(c, "success", nil)
}

func getSlaveNum(masterIP string, masterPort int) (int, error) {
	info, err := redisc.Info(masterIP, masterPort, "Replication")
	if err != nil {
		return -1, fmt.Errorf("failed to get redis info from %s:%d, error=(%v)", masterIP, masterPort, err)
	}
	if info["role"] != omodel.REDIS_ROLE_MASTER {
		return -1, fmt.Errorf("redis %s:%d is not a master", masterIP, masterPort)
	}

	connectedSlaves, exist := info["connected_slaves"]
	if !exist {
		return -1, fmt.Errorf("connected_slaves not found in redis info, port=%d", masterPort)
	}

	slaveNum, err := strconv.Atoi(connectedSlaves)
	if err != nil {
		return -1, fmt.Errorf("failed to convert info[connected_slaves] to int, value=%s", info["connected_slaves"])
	}

	return slaveNum, nil
}
