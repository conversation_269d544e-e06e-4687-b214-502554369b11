package redis

import (
	"github.com/gin-gonic/gin"

	"dt-common/errs"
	"dt-common/gintool"
	"dt-common/logger"
	"redis-agent/library/errc"
	"redis-agent/model/worker"
)

// redis启动
func RedisStart(c *gin.Context) {
	type schema struct {
		Port int `json:"port" binding:"required"`
	}

	// 解析路由参数
	var params schema
	err := c.ShouldBindJSON(&params)
	if err != nil {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Append(err.Error()))
		return
	}
	// 判断进程存活状态
	alive := worker.IsRedisAlive(params.Port)
	if alive {
		logger.Info("redis with port %d already started", params.Port)
		gintool.JSON(c, "success", nil)
		return
	}

	// 启动redis
	err = worker.RedisControl(params.Port, worker.OperationStart)
	if err != nil {
		// redis启动失败；
		logger.Error("failed to start Redis on port %d, error=(%v)", params.Port, err)
		gintool.JSON(c, nil, errc.CodeRedisExecFailed.Append(err.Error()))
		return
	}

	// 启动成功，执行成功日志会在Start方法中打印
	gintool.JSON(c, "success", nil)
}
