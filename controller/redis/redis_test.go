package redis

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/http/httptest"
	"os"
	"strconv"
	"strings"
	"testing"

	"github.com/gin-gonic/gin"

	"dt-common/errs"
	"dt-common/redisc"
	"dt-common/utils"
	"redis-agent/env"
)

func TestExecCommandHelper(t *testing.T) {
	if os.Getenv("GO_WANT_HELPER_PROCESS") != "1" {
		return
	}

	// println("Mocked stdout:", os.Getenv("STDOUT"))
	fmt.Fprint(os.Stdout, os.Getenv("STDOUT"))
	i, _ := strconv.Atoi(os.Getenv("EXIT_STATUS"))
	os.Exit(i)
}

// 单测：集群从库关停
func TestGetShardSlaveNum(t *testing.T) {
	// 初始化配置
	env.Mock(t, "../../config/config.yaml")
	rm := redisc.Mock()

	type args struct {
		ip   string
		port int
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, int)
	}{
		{
			name: "test1",
			before: func() {
				rm.ExpectInfo("Replication").SetVal("role:master\nconnected_slaves:2")
			},
			args:    args{ip: "************", port: 7010},
			wantErr: false,
			expect: func(t *testing.T, num int) {
				if num != 2 {
					t.Errorf("getShardSlaveNum() = %v, want %v", num, 2)
				}
			},
		},
		{
			name: "test2",
			before: func() {
				rm.ExpectInfo("Replication").SetVal("role:master\nconnected_slaves:1")
			},
			args:    args{ip: "************", port: 7010},
			wantErr: false,
			expect: func(t *testing.T, num int) {
				if num != 1 {
					t.Errorf("getShardSlaveNum() = %v, want %v", num, 2)
				}
			},
		},
		{
			name: "test3",
			before: func() {
				rm.ExpectInfo("Replication").SetVal("role:slave")
			},
			args:    args{ip: "************", port: 7010},
			wantErr: true,
		},
		{
			name: "test4",
			before: func() {
				rm.ExpectInfo("Replication").SetVal("role:master")
			},
			args:    args{ip: "************", port: 7010},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			num, err := getSlaveNum(tt.args.ip, tt.args.port)
			if (err != nil) != tt.wantErr {
				t.Errorf("getShardSlaveNum() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, num)
			}
		})
	}
}

// 单侧：关停redis
func TestStart(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")
	gin.SetMode(gin.TestMode)

	utils.ActivateExecMock()
	defer utils.DeactivateExecMock()

	type args struct {
		c *gin.Context
		w *httptest.ResponseRecorder
	}
	tests := []struct {
		name   string
		before func()
		args   args
		expect func(*testing.T, *httptest.ResponseRecorder)
	}{
		{
			name: "test1: check args",
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Request = &http.Request{
					Body: io.NopCloser(strings.NewReader("-1")),
				}
				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] == string(errs.Success) {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
		{
			name: "test2: is still alive",
			before: func() {
				utils.ExpectExec(0, "")
			},
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				params := InstanceRequest{
					Port: 7000,
				}
				b, _ := json.Marshal(params)
				c.Request = &http.Request{
					Body: io.NopCloser(strings.NewReader(string(b))),
				}
				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] != string(errs.Success) {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
		{
			name: "test3: success",
			before: func() {
				utils.ExpectExec(-1, "1")
				utils.ExpectExec(0, "2")
				utils.ExpectExec(0, "3")
			},
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				params := InstanceRequest{
					Port: 7001,
				}
				b, _ := json.Marshal(params)
				c.Request = &http.Request{
					Body: io.NopCloser(strings.NewReader(string(b))),
				}
				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] != string(errs.Success) {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			RedisStart(tt.args.c)
			if tt.expect != nil {
				tt.expect(t, tt.args.w)
			}
		})
	}
}

// 单侧：关停redis
func TestStop(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")
	gin.SetMode(gin.TestMode)

	utils.ActivateExecMock()
	defer utils.DeactivateExecMock()

	type args struct {
		c *gin.Context
		w *httptest.ResponseRecorder
	}
	tests := []struct {
		name   string
		before func()
		args   args
		expect func(*testing.T, *httptest.ResponseRecorder)
	}{
		{
			name: "test1: check args",
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Request = &http.Request{
					Body: io.NopCloser(strings.NewReader("-1")),
				}
				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] == string(errs.Success) {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
		{
			name: "test2: is already dead",
			before: func() {
				utils.ExpectExec(-1, "1")
			},
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				params := InstanceRequest{
					Port: 7001,
				}
				b, _ := json.Marshal(params)
				c.Request = &http.Request{
					Body: io.NopCloser(strings.NewReader(string(b))),
				}
				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] != string(errs.Success) {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
		{
			name: "test3: get info failed",
			before: func() {
				utils.ExpectExec(0, "1")
				rm := redisc.Mock()
				rm.ExpectInfo("Replication").SetErr(fmt.Errorf("123"))
			},
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				params := InstanceRequest{
					Port: 7000,
				}
				b, _ := json.Marshal(params)
				c.Request = &http.Request{
					Body: io.NopCloser(strings.NewReader(string(b))),
				}
				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] == string(errs.Success) {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
		{
			name: "test4: not a slave",
			before: func() {
				utils.ExpectExec(0, "1")
				rm := redisc.Mock()
				rm.ExpectInfo("Replication").SetVal("role:master\nconnected_slaves:2")
			},
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				params := InstanceRequest{
					Port: 7000,
				}
				b, _ := json.Marshal(params)
				c.Request = &http.Request{
					Body: io.NopCloser(strings.NewReader(string(b))),
				}
				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] == string(errs.Success) {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
		{
			name: "test5: port convert failed",
			before: func() {
				utils.ExpectExec(0, "1")
				rm := redisc.Mock()
				rm.ExpectInfo("Replication").SetVal("role:slave\nmaster_port:700a0\nmaster_host:************")
			},
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				params := InstanceRequest{
					Port:  7000,
					Force: true,
				}
				b, _ := json.Marshal(params)
				c.Request = &http.Request{
					Body: io.NopCloser(strings.NewReader(string(b))),
				}
				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] == string(errs.Success) {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
		{
			name: "test6: get slave num failed",
			before: func() {
				utils.ExpectExec(0, "1")
				rm := redisc.Mock()
				rm.ExpectInfo("Replication").SetVal("role:slave\nmaster_port:7000\nmaster_host:************")
				rm.ExpectInfo("Replication").SetVal("role:slave\nmaster_port:7000\nmaster_host:************")
			},
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				params := InstanceRequest{
					Port:  7000,
					Force: true,
				}
				b, _ := json.Marshal(params)
				c.Request = &http.Request{
					Body: io.NopCloser(strings.NewReader(string(b))),
				}
				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] == string(errs.Success) {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
		{
			name: "test7: less than 2 slave",
			before: func() {
				utils.ExpectExec(0, "1")
				rm := redisc.Mock()
				rm.ExpectInfo("Replication").SetVal("role:slave\nmaster_port:7000\nmaster_host:************")
				rm.ExpectInfo("Replication").SetVal("role:master\nconnected_slaves:1")
			},
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				params := InstanceRequest{
					Port:  7000,
					Force: true,
				}
				b, _ := json.Marshal(params)
				c.Request = &http.Request{
					Body: io.NopCloser(strings.NewReader(string(b))),
				}
				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] == string(errs.Success) {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
		{
			name: "test8: success",
			before: func() {
				utils.ExpectExec(0, "1")
				rm := redisc.Mock()
				rm.ExpectInfo("Replication").SetVal("role:slave\nmaster_port:7000\nmaster_host:************")

				utils.ExpectExec(0, "1")
				utils.ExpectExec(-1, "1")
			},
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				params := InstanceRequest{
					Port: 7000,
				}
				b, _ := json.Marshal(params)
				c.Request = &http.Request{
					Body: io.NopCloser(strings.NewReader(string(b))),
				}
				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] != string(errs.Success) {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			RedisStop(tt.args.c)
			if tt.expect != nil {
				tt.expect(t, tt.args.w)
			}
		})
	}
}
