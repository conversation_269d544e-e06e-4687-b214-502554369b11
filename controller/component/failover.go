package component

import (
	"reflect"

	"github.com/gin-gonic/gin"

	"dt-common/errs"
	"dt-common/gintool"
	"dt-common/omodel"
	"redis-cmanager/library/stage"
	"redis-cmanager/model/component"
)

// FailoverParams 主从切换请求结构体
type FailoverParams struct {
	StageID       int64           `json:"stageId"`
	ClusterName   string          `json:"clusterName"`
	ShardList     []*omodel.Shard `json:"shardList"`
	MaxConcurnecy int             `json:"maxConcurnecy"`
	IntervalTime  int             `json:"intervalTime"`
}

// 主从切换
func Failover(c *gin.Context) {
	var params FailoverParams
	err := c.ShouldBindJSON(&params)
	if err != nil {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}
	// 入参校验
	if params.ClusterName == "" {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail("expect parameter clusterName but got empty value"))
		return
	}
	if len(params.ShardList) == 0 {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail("expect parameter shardList but got empty value"))
		return
	}
	if params.MaxConcurnecy <= 0 {
		params.MaxConcurnecy = 1
	}
	if params.IntervalTime <= 0 {
		params.IntervalTime = 120 // 2min
	}

	args := make([]reflect.Value, 5)
	args[0] = reflect.ValueOf(params.StageID)
	args[1] = reflect.ValueOf(params.ClusterName)
	args[2] = reflect.ValueOf(params.ShardList)
	args[3] = reflect.ValueOf(params.MaxConcurnecy)
	args[4] = reflect.ValueOf(params.IntervalTime)
	err = stage.Async(params.StageID, component.Failover, args...)
	if err != nil {
		gintool.JSON(c, nil, err)
		return
	}

	gintool.JSON(c, "ok", nil)
}

// 停止主从切换任务
func StopFailover(c *gin.Context) {
	type schema struct {
		StageID int64 `json:"stageId"`
	}
	var params schema
	err := c.ShouldBindJSON(&params)
	if err != nil {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}

	args := make([]reflect.Value, 1)
	args[0] = reflect.ValueOf(params.StageID)
	err = stage.Async(params.StageID, component.StopFailover, args...)
	if err != nil {
		gintool.JSON(c, nil, err)
		return
	}

	gintool.JSON(c, "ok", nil)
}
