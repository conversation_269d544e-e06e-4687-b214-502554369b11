package component

import (
	"reflect"

	"github.com/gin-gonic/gin"

	"dt-common/errs"
	"dt-common/gintool"
	"redis-cmanager/library/renderer"
	"redis-cmanager/library/stage"
)

// FailoverParams 主从切换请求结构体
type RemoveParams struct {
	StageID     int64  `json:"stageId"`
	ClusterName string `json:"clusterName"`
}

// 污点proxy实例退还
func RemoveProxy(c *gin.Context) {
	var params RemoveParams
	err := c.ShouldBindJSON(&params)
	if err != nil {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}

	args := make([]reflect.Value, 1)
	args[0] = reflect.ValueOf(params.ClusterName)
	err = stage.Async(params.StageID, renderer.DeleteTaintedProxy, args...)
	if err != nil {
		gintool.JSON(c, nil, err)
		return
	}

	gintool.JSON(c, "ok", nil)
}
