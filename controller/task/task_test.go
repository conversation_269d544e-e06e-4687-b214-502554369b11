package task

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/http/httptest"
	"net/url"
	"regexp"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/jarcoal/httpmock"

	"dt-common/ent"
	"dt-common/mysql"
	"dt-common/user-center/authc"
	"redis-xweb/env"
)

// 单测：查询详情
func TestQueryTaskList(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")
	objC := env.MockCluster("query_task_test")
	db, _ := mysql.Database()

	type args struct {
		keyword        string
		flowInstanceID uint32
		page           int
		pageSize       int
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, []*ent.Task, int)
	}{
		{
			name: "test1: flowInstanceID",
			before: func() {
				db.Task.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetFlowInstanceID(1).SetApplicant("jiayiming_dxm").SetDescription("123").SetName("test").SetType("migration").ExecX(context.Background())
			},
			args: args{
				keyword:        "",
				flowInstanceID: 1,
				page:           1,
				pageSize:       20,
			},
			wantErr: false,
		},
		{
			name: "test2: keyword",
			args: args{
				keyword:  "jiayiming",
				page:     1,
				pageSize: 20,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			taskList, count, err := QueryTaskList(tt.args.keyword, tt.args.flowInstanceID, tt.args.page, tt.args.pageSize)
			if (err != nil) != tt.wantErr {
				t.Errorf("QueryTaskList() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, taskList, count)
			}
		})
	}
}

// 单测：获取任务列表接口
func TestGetTaskList(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")
	objC := env.MockCluster("get_task_list")
	objT, _ := env.MockTaskWithStage(objC)
	env.MockStage(objT)
	gin.SetMode(gin.TestMode)
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		c *gin.Context
		w *httptest.ResponseRecorder
	}
	tests := []struct {
		name   string
		before func()
		args   args
		expect func(*testing.T, *httptest.ResponseRecorder)
	}{
		{
			name: "test1: no user",
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Request = &http.Request{
					URL: &url.URL{
						Path: "/api/v1/task/stage?flowInstanceId=abc",
					},
				}
				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] == 0.0 {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
		{
			name: "test2: check admin failed",
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Set("user", &authc.User{ID: 1, Name: "admin"})
				c.Request = &http.Request{
					URL: &url.URL{
						Path: "/api/v1/task/stage",
					},
				}
				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] == 0.0 {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
		{
			name: "test3: not admin",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/auth/GetUsersByRole`),
					httpmock.NewStringResponder(200, "{\"errno\":0,\"errmsg\":\"\",\"data\":\"jiayiming_dxm,huzhaoyun_dxm\"}"))
			},
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Set("user", &authc.User{ID: 1, Name: "admin"})
				c.Request = &http.Request{
					URL: &url.URL{
						Path:     "/api/v1/task/stage?stageId=abc",
						RawQuery: "stageId=99",
					},
				}
				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] == 0.0 {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
		{
			name: "test4: success",
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Set("user", &authc.User{ID: 1, Name: "jiayiming_dxm"})
				c.Request = &http.Request{
					URL: &url.URL{
						Path:     "/api/v1/task/stage?stageId=abc",
						RawQuery: "keyword=test",
					},
				}
				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] != 0.0 {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			GetTaskList(tt.args.c)
			if tt.expect != nil {
				tt.expect(t, tt.args.w)
			}
		})
	}
}

// 单测：查询详情
func TestQueryStageList(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")
	objC := env.MockCluster("query_stages_test")
	objT, _ := env.MockTaskWithStage(objC)

	type args struct {
		taskId int64
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, *ent.Task, []*ent.Stage)
	}{
		{
			name: "test1: not found",
			args: args{
				taskId: 99,
			},
			wantErr: true,
		},
		{
			name: "test2: success",
			args: args{
				taskId: objT.ID,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			taskList, count, err := QueryStageList(tt.args.taskId)
			if (err != nil) != tt.wantErr {
				t.Errorf("QueryStageList() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, taskList, count)
			}
		})
	}
}

// 单测：获取任务列表接口
func TestGetTaskDetail(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")
	objC := env.MockCluster("controller_get_task_detail")
	objT, _ := env.MockTaskWithStage(objC)
	gin.SetMode(gin.TestMode)
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		c *gin.Context
		w *httptest.ResponseRecorder
	}
	tests := []struct {
		name   string
		before func()
		args   args
		expect func(*testing.T, *httptest.ResponseRecorder)
	}{
		{
			name: "test1: bind query",
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Request = &http.Request{
					URL: &url.URL{
						Path: "/api/v1/task/stage?stageId=abc",
					},
				}
				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] == 0.0 {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
		{
			name: "test2: no user",
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Request = &http.Request{
					URL: &url.URL{
						Path:     "/api/v1/task/stage",
						RawQuery: "taskId=1",
					},
				}
				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] == 0.0 {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
		{
			name: "test3: check admin failed",
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Set("user", &authc.User{ID: 1, Name: "admin"})
				c.Request = &http.Request{
					URL: &url.URL{
						Path:     "/api/v1/task/stage",
						RawQuery: "taskId=1",
					},
				}
				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] == 0.0 {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
		{
			name: "test3: not admin",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/auth/GetUsersByRole`),
					httpmock.NewStringResponder(200, "{\"errno\":0,\"errmsg\":\"\",\"data\":\"jiayiming_dxm,huzhaoyun_dxm\"}"))
			},
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Set("user", &authc.User{ID: 1, Name: "admin"})
				c.Request = &http.Request{
					URL: &url.URL{
						Path:     "/api/v1/task/stage?stageId=abc",
						RawQuery: "taskId=1",
					},
				}
				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] == 0.0 {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
		{
			name: "test4: QueryStageList failed",
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Set("user", &authc.User{ID: 1, Name: "jiayiming_dxm"})
				c.Request = &http.Request{
					URL: &url.URL{
						Path:     "/api/v1/task/stage?stageId=abc",
						RawQuery: "taskId=99",
					},
				}
				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] == 0.0 {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
		{
			name: "test5: success",
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Set("user", &authc.User{ID: 1, Name: "jiayiming_dxm"})
				c.Request = &http.Request{
					URL: &url.URL{
						Path:     "/api/v1/task/stage?stageId=abc",
						RawQuery: fmt.Sprintf("taskId=%d", objT.ID),
					},
				}
				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] != 0.0 {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			GetTaskDetail(tt.args.c)
			if tt.expect != nil {
				tt.expect(t, tt.args.w)
			}
		})
	}
}
