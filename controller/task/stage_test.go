package task

import (
	"context"
	"encoding/json"
	"io"
	"net/http"
	"net/http/httptest"
	"net/url"
	"testing"

	"github.com/gin-gonic/gin"

	"dt-common/ent/stage"
	"dt-common/mysql"
	"redis-xweb/env"
)

// 单测：查询详情
func TestQueryStage(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")
	objC := env.MockCluster("query_stage_test")
	_, objS := env.MockTaskWithStage(objC)
	db, _ := mysql.Database()

	type args struct {
		stageId int64
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, *StageListItem)
	}{
		{
			name: "test1: nil stage",
			args: args{
				stageId: 99,
			},
			wantErr: true,
		},
		{
			name: "test2: unmarshal failed",
			before: func() {
				params := "-1"
				b, _ := json.Marshal(params)
				s := string(b)
				db.Stage.Update().SetParameter(s).Where(stage.ID(objS.ID)).ExecX(context.Background())
			},
			args: args{
				stageId: objS.ID,
			},
			wantErr: true,
		},
		{
			name: "test3: success",
			before: func() {
				params := map[string]interface{}{
					"test": "1",
				}
				b, _ := json.Marshal(params)
				s := string(b)
				db.Stage.Update().SetParameter(s).Where(stage.ID(objS.ID)).ExecX(context.Background())
			},
			args: args{
				stageId: objS.ID,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			stageList, err := queryStage(tt.args.stageId)
			if (err != nil) != tt.wantErr {
				t.Errorf("queryStage() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, stageList)
			}
		})
	}
}

func TestGetStageDetail(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")
	gin.SetMode(gin.TestMode)

	type args struct {
		c *gin.Context
		w *httptest.ResponseRecorder
	}
	tests := []struct {
		name   string
		before func()
		args   args
		expect func(*testing.T, *httptest.ResponseRecorder)
	}{
		{
			name: "test1: check args",
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Request = &http.Request{
					URL: &url.URL{
						Path: "/api/v1/task/stage?stageId=",
					},
				}

				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] == 0.0 {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
		{
			name: "test2: check stageId",
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Request = &http.Request{
					URL: &url.URL{
						Path:     "/api/v1/task/stage?stageId=abc",
						RawQuery: "stageId=abc",
					},
				}

				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] == 0.0 {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
		{
			name: "test3: query stage failed",
			args: func() args {
				w := httptest.NewRecorder()
				c, _ := gin.CreateTestContext(w)
				c.Request = &http.Request{
					URL: &url.URL{
						Path:     "/api/v1/task/stage?stageId=abc",
						RawQuery: "stageId=99",
					},
				}

				return args{c: c, w: w}
			}(),
			expect: func(t *testing.T, w *httptest.ResponseRecorder) {
				b, _ := io.ReadAll(w.Body)
				var resp map[string]interface{}
				json.Unmarshal(b, &resp)
				if resp["code"] == 0.0 {
					t.Error(w.Code, string(b))
					return
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			GetStageDetail(tt.args.c)
			if tt.expect != nil {
				tt.expect(t, tt.args.w)
			}
		})
	}
}
