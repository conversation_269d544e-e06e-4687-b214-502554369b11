package task

import (
	"github.com/gin-gonic/gin"

	"dt-common/ent/stage"
	"dt-common/errs"
	"dt-common/gintool"
	"dt-common/logger"
	"dt-common/mysql"
	"dt-common/omodel"
	"redis-xweb/model/workflow"
)

// 空阶段流程管理，空阶段占位用
// 适用场景：服务发布、SMART扩缩容、流量排查
func StageRun(c *gin.Context) {
	type schema struct {
		StageId    int64 `json:"stageId"`
		IsRollback bool  `json:"isRollback"`
	}
	// 解析路由参数
	var args schema
	err := c.ShouldBindJSON(&args)
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}

	if !args.IsRollback {
		err = workflow.Exec(args.StageId)
	} else {
		err = workflow.Rollback(args.StageId)
	}

	if err != nil {
		gintool.JSON2FE(c, nil, err)
		return
	}

	gintool.JSON2FE(c, "ok", nil)
}

// ======================================
//  	         阶段停止
// ======================================

// 取消阶段执行
// 适用场景：主从切换
func StageStop(c *gin.Context) {
	type schema struct {
		StageId int64 `json:"stageId"`
	}
	var args schema
	err := c.ShouldBindJSON(&args)
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}

	err = workflow.Stop(args.StageId)
	if err != nil {
		gintool.JSON2FE(c, nil, err)
		return
	}

	gintool.JSON2FE(c, "ok", nil)
}

// Manager & RAS 异步回调接口
// 1、修改stage状态
// 2、特殊类型的stage特殊处理
// 阶段失败则任务失败，任务失败即流程失败
// 阶段失败不回滚，人工介入，人工决定是否重试
func AsyncCallback(c *gin.Context) {
	type schema struct {
		StageID int64  `json:"stageId" binding:"required"`
		Success bool   `json:"success"`
		Message string `json:"message,omitempty"`
	}

	// 解析路由参数
	var args schema
	err := c.ShouldBindJSON(&args)
	if err != nil {
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}

	// 获取数据库连接
	db, err := mysql.Database()
	if err != nil {
		logger.Error("failed to get mysql connection, error=(%v)", err)
		gintool.JSON(c, nil, errs.CodeDatabase.Detail("数据库连接获取失败"))
		return
	}
	// 查询stage
	ctx, cancel := mysql.ContextWithTimeout()
	stageData, err := db.Stage.Query().Where(stage.ID(args.StageID)).Only(ctx)
	cancel()
	if err != nil {
		logger.Error("failed to query Task with Stages, taskId=%d, error=(%v)", args.StageID, err)
		gintool.JSON(c, nil, errs.CodeDatabase.Detail(err.Error()))
		return
	}

	// 检查Stage状态是否正确
	switch stageData.Status {
	case string(omodel.STAGE_STATUS_RUNNING):
		omodel.StageAppendInfoLog(args.StageID, "Callback request received")
		go workflow.CallbackExec(stageData, args.Success, args.Message)
	case string(omodel.STAGE_STATUS_ROLLBACK_RUNNING):
		omodel.StageAppendInfoLog(args.StageID, "Callback request received")
		go workflow.CallbackRollback(stageData, args.Success, args.Message)
	default:
		omodel.StageAppendInfoLog(args.StageID, "Callback request received, but stage status is "+stageData.Status+", can not run callback function")
		gintool.JSON(c, nil, errs.CodeRequestParameter.Detail("stage is not running status"))
		return
	}

	gintool.JSON(c, "ok", nil)
}
