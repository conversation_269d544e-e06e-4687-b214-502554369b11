package task

import (
	"encoding/json"
	"strconv"

	"github.com/gin-gonic/gin"

	"dt-common/errs"
	"dt-common/gintool"
	"dt-common/logger"
	"dt-common/omodel"
)

// ==========================
//  获取详情（用于查看日志和状态）
// ==========================

// 查询日志
func queryStage(stageId int64) (*StageListItem, error) {
	stageData, err := omodel.GetStageDetail(stageId)
	if err != nil {
		logger.Warn("failed to get Stage, id=%d, error=(%v)", stageId, err)
		return nil, errs.CodeRequestFailed.Detail("阶段详情获取失败")
	}

	params := make(map[string]any)
	if *stageData.Parameter != "" {
		err = json.Unmarshal([]byte(*stageData.Parameter), &params)
		if err != nil {
			logger.Error("failed to unmarshal stage parameter, stageId=%d, content=%s", stageData.ID, *stageData.Parameter)
			return nil, errs.CodeDatabase.Detail("查询出错，请稍后刷新重试")
		}
	}

	// 整理数据
	result := StageListItem{
		ID:        stageData.ID,
		Name:      stageData.Name,
		Type:      stageData.Type,
		Sequence:  stageData.Sequence,
		Parameter: params,
		Log:       stageData.Log,
		Operator:  stageData.Operator,
		Status:    stageData.Status,
	}
	return &result, nil
}

// 空阶段流程管理，空阶段占位用
// 适用场景：服务发布、SMART扩缩容、流量排查
func GetStageDetail(c *gin.Context) {
	// 参数解析
	stageIdStr := c.Query("stageId")
	if stageIdStr == "" {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail("缺少参数stageId"))
		return
	}
	stageId, err := strconv.ParseInt(stageIdStr, 10, 64)
	if err != nil {
		logger.Warn("StageId can not convert to int, value=%s", stageIdStr)
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail("参数stageId格式错误"))
		return
	}

	result, err := queryStage(stageId)
	if err != nil {
		gintool.JSON2FE(c, nil, err)
		return
	}

	gintool.JSON2FE(c, result, nil)
}
