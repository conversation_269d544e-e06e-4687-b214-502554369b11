package task

import (
	"context"
	"encoding/json"
	"strings"
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/gin-gonic/gin"
	"golang.org/x/sync/errgroup"

	"dt-common/ent"
	"dt-common/ent/predicate"
	"dt-common/ent/stage"
	"dt-common/ent/task"
	"dt-common/errs"
	"dt-common/gintool"
	"dt-common/logger"
	"dt-common/mysql"
	"dt-common/omodel"
	"redis-xweb/library/errc"
	"redis-xweb/model"
)

// 任务列表返回值结构
type TaskListResponse struct {
	ID             int64      `json:"id"`                       // TaskID
	FlowInstanceID uint32     `json:"flowInstanceId,omitempty"` // 工单ID，如果有
	ClusterName    string     `json:"clusterName"`              // 集群名
	Type           string     `json:"type"`                     // 类型
	Name           string     `json:"name"`                     // 任务名
	Applicant      string     `json:"applicant"`                // 申请人
	Progress       int        `json:"progress"`                 // 进度
	Status         string     `json:"status,omitempty"`         // 当前状态
	CreatedAt      *time.Time `json:"createdAt,omitempty"`      // 创建时间
}

// 获取任务列表，以updateAt倒排
func GetTaskList(c *gin.Context) {
	type schema struct {
		Keyword        string `form:"keyword"`
		FlowInstanceID uint32 `form:"flowInstanceId"`
		Page           int    `form:"page"`
		PageSize       int    `form:"pageSize"`
	}

	// 参数校验
	var args schema
	err := c.ShouldBindQuery(&args)
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}
	if args.Page == 0 {
		args.Page = 1
	}
	if args.PageSize == 0 {
		args.PageSize = 10
	}
	if args.Keyword != "" {
		args.Keyword = strings.ToLower(args.Keyword)
	}

	// 仅管理员可见
	user, err := gintool.GetUser(c)
	if err != nil {
		logger.Warn("cannot get user info, error=(%v)", err)
		gintool.JSON2FE(c, nil, errs.CodeAuthCenterRequestFailed.Detail("用户信息获取失败"))
		return
	}
	isAdmin, err := model.IsRedisSuperAdmin(user.Name)
	if err != nil {
		logger.Warn("failed to get local role redis-manager, error=(%v)", err)
		gintool.JSON2FE(c, nil, errs.CodeAuthCenterRequestFailed.Detail("管理员鉴权失败"))
		return
	}
	if !isAdmin {
		gintool.JSON2FE(c, nil, errc.CodeHaveNoPermission.Detail("仅管理员可见"))
		return
	}

	tasks, total, err := QueryTaskList(args.Keyword, args.FlowInstanceID, args.Page, args.PageSize)
	if err != nil {
		gintool.JSON2FE(c, nil, err)
		return
	}

	// 整理数据
	rows := make([]TaskListResponse, len(tasks))
	for i, t := range tasks {
		// 计算progress
		progress := 0
		if len(t.Edges.Stages) != 0 {
			done := 0
			undone := 0
			for _, s := range t.Edges.Stages {
				if s.Status == string(omodel.STAGE_STATUS_DONE) {
					done += 1
				} else {
					undone += 1
				}
			}
			progress = done * 100 / (done + undone)
		}

		if progress == 100 {
			t.Status = "done"
		}

		rows[i].ID = t.ID
		rows[i].FlowInstanceID = t.FlowInstanceID
		rows[i].ClusterName = t.ClusterName
		rows[i].Name = t.Name
		rows[i].Type = t.Type
		rows[i].Applicant = t.Applicant
		rows[i].Progress = progress
		rows[i].Status = t.Status
		rows[i].CreatedAt = &t.CreatedAt
	}

	gintool.JSON2FE(c, gintool.FormatPaging(total, rows, args.Page, args.PageSize), nil)
}

// 分页查询任务
func QueryTaskList(keyword string, flowInstanceID uint32, page, pageSize int) ([]*ent.Task, int, error) {
	// 查询任务列表
	db, err := mysql.Database()
	if err != nil {
		logger.Error("failed to get mysql connection, error=(%v)", err)
		return nil, 0, errs.CodeDatabase.Detail("数据库连接获取失败")
	}

	// 整理查询条件
	predicates := []predicate.Task{}
	if flowInstanceID != 0 {
		predicates = append(predicates, task.FlowInstanceID(flowInstanceID))
	} else if keyword != "" {
		predicates = append(predicates, task.Or(
			task.NameContains(keyword),
			task.ClusterNameContains(keyword),
			task.ApplicantContains(keyword),
			task.DescriptionContains(keyword),
		))
	}

	// 分别查询rows和count
	var total int
	var tasks []*ent.Task
	g, _ := errgroup.WithContext(context.Background())
	g.Go(func() error {
		ctx, cancel := mysql.ContextWithTimeout()
		tasks, err = db.Task.Query().
			Where(predicates...).
			WithStages().
			Order(task.ByUpdatedAt(sql.OrderDesc())).
			Limit(pageSize).Offset((page - 1) * pageSize).
			All(ctx)
		cancel()
		return err
	})
	g.Go(func() error {
		ctx, cancel := mysql.ContextWithTimeout()
		total, err = db.Task.Query().Where(predicates...).Count(ctx)
		cancel()
		return err
	})
	if err := g.Wait(); err != nil {
		logger.Error("failed to select Task, error=(%v)", err)
		return nil, 0, errs.CodeDatabase.Detail("查询出错，请稍后刷新重试")
	}

	return tasks, total, nil
}

// ==============================
// 			任务详情
// ==============================

// 任务详情返回值结构
type TaskDetailResponse struct {
	ID          int64            `json:"id"`
	ClusterName string           `json:"clusterName"`
	Name        string           `json:"name"`
	Applicant   string           `json:"applicant"`
	StageList   *[]StageListItem `json:"stageList"`
	CreatedAt   time.Time        `json:"createdAt"`
}

// 阶段列表Item
type StageListItem struct {
	ID        int64          `json:"id"`            // 主键ID
	Name      string         `json:"name"`          // 任务名称
	Type      string         `json:"type"`          // 级别
	Sequence  int            `json:"sequence"`      // 顺序
	Parameter map[string]any `json:"parameter"`     // 参数
	Operator  string         `json:"operator"`      // 归属部门
	Log       *string        `json:"log,omitempty"` // 归属人
	Status    string         `json:"status"`        // 状态
}

// QueryTaskStage 不分页
func QueryStageList(taskId int64) (*ent.Task, []*ent.Stage, error) {
	// 查询阶段列表
	db, err := mysql.Database()
	if err != nil {
		logger.Error("failed to get mysql connection, error=(%v)", err)
		return nil, nil, errs.CodeDatabase.Detail("数据库连接获取失败")
	}

	ctx, cancel := mysql.ContextWithTimeout()
	taskData, err := db.Task.Query().Where(task.ID(taskId)).Only(ctx)
	cancel()
	if err != nil {
		logger.Error("failed to select Task, id=%d, error=(%v)", taskId, err)
		return nil, nil, errs.CodeDatabase.Detail("查询出错，请稍后刷新重试")
	}

	ctx, cancel = mysql.ContextWithTimeout()
	stages, err := db.Stage.Query().Where(
		stage.HasTaskWith(task.ID(taskId)),
	).Order(stage.BySequence(sql.OrderAsc())).All(ctx)
	cancel()
	if err != nil {
		logger.Error("failed to select Stage, error=(%v)", err)
		return nil, nil, errs.CodeDatabase.Detail("查询出错，请稍后刷新重试")
	}

	return taskData, stages, nil
}

// 获取阶段列表
func GetTaskDetail(c *gin.Context) {
	type schema struct {
		TaskID int64 `form:"taskId" binding:"required"`
	}

	// 参数校验
	var args schema
	err := c.ShouldBindQuery(&args)
	if err != nil {
		gintool.JSON2FE(c, nil, errs.CodeRequestParameter.Detail(err.Error()))
		return
	}

	// 仅管理员可见
	user, err := gintool.GetUser(c)
	if err != nil {
		logger.Warn("cannot get user info, error=(%v)", err)
		gintool.JSON2FE(c, nil, errs.CodeAuthCenterRequestFailed.Detail("用户信息获取失败"))
		return
	}
	isAdmin, err := model.IsRedisSuperAdmin(user.Name)
	if err != nil {
		logger.Warn("failed to get local role redis-manager, error=(%v)", err)
		gintool.JSON2FE(c, nil, errs.CodeAuthCenterRequestFailed.Detail("管理员鉴权失败"))
		return
	}
	if !isAdmin {
		gintool.JSON2FE(c, nil, errc.CodeHaveNoPermission.Detail("仅管理员可见"))
		return
	}

	taskData, stages, err := QueryStageList(args.TaskID)
	if err != nil {
		gintool.JSON2FE(c, nil, err)
		return
	}

	// 整理数据
	rows := make([]StageListItem, len(stages))
	for i, s := range stages {
		params := make(map[string]any)
		if *s.Parameter != "" {
			err = json.Unmarshal([]byte(*s.Parameter), &params)
			if err != nil {
				logger.Error("failed to unmarshal stage parameter, stageId=%d, content=%s", s.ID, *s.Parameter)
				gintool.JSON2FE(c, nil, errs.CodeDatabase.Detail("查询出错，请稍后刷新重试"))
				return
			}
		}

		rows[i].ID = s.ID
		rows[i].Name = s.Name
		rows[i].Type = s.Type
		rows[i].Sequence = s.Sequence
		rows[i].Parameter = params
		rows[i].Operator = s.Operator
		rows[i].Status = s.Status
	}

	result := &TaskDetailResponse{
		ID:          taskData.ID,
		ClusterName: taskData.ClusterName,
		Name:        taskData.Name,
		Applicant:   taskData.Applicant,
		StageList:   &rows,
		CreatedAt:   taskData.CreatedAt,
	}

	gintool.JSON2FE(c, result, nil)
}
