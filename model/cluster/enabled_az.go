package cluster

import (
	"slices"

	"dt-common/omodel"
	"redis-cmanager/library/renderer"
)

// 修改集群可用区
func ChangeEnabledAZ(stageId int64, clusterName string, az []string) error {
	// 1、修改enabledAz
	deployment, err := renderer.GetDeploymentFromDB(clusterName)
	if err != nil {
		omodel.StageAppendErrorLog(stageId, "failed to get deployment from db, cluster=%s, error=(%v)", clusterName, err)
		return err
	}

	if slices.Equal(deployment.EnabledAZ, az) {
		err = renderer.InitDeploymentRender(clusterName)
		if err != nil {
			omodel.StageAppendErrorLog(stageId, "failed to init deployment render, cluster=%s, error=(%v)", clusterName, err)
			return err
		}
		omodel.StageAppendInfoLog(stageId, "cluster %s enabledAz already changed to %v", clusterName, az)
	} else {
		deployment.EnabledAZ = az
		deployment.Spec.Redis.Replicas["hba"] = 1                             // 兼容支付云3改5迁移
		deployment.InspectionMode = omodel.MODE_FULL_CARE                     // 不改成fullCare不会触发渲染
		deployment.InspectionResult.State = renderer.INSPECTION_STATE_INIT    // 改成""用于检测结果
		deployment.InspectionResult.StepErrTimes = [renderer.TOTAL_STEP]int{} //
		err = renderer.SaveNewDeployment(deployment)
		if err != nil {
			omodel.StageAppendErrorLog(stageId, "failed to save new deployment to db, cluster=%s, error=(%v)", clusterName, err)
			return err
		}
		omodel.StageAppendInfoLog(stageId, "cluster %s enabledAz changed to %v", clusterName, az)
	}

	// 轮询过程中发生错误，更新状态并退出
	err = WaitForRenderSuccess(stageId, clusterName)
	if err != nil {
		omodel.StageAppendWarnLog(stageId, "failed to check enabledAz change, cluster=%s, error=(%v)", clusterName, err)
		return err
	}
	omodel.StageAppendInfoLog(stageId, "succeed to check enabledAz change, cluster=%s", clusterName)
	return nil
}
