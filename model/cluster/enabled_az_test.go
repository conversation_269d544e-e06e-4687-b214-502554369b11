package cluster

import (
	"testing"

	"github.com/jarcoal/httpmock"

	"dt-common/omodel"
	"redis-cmanager/env"
	"redis-cmanager/library/renderer"
	"redis-cmanager/library/renderer/common"
)

func TestChangeEnabledAZ(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")
	httpmock.Deactivate()
	env.MockCluster("test1")
	env.MockCluster("test3")
	env.MockCluster("test4")
	common.CFG = common.Config{
		Image: &common.ImageConfig{
			Redis:    "1",
			Sentinel: "1",
			Router:   "1",
			Version:  "1",
		},
	}
	redisCluster := &renderer.Cluster{
		Name:           "test1",
		Alias:          "test1",
		EnabledAZ:      []string{"hba"},
		InspectionMode: omodel.MODE_FULL_CARE,
		Spec: &renderer.Spec{
			App: &renderer.AppSettings{
				AppPrefix:   "test1",
				ProductLine: "siod-kafka",
			},
			Router: &renderer.RouterSettings{
				Replicas: map[string]int{"hbb": 1},
				Port:     8005,
			},
			Sentinel: &renderer.SentinelSettings{
				Replicas: map[string]int{"hbb": 1},
				Port:     9105,
			},
			Redis: &renderer.RedisSettings{
				Replicas: map[string]int{"hbb": 1},
				Port:     7000,
			},
		},
		InspectionResult: &renderer.InspectionResult{
			State: renderer.INSPECTION_STATE_NORMAL,
		},
	}
	redisCluster1 := &renderer.Cluster{
		Name:           "test2",
		Alias:          "test2",
		EnabledAZ:      []string{"hba"},
		InspectionMode: omodel.MODE_FULL_CARE,
		Spec: &renderer.Spec{
			App: &renderer.AppSettings{
				AppPrefix:   "test2",
				ProductLine: "siod-kafka",
			},
			Router: &renderer.RouterSettings{
				Replicas: map[string]int{"hbb": 1},
				Port:     8005,
			},
			Sentinel: &renderer.SentinelSettings{
				Replicas: map[string]int{"hbb": 1},
				Port:     9105,
			},
			Redis: &renderer.RedisSettings{
				Replicas: map[string]int{"hbb": 1},
				Port:     7000,
			},
		},
		InspectionResult: &renderer.InspectionResult{
			State: renderer.INSPECTION_STATE_NORMAL,
		},
	}
	redisCluster2 := &renderer.Cluster{
		Name:           "test3",
		Alias:          "test3",
		EnabledAZ:      []string{"hba"},
		InspectionMode: omodel.MODE_FULL_CARE,
		Spec: &renderer.Spec{
			App: &renderer.AppSettings{
				AppPrefix:   "test3",
				ProductLine: "siod-kafka",
			},
			Router: &renderer.RouterSettings{
				Replicas: map[string]int{"hbb": 1},
				Port:     8005,
			},
			Sentinel: &renderer.SentinelSettings{
				Replicas: map[string]int{"hbb": 1},
				Port:     9105,
			},
			Redis: &renderer.RedisSettings{
				Replicas: map[string]int{"hbb": 1},
				Port:     7000,
			},
		},
		InspectionResult: &renderer.InspectionResult{
			State: renderer.INSPECTION_STATE_ERROR,
		},
	}
	redisCluster3 := &renderer.Cluster{
		Name:           "test4",
		Alias:          "test4",
		EnabledAZ:      []string{"hba"},
		InspectionMode: omodel.MODE_FULL_CARE,
		Spec: &renderer.Spec{
			App: &renderer.AppSettings{
				AppPrefix:   "test4",
				ProductLine: "siod-kafka",
			},
			Router: &renderer.RouterSettings{
				Replicas: map[string]int{"hbb": 1},
				Port:     8005,
			},
			Sentinel: &renderer.SentinelSettings{
				Replicas: map[string]int{"hbb": 1},
				Port:     9105,
			},
			Redis: &renderer.RedisSettings{
				Replicas: map[string]int{"hbb": 1},
				Port:     7000,
			},
		},
		InspectionResult: &renderer.InspectionResult{
			State: renderer.INSPECTION_STATE_TEST,
		},
	}
	renderer.SaveNewDeployment(redisCluster)
	renderer.SaveNewDeployment(redisCluster1)
	renderer.SaveNewDeployment(redisCluster2)
	renderer.SaveNewDeployment(redisCluster3)
	type args struct {
		stageId     int64
		clusterName string
		az          []string
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "通过",
			args: args{
				stageId:     1,
				clusterName: "test1",
				az:          []string{"hbb"},
			},
			wantErr: false,
		},
		{
			name: "通过",
			args: args{
				stageId:     1,
				clusterName: "test2",
				az:          []string{"hbb"},
			},
			wantErr: true,
		},
		{
			name: "test3",
			args: args{
				stageId:     1,
				clusterName: "test3",
				az:          []string{"hbb"},
			},
			wantErr: true,
		},
		{
			name: "test4",
			args: args{
				stageId:     1,
				clusterName: "test4",
				az:          []string{"hbb"},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := ChangeEnabledAZ(tt.args.stageId, tt.args.clusterName, tt.args.az); (err != nil) != tt.wantErr {
				t.Errorf("ChangeEnabledAZ() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
