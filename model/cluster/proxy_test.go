package cluster

import (
	"testing"
	"time"

	"github.com/jarcoal/httpmock"

	"dt-common/omodel"
	"redis-cmanager/env"
	"redis-cmanager/library/renderer"
)

func TestProxyScaling(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")
	objC := env.MockCluster("proxy_scaling")
	_, objS := env.MockTaskWithStage(objC)

	type args struct {
		stageId     int64
		clusterName string
		proxyNum    int
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
	}{
		{
			name: "scale up",
			before: func() {
				renderer.SaveNewDeployment(&renderer.Cluster{
					ID:             objC.ID,
					Name:           "proxy_scaling",
					Alias:          "proxy-scaling",
					EnabledAZ:      []string{"hba", "hbb", "hbc"},
					InspectionMode: omodel.MODE_FULL_CARE,
					Spec: &renderer.Spec{
						App:      &renderer.AppSettings{AppPrefix: "proxy-scaling", ProductLine: "siod-redis"},
						Router:   &renderer.RouterSettings{Replicas: map[string]int{"hba": 1, "hbb": 1}, Port: 8005},
						Sentinel: &renderer.SentinelSettings{Replicas: map[string]int{"hba": 1, "hbb": 1, "hbc": 1}, Port: 9105},
						Redis:    &renderer.RedisSettings{Replicas: map[string]int{"hba": 1, "hbb": 1}, Port: 7000},
					},
					InspectionResult: &renderer.InspectionResult{
						State: renderer.INSPECTION_STATE_INIT,
					},
					Status: renderer.DEPLOYMENT_STATUS_NORMAL,
				})

				httpmock.Reset()
				httpmock.RegisterResponder("POST", "http://*************:8536/fec-manager/api/v1/fec/listPod",
					httpmock.NewStringResponder(200, `{"errno": 0,"errmsg": "","data": [
						{"id": "","podName": "p-proxy-scaling-az","productName": "siod-redis","appName": "proxy-scaling-router","podIp": "*********","idc": "BDDX","comboDesc": "","safeRegion": "test_internal","owner": "jiayiming_dxm","applicant": "","status": "Running","labels": {"avoid_delete_by_accident": "true","dxm-redis/component": "router","dxm-redis/name": "p-test","dxm-redis/taint": "false","fec2/app_name": "p-test-router","fec2/bnsMount": "false","fec2/budget_execution_id": "","fec2/budget_number": "","fec2/buget-checked": "pass","fec2/cni-safe-region": "test_internal","fec2/combo_code": "567756428","fec2/device-id": "fec-PMOYhZDwP","fec2/disable-inject-sidecar": "false","fec2/fec-id": "fid-XxqWgKwoo","fec2/idc": "BDDX","fec2/over_budget": "true","fec2/owner": "jiayiming_dxm","fec2/product_name": "siod-kafka","fec2/retain": "true","fec2/saas": "redis","fec2/service_level": "","fec2/version": "v0.0.2","fec2/white": "false"},"hostname": "p-test-az.fec.kj01.bddx.dxm-int.com","scheduleLimit": null,"containerInfo": [{"containerName": "r-r3-rd-test-0","imageName": "r.duxiaoman-int.com/siod_redis/redis-docker","imageVersion": "v2024072402","imagePullPolicy": "IfNotPresent","cpu": 1,"mem": 1,"port": null,"entryCommand": "","execArgs": null,"envArgs": {"CLOUD_TYPE": "kj","CONTAINER_NAME": "r-r3-rd-test-0","CONTAINER_TYPE": "main","DEVICE_TYPE": "pod","FECID": "fid-XxqWgKwoo","FEC_OPERATOR_URL": "https://************:11443/fec2","HAWKING_ENV": "container","INIT_NOAH_USER": "redis","MAX_MEMORY_POLICY": "volatile-lru","NODE_IP": "","POD_IP": "","POD_NAME": "","REDIS_PORT": "7000"},"diskApplication": 0,"mountType": "","volumeMounts": null,"status": "Running","privileged": false}]},
						{"id": "","podName": "p-proxy-scaling-sc","productName": "siod-redis","appName": "proxy-scaling-router","podIp": "*********","idc": "ZZJG","comboDesc": "","safeRegion": "test_internal","owner": "jiayiming_dxm","applicant": "","status": "Running","labels": {"avoid_delete_by_accident": "true","dxm-redis/component": "router","dxm-redis/name": "p-test","dxm-redis/taint": "false","fec2/app_name": "p-test-router","fec2/bnsMount": "false","fec2/budget_execution_id": "","fec2/budget_number": "","fec2/buget-checked": "pass","fec2/cni-safe-region": "test_internal","fec2/combo_code": "567756428","fec2/device-id": "fec-PMOYhZDwP","fec2/disable-inject-sidecar": "false","fec2/fec-id": "fid-XxqWgKwoo","fec2/idc": "BDDX","fec2/over_budget": "true","fec2/owner": "jiayiming_dxm","fec2/product_name": "siod-kafka","fec2/retain": "true","fec2/saas": "redis","fec2/service_level": "","fec2/version": "v0.0.2","fec2/white": "false"},"hostname": "p-test-sc.fec.kj01.bddx.dxm-int.com","scheduleLimit": null,"containerInfo": [{"containerName": "r-r3-rd-test-0","imageName": "r.duxiaoman-int.com/siod_redis/redis-docker","imageVersion": "v2024072402","imagePullPolicy": "IfNotPresent","cpu": 1,"mem": 1,"port": null,"entryCommand": "","execArgs": null,"envArgs": {"CLOUD_TYPE": "kj","CONTAINER_NAME": "r-r3-rd-test-0","CONTAINER_TYPE": "main","DEVICE_TYPE": "pod","FECID": "fid-XxqWgKwoo","FEC_OPERATOR_URL": "https://************:11443/fec2","HAWKING_ENV": "container","INIT_NOAH_USER": "redis","MAX_MEMORY_POLICY": "volatile-lru","NODE_IP": "","POD_IP": "","POD_NAME": "","REDIS_PORT": "7000"},"diskApplication": 0,"mountType": "","volumeMounts": null,"status": "Running","privileged": false}]}
					]}`),
				)
				httpmock.RegisterResponder("GET", "http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-redis/apps/proxy-scaling-router/instances?showPage=0",
					httpmock.NewStringResponder(200, `{"success": true, "message": "OK", "data": [
						{"name": "0.proxy-scaling-router.siod-redis","hostName": "p-proxy-scaling-az.fec.kj01.bddx.dxm-int.com","ip": "*********","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8001},"deployInfo": {"deployPath": "/home/<USER>/local/router"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"deploy":"docker","idc": "hba","service":"hbas"},"disable": false,"instanceType": "host","podId": null,"containerList": []},
						{"name": "2.proxy-scaling-router.siod-redis","hostName": "p-proxy-scaling-sc.fec.kj01.zzjg.dxm-int.com","ip": "*********","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8001},"deployInfo": {"deployPath": "/home/<USER>/local/router"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"deploy":"docker","idc": "hbb","service":"hbbs"},"disable": false,"instanceType": "host","podId": null,"containerList": []}
					]}`),
				)

				go func() {
					time.Sleep(5 * time.Second)
					err := renderer.UpdateDeploymentResult(objC.Name, &renderer.InspectionResult{
						State:        renderer.INSPECTION_STATE_NORMAL,
						StepProgress: [renderer.TOTAL_STEP]int{1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1},
					})
					if err != nil {
						t.Errorf("%v", err)
					}
				}()
			},
			args: args{
				stageId:     objS.ID,
				clusterName: objC.Name,
				proxyNum:    6,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := ProxyScaling(tt.args.stageId, tt.args.clusterName, tt.args.proxyNum)
			if (err != nil) != tt.wantErr {
				t.Errorf("ProxyScaling() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}
