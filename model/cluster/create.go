package cluster

import (
	"fmt"
	"strings"

	"dt-common/omodel"
	"redis-cmanager/library/renderer"
	"redis-cmanager/library/renderer/common"
)

// 新集群部署
func ClusterCreate(clusterOptions *omodel.DeployOptions) error {
	cpu50 := 500    // cpu50 0.5c
	cpu100 := 1000  // cpu100 1c
	mem512 := 512   // 内存 0.5GB sentinel
	mem2048 := 2048 // 内存 2GB，proxy

	// _转-作为k8s中使用的集群名
	alias := strings.ReplaceAll(clusterOptions.Name, "_", "-")
	deployment := &renderer.Cluster{
		ID:    clusterOptions.ClusterID,
		Name:  clusterOptions.Name,
		Alias: alias,
		Spec: &renderer.Spec{
			Namespace: "redis",
			Redis: &renderer.RedisSettings{
				NumOfShards:  clusterOptions.ShardNum,
				Image:        common.CFG.Image.Redis,
				ImageVersion: common.CFG.Image.Version,
				Replicas:     clusterOptions.ShardReplicas,
				Port:         clusterOptions.ShardPort,
				CustomConfig: []string{
					"save \"\"",
					fmt.Sprintf("maxmemory-policy %s", clusterOptions.MaxmemoryPolicy), // redis maxmemory-policy配置
					fmt.Sprintf("maxmemory %vGB", clusterOptions.ShardMem),             // redis maxmemory配置
				},
				Resource: &renderer.Resource{
					CPU: cpu50,
					Mem: clusterOptions.ShardMem * 1024 * 2,
				},
			},
			Sentinel: &renderer.SentinelSettings{
				Image:        common.CFG.Image.Sentinel,
				ImageVersion: common.CFG.Image.Version,
				Replicas:     clusterOptions.SentinelReplicas,
				Port:         clusterOptions.SentinelPort,
				Resource: &renderer.Resource{
					CPU: cpu50,
					Mem: mem512,
				},
			},
			Router: &renderer.RouterSettings{
				Image:        common.CFG.Image.Router,
				ImageVersion: common.CFG.Image.Version,
				Replicas:     clusterOptions.ProxyReplicas,
				Port:         clusterOptions.ProxyPort,
				Resource: &renderer.Resource{
					CPU: cpu100,
					Mem: mem2048,
				},
				SPort:             clusterOptions.ProxyPort + 1000,
				ClientAuth:        clusterOptions.ProxyPassword,
				ClientConnections: 10000,
			},
			App: &renderer.AppSettings{
				ProductLine:       common.CFG.App.ProductLine,
				Subsystem:         alias,
				SubsystemAlias:    alias,
				SentinelSubsystem: "sentinel",
				AppPrefix:         alias,
				AppPrefixAlias:    alias,
				Level:             "2",
				DepartmentId:      common.CFG.App.DepartmentID,
				DepartmentName:    common.CFG.App.Department,
				Description:       "",
				RDOwner:           common.CFG.App.Owner,
				OPOwner:           common.CFG.App.Owner,
			},
		},
		InspectionResult: &renderer.InspectionResult{
			StepErrTimes: [renderer.TOTAL_STEP]int{},
			StepProgress: [renderer.TOTAL_STEP]int{},
		},
		EnabledAZ:      clusterOptions.EnabledAZ,
		InspectionMode: omodel.MODE_FULL_CARE,
		Version:        0,
		Status:         renderer.DEPLOYMENT_STATUS_INIT, // 初建集群，集群部署完再改成 normal
	}

	// 保存deployment
	err := renderer.SaveNewDeployment(deployment)
	if err != nil {
		omodel.StageAppendErrorLog(clusterOptions.StageID, "failed to save new deployment, cluster=%s, error=(%v)", clusterOptions.Name, err)
		return err
	}

	// 轮询过程中发生错误，更新状态并退出
	err = WaitForRenderSuccess(clusterOptions.StageID, clusterOptions.Name)
	if err != nil {
		omodel.StageAppendWarnLog(clusterOptions.StageID, "failed to deploy cluster %s, error=(%v)", clusterOptions.Name, err)
		return err
	}
	omodel.StageAppendInfoLog(clusterOptions.StageID, "succeed to deploy new cluster %s", clusterOptions.Name)

	// 修改deployment.status 为 normal
	err = renderer.UpdateDeploymentStatus(clusterOptions.Name, renderer.DEPLOYMENT_STATUS_NORMAL)
	if err != nil {
		omodel.StageAppendWarnLog(clusterOptions.StageID, "failed to update status from init to normal, cluster=%s, error=(%v)", clusterOptions.Name, err)
		return err
	}
	omodel.StageAppendInfoLog(clusterOptions.StageID, "succeed to update status from init to normal, cluster=%s", clusterOptions.Name)

	return nil
}
