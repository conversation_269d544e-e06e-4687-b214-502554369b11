package cluster

import (
	"errors"
	"time"

	"dt-common/omodel"
	"redis-cmanager/library/renderer"
)

// 定时检查Deployment渲染状态
func WaitForRenderSuccess(stageId int64, clusterName string) error {
	// 定义 Ticker 用于定时执行轮询，定义 timer 用于超时控制
	delayTicker, timeoutTimer := time.NewTicker(30*time.Second), time.NewTimer(15*time.Minute)
	defer delayTicker.Stop()
	defer timeoutTimer.Stop()

	// 定时执行循环
	for range delayTicker.C {
		select {
		case <-timeoutTimer.C:
			// 状态轮询更新超时，直接返回错误
			return errors.New("render state check timeout")
		default:
			// 获取deployment
			deployment, err := renderer.GetDeploymentFromDB(clusterName)
			if err != nil {
				omodel.StageAppendErrorLog(stageId, "failed to query deployment, cluster=%s, error=(%v)", clusterName, err)
				return err
			}

			// 单阶段失败5次变notCare，认定为部署失败，需要人工介入
			if deployment.InspectionMode == omodel.MODE_NOT_CARE {
				omodel.StageAppendErrorLog(stageId, "failed to adaptive deployment change, cluster=%s, error=(%v)", clusterName, deployment.InspectionResult.ErrMsg)
				return errors.New(deployment.InspectionResult.ErrMsg)
			}

			// 渲染成功
			if deployment.InspectionResult.State == renderer.INSPECTION_STATE_NORMAL {
				return nil
			}

			// 打印日志
			currentStep := 0
			for _, i2 := range deployment.InspectionResult.StepProgress {
				if i2 == renderer.STEP_COMPLETED {
					currentStep++
				}
			}
			omodel.StageAppendInfoLog(stageId, "cluster deployment progress is %d/%d", currentStep, renderer.TOTAL_STEP)
		}
	}
	return nil
}
