package cluster

import (
	"context"
	"strings"
	"testing"

	"github.com/jarcoal/httpmock"

	"dt-common/mysql"
	"dt-common/omodel"
	"redis-cmanager/env"
	"redis-cmanager/library/renderer"
	"redis-cmanager/library/renderer/common"
)

func TestWaitForRenderSuccess(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")
	common.CFG = common.Config{}

	objC := env.MockCluster("refresh_create_cluster_status")
	_, objS := env.MockTaskWithStage(objC)

	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		stageId     int64
		clusterName string
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
	}{
		{
			name: "init",
			before: func() {
				result := renderer.InspectionResult{
					StepProgress: [renderer.TOTAL_STEP]int{1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1},
				}

				db, _ := mysql.Database()
				db.Deployment.Create().
					SetClusterID(objC.ID).
					SetClusterName(objC.Name).
					SetAlias(strings.ReplaceAll(objC.Name, "_", "-")).
					SetVersion(1).
					SetSpec((&renderer.Spec{}).ToString()).
					SetEnabledAz("hbb").
					SetInspectionMode(omodel.MODE_FULL_CARE).
					SetInspectionResult(result.ToString()).
					SetStatus(renderer.DEPLOYMENT_STATUS_INIT).
					OnConflict().UpdateSpec().UpdateEnabledAz().UpdateInspectionMode().UpdateInspectionResult().
					Exec(context.Background())
			},
			args: args{
				stageId:     objS.ID,
				clusterName: objC.Name,
			},
			wantErr: true,
		},
		{
			name: "error",
			before: func() {
				result := renderer.InspectionResult{
					State:        renderer.INSPECTION_STATE_ERROR,
					StepProgress: [renderer.TOTAL_STEP]int{1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1},
				}

				db, _ := mysql.Database()
				db.Deployment.Create().
					SetClusterID(objC.ID).
					SetClusterName(objC.Name).
					SetAlias(strings.ReplaceAll(objC.Name, "_", "-")).
					SetVersion(1).
					SetSpec((&renderer.Spec{}).ToString()).
					SetEnabledAz("hbb").
					SetInspectionMode(omodel.MODE_FULL_CARE).
					SetInspectionResult(result.ToString()).
					SetStatus(renderer.DEPLOYMENT_STATUS_INIT).
					OnConflict().UpdateSpec().UpdateEnabledAz().UpdateInspectionMode().UpdateInspectionResult().
					Exec(context.Background())
			},
			args: args{
				stageId:     objS.ID,
				clusterName: objC.Name,
			},
			wantErr: true,
		},
		{
			name: "normal",
			before: func() {
				result := renderer.InspectionResult{
					State:        renderer.INSPECTION_STATE_NORMAL,
					StepProgress: [renderer.TOTAL_STEP]int{1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1},
				}

				db, _ := mysql.Database()
				db.Deployment.Create().
					SetClusterID(objC.ID).
					SetClusterName(objC.Name).
					SetAlias(strings.ReplaceAll(objC.Name, "_", "-")).
					SetVersion(2).
					SetSpec((&renderer.Spec{}).ToString()).
					SetEnabledAz("hbb").
					SetInspectionMode(omodel.MODE_FULL_CARE).
					SetInspectionResult(result.ToString()).
					SetStatus(renderer.DEPLOYMENT_STATUS_INIT).
					OnConflict().UpdateSpec().UpdateEnabledAz().UpdateInspectionMode().UpdateInspectionResult().
					Exec(context.Background())
			},
			args: args{
				stageId:     objS.ID,
				clusterName: objC.Name,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := WaitForRenderSuccess(tt.args.stageId, tt.args.clusterName)
			if (err != nil) != tt.wantErr {
				t.Errorf("WaitForRenderSuccess() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
