package cluster

import (
	"context"
	"testing"
	"time"

	"github.com/jarcoal/httpmock"

	"dt-common/ent/deployment"
	"dt-common/mysql"
	"dt-common/omodel"
	"redis-cmanager/env"
	"redis-cmanager/library/renderer"
	"redis-cmanager/library/renderer/common"
)

func TestClusterCreate(t *testing.T) {
	// 初始化
	env.Mock(t, "../../config/config.yaml")
	common.CFG = common.Config{
		App: &common.AppConfig{
			ProductLine: "siod-kafka",
		},
		Image: &common.ImageConfig{
			Redis:    "1",
			Sentinel: "1",
			Router:   "1",
			Version:  "1",
		},
	}
	objC := env.MockCluster("r3_create_test")
	httpmock.Activate()
	defer httpmock.Deactivate()

	// cases
	type args struct {
		clusterInfo *omodel.DeployOptions
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
	}{
		{
			name: "error",
			before: func() {
				go func() {
					time.Sleep(2 * time.Second)
					db, _ := mysql.Database()
					db.Deployment.Update().
						SetInspectionResult((&renderer.InspectionResult{State: renderer.INSPECTION_STATE_ERROR, ErrMsg: "test"}).ToString()).
						Where(deployment.ClusterName(objC.Name)).
						Exec(context.Background())
				}()
			},
			args: args{
				clusterInfo: &omodel.DeployOptions{
					ShardNum:         1,
					Name:             objC.Name,
					ShardReplicas:    map[string]int{"hba": 1},
					ShardPort:        7000,
					MaxmemoryPolicy:  "volatile-lru",
					SentinelReplicas: map[string]int{"hba": 1},
					SentinelPort:     9000,
					ProxyReplicas:    map[string]int{"hba": 1},
					ProxyPort:        8000,
					ProxyPassword:    "dxm123",
					EnabledAZ:        []string{"hba"},
				},
			},
			wantErr: true,
		},
		{
			name: "通过",
			before: func() {
				go func() {
					time.Sleep(3 * time.Second)
					db, _ := mysql.Database()
					db.Deployment.Update().
						SetInspectionResult((&renderer.InspectionResult{State: renderer.INSPECTION_STATE_NORMAL}).ToString()).
						Where(deployment.ClusterName(objC.Name)).
						Exec(context.Background())
				}()
			},
			args: args{
				clusterInfo: &omodel.DeployOptions{
					ShardNum:         1,
					Name:             objC.Name,
					ShardReplicas:    map[string]int{"hba": 1},
					ShardPort:        7000,
					MaxmemoryPolicy:  "volatile-lru",
					SentinelReplicas: map[string]int{"hba": 1},
					SentinelPort:     9000,
					ProxyReplicas:    map[string]int{"hba": 1},
					ProxyPort:        8000,
					ProxyPassword:    "dxm123",
					EnabledAZ:        []string{"hba"},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := ClusterCreate(tt.args.clusterInfo)
			if (err != nil) != tt.wantErr {
				t.Errorf("ClusterCreate() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
