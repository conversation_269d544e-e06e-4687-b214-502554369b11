package cluster

import (
	"fmt"

	"dt-common/omodel"
	"redis-cmanager/library/renderer"
)

// Proxy扩缩容，修改deployment配置
func ProxyScaling(stageId int64, clusterName string, proxyNum int) error {
	deployment, err := renderer.GetDeploymentFromDB(clusterName)
	if err != nil {
		omodel.StageAppendErrorLog(stageId, "failed to get deployment from db, cluster=%s, error=(%v)", clusterName, err)
		return err
	}

	// 检查是否有污点实例，有污点不能往下进行
	ok, err := renderer.IsThereTaintedProxy(deployment)
	if err != nil {
		omodel.StageAppendErrorLog(stageId, "failed to get pod list, cluster=%s, error=(%v)", clusterName, err)
		return err
	}
	if ok {
		return fmt.Errorf("there are tained pods")
	}

	// 检查当前配置，因涉及到机房切流，所以各机房的proxy数量应当保持一致
	if proxyNum%len(deployment.Spec.Router.Replicas) != 0 {
		return fmt.Errorf("the number of proxy instances is at least %d", len(deployment.Spec.Router.Replicas))
	}
	proxyNumPerIDC := proxyNum / len(deployment.Spec.Router.Replicas)
	alreadyUpdated := false
	for idc, num := range deployment.Spec.Router.Replicas {
		if num == proxyNumPerIDC {
			alreadyUpdated = true
		} else {
			deployment.Spec.Router.Replicas[idc] = proxyNumPerIDC
			alreadyUpdated = false
		}
	}

	// 更新配置，重启检查
	if alreadyUpdated {
		err = renderer.InitDeploymentRender(clusterName)
		if err != nil {
			omodel.StageAppendErrorLog(stageId, "failed to init deployment render, cluster=%s, error=(%v)", clusterName, err)
			return err
		}
		omodel.StageAppendInfoLog(stageId, "cluster %s's number of proxy already changed to %v", clusterName, proxyNum)
	} else {
		deployment.InspectionMode = omodel.MODE_FULL_CARE                     // 不改成fullCare不会触发渲染
		deployment.InspectionResult.State = renderer.INSPECTION_STATE_INIT    // 改成""用于检测结果
		deployment.InspectionResult.StepErrTimes = [renderer.TOTAL_STEP]int{} //
		err = renderer.SaveNewDeployment(deployment)
		if err != nil {
			omodel.StageAppendErrorLog(stageId, "failed to save new deployment to db, cluster=%s, error=(%v)", clusterName, err)
			return err
		}
		omodel.StageAppendInfoLog(stageId, "cluster %s's number of proxy changed to %v", clusterName, proxyNum)
	}

	// 如果是缩容，挑选proxy屏蔽+污点
	err = renderer.LabelProxyToScaleDown(deployment, proxyNumPerIDC)
	if err != nil {
		omodel.StageAppendErrorLog(stageId, "failed to save new deployment to db, cluster=%s, error=(%v)", clusterName, err)
		return err
	}

	// 轮询过程中发生错误，更新状态并退出
	err = WaitForRenderSuccess(stageId, clusterName)
	if err != nil {
		omodel.StageAppendWarnLog(stageId, "failed to check proxy number change, cluster=%s, error=(%v)", clusterName, err)
		return err
	}
	omodel.StageAppendInfoLog(stageId, "succeed to check proxy number change, cluster=%s", clusterName)
	return nil
}
