package model

import (
	"testing"

	"redis-xweb/env"
)

func TestGetAreaList(t *testing.T) {
	// 初始化配置
	env.<PERSON><PERSON>(t, "../config/config.yaml")

	type args struct{}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, *[]Area)
	}{
		{
			name:    "test1",
			args:    args{},
			wantErr: false,
			expect: func(t *testing.T, areas *[]Area) {
				if len(*areas) == 0 {
					t.<PERSON>rrorf("GetAreaList() areas should not be empty")
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			areaList, err := GetAreaList()
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAreaList() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, areaList)
			}
		})
	}
}

func TestGetAreaName(t *testing.T) {
	// 初始化配置
	env.Mock(t, "../config/config.yaml")

	type args struct {
		area string
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, string)
	}{
		{
			name: "test1",
			args: args{
				area: "public",
			},
			wantErr: false,
			expect: func(t *testing.T, name string) {
				if name != "公共区" {
					t.Errorf("GetAreaName() name should be 公共区, but %v", name)
				}
			},
		},
		{
			name: "test2",
			args: args{
				area: "public1",
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			name, err := GetAreaName(tt.args.area)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAreaName() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, name)
			}
		})
	}
}

func TestIsAreaExists(t *testing.T) {
	// 初始化配置
	env.Mock(t, "../config/config.yaml")

	type args struct {
		name string
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, bool)
	}{
		{
			name: "test1",
			args: args{
				name: "public",
			},
			expect: func(t *testing.T, exist bool) {
				if !exist {
					t.Errorf("IsAreaExists() name public should exist, but got false")
				}
			},
		},
		{
			name: "test2",
			args: args{
				name: "公共区",
			},
			expect: func(t *testing.T, exist bool) {
				if exist {
					t.Errorf("IsAreaExists() name 公共区 should not exist, but got true")
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			exist := IsAreaExists(tt.args.name)
			if tt.expect != nil {
				tt.expect(t, exist)
			}
		})
	}
}
