package component

import (
	"context"
	"regexp"
	"testing"

	"github.com/jarcoal/httpmock"

	"dt-common/mysql"
	"redis-cmanager/env"
)

// 单测：Proxy启动
func TestStartProxy(t *testing.T) {
	// 初始化配置
	env.Mock(t, "../../../config/config.yaml")
	objC := env.MockCluster("test_start_proxy")
	_, objS := env.MockTaskWithStage(objC)

	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	db, _ := mysql.Database()
	// mock proxy data
	db.Proxy.Create().SetClusterID(objC.ID).SetClusterName("test_start_proxy").SetDocker(0).SetBns("r3-test-router.siod-redis").SetIP("************").SetPort(8001).SetIdc("hba").Save(context.Background())
	db.Proxy.Create().SetClusterID(objC.ID).SetClusterName("test_start_proxy").SetDocker(0).SetBns("r3-test-router.siod-redis").SetIP("************").SetPort(8001).SetIdc("hbb").Save(context.Background())
	db.Proxy.Create().SetClusterID(objC.ID).SetClusterName("test_start_proxy").SetDocker(1).SetBns("r3-test-router.siod-redis").SetIP("************").SetPort(8001).SetIdc("hba").Save(context.Background())
	db.Proxy.Create().SetClusterID(objC.ID).SetClusterName("test_start_proxy").SetDocker(1).SetBns("r3-test-router.siod-redis").SetIP("************").SetPort(8001).SetIdc("hbb").Save(context.Background())

	type args struct {
		stageId int64
		bns     string
		idc     string
		ipList  []string
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(t *testing.T)
	}{
		{
			name: "test1",
			before: func() {
				httpmock.RegisterResponder("GET", "http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-redis/apps/r3-test-router/instances?showPage=0",
					httpmock.NewStringResponder(200, `{"success": true, "message": "OK", "data": [
						{"name": "0.redis-cmanager.siod-kafka","hostName": "kafka0002.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hba"},"disable": false,"instanceType": "host","podId": null,"containerList": []},
						{"name": "1.redis-cmanager.siod-kafka","hostName": "kafka0000.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hbb"},"disable": false,"instanceType": "host","podId": null,"containerList": []},
						{"name": "2.redis-cmanager.siod-kafka","hostName": "kafka0051.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hba"},"disable": false,"instanceType": "host","podId": null,"containerList": []},
						{"name": "3.redis-cmanager.siod-kafka","hostName": "kafka0052.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hbb"},"disable": false,"instanceType": "host","podId": null,"containerList": []}
					]}`),
				)

				// mock redis-agent
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/proxy/start`),
					httpmock.NewStringResponder(200, `{"code": "00000", "data": "mock ok"}`))
			},
			args: args{
				stageId: objS.ID,
				bns:     "r3-test-router.siod-redis",
				ipList:  []string{"************", "************"},
			},
			wantErr: false,
			expect: func(t *testing.T) {
				info := httpmock.GetCallCountInfo()
				if info["POST =~://[\\w\\W]+/proxy/start"] != 2 {
					t.Errorf("httpmock not called")
				}
			},
		},
		{
			name: "test1.1",
			before: func() {
				httpmock.RegisterResponder("GET", "http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-redis/apps/r3-test-router/instances?showPage=0",
					httpmock.NewStringResponder(200, `{"success": true, "message": "OK", "data": [
						{"name": "0.redis-cmanager.siod-kafka","hostName": "kafka0002.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hba"},"disable": false,"instanceType": "host","podId": null,"containerList": []},
						{"name": "1.redis-cmanager.siod-kafka","hostName": "kafka0000.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hbb"},"disable": false,"instanceType": "host","podId": null,"containerList": []},
						{"name": "2.redis-cmanager.siod-kafka","hostName": "kafka0051.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hba"},"disable": false,"instanceType": "host","podId": null,"containerList": []},
						{"name": "3.redis-cmanager.siod-kafka","hostName": "kafka0052.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hbb"},"disable": false,"instanceType": "host","podId": null,"containerList": []}
					]}`),
				)

				// mock redis-agent
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/proxy/start`),
					httpmock.NewStringResponder(200, `{"code": "00000", "data": "mock ok"}`))
			},
			args: args{
				stageId: objS.ID,
				bns:     "r3-test-router.siod-redis",
				// ipList:  []string{"************", "************"},
				idc: "hba",
			},
			wantErr: false,
			expect: func(t *testing.T) {
				info := httpmock.GetCallCountInfo()
				if info["POST =~://[\\w\\W]+/proxy/start"] != 2 {
					t.Errorf("httpmock not called")
				}
			},
		},
		{
			name: "test2",
			args: args{
				stageId: objS.ID,
				bns:     "r3-test-route1r.siod-redis",
				ipList:  []string{"************", "************"},
			},
			wantErr: true,
		},
		{
			name: "test3",
			args: args{
				stageId: objS.ID,
				bns:     "r3-test-route1r.siod-redis",
				ipList:  []string{"************", "************"},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		if tt.before != nil {
			tt.before()
		}
		t.Run(tt.name, func(t *testing.T) {
			err := StartProxy(tt.args.stageId, tt.args.bns, tt.args.idc, tt.args.ipList)
			if (err != nil) != tt.wantErr {
				t.Errorf("StartProxy() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

// 单测：proxy关停
func TestStopProxy(t *testing.T) {
	// 初始化配置
	env.Mock(t, "../../../config/config.yaml")

	objC := env.MockCluster("test_stop_proxy")
	_, objS := env.MockTaskWithStage(objC)

	db, _ := mysql.Database()
	// mock proxy data
	db.Proxy.Create().SetClusterID(objC.ID).SetClusterName("test_start_proxy").SetDocker(0).SetBns("r3-test-router.siod-redis").SetIP("************").SetPort(8001).SetIdc("hba").Save(context.Background())
	db.Proxy.Create().SetClusterID(objC.ID).SetClusterName("test_start_proxy").SetDocker(0).SetBns("r3-test-router.siod-redis").SetIP("************").SetPort(8001).SetIdc("hbb").Save(context.Background())
	db.Proxy.Create().SetClusterID(objC.ID).SetClusterName("test_start_proxy").SetDocker(1).SetBns("r3-test-router.siod-redis").SetIP("************").SetPort(8001).SetIdc("hba").Save(context.Background())
	db.Proxy.Create().SetClusterID(objC.ID).SetClusterName("test_start_proxy").SetDocker(1).SetBns("r3-test-router.siod-redis").SetIP("************").SetPort(8001).SetIdc("hbb").Save(context.Background())

	type args struct {
		stageId int64
		bns     string
		idc     string
		ipList  []string
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(t *testing.T)
	}{
		{
			name: "test0",
			args: args{
				stageId: objS.ID,
				bns:     "r3-test-router.siod-redis",
				ipList:  []string{"************"},
			},
			wantErr: true,
		},
		{
			name: "test1",
			before: func() {
				// mock noah
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/apptreeNew/[\w\W]+`),
					httpmock.NewStringResponder(200, `{"success": true, "data": [{"ip":"************","name":"0.redis-test-router.siod-redis","portInfo": {"main": 8811},"disable":true}]}`))
				// mock redis-agent
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/proxy/stop`),
					httpmock.NewStringResponder(200, `{"code": "00000", "data": "mockok"}`))
			},
			args: args{
				stageId: objS.ID,
				bns:     "r3-test-router.siod-redis",
				ipList:  []string{"************"},
			},
			wantErr: false,
			expect: func(t *testing.T) {
				info := httpmock.GetCallCountInfo()
				if info["POST =~://[\\w\\W]+/proxy/stop"] != 1 {
					t.Errorf("POST =~://[\\w\\W]+/proxy/stop want 1, but got %d", info["POST =~://[\\w\\W]+/proxy/stop"])
				}
				httpmock.ZeroCallCounters()
			},
		},
		{
			name: "test1.1",
			before: func() {
				// mock noah
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/apptreeNew/[\w\W]+`),
					httpmock.NewStringResponder(200, `{"success": true, "data": [
						{"ip":"************","name":"0.redis-test-router.siod-redis","portInfo": {"main": 8811},"disable":true,"tags": {"idc": "hba"}},
						{"ip":"************","name":"1.redis-test-router.siod-redis","portInfo": {"main": 8811},"disable":true,"tags": {"idc": "hbb"}},
						{"ip":"************","name":"2.redis-test-router.siod-redis","portInfo": {"main": 8811},"disable":false,"tags": {"idc": "hbb"}}
					]}`))
				// mock redis-agent
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/proxy/stop`),
					httpmock.NewStringResponder(200, `{"code": "00000", "data": "mockok"}`))
			},
			args: args{
				stageId: objS.ID,
				bns:     "r3-test-router.siod-redis",
				ipList:  []string{"************"},
				idc:     "hba",
			},
			wantErr: false,
			expect: func(t *testing.T) {
				info := httpmock.GetCallCountInfo()
				if info["POST =~://[\\w\\W]+/proxy/stop"] != 1 {
					t.Errorf("POST =~://[\\w\\W]+/proxy/stop want 1, but got %d", info["POST =~://[\\w\\W]+/proxy/stop"])
				}
				httpmock.ZeroCallCounters()
			},
		},
		{
			name: "test1.2",
			before: func() {
				// mock noah
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/apptreeNew/[\w\W]+`),
					httpmock.NewStringResponder(200, `{"success": true, "data": [
						{"ip":"************","name":"2.redis-test-router.siod-redis","portInfo": {"main": 8811},"disable":false,"tags": {"idc": "hbb"}}
					]}`))
				// mock redis-agent
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/proxy/stop`),
					httpmock.NewStringResponder(200, `{"code": "00000", "data": "mockok"}`))
			},
			args: args{
				stageId: objS.ID,
				bns:     "r3-test-router.siod-redis",
				idc:     "hbb",
			},
			wantErr: true,
		},
		{
			name: "test2",
			before: func() {
				// mock noah
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/apptreeNew/[\w\W]+`),
					httpmock.NewStringResponder(200, `{"success": true, "data": [{"ip":"************","name":"0.redis-test-router.siod-redis","portInfo": {"main": 8811},"disable":true}] }`))
				// mock redis-agent
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/proxy/stop`),
					httpmock.NewStringResponder(200, `{"code": "00000", "data": "mockok"}`))
			},
			args: args{
				stageId: objS.ID,
				bns:     "r3-test-router.siod-redis",
				ipList:  []string{"************", "************"},
			},
			wantErr: true,
			expect: func(t *testing.T) {
				info := httpmock.GetCallCountInfo()
				if info["POST =~://[\\w\\W]+/proxy/stop"] != 0 {
					t.Errorf("POST =~://[\\w\\W]+/proxy/stop want 0, but got %d", info["POST =~://[\\w\\W]+/proxy/stop"])
				}
			},
		},
	}
	for _, tt := range tests {
		if tt.before != nil {
			tt.before()
		}
		t.Run(tt.name, func(t *testing.T) {
			err := StopProxy(tt.args.stageId, tt.args.bns, tt.args.idc, tt.args.ipList)
			if (err != nil) != tt.wantErr {
				t.Errorf("StopProxy() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}
