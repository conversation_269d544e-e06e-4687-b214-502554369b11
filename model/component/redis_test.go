package component

import (
	"context"
	"testing"

	"dt-common/ent/redis"
	"dt-common/mysql"
	"dt-common/redisc"
	"redis-cmanager/env"
)

func Test_updateRole(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")
	objC := env.MockCluster("failover_test")
	db, _ := mysql.Database()
	// mock redis data
	db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetBns("failover-test-redis.siod-redis").SetIP("************").SetIP("************").SetPort(7000).SetIdc("hba").SetName("server1").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).Save(context.Background())
	db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetBns("failover-test-redis.siod-redis").SetIP("************").SetIP("************").SetPort(7000).SetIdc("hbb").SetName("server1").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).Save(context.Background())

	type schema struct {
		masterIP   string
		masterPort int
		slaveIP    string
		slavePort  int
	}
	tests := []struct {
		name    string
		before  func()
		args    schema
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test1: success",
			args: schema{
				masterIP:   "************",
				masterPort: 7000,
				slaveIP:    "************",
				slavePort:  7000,
			},
			wantErr: false,
			expect: func(t *testing.T) {
				redisData, err := db.Redis.Query().Where(
					redis.IP("************"),
					redis.Port(7000),
				).Only(context.Background())
				if err != nil {
					t.Errorf("query redis error")
				}
				if redisData.Role != "master" {
					t.Errorf("************:7000 should be master, but got %s", redisData.Role)
				}
			},
		},
	}
	for _, tt := range tests {
		if tt.before != nil {
			tt.before()
		}
		t.Run(tt.name, func(t *testing.T) {
			err := updateRole(tt.args.masterIP, tt.args.masterPort, tt.args.slaveIP, tt.args.slavePort)
			if (err != nil) != tt.wantErr {
				t.Errorf("updateRole() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

func Test_setAOF(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")

	type schema struct {
		masterIP   string
		masterPort int
		slaveIP    string
		slavePort  int
	}
	tests := []struct {
		name    string
		before  func()
		args    schema
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test1: success",
			before: func() {
				redisc.Mock().ExpectConfigSet("appendonly", "no").SetVal("ok")
				redisc.Mock().ExpectConfigSet("appendonly", "yes").SetVal("ok")
				redisc.Mock().ExpectConfigRewrite().SetVal("ok")
				redisc.Mock().ExpectConfigRewrite().SetVal("ok")
			},
			args: schema{
				masterIP:   "************",
				masterPort: 7000,
				slaveIP:    "************",
				slavePort:  7000,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		if tt.before != nil {
			tt.before()
		}
		t.Run(tt.name, func(t *testing.T) {
			err := setAOF(tt.args.masterIP, tt.args.masterPort, tt.args.slaveIP, tt.args.slavePort)
			if (err != nil) != tt.wantErr {
				t.Errorf("setAOF() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}
