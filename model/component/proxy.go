package component

import (
	"fmt"
	"strings"

	"dt-common/ent/proxy"
	"dt-common/mysql"
	"dt-common/noah"
	"dt-common/omodel"
	"redis-cmanager/library/ragent"
)

// 启动proxy
func StartProxy(stageId int64, bns string, idc string, ipList []string) error {
	// 调用noah接口获取proxy实例列表
	tmp := strings.Split(bns, ".")
	if len(tmp) != 2 {
		return fmt.Errorf("the format of bns %s is incorrect", bns)
	}

	product, app := tmp[1], tmp[0]
	instances, err := noah.GetInstances(product, app)
	if err != nil {
		omodel.StageAppendErrorLog(stageId, "failed to get instances from noah bns, bns=%s, error=(%v)", bns, err)
		return err
	}

	// 获取最终操作的iplist
	ipMap := make(map[string]int)
	if idc != "" {
		for _, instance := range instances {
			if instance.Tags["idc"] == idc {
				ipMap[instance.IP] = instance.PortInfo.Main
			}
		}
	} else {
		for _, ip := range ipList {
			ipMap[ip] = -1
		}
		for _, instance := range instances {
			if _, exist := ipMap[instance.IP]; exist {
				ipMap[instance.IP] = instance.PortInfo.Main
			}
		}
		// 检查参数是否都遍历到了
		for ip, port := range ipMap {
			if port == -1 {
				omodel.StageAppendWarnLog(stageId, "proxy %s:%d not in bns %s", ip, port, bns)
				return fmt.Errorf("proxy %s:%d is not in bns %s", ip, port, bns)
			}
		}
	}

	// 遍历proxy实例并启动
	db, err := mysql.Database()
	if err != nil {
		omodel.StageAppendErrorLog(stageId, "failed to connect mysql, error=(%v)", err)
		return err
	}
	for ip, port := range ipMap {
		// 1 调用agent启动proxy
		err = ragent.ProxyStart(ip, port)
		if err != nil {
			omodel.StageAppendErrorLog(stageId, "failed to start proxy %s:%d, error=(%v)", ip, port, err)
			return err
		}
		omodel.StageAppendErrorLog(stageId, "succeed to start proxy %s:%d, proceed to update metadata", ip, port)

		// 2 更新状态
		ctx, cancel := mysql.ContextWithTimeout()
		_, err = db.Proxy.Update().
			Where(proxy.IP(ip), proxy.Port(port)).
			SetStatus(omodel.INSTANCE_STATUS_NORMAL).
			Save(ctx)
		cancel()
		if err != nil {
			// 更新失败人工介入将元数据修改正确
			omodel.StageAppendErrorLog(stageId, "failed to update the database, error=(%v)", err)
			return err
		}
		omodel.StageAppendInfoLog(stageId, "succeed to update %s:%d metadata status to normal, proceed to check sync status", ip, port)
	}
	omodel.StageAppendInfoLog(stageId, "succeed to start all proxies")

	return nil
}

// 关停proxy,未屏蔽不能关停
func StopProxy(stageId int64, bns string, idc string, ipList []string) error {
	// 调用noah接口获取proxy实例列表
	tmp := strings.Split(bns, ".")
	if len(tmp) != 2 {
		return fmt.Errorf("the format of bns %s is incorrect", bns)
	}

	product, app := tmp[1], tmp[0]
	instances, err := noah.GetInstances(product, app)
	if err != nil {
		omodel.StageAppendErrorLog(stageId, "failed to get instances from noah bns, bns=%s, error=(%v)", bns, err)
		return err
	}

	// 获取最终操作的iplist
	hasProxyNotDisabled := false
	ipMap := make(map[string]int)
	if idc != "" {
		for _, instance := range instances {
			if instance.Tags["idc"] != idc {
				continue
			}
			ipMap[instance.IP] = instance.PortInfo.Main
			if !instance.Disable {
				hasProxyNotDisabled = true
				omodel.StageAppendErrorLog(stageId, "%s:%d is not disabled", instance.IP, instance.PortInfo.Main)
				continue
			}
		}
	} else {
		for _, ip := range ipList {
			ipMap[ip] = -1
		}
		for _, instance := range instances {
			if _, exist := ipMap[instance.IP]; !exist {
				continue
			}
			ipMap[instance.IP] = instance.PortInfo.Main
			if !instance.Disable {
				hasProxyNotDisabled = true
				omodel.StageAppendErrorLog(stageId, "%s:%d is not disabled", instance.IP, instance.PortInfo.Main)
				continue
			}
		}
		// 检查参数是否都遍历到了
		for ip, port := range ipMap {
			if port == -1 {
				omodel.StageAppendWarnLog(stageId, "proxy %s:%d not in bns %s", ip, port, bns)
				return fmt.Errorf("proxy %s:%d is not in bns %s", ip, port, bns)
			}
		}
	}
	// 如果有proxy没被屏蔽报错返回
	if hasProxyNotDisabled {
		return fmt.Errorf("there is a proxy still enabled")
	}

	// 遍历proxy实例并关停
	db, err := mysql.Database()
	if err != nil {
		omodel.StageAppendErrorLog(stageId, "failed to connect mysql, error=(%v)", err)
		return err
	}
	for ip, port := range ipMap {
		// 1 调用agent关停proxy
		err = ragent.ProxyStop(ip, port)
		if err != nil {
			omodel.StageAppendErrorLog(stageId, "failed to stop proxy %s:%d, error=(%v)", ip, port, err)
			return err
		}
		omodel.StageAppendErrorLog(stageId, "succeed to stop proxy %s:%d, proceed to update metadata", ip, port)

		// 2 更新状态
		ctx, cancel := mysql.ContextWithTimeout()
		_, err = db.Proxy.Update().
			Where(proxy.IP(ip), proxy.Port(port)).
			SetStatus(omodel.INSTANCE_STATUS_STOPPED).
			Save(ctx)
		cancel()
		if err != nil {
			// 更新失败人工介入将元数据修改正确
			omodel.StageAppendErrorLog(stageId, "failed to update the database, error=(%v)", err)
			return err
		}
		omodel.StageAppendInfoLog(stageId, "succeed to update %s:%d metadata status to stopped", ip, port)
	}
	omodel.StageAppendInfoLog(stageId, "succeed to stop all proxies")

	return nil
}
