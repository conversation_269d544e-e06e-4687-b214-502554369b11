package component

import (
	"context"
	"fmt"
	"math"
	"strconv"
	"strings"
	"time"

	"github.com/go-redis/redis/v8"
	"golang.org/x/sync/errgroup"

	"dt-common/ent"
	"dt-common/ent/sentinel"
	"dt-common/logger"
	"dt-common/mysql"
	"dt-common/omodel"
	"dt-common/redisc"
)

// =========================================
// 			  Before Failover
// =========================================

// 通过sentinel获取master和slaves的ip、port
// ************:9103> SENTINEL SLAVES panshi_score-server23
//  1. 1） "name"
//     2） "************:7002"
//     3） "ip"
//     4） "************"
//     5） "port"
//     6） "7002"
//     7） "runid"
//     8） "2e3144206f349bf23a123f18ab96710a492a0015"
//     9） "flags"
//     10） "slave"
//     11） "link-pending-commands"
//     12） "0"
//     13） "link-refcount"
//     14） "1"
//     15） "last-ping-sent"
//     16） "0"
//     17） "last-ok-ping-reply"
//     18） "713"
//     19） "last-ping-reply"
//     20） "713"
//     21） "down-after-milliseconds"
//     22） "20000"
//     23） "info-refresh"
//     24） "2083"
//     25） "role-reported"
//     26） "slave"
//     27） "role-reported-time"
//     28） "157248733"
//     29） "master-link-down-time"
//     30） "0"
//     31） "master-link-status"
//     32） "ok"
//     33） "master-host"
//     34） "***********"
//     35） "master-port"
//     36） "7002"
//     37） "slave-priority"
//     38） "100"
//     39） "slave-repl-offset"
//     40） "82001606650"
func getMasterAndSlaves(aliveSentinel *ent.Sentinel, name string) (*omodel.Instance, []*omodel.Instance, error) {
	client, err := redisc.Client(aliveSentinel.IP, aliveSentinel.Port)
	if err != nil {
		return nil, nil, err
	}
	defer client.Close()

	// 获取当前的slaves信息
	ctx, cancel := mysql.ContextWithTimeout()
	slaveList, err := client.Do(ctx, "sentinel", "slaves", name).Slice()
	cancel()
	if err != nil {
		return nil, nil, err
	}
	logger.Debug("getMasterAndSlaves %s, %v", name, slaveList)

	var master *omodel.Instance
	slaves := []*omodel.Instance{}
	for _, info := range slaveList {
		sdown := false
		var masterIP, slaveIP string
		var masterPort, slavePort int
		infos := info.([]interface{})
		for idx, item := range infos {
			switch item.(string) {
			case "ip":
				slaveIP = infos[idx+1].(string)
			case "master-host":
				masterIP = infos[idx+1].(string)
			case "port":
				slavePort, err = strconv.Atoi(infos[idx+1].(string))
				if err != nil {
					return nil, nil, err
				}
			case "master-port":
				masterPort, err = strconv.Atoi(infos[idx+1].(string))
				if err != nil {
					return nil, nil, err
				}
			case "flags":
				// s_down,slave,disconnected
				flags := infos[idx+1].(string)
				sdown = (strings.Contains(flags, "s_down") && strings.Contains(flags, "disconnected"))
			}
		}

		if !sdown {
			master = &omodel.Instance{IP: masterIP, Port: masterPort}
			slaves = append(slaves, &omodel.Instance{IP: slaveIP, Port: slavePort})
		}
	}

	// todo: if len(slaves); sentinel master name

	return master, slaves, nil
}

// =========================================
// 			  Stop Writing Funcs
// =========================================

// 主库停写，通过设置 min-slaves-to-write: 2 实现
func forbidWriting(masterIp string, masterPort int, slaveIp string, slavePort int) error {
	dbIndex := 1
	cli, err := redisc.Client(masterIp, masterPort, redisc.ConnOptions{DB: &dbIndex})
	if err != nil {
		return fmt.Errorf("failed to connect redis, redis=[%s:%d], error=(%v)", masterIp, masterPort, err)
	}
	defer cli.Close()
	ctx, cancel := redisc.WriteTimeout()
	err = cli.ConfigSet(ctx, "min-slaves-to-write", "2").Err()
	cancel()
	if err != nil {
		return fmt.Errorf("failed to set config min-slaves-to-write to 2, redis=[%s:%d], error=(%v)", masterIp, masterPort, err)
	}

	// 尝试写入一个key，如果报错说明停写成功了
	ctx, cancel = redisc.WriteTimeout()
	err = cli.Set(ctx, "stop_write_check", time.Now().Format("200601021504"), 3600*time.Second).Err()
	cancel()
	if err == nil {
		return fmt.Errorf("failed to stop writing, redis=[%s:%d], error=(set stop_write_check ok)", masterIp, masterPort)
	}

	// 获取master_repl_offset用以比较
	info, err := redisc.Info(masterIp, masterPort, "replication")
	if err != nil {
		return fmt.Errorf("failed to get info, redis=[%s:%d], error=(%v)", masterIp, masterPort, err)
	}
	curMasterOffset, err := strconv.Atoi(info["master_repl_offset"])
	if err != nil {
		return fmt.Errorf("failed to convert master_repl_offset to int, redis=[%s:%d], error=(%v)", masterIp, masterPort, err)
	}

	// 检查从库offset是否追上，3s超时
	ctx, cancel = context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()
	for {
		select {
		case <-ctx.Done():
			return fmt.Errorf("check offset timeout, redis=[%s:%d]", slaveIp, slavePort)
		default:
			replication, err := redisc.Info(slaveIp, slavePort, "replication")
			if err != nil {
				return fmt.Errorf("failed to get info, redis=[%s:%d], error=(%v)", slaveIp, slavePort, err)
			}

			slaveOffset, err := strconv.Atoi(replication["slave_repl_offset"])
			if err != nil {
				return fmt.Errorf("failed to convert slave_repl_offset to int, redis=[%s:%d], error=(%v)", slaveIp, slavePort, err)
			}

			// slave offset 追上 master offset 即视为业务数据版本一致了
			if slaveOffset >= curMasterOffset {
				return nil
			}
		}
	}
}

// 解除禁写，最大重试3次
func unforbidWriting(redisIp string, redisPort int) error {
	var err error
	var cli *redis.Client
	for i := 2; i >= 0; i-- {
		cli, err = redisc.Client(redisIp, redisPort)
		if err != nil {
			logger.Warn("failed to connect redis %s:%d, remain retry times %d, error=(%v)", redisIp, redisPort, i, err)
			continue
		}
		defer cli.Close()

		// 重置配置min-slaves-to-write
		ctx, cancel := redisc.WriteTimeout()
		err = cli.ConfigSet(ctx, "min-slaves-to-write", "0").Err()
		cancel()
		if err != nil {
			logger.Warn("failed to set config min-slaves-to-write:0 to redis %s:%d, remain retry times %d, error=(%v)", redisIp, redisPort, i, err)
			continue
		}

		// 检查结果，确保配置成功改回
		var results []interface{}
		ctx, cancel = redisc.ReadTimeout()
		results, err = cli.ConfigGet(ctx, "min-slaves-to-write").Result()
		cancel()
		if err != nil {
			logger.Warn("failed to get config min-slaves-to-write, remain retry times %d, error=(%v)", redisIp, redisPort, i, err)
			continue
		}
		if results[1] != "0" {
			err = fmt.Errorf("config min-slaves-to-write is not 0")
			logger.Warn("config min-slaves-to-write is not 0, got=(%s), remain retry times %d", redisIp, redisPort, results[1], i)
			continue
		}

		break
	}

	return err
}

// ===========================================
// 			   Sentinel Failover
// ===========================================

// 执行主从切换命令
func doFailover(aliveSentinel *ent.Sentinel, name string) error {
	client, err := redisc.Client(aliveSentinel.IP, aliveSentinel.Port)
	if err != nil {
		return err
	}
	defer client.Close()

	// 执行sentinel failover
	ctx, cancel := mysql.ContextWithTimeout()
	err = client.Do(ctx, "sentinel", "failover", name).Err()
	cancel()

	return err
}

// 单分片切换流程，数据完成同步才算切换完成
func FailoverShard(stageID int64, shard *omodel.Shard, aliveSentinel *ent.Sentinel) error {
	// 单测
	if shard.Name == "test" {
		return nil
	}

	// 1、获取分片实例
	currentMaster, currentSlaveList, err := getMasterAndSlaves(aliveSentinel, shard.Name)
	if err != nil {
		omodel.StageAppendErrorLog(stageID, "[%s] failed to get master and slaves info from sentinel %s:%d, error=(%v)", shard.Name, aliveSentinel.IP, aliveSentinel.Port, err)
		return err
	}
	// check: 检查从库数量，是否等于 1
	if len(currentSlaveList) != 1 {
		err = fmt.Errorf("[%s] master has more than 1 slaves, slaves=%+v", shard.Name, currentSlaveList)
		omodel.StageAppendWarnLog(stageID, "%v", err)
		return err
	}

	// 2、匹配主库和从库地址
	currentMasterHost := fmt.Sprintf("%s:%d", currentMaster.IP, currentMaster.Port)
	currentSlaveHost := fmt.Sprintf("%s:%d", currentSlaveList[0].IP, currentSlaveList[0].Port)
	targetMasterHost := fmt.Sprintf("%s:%d", shard.Master.IP, shard.Master.Port)
	targetSlaveHost := fmt.Sprintf("%s:%d", shard.Slaves[0].IP, shard.Slaves[0].Port)
	// 主库IP不是该分片的ip
	if currentMasterHost != targetMasterHost && currentMasterHost != targetSlaveHost {
		err = fmt.Errorf("[%s] master host is %s, neither %s nor %s", shard.Name, currentMasterHost, targetMasterHost, targetSlaveHost)
		omodel.StageAppendWarnLog(stageID, "%v", err)
		return err
	}
	// 从库IP不是该分片的ip
	if currentSlaveHost != targetMasterHost && currentSlaveHost != targetSlaveHost {
		err = fmt.Errorf("[%s] slave host is %s, neither %s nor %s", shard.Name, currentSlaveHost, targetMasterHost, targetSlaveHost)
		omodel.StageAppendWarnLog(stageID, "%v", err)
		return err
	}
	// 分片已经切换完成，可以跳过切换直接检查同步状态
	if currentMasterHost == targetMasterHost && currentSlaveHost == targetSlaveHost {
		omodel.StageAppendInfoLog(stageID, "[%s] already switched", shard.Name)
	}

	// 3、符合规则，执行主从切换
	if currentMasterHost == targetSlaveHost && currentSlaveHost == targetMasterHost {
		// 连通性检查，预防redis-server之间白名单缺失和数据不同步问题
		omodel.StageAppendInfoLog(stageID, "[%s] master's and slave's ip match, test connectivity", shard.Name)
		err := redisc.ConnectivityTest(currentMaster.IP, currentMaster.Port, currentSlaveList[0].IP, currentSlaveList[0].Port)
		if err != nil {
			omodel.StageAppendErrorLog(stageID, "[%s] failed to test connectivity, error=(%v)", shard.Name, err)
			return err
		}

		// 检查slave的min-slaves-to-write，避免切后写不进去
		omodel.StageAppendInfoLog(stageID, "[%s] connectivity passed, check slave config", shard.Name)
		err = unforbidWriting(shard.Master.IP, shard.Master.Port)
		if err != nil {
			cmd := fmt.Sprintf("~/redis_monitor/redis-cli -h %s -p %d config set min-slaves-to-write 0", shard.Master.IP, shard.Master.Port)
			omodel.StageAppendErrorLog(stageID, "[%s] failed to repair slave writing config, exec shell manually, cmd=(%s), error=(%v)", shard.Name, cmd, err)
			return err
		}

		// 修改min-slaves-to-write实现主库停写
		omodel.StageAppendInfoLog(stageID, "[%s] slave config ok, stop writing...", shard.Name)
		err = forbidWriting(currentMaster.IP, currentMaster.Port, currentSlaveList[0].IP, currentSlaveList[0].Port)
		if err != nil {
			omodel.StageAppendErrorLog(stageID, "[%s] failed to stop writing, start to rollback and exit, error=(%v)", shard.Name, err)
			rollbackErr := unforbidWriting(currentMaster.IP, currentMaster.Port)
			if rollbackErr != nil {
				cmd := fmt.Sprintf("~/redis_monitor/redis-cli -h %s -p %d config set min-slaves-to-write 0", currentMaster.IP, currentMaster.Port)
				omodel.StageAppendErrorLog(stageID, "[%s] failed to rollback, exec shell manually, cmd=(%s), error=(%v)", shard.Name, cmd, rollbackErr)
			}
			return err
		}

		// sentinel failover
		omodel.StageAppendInfoLog(stageID, "[%s] writing stopped, exec failover", shard.Name)
		err = doFailover(aliveSentinel, shard.Name)
		if err != nil {
			omodel.StageAppendErrorLog(stageID, "[%s] failed to exec sentinel failover, error=(%v)", shard.Name, err)
			return err
		}
	}

	// 4、先落库，更新metadata
	omodel.StageAppendInfoLog(stageID, "[%s] sync completed, proceeed to update metadata", shard.Name)
	err = updateRole(shard.Master.IP, shard.Master.Port, shard.Slaves[0].IP, shard.Slaves[0].Port)
	if err != nil {
		omodel.StageAppendInfoLog(stageID, "[%s] failed to update metadata, error=(%v)", shard.Name, err)
		return err
	}

	// 5、旧主库需要一段时间才能变为从，立刻检查会报错
	omodel.StageAppendInfoLog(stageID, "[%s] wait 15s to check sync status, timeout 2min", shard.Name)
	time.Sleep(15 * time.Second)

	// 6、等待同步完成，2分钟超时设置
	err = redisc.WaitForSyncComplete(shard.Master.IP, shard.Master.Port, shard.Slaves[0].IP, shard.Slaves[0].Port)
	if err != nil {
		omodel.StageAppendErrorLog(stageID, "%v", err)
		return err
	}

	// 7、设置aof
	omodel.StageAppendInfoLog(stageID, "[%s] metadata update completed, proceeed to set aof", shard.Name)
	err = setAOF(shard.Master.IP, shard.Master.Port, shard.Slaves[0].IP, shard.Slaves[0].Port)
	if err != nil {
		omodel.StageAppendInfoLog(stageID, "[%s] failed to set aof, error=(%v)", shard.Name, err)
		return err
	}

	// 8、重置min-slaves-to-write参数，恢复旧主库写入
	omodel.StageAppendInfoLog(stageID, "[%s] sync status up, repair writing config", shard.Name)
	err = unforbidWriting(shard.Slaves[0].IP, shard.Slaves[0].Port)
	if err != nil {
		cmd := fmt.Sprintf("~/redis_monitor/redis-cli -h %s -p %d config set min-slaves-to-write 0", shard.Slaves[0].IP, shard.Slaves[0].Port)
		omodel.StageAppendErrorLog(stageID, "[%s] failed to repair writing config, exec shell manually, cmd=(%s), error=(%v)", shard.Name, cmd, err)
		return err
	}

	return nil
}

// 串行切换，切换进程受context控制，如果收到取消信号，则终止继续切换
// 切换前需检查ip参数是否与实际符合，如果ip参数不正确，则返回错误
// 若分片已完成切换（重入），依然要执行同步状态检查
func SerialFailover(globalCtx context.Context, stageID int64, shardList []*omodel.Shard, aliveSentinel *ent.Sentinel, intervalTime int) error {
	for i, shard := range shardList {
		select {
		// 全局总控，如果收到取消信号，终止下一个分片的切换
		case <-globalCtx.Done():
			omodel.StageAppendInfoLog(stageID, "[%s] failover canceled", shard.Name)
			return globalCtx.Err()
		default:
			// 执行单分片切换
			err := FailoverShard(stageID, shard, aliveSentinel)
			if err != nil {
				return err
			}

			// 若后续还有分片要切，灰度观察时间
			if i+1 < len(shardList) && intervalTime > 0 && shard.Operated {
				omodel.StageAppendInfoLog(stageID, "[%s] failover completed, continue to execute the next shard after %d seconds", shard.Name, intervalTime)
				time.Sleep(time.Duration(intervalTime) * time.Second)
			} else {
				omodel.StageAppendInfoLog(stageID, "[%s] failover completed", shard.Name)
			}
		}
	}

	return nil
}

// =========================================
// 			  Before 并发执行
// =========================================

// 获取一个可用的sentinel实例用来实施主从切换
func findAliveSentinel(clusterName string) (*ent.Sentinel, error) {
	// 获取集群sentinel信息
	db, err := mysql.Database()
	if err != nil {
		logger.Error(err.Error())
		return nil, err
	}

	ctx, cancel := mysql.ContextWithTimeout()
	sentinelList, err := db.Sentinel.Query().Where(
		sentinel.ClusterName(clusterName),
		sentinel.Status(omodel.INSTANCE_STATUS_NORMAL),
	).All(ctx)
	cancel()
	if err != nil {
		logger.Error("failed to query sentinel, cluster=%s, status=%s, error=(%v)", clusterName, omodel.INSTANCE_STATUS_NORMAL, err)
		return nil, err
	}

	var objSentinel *ent.Sentinel
	for _, obj := range sentinelList {
		_, err := redisc.Info(obj.IP, obj.Port, "Sentinel")
		if err != nil {
			logger.Warn("failed to info sentinel %s:%d, error=(%v)", obj.IP, obj.Port, err)
			continue
		}
		objSentinel = obj
		break
	}

	if objSentinel == nil {
		return nil, fmt.Errorf("no sentinel instance available")
	}

	return objSentinel, nil
}

// 按主库ip将分片分组
func groupByMasterIP(shardList []*omodel.Shard) [][]*omodel.Shard {
	indexMap := map[string]int{}
	shardLists := [][]*omodel.Shard{}

	ipNum := 0
	for _, shard := range shardList {
		idx, ok := indexMap[shard.Master.IP]
		if !ok {
			shardLists = append(shardLists, []*omodel.Shard{})
			indexMap[shard.Master.IP] = ipNum
			idx = ipNum
			ipNum += 1
		}

		shardLists[idx] = append(shardLists[idx], shard)
	}
	return shardLists
}

// 格式化并发池 [并发数][]*Shard，[]*Shard：串行数组
// 原则：每台物理机的并发数尽量保持一致
//
// 如果并发数<=物理机数，将多余的串行数组按顺序追加到各个并发池中
// 如果并发数> 物理机数，从每个串行数组中转移若干Shard到空闲并发池中
func getPools(shardList []*omodel.Shard, maxConcurnecy int) [][]*omodel.Shard {
	totalNum := len(shardList)
	shardLists := groupByMasterIP(shardList)

	result := make([][]*omodel.Shard, maxConcurnecy)
	// 并发数小于master主机数，将多余的串行数组按顺序追加到各个并发池中
	if maxConcurnecy <= len(shardLists) {
		for idx, shards := range shardLists {
			result[idx%maxConcurnecy] = append(result[idx%maxConcurnecy], shards...)
		}
	}

	// 并发数大于master主机数，从shardMap每个成员组中取若干转移到多出的pool中
	if maxConcurnecy > len(shardLists) {
		avgNum := int(math.Floor(float64(totalNum)/float64(maxConcurnecy) + 0.5)) // 平均每个pool的shard数量(max)
		divisor := maxConcurnecy - len(shardLists)

		for idx, shards := range shardLists {
			if len(shards) <= avgNum {
				result[idx] = shards
				continue
			}
			result[idx] = shards[0:avgNum]

			// 还剩 len(shards) - avgNum 个 shard，需要平均放到 maxConcurnecy - len(shardMap) 个 pool 中
			itemNum := (len(shards) - avgNum) / divisor
			if itemNum < 1 {
				itemNum = 1
			}

			// 多出来pool，每个放 itemNum 个shard，最小为1
			extraIdx := 0
			for i := len(shardLists); i < maxConcurnecy; i++ {
				sIdx := avgNum + itemNum*extraIdx
				eIdx := avgNum + itemNum*(extraIdx+1)
				if eIdx > len(shards) {
					break
				}

				result[i] = append(result[i], shards[sIdx:eIdx]...)
				extraIdx += 1
			}
		}
	}

	return result
}

// 主从切换
// []*Shard 		无序的redis分片数组
// maxConcurnecy 	最大并发数
// intervalTime 	间隔时间
func Failover(stageID int64, clusterName string, shardList []*omodel.Shard, maxConcurnecy int, intervalTime int) error {
	if _, exist := taskHandler.Load(stageID); exist {
		return fmt.Errorf("failover task %d is running", stageID)
	}

	// 主从切换只能操作1主1从
	for _, shard := range shardList {
		if len(shard.Slaves) != 1 {
			return fmt.Errorf("shard has more than one slave, can not proceeed failover, shard=%s", shard)
		}
	}

	// 初始化全局上下文，当触发取消请求时，调用cancel方法
	ctx, cancel := context.WithCancel(context.Background())
	taskHandler.Store(stageID, cancel)
	defer taskHandler.Delete(stageID)
	omodel.StageAppendInfoLog(stageID, "start failover, stageID=%d", stageID)

	// 获取一个可用的sentinel实例
	aliveSentinel, err := findAliveSentinel(clusterName)
	if err != nil {
		omodel.StageAppendWarnLog(stageID, "failed to get a usable sentinel, error=(%v)", err)
		return err
	}
	omodel.StageAppendInfoLog(stageID, "use sentinel %s:%d", aliveSentinel.IP, aliveSentinel.Port)

	// 最大len(shardList)个并发，最小为1
	if maxConcurnecy > len(shardList) {
		maxConcurnecy = len(shardList)
	}
	// 将shardList按规则划分成maxConcurnecy个数组，每个数组串行切换
	pools := getPools(shardList, maxConcurnecy)
	// 组间并行，组内串行，当一个组error退出时，所有组停止
	g, ctx := errgroup.WithContext(ctx)
	for tmpIdx := range pools {
		i := tmpIdx
		omodel.StageAppendInfoLog(stageID, "maxConcurnecy=%d, no=%d, pool=%+v", maxConcurnecy, i+1, pools[i])
		g.Go(func() error {
			return SerialFailover(ctx, stageID, pools[i], aliveSentinel, intervalTime)
		})
	}
	if err = g.Wait(); err != nil {
		omodel.StageAppendErrorLog(stageID, "failed to failover, cluster=%s, error=(%v)", clusterName, err)
		return err
	}
	omodel.StageAppendInfoLog(stageID, "succeed to failover, cluster=%s", clusterName)

	return nil
}

// =========================================
// 			  	Stop Failover
// =========================================

// 停止failover任务
func StopFailover(stageID int64) error {
	omodel.StageAppendInfoLog(stageID, "start to stop failover")
	cancelFunc, loaded := taskHandler.LoadAndDelete(stageID)
	if !loaded {
		err := fmt.Errorf("failover task not found")
		omodel.StageAppendWarnLog(stageID, "%v", err)
		return err
	}

	cancelFunc.(context.CancelFunc)()
	omodel.StageAppendInfoLog(stageID, "failover task canceled")
	return nil
}
