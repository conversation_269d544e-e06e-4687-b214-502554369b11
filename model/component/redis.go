package component

import (
	"sync"

	"golang.org/x/sync/errgroup"

	"dt-common/ent/redis"
	"dt-common/mysql"
	"dt-common/omodel"
	"dt-common/redisc"
)

// 任务中断标记，key为stageID
var taskHandler sync.Map

// 设置AOF，主库关闭，从库打开
func setAOF(masterIP string, masterPort int, slaveIP string, slavePort int) error {
	masterClient, err := redisc.Client(masterIP, masterPort)
	if err != nil {
		return err
	}
	defer masterClient.Close()

	slaveClient, err := redisc.Client(slaveIP, slavePort)
	if err != nil {
		return err
	}
	defer slaveClient.Close()

	ctx, cancel := redisc.WriteTimeout()
	err = masterClient.ConfigSet(ctx, "appendonly", "no").Err()
	cancel()
	if err != nil {
		return err
	}

	ctx, cancel = redisc.WriteTimeout()
	err = slaveClient.ConfigSet(ctx, "appendonly", "yes").Err()
	cancel()
	if err != nil {
		return err
	}

	ctx, cancel = redisc.WriteTimeout()
	err = masterClient.ConfigRewrite(ctx).Err()
	cancel()
	if err != nil {
		return err
	}

	ctx, cancel = redisc.WriteTimeout()
	err = slaveClient.ConfigRewrite(ctx).Err()
	cancel()
	if err != nil {
		return err
	}

	return nil
}

// 更新元数据
func updateRole(masterIP string, masterPort int, slaveIP string, slavePort int) error {
	db, err := mysql.Database()
	if err != nil {
		return err
	}

	g := errgroup.Group{}
	g.Go(func() error {
		ctx, cancel := mysql.ContextWithTimeout()
		err = db.Redis.Update().SetRole(omodel.REDIS_ROLE_MASTER).Where(
			redis.IP(masterIP),
			redis.Port(masterPort),
		).Exec(ctx)
		cancel()
		return err
	})
	g.Go(func() error {
		ctx, cancel := mysql.ContextWithTimeout()
		err = db.Redis.Update().SetRole(omodel.REDIS_ROLE_SLAVE).Where(
			redis.IP(slaveIP),
			redis.Port(slavePort),
		).Exec(ctx)
		cancel()
		return err
	})

	if err := g.Wait(); err != nil {
		return err
	}

	return nil
}
