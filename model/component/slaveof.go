package component

import (
	"context"
	"fmt"

	"golang.org/x/sync/errgroup"

	"dt-common/omodel"
	"dt-common/redisc"
)

// 单分片SlaveOf
func SlaveOfShard(stageID int64, shard *omodel.Shard) error {
	// 1、避免级联，需要检查主库是不是master
	masterInfo, err := redisc.Info(shard.Master.IP, shard.Master.Port, "Replication")
	if err != nil {
		omodel.StageAppendErrorLog(stageID, "failed to get redis %s:%d info, error=(%v)", shard.Master.IP, shard.Master.Port, err)
		return err
	}
	if masterInfo["role"] != omodel.REDIS_ROLE_MASTER {
		err := fmt.Errorf("master redis %s:%d is not a master, cannot exec slaveOf", shard.Master.IP, shard.Master.Port)
		omodel.StageAppendErrorLog(stageID, "%v", err)
		return err
	}
	// 2、避免级联，从库不能是有从库的主库
	slaveInfo, err := redisc.Info(shard.Slaves[0].IP, shard.Slaves[0].Port, "Replication")
	if err != nil {
		omodel.StageAppendErrorLog(stageID, "failed to get redis %s:%d info, error=(%v)", shard.Slaves[0].IP, shard.Slaves[0].Port, err)
		return err
	}
	if slaveInfo["role"] == omodel.REDIS_ROLE_MASTER && slaveInfo["slave0"] != "" {
		err := fmt.Errorf("redis %s:%d is a master with slaves, cannot exec slaveOf %s:%d", shard.Slaves[0].IP, shard.Slaves[0].Port, shard.Master.IP, shard.Master.Port)
		omodel.StageAppendErrorLog(stageID, "%v", err)
		return err
	}
	// 3、从库不能是其他主库的从库，只能是slaveOf自己，或者已经slaveOf了主库
	slaveOfMyself := false
	if slaveInfo["master_host"] == "127.0.0.1" && slaveInfo["master_port"] == fmt.Sprintf("%d", shard.Slaves[0].Port) {
		slaveOfMyself = true
	}
	alreadySlaveOf := false
	if slaveInfo["master_host"] == shard.Master.IP && slaveInfo["master_port"] == fmt.Sprintf("%d", shard.Master.Port) {
		alreadySlaveOf = true
	}
	if slaveInfo["role"] != omodel.REDIS_ROLE_MASTER && !slaveOfMyself && !alreadySlaveOf {
		err := fmt.Errorf("redis %s:%d already slave of another master %s:%s, cannot exec slaveOf %s:%d", shard.Slaves[0].IP, shard.Slaves[0].Port, slaveInfo["master_host"], slaveInfo["master_port"], shard.Master.IP, shard.Master.Port)
		omodel.StageAppendErrorLog(stageID, "%v", err)
		return err
	}

	// 4、exec cmd slaveof
	if !alreadySlaveOf {
		slaveClient, err := redisc.Client(shard.Slaves[0].IP, shard.Slaves[0].Port)
		if err != nil {
			omodel.StageAppendErrorLog(stageID, "failed to get redis %s:%d client, error=(%v)", shard.Slaves[0].IP, shard.Slaves[0].Port, err)
			return err
		}
		ctx, cancel := redisc.WriteTimeout()
		err = slaveClient.SlaveOf(ctx, shard.Master.IP, fmt.Sprintf("%d", shard.Master.Port)).Err()
		slaveClient.Close()
		cancel()
		if err != nil {
			omodel.StageAppendErrorLog(stageID, "failed to exec slaveOf, redis=%s:%d, error=(%v)", shard.Slaves[0].IP, shard.Slaves[0].Port, err)
			return err
		}
		omodel.StageAppendInfoLog(stageID, "[%s] redis %s:%d now become a slave of redis %s:%d", shard.Name, shard.Slaves[0].IP, shard.Slaves[0].Port, shard.Master.IP, shard.Master.Port)
	}

	// 5、更新元数据
	err = updateRole(shard.Master.IP, shard.Master.Port, shard.Slaves[0].IP, shard.Slaves[0].Port)
	if err != nil {
		omodel.StageAppendErrorLog(stageID, "failed to connect mysql, error=(%v)", err)
		return err
	}
	omodel.StageAppendInfoLog(stageID, "[%s] succeed to update redis %s:%d metadata role to slave, wait for data sync", shard.Name, shard.Slaves[0].IP, shard.Slaves[0].Port)

	// 6、检查同步状态
	err = redisc.WaitForSyncComplete(shard.Master.IP, shard.Master.Port, shard.Slaves[0].IP, shard.Slaves[0].Port)
	if err != nil {
		omodel.StageAppendErrorLog(stageID, "%v", err)
		return err
	}

	// 7、设置aof
	omodel.StageAppendInfoLog(stageID, "[%s] metadata update completed, proceeed to set aof", shard.Name)
	err = setAOF(shard.Master.IP, shard.Master.Port, shard.Slaves[0].IP, shard.Slaves[0].Port)
	if err != nil {
		omodel.StageAppendInfoLog(stageID, "[%s] failed to set aof, error=(%v)", shard.Name, err)
		return err
	}

	return nil
}

// 串行SlaveOf
func SerialSlaveOf(globalCtx context.Context, stageID int64, shardList []*omodel.Shard) error {
	for _, shard := range shardList {
		select {
		// 全局总控，如果收到取消信号，终止下一个分片的SlaveOf
		case <-globalCtx.Done():
			omodel.StageAppendInfoLog(stageID, "[%s] slaveOf canceled", shard.Name)
			return globalCtx.Err()
		default:
			// 执行单分片切换
			err := SlaveOfShard(stageID, shard)
			if err != nil {
				return err
			}
		}
	}

	return nil
}

// 并行SlaveOf
func SlaveOf(stageID int64, shardList []*omodel.Shard) error {
	if _, exist := taskHandler.Load(stageID); exist {
		return fmt.Errorf("slaveOf task %d is running", stageID)
	}

	// slaveof一次只能1个从库
	for _, shard := range shardList {
		if len(shard.Slaves) != 1 {
			return fmt.Errorf("shard has more than one slave, can not proceeed slaveof, shard=%s", shard)
		}
	}

	// 初始化全局上下文，当触发取消请求时，调用cancel方法
	ctx, cancel := context.WithCancel(context.Background())
	taskHandler.Store(stageID, cancel)
	defer taskHandler.Delete(stageID)
	omodel.StageAppendInfoLog(stageID, "start slaveOf, stageId=%d", stageID)

	// 将shardList按IP划分成个多个数组，每个数组串行切换
	shardLists := groupByMasterIP(shardList)
	g, ctx := errgroup.WithContext(context.Background())
	for i, shards := range shardLists {
		tmp := shards
		omodel.StageAppendInfoLog(stageID, "no=%d, shardList=%+v", i+1, tmp)
		g.Go(func() error {
			return SerialSlaveOf(ctx, stageID, tmp)
		})
	}
	if err := g.Wait(); err != nil {
		omodel.StageAppendErrorLog(stageID, "failed to slaveOf, error=(%v)", err)
		return err
	}
	omodel.StageAppendInfoLog(stageID, "succeed to slaveOf, stageId=%d", stageID)

	return nil
}

// =========================================
// 			  	Stop SlaveOf
// =========================================

// 停止SlaveOf任务
func StopSlaveOf(stageId int64) error {
	omodel.StageAppendInfoLog(stageId, "start to stop slaveOf")
	cancelFunc, loaded := taskHandler.LoadAndDelete(stageId)
	if !loaded {
		err := fmt.Errorf("slaveOf task not found")
		omodel.StageAppendWarnLog(stageId, "%v", err)
		return err
	}

	cancelFunc.(context.CancelFunc)()
	omodel.StageAppendInfoLog(stageId, "slaveOf task canceled")
	return nil
}
