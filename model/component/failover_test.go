package component

import (
	"context"
	"testing"
	"time"

	"dt-common/ent"
	"dt-common/mysql"
	"dt-common/omodel"
	"dt-common/redisc"
	"redis-cmanager/env"
)

func TestFindAliveSentinel(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")
	objC := env.MockCluster("failover_test")

	type schema struct {
		clusterName string
	}
	tests := []struct {
		name    string
		before  func()
		args    schema
		wantErr bool
		expect  func(*testing.T, *ent.Sentinel)
	}{
		{
			name: "test1: no alive sentinel",
			before: func() {
				db, _ := mysql.Database()
				db.Sentinel.Create().SetClusterID(objC.ID).SetClusterName("failover_test").SetBns("failover-test-sentinel.siod-redis").SetIP("************").SetPort(9001).SetIdc("hba").Save(context.Background())
				db.Sentinel.Create().SetClusterID(objC.ID).SetClusterName("failover_test").SetBns("failover-test-sentinel.siod-redis").SetIP("************").SetPort(9001).SetIdc("hbb").Save(context.Background())
				db.Sentinel.Create().SetClusterID(objC.ID).SetClusterName("failover_test").SetBns("failover-test-sentinel.siod-redis").SetIP("************").SetPort(9001).SetIdc("hbb").Save(context.Background())
			},
			args: schema{
				clusterName: "failover_test",
			},
			wantErr: true,
			expect: func(t *testing.T, s *ent.Sentinel) {
				if s != nil {
					t.Errorf("expect nil, but %v", s)
				}
			},
		},
		{
			name: "test2: success",
			before: func() {
				redisc.Mock().ExpectInfo("Sentinel").SetVal("s:s")
			},
			args: schema{
				clusterName: "failover_test",
			},
			wantErr: false,
			expect: func(t *testing.T, s *ent.Sentinel) {
				if s == nil {
					t.Errorf("expect not nil, but %v", s)
				}
			},
		},
	}
	for _, tt := range tests {
		if tt.before != nil {
			tt.before()
		}
		t.Run(tt.name, func(t *testing.T) {
			sentinel, err := findAliveSentinel(tt.args.clusterName)
			if (err != nil) != tt.wantErr {
				t.Errorf("findAliveSentinel() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, sentinel)
			}
		})
	}
}

func TestGroupByMasterIP(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")

	type schema struct {
		shardList []*omodel.Shard
	}
	tests := []struct {
		name    string
		before  func()
		args    schema
		wantErr bool
		expect  func(*testing.T, [][]*omodel.Shard)
	}{
		{
			name: "test1: success",
			args: schema{
				shardList: []*omodel.Shard{
					{Name: "redis_public_test-server1", Master: &omodel.Instance{IP: "************", Port: 7000}, Slaves: []*omodel.Instance{{IP: "************", Port: 7000}}},
					{Name: "redis_public_test-server2", Master: &omodel.Instance{IP: "************", Port: 7001}, Slaves: []*omodel.Instance{{IP: "************", Port: 7001}}},
					{Name: "redis_public_test-server3", Master: &omodel.Instance{IP: "************", Port: 7000}, Slaves: []*omodel.Instance{{IP: "************", Port: 7000}}},
					{Name: "redis_public_test-server4", Master: &omodel.Instance{IP: "************", Port: 7001}, Slaves: []*omodel.Instance{{IP: "************", Port: 7001}}},
					{Name: "redis_public_test-server5", Master: &omodel.Instance{IP: "************", Port: 7000}, Slaves: []*omodel.Instance{{IP: "************", Port: 7000}}},
					{Name: "redis_public_test-server6", Master: &omodel.Instance{IP: "************", Port: 7001}, Slaves: []*omodel.Instance{{IP: "************", Port: 7001}}},
					{Name: "redis_public_test-server7", Master: &omodel.Instance{IP: "************", Port: 7000}, Slaves: []*omodel.Instance{{IP: "************", Port: 7000}}},
					{Name: "redis_public_test-server8", Master: &omodel.Instance{IP: "************", Port: 7001}, Slaves: []*omodel.Instance{{IP: "************", Port: 7001}}},
				},
			},
			expect: func(t *testing.T, shardPools [][]*omodel.Shard) {
				if len(shardPools) != 4 {
					t.Errorf("len(shardPools) = %v, want 1", len(shardPools))
				}
				if len(shardPools[0]) != 2 {
					t.Errorf("len(shardPools[0]) = %v, want 2", len(shardPools[0]))
				}
			},
		},
	}
	for _, tt := range tests {
		if tt.before != nil {
			tt.before()
		}
		t.Run(tt.name, func(t *testing.T) {
			shardPools := groupByMasterIP(tt.args.shardList)
			// if (err != nil) != tt.wantErr {
			// 	t.Errorf("groupByMasterIP() error = %v, wantErr %v", err, tt.wantErr)
			// }
			if tt.expect != nil {
				tt.expect(t, shardPools)
			}
		})
	}
}

func TestGetPools(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")

	type schema struct {
		shardList     []*omodel.Shard
		maxConcurnecy int
	}
	tests := []struct {
		name    string
		before  func()
		args    schema
		wantErr bool
		expect  func(*testing.T, [][]*omodel.Shard)
	}{
		{
			name: "test1: 8 shard, 2 bbc, 1 concurrency, avg",
			args: schema{
				shardList: []*omodel.Shard{
					{Name: "redis_public_test-server1", Master: &omodel.Instance{IP: "************", Port: 7000}, Slaves: []*omodel.Instance{{IP: "************", Port: 7000}}},
					{Name: "redis_public_test-server2", Master: &omodel.Instance{IP: "************", Port: 7001}, Slaves: []*omodel.Instance{{IP: "************", Port: 7001}}},
					{Name: "redis_public_test-server3", Master: &omodel.Instance{IP: "************", Port: 7002}, Slaves: []*omodel.Instance{{IP: "************", Port: 7002}}},
					{Name: "redis_public_test-server4", Master: &omodel.Instance{IP: "************", Port: 7003}, Slaves: []*omodel.Instance{{IP: "************", Port: 7003}}},
					{Name: "redis_public_test-server5", Master: &omodel.Instance{IP: "************", Port: 7000}, Slaves: []*omodel.Instance{{IP: "************", Port: 7000}}},
					{Name: "redis_public_test-server6", Master: &omodel.Instance{IP: "************", Port: 7001}, Slaves: []*omodel.Instance{{IP: "************", Port: 7001}}},
					{Name: "redis_public_test-server7", Master: &omodel.Instance{IP: "************", Port: 7002}, Slaves: []*omodel.Instance{{IP: "************", Port: 7002}}},
					{Name: "redis_public_test-server8", Master: &omodel.Instance{IP: "************", Port: 7003}, Slaves: []*omodel.Instance{{IP: "************", Port: 7003}}},
				},
				maxConcurnecy: 1,
			},
			wantErr: false,
			expect: func(t *testing.T, shardList [][]*omodel.Shard) {
				if len(shardList) != 1 {
					t.Errorf("len(shardList) = %v, want 1", len(shardList))
				}
				if len(shardList[0]) != 8 {
					t.Errorf("len(shardList[0]) = %v, want 8", len(shardList[0]))
				}
			},
		},
		{
			name: "test2: 8 shard, 2 bbc, 2 concurrency, avg",
			args: schema{
				shardList: []*omodel.Shard{
					{Name: "redis_public_test-server1", Master: &omodel.Instance{IP: "************", Port: 7000}, Slaves: []*omodel.Instance{{IP: "************", Port: 7000}}},
					{Name: "redis_public_test-server2", Master: &omodel.Instance{IP: "************", Port: 7001}, Slaves: []*omodel.Instance{{IP: "************", Port: 7001}}},
					{Name: "redis_public_test-server3", Master: &omodel.Instance{IP: "************", Port: 7002}, Slaves: []*omodel.Instance{{IP: "************", Port: 7002}}},
					{Name: "redis_public_test-server4", Master: &omodel.Instance{IP: "************", Port: 7003}, Slaves: []*omodel.Instance{{IP: "************", Port: 7003}}},
					{Name: "redis_public_test-server5", Master: &omodel.Instance{IP: "************", Port: 7000}, Slaves: []*omodel.Instance{{IP: "************", Port: 7000}}},
					{Name: "redis_public_test-server6", Master: &omodel.Instance{IP: "************", Port: 7001}, Slaves: []*omodel.Instance{{IP: "************", Port: 7001}}},
					{Name: "redis_public_test-server7", Master: &omodel.Instance{IP: "************", Port: 7002}, Slaves: []*omodel.Instance{{IP: "************", Port: 7002}}},
					{Name: "redis_public_test-server8", Master: &omodel.Instance{IP: "************", Port: 7003}, Slaves: []*omodel.Instance{{IP: "************", Port: 7003}}},
				},
				maxConcurnecy: 2,
			},
			wantErr: false,
			expect: func(t *testing.T, shardList [][]*omodel.Shard) {
				if len(shardList) != 2 {
					t.Errorf("len(shardList) = %v, want 2", len(shardList))
				}
				if len(shardList[0]) != 4 {
					t.Errorf("len(shardList[0]) = %v, want 4", len(shardList[0]))
				}
				for _, shard := range shardList[0] {
					if shard.Master.IP != "************" {
						t.Errorf("ip = %v, want ************", shard.Master.IP)
					}
				}
			},
		},
		{
			name: "test3: 8 shard, 2 bbc, 3 concurrency, avg",
			args: schema{
				shardList: []*omodel.Shard{
					{Name: "redis_public_test-server1", Master: &omodel.Instance{IP: "************", Port: 7000}, Slaves: []*omodel.Instance{{IP: "************", Port: 7000}}},
					{Name: "redis_public_test-server2", Master: &omodel.Instance{IP: "************", Port: 7001}, Slaves: []*omodel.Instance{{IP: "************", Port: 7001}}},
					{Name: "redis_public_test-server3", Master: &omodel.Instance{IP: "************", Port: 7002}, Slaves: []*omodel.Instance{{IP: "************", Port: 7002}}},
					{Name: "redis_public_test-server4", Master: &omodel.Instance{IP: "************", Port: 7003}, Slaves: []*omodel.Instance{{IP: "************", Port: 7003}}},
					{Name: "redis_public_test-server5", Master: &omodel.Instance{IP: "************", Port: 7000}, Slaves: []*omodel.Instance{{IP: "************", Port: 7000}}},
					{Name: "redis_public_test-server6", Master: &omodel.Instance{IP: "************", Port: 7001}, Slaves: []*omodel.Instance{{IP: "************", Port: 7001}}},
					{Name: "redis_public_test-server7", Master: &omodel.Instance{IP: "************", Port: 7002}, Slaves: []*omodel.Instance{{IP: "************", Port: 7002}}},
					{Name: "redis_public_test-server8", Master: &omodel.Instance{IP: "************", Port: 7003}, Slaves: []*omodel.Instance{{IP: "************", Port: 7003}}},
				},
				maxConcurnecy: 3,
			},
			wantErr: false,
			expect: func(t *testing.T, shardList [][]*omodel.Shard) {
				if len(shardList) != 3 {
					t.Errorf("len(shardList) = %v, want 3", len(shardList))
				}
				if len(shardList[0]) != 3 {
					t.Errorf("len(shardList[0]) = %v, want 3", len(shardList[0]))
				}
				if len(shardList[1]) != 3 {
					t.Errorf("len(shardList[1]) = %v, want 3", len(shardList[1]))
				}
				if len(shardList[2]) != 2 {
					t.Errorf("len(shardList[2]) = %v, want 2", len(shardList[2]))
				}
			},
		},
		{
			name: "test4: 8 shard, 2 bbc, 4 concurrency, avg",
			args: schema{
				shardList: []*omodel.Shard{
					{Name: "redis_public_test-server1", Master: &omodel.Instance{IP: "************", Port: 7000}, Slaves: []*omodel.Instance{{IP: "************", Port: 7000}}},
					{Name: "redis_public_test-server2", Master: &omodel.Instance{IP: "************", Port: 7001}, Slaves: []*omodel.Instance{{IP: "************", Port: 7001}}},
					{Name: "redis_public_test-server3", Master: &omodel.Instance{IP: "************", Port: 7002}, Slaves: []*omodel.Instance{{IP: "************", Port: 7002}}},
					{Name: "redis_public_test-server4", Master: &omodel.Instance{IP: "************", Port: 7003}, Slaves: []*omodel.Instance{{IP: "************", Port: 7003}}},
					{Name: "redis_public_test-server5", Master: &omodel.Instance{IP: "************", Port: 7000}, Slaves: []*omodel.Instance{{IP: "************", Port: 7000}}},
					{Name: "redis_public_test-server6", Master: &omodel.Instance{IP: "************", Port: 7001}, Slaves: []*omodel.Instance{{IP: "************", Port: 7001}}},
					{Name: "redis_public_test-server7", Master: &omodel.Instance{IP: "************", Port: 7002}, Slaves: []*omodel.Instance{{IP: "************", Port: 7002}}},
					{Name: "redis_public_test-server8", Master: &omodel.Instance{IP: "************", Port: 7003}, Slaves: []*omodel.Instance{{IP: "************", Port: 7003}}},
				},
				maxConcurnecy: 4,
			},
			wantErr: false,
			expect: func(t *testing.T, shardList [][]*omodel.Shard) {
				if len(shardList) != 4 {
					t.Errorf("len(shardList) = %v, want 4", len(shardList))
				}
				for i := range shardList {
					if len(shardList[i]) != 2 {
						t.Errorf("len(shardList[%d]) = %v, want 2", i, len(shardList[0]))
					}
				}
			},
		},
		{
			name: "test5: 6 shard, 3 bbc, 5 concurrency, unequal",
			args: schema{
				shardList: []*omodel.Shard{
					{Name: "redis_public_test-server1", Master: &omodel.Instance{IP: "************", Port: 7000}, Slaves: []*omodel.Instance{{IP: "************", Port: 7000}}},
					{Name: "redis_public_test-server3", Master: &omodel.Instance{IP: "************", Port: 7002}, Slaves: []*omodel.Instance{{IP: "************", Port: 7002}}},
					{Name: "redis_public_test-server5", Master: &omodel.Instance{IP: "************", Port: 7000}, Slaves: []*omodel.Instance{{IP: "************", Port: 7000}}},
					{Name: "redis_public_test-server6", Master: &omodel.Instance{IP: "************", Port: 7001}, Slaves: []*omodel.Instance{{IP: "************", Port: 7001}}},
					{Name: "redis_public_test-server7", Master: &omodel.Instance{IP: "************", Port: 7002}, Slaves: []*omodel.Instance{{IP: "************", Port: 7002}}},
					{Name: "redis_public_test-server12", Master: &omodel.Instance{IP: "************", Port: 7003}, Slaves: []*omodel.Instance{{IP: "************", Port: 7003}}},
				},
				maxConcurnecy: 5,
			},
			wantErr: false,
			expect: func(t *testing.T, shardList [][]*omodel.Shard) {
				if len(shardList) != 5 {
					t.Errorf("len(shardList) = %v, want 5", len(shardList))
				}
				if len(shardList[0]) != 1 {
					t.Errorf("len(shardList[0]) = %v, want 1", len(shardList[0]))
				}
				if len(shardList[3]) != 2 {
					t.Errorf("len(shardList[3]) = %v, want 2", len(shardList[3]))
				}
			},
		},
		{
			name: "test6: 8 shard, 8 bbc, 3 concurrency",
			args: schema{
				shardList: []*omodel.Shard{
					{Name: "redis_public_test-server1", Master: &omodel.Instance{IP: "************", Port: 7000}, Slaves: []*omodel.Instance{{IP: "************", Port: 7000}}},
					{Name: "redis_public_test-server2", Master: &omodel.Instance{IP: "************", Port: 7000}, Slaves: []*omodel.Instance{{IP: "************", Port: 7000}}},
					{Name: "redis_public_test-server3", Master: &omodel.Instance{IP: "************", Port: 7000}, Slaves: []*omodel.Instance{{IP: "************", Port: 7000}}},
					{Name: "redis_public_test-server4", Master: &omodel.Instance{IP: "************", Port: 7000}, Slaves: []*omodel.Instance{{IP: "************", Port: 7000}}},
					{Name: "redis_public_test-server5", Master: &omodel.Instance{IP: "************", Port: 7000}, Slaves: []*omodel.Instance{{IP: "************", Port: 7000}}},
					{Name: "redis_public_test-server6", Master: &omodel.Instance{IP: "************", Port: 7000}, Slaves: []*omodel.Instance{{IP: "************", Port: 7000}}},
					{Name: "redis_public_test-server7", Master: &omodel.Instance{IP: "************", Port: 7000}, Slaves: []*omodel.Instance{{IP: "************", Port: 7000}}},
					{Name: "redis_public_test-server8", Master: &omodel.Instance{IP: "************", Port: 7000}, Slaves: []*omodel.Instance{{IP: "************", Port: 7000}}},
				},
				maxConcurnecy: 3,
			},
			wantErr: false,
			expect: func(t *testing.T, shardList [][]*omodel.Shard) {
				if len(shardList) != 3 {
					t.Errorf("len(shardList) = %v, want 3", len(shardList))
				}
				if len(shardList[0]) != 3 {
					t.Errorf("len(shardList[0]) = %v, want 3", len(shardList[0]))
				}
			},
		},
	}
	for _, tt := range tests {
		if tt.before != nil {
			tt.before()
		}
		t.Run(tt.name, func(t *testing.T) {
			shardPool := getPools(tt.args.shardList, tt.args.maxConcurnecy)
			// if (err != nil) != tt.wantErr {
			// 	t.Errorf("getPools() error = %v, wantErr %v", err, tt.wantErr)
			// }
			if tt.expect != nil {
				tt.expect(t, shardPool)
			}
		})
	}
}

func TestGetMasterAndSlaves(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")

	type schema struct {
		sentinel *ent.Sentinel
		name     string
	}
	tests := []struct {
		name    string
		before  func()
		args    schema
		wantErr bool
		expect  func(*testing.T, *omodel.Instance, []*omodel.Instance)
	}{
		{
			name: "test1",
			args: schema{
				sentinel: &ent.Sentinel{IP: "************", Port: 8999},
				name:     "redis_public_test-server1",
			},
			wantErr: true,
		},
		{
			name: "test2: port atoi failed",
			before: func() {
				redisc.Mock().ExpectDo("sentinel", "slaves", "redis_public_test-server1").SetVal([]any{[]any{"name", "************:7000", "ip", "************", "port", "7a000", "runid", "233cf9d8cc205201523e16d325ab4e653666cba7", "flags", "slave", "link-pending-commands", "0", "link-refcount", "1", "last-ping-sent", "0", "last-ok-ping-reply", "967", "last-ping-reply", "967", "down-after-milliseconds", "30000", "info-refresh", "7805", "role-reported", "slave", "role-reported-time", "4746715447", "master-link-down-time", "0", "master-link-status", "ok", "master-host", "************", "master-port", "7000", "slave-priority", "100", "slave-repl-offset", "412556657252"}})
			},
			args: schema{
				sentinel: &ent.Sentinel{IP: "************", Port: 8999},
				name:     "redis_public_test-server1",
			},
			wantErr: true,
		},
		{
			name: "test2: port atoi failed",
			before: func() {
				redisc.Mock().ExpectDo("sentinel", "slaves", "redis_public_test-server1").SetVal([]any{[]any{"name", "************:7000", "ip", "************", "port", "7000", "runid", "233cf9d8cc205201523e16d325ab4e653666cba7", "flags", "slave", "link-pending-commands", "0", "link-refcount", "1", "last-ping-sent", "0", "last-ok-ping-reply", "967", "last-ping-reply", "967", "down-after-milliseconds", "30000", "info-refresh", "7805", "role-reported", "slave", "role-reported-time", "4746715447", "master-link-down-time", "0", "master-link-status", "ok", "master-host", "************", "master-port", "700a0", "slave-priority", "100", "slave-repl-offset", "412556657252"}})
			},
			args: schema{
				sentinel: &ent.Sentinel{IP: "************", Port: 8999},
				name:     "redis_public_test-server1",
			},
			wantErr: true,
		},
		{
			name: "test3: success",
			before: func() {
				redisc.Mock().ExpectDo("sentinel", "slaves", "redis_public_test-server1").SetVal([]any{[]any{"name", "************:7000", "ip", "************", "port", "7000", "runid", "233cf9d8cc205201523e16d325ab4e653666cba7", "flags", "slave", "link-pending-commands", "0", "link-refcount", "1", "last-ping-sent", "0", "last-ok-ping-reply", "967", "last-ping-reply", "967", "down-after-milliseconds", "30000", "info-refresh", "7805", "role-reported", "slave", "role-reported-time", "4746715447", "master-link-down-time", "0", "master-link-status", "ok", "master-host", "************", "master-port", "7000", "slave-priority", "100", "slave-repl-offset", "412556657252"}})
			},
			args: schema{
				sentinel: &ent.Sentinel{IP: "************", Port: 8999},
				name:     "redis_public_test-server1",
			},
			wantErr: false,
		},
		{
			name: "test4: s_down",
			before: func() {
				redisc.Mock().ExpectDo("sentinel", "slaves", "redis_public_test-server1").SetVal([]any{[]any{"name", "************:7000", "ip", "************", "port", "7000", "runid", "233cf9d8cc205201523e16d325ab4e653666cba7", "flags", "s_down,slave,disconnected", "link-pending-commands", "0", "link-refcount", "1", "last-ping-sent", "0", "last-ok-ping-reply", "967", "last-ping-reply", "967", "down-after-milliseconds", "30000", "info-refresh", "7805", "role-reported", "slave", "role-reported-time", "4746715447", "master-link-down-time", "0", "master-link-status", "ok", "master-host", "************", "master-port", "7000", "slave-priority", "100", "slave-repl-offset", "412556657252"}})
			},
			args: schema{
				sentinel: &ent.Sentinel{IP: "************", Port: 8999},
				name:     "redis_public_test-server1",
			},
			wantErr: false,
			expect: func(t *testing.T, i1 *omodel.Instance, i2 []*omodel.Instance) {
				if len(i2) != 0 {
					t.Errorf("expect len(i2) == 0, but got %d", len(i2))
				}
			},
		},
	}
	for _, tt := range tests {
		if tt.before != nil {
			tt.before()
		}
		t.Run(tt.name, func(t *testing.T) {
			master, slaves, err := getMasterAndSlaves(tt.args.sentinel, tt.args.name)
			if (err != nil) != tt.wantErr {
				t.Errorf("getMasterAndSlaves() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, master, slaves)
			}
		})
	}
}

func TestDoFailover(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")

	type schema struct {
		sentinel *ent.Sentinel
		name     string
	}
	tests := []struct {
		name    string
		before  func()
		args    schema
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test1: success",
			before: func() {
				redisc.Mock().ExpectDo("sentinel", "failover", "redis_public_test-server1").SetVal("ok")
			},
			args: schema{
				sentinel: &ent.Sentinel{IP: "************", Port: 8999},
				name:     "redis_public_test-server1",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		if tt.before != nil {
			tt.before()
		}
		t.Run(tt.name, func(t *testing.T) {
			err := doFailover(tt.args.sentinel, tt.args.name)
			if (err != nil) != tt.wantErr {
				t.Errorf("doFailover() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

func TestFailoverShard(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")
	objC := env.MockCluster("failover_test")
	_, objS := env.MockTaskWithStage(objC)

	type schema struct {
		stageId       int64
		shard         *omodel.Shard
		aliveSentinel *ent.Sentinel
	}
	tests := []struct {
		name    string
		before  func()
		args    schema
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test1: getMasterAndSlaves failed",
			args: schema{
				stageId: objS.ID,
				shard: &omodel.Shard{
					Name: "redis_public_test-server1",
					Master: &omodel.Instance{
						IP: "************", Port: 7000,
					},
					Slaves: []*omodel.Instance{
						{IP: "************", Port: 7000},
					},
				},
				aliveSentinel: &ent.Sentinel{},
			},
			wantErr: true,
		},
		{
			name: "test2: currentSlaveList not equal 1",
			before: func() {
				redisc.Mock().ExpectDo("sentinel", "slaves", "redis_public_test-server1").SetVal([]any{
					[]any{"name", "************:7000", "ip", "************", "port", "7000", "runid", "233cf9d8cc205201523e16d325ab4e653666cba7", "flags", "slave", "link-pending-commands", "0", "link-refcount", "1", "last-ping-sent", "0", "last-ok-ping-reply", "967", "last-ping-reply", "967", "down-after-milliseconds", "30000", "info-refresh", "7805", "role-reported", "slave", "role-reported-time", "4746715447", "master-link-down-time", "0", "master-link-status", "ok", "master-host", "************", "master-port", "7000", "slave-priority", "100", "slave-repl-offset", "412556657252"},
					[]any{"name", "************:7000", "ip", "************", "port", "7000", "runid", "233cf9d8cc205201523e16d325ab4e653666cb24", "flags", "slave", "link-pending-commands", "0", "link-refcount", "1", "last-ping-sent", "0", "last-ok-ping-reply", "967", "last-ping-reply", "967", "down-after-milliseconds", "30000", "info-refresh", "7805", "role-reported", "slave", "role-reported-time", "4746715447", "master-link-down-time", "0", "master-link-status", "ok", "master-host", "************", "master-port", "7000", "slave-priority", "100", "slave-repl-offset", "412556657252"},
				})
			},
			args: schema{
				stageId: objS.ID,
				shard: &omodel.Shard{
					Name: "redis_public_test-server1",
					Master: &omodel.Instance{
						IP: "************", Port: 7000,
					},
					Slaves: []*omodel.Instance{{
						IP: "************", Port: 7000,
					}},
				},
				aliveSentinel: &ent.Sentinel{IP: "************", Port: 8999},
			},
			wantErr: true,
		},
		{
			name: "test3: master ip not match",
			before: func() {
				redisc.Mock().ExpectDo("sentinel", "slaves", "redis_public_test-server1").SetVal([]any{
					[]any{"name", "************:7004", "ip", "************", "port", "7000", "runid", "233cf9d8cc205201523e16d325ab4e653666cba7", "flags", "slave", "link-pending-commands", "0", "link-refcount", "1", "last-ping-sent", "0", "last-ok-ping-reply", "967", "last-ping-reply", "967", "down-after-milliseconds", "30000", "info-refresh", "7805", "role-reported", "slave", "role-reported-time", "4746715447", "master-link-down-time", "0", "master-link-status", "ok", "master-host", "************", "master-port", "7004", "slave-priority", "100", "slave-repl-offset", "412556657252"},
				})
			},
			args: schema{
				stageId: objS.ID,
				shard: &omodel.Shard{
					Name: "redis_public_test-server1",
					Master: &omodel.Instance{
						IP: "************", Port: 7000,
					},
					Slaves: []*omodel.Instance{{
						IP: "************", Port: 7000,
					}},
				},
				aliveSentinel: &ent.Sentinel{
					IP: "************", Port: 8999,
				},
			},
			wantErr: true,
		},
		{
			name: "test4: slave ip not match",
			before: func() {
				redisc.Mock().ExpectDo("sentinel", "slaves", "redis_public_test-server1").SetVal([]any{
					[]any{"name", "************:7004", "ip", "************", "port", "7004", "runid", "233cf9d8cc205201523e16d325ab4e653666cba7", "flags", "slave", "link-pending-commands", "0", "link-refcount", "1", "last-ping-sent", "0", "last-ok-ping-reply", "967", "last-ping-reply", "967", "down-after-milliseconds", "30000", "info-refresh", "7805", "role-reported", "slave", "role-reported-time", "4746715447", "master-link-down-time", "0", "master-link-status", "ok", "master-host", "************", "master-port", "7000", "slave-priority", "100", "slave-repl-offset", "412556657252"},
				})
			},
			args: schema{
				stageId: objS.ID,
				shard: &omodel.Shard{
					Name:   "redis_public_test-server1",
					Master: &omodel.Instance{IP: "************", Port: 7000},
					Slaves: []*omodel.Instance{{IP: "************", Port: 7000}},
				},
				aliveSentinel: &ent.Sentinel{
					IP: "************", Port: 8999,
				},
			},
			wantErr: true,
		},
		{
			name: "test5: failover failed",
			before: func() {
				redisc.Mock().ExpectDo("sentinel", "slaves", "redis_public_test-server1").SetVal([]any{
					[]any{"name", "************:7000", "ip", "************", "port", "7000", "runid", "233cf9d8cc205201523e16d325ab4e653666cba7", "flags", "slave", "link-pending-commands", "0", "link-refcount", "1", "last-ping-sent", "0", "last-ok-ping-reply", "967", "last-ping-reply", "967", "down-after-milliseconds", "30000", "info-refresh", "7805", "role-reported", "slave", "role-reported-time", "4746715447", "master-link-down-time", "0", "master-link-status", "ok", "master-host", "************", "master-port", "7000", "slave-priority", "100", "slave-repl-offset", "412556657252"},
				})
			},
			args: schema{
				stageId: objS.ID,
				shard: &omodel.Shard{
					Name:   "redis_public_test-server1",
					Master: &omodel.Instance{IP: "************", Port: 7000},
					Slaves: []*omodel.Instance{{IP: "************", Port: 7000}},
				},
				aliveSentinel: &ent.Sentinel{
					IP: "************", Port: 8999,
				},
			},
			wantErr: true,
		},
		{
			name: "test6: already switched and sync check failed",
			before: func() {
				redisc.Mock().ExpectDo("sentinel", "slaves", "redis_public_test-server1").SetVal([]any{
					[]any{"name", "************:7000", "ip", "************", "port", "7000", "runid", "233cf9d8cc205201523e16d325ab4e653666cba7", "flags", "slave", "link-pending-commands", "0", "link-refcount", "1", "last-ping-sent", "0", "last-ok-ping-reply", "967", "last-ping-reply", "967", "down-after-milliseconds", "30000", "info-refresh", "7805", "role-reported", "slave", "role-reported-time", "4746715447", "master-link-down-time", "0", "master-link-status", "ok", "master-host", "************", "master-port", "7000", "slave-priority", "100", "slave-repl-offset", "412556657252"},
				})

				redisc.Mock().ExpectDo("sentinel", "failover", "redis_public_test-server1").SetVal("ok")
			},
			args: schema{
				stageId: objS.ID,
				shard: &omodel.Shard{
					Name:   "redis_public_test-server1",
					Master: &omodel.Instance{IP: "************", Port: 7000},
					Slaves: []*omodel.Instance{{IP: "************", Port: 7000}},
				},
				aliveSentinel: &ent.Sentinel{
					IP: "************", Port: 8999,
				},
			},
			wantErr: true,
		},
		{
			name: "test7: set aof failed",
			before: func() {
				redisc.Mock().ExpectDo("sentinel", "slaves", "redis_public_test-server1").SetVal([]any{
					[]any{"name", "************:7000", "ip", "************", "port", "7000", "runid", "233cf9d8cc205201523e16d325ab4e653666cba7", "flags", "slave", "link-pending-commands", "0", "link-refcount", "1", "last-ping-sent", "0", "last-ok-ping-reply", "967", "last-ping-reply", "967", "down-after-milliseconds", "30000", "info-refresh", "7805", "role-reported", "slave", "role-reported-time", "4746715447", "master-link-down-time", "0", "master-link-status", "ok", "master-host", "************", "master-port", "7000", "slave-priority", "100", "slave-repl-offset", "412556657252"},
				})

				redisc.Mock().ExpectInfo("Replication").SetVal("role:slave\nmaster_host:************\nmaster_port:7000")
				redisc.Mock().ExpectInfo("Replication").SetVal("master_link_status:up\nmaster_sync_in_progress:0\nrole:slave\nmaster_host:************\nmaster_port:7000")

				redisc.Mock().ExpectSet("op_sync_check", time.Now().Format("200601021504"), 5*time.Second).SetVal("ok")
				redisc.Mock().ExpectGet("op_sync_check").SetVal(time.Now().Format("200601021504"))
			},
			args: schema{
				stageId: objS.ID,
				shard: &omodel.Shard{
					Name:   "redis_public_test-server1",
					Master: &omodel.Instance{IP: "************", Port: 7000},
					Slaves: []*omodel.Instance{{IP: "************", Port: 7000}},
				},
				aliveSentinel: &ent.Sentinel{
					IP: "************", Port: 8999,
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		if tt.before != nil {
			tt.before()
		}
		t.Run(tt.name, func(t *testing.T) {
			err := FailoverShard(tt.args.stageId, tt.args.shard, tt.args.aliveSentinel)
			if (err != nil) != tt.wantErr {
				t.Errorf("FailoverShard() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

// 单测：串行切换
func TestSerialFailover(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")
	objC := env.MockCluster("failover_test")
	_, objS := env.MockTaskWithStage(objC)

	// 在等待第一个分片同步时取消任务
	ctx1, cancel1 := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel1()
	ctx2, cancel2 := context.WithTimeout(context.Background(), 10*time.Second)

	type schema struct {
		context       context.Context
		stageId       int64
		shardList     []*omodel.Shard
		aliveSentinel *ent.Sentinel
		intervalTime  int
	}
	tests := []struct {
		name    string
		before  func()
		args    schema
		wantErr bool
		expect  func(*testing.T, error)
	}{
		{
			name: "test1: failover failed",
			args: schema{
				context: ctx1,
				stageId: objS.ID,
				shardList: []*omodel.Shard{
					{Name: "redis_public_test-server1", Master: &omodel.Instance{IP: "************", Port: 7000}, Slaves: []*omodel.Instance{{IP: "************", Port: 7000}}},
					{Name: "redis_public_test-server2", Master: &omodel.Instance{IP: "************", Port: 7001}, Slaves: []*omodel.Instance{{IP: "************", Port: 7001}}},
				},
				aliveSentinel: &ent.Sentinel{
					IP: "************", Port: 8999,
				},
				intervalTime: 5,
			},
			wantErr: true,
		},
		{
			name: "test2: wait failed",
			args: schema{
				context: ctx2,
				stageId: objS.ID,
				shardList: []*omodel.Shard{
					{Name: "test", Master: &omodel.Instance{IP: "************", Port: 7000}, Slaves: []*omodel.Instance{{IP: "************", Port: 7000}}},
					{Name: "test", Master: &omodel.Instance{IP: "************", Port: 7001}, Slaves: []*omodel.Instance{{IP: "************", Port: 7001}}},
				},
				aliveSentinel: &ent.Sentinel{
					IP: "************", Port: 8999,
				},
				intervalTime: 5,
			},
			wantErr: false,
			expect: func(t *testing.T, err error) {
				cancel2()
			},
		},
		{
			name: "test3: globalCtx canceled",
			args: schema{
				context: ctx2,
				stageId: objS.ID,
				shardList: []*omodel.Shard{
					{Name: "test", Master: &omodel.Instance{IP: "************", Port: 7000}, Slaves: []*omodel.Instance{{IP: "************", Port: 7000}}},
					{Name: "test", Master: &omodel.Instance{IP: "************", Port: 7001}, Slaves: []*omodel.Instance{{IP: "************", Port: 7001}}},
				},
				aliveSentinel: &ent.Sentinel{
					IP: "************", Port: 8999,
				},
				intervalTime: 5,
			},
			wantErr: true,
			expect: func(t *testing.T, err error) {
				if err.Error() != "context canceled" {
					t.Errorf("SerialFailover() error = %v, wantErr %v", err, "context canceled")
				}
			},
		},
	}
	for _, tt := range tests {
		if tt.before != nil {
			tt.before()
		}
		t.Run(tt.name, func(t *testing.T) {
			err := SerialFailover(tt.args.context, tt.args.stageId, tt.args.shardList, tt.args.aliveSentinel, tt.args.intervalTime)
			if (err != nil) != tt.wantErr {
				t.Errorf("SerialFailover() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, err)
			}
		})
	}
}

func TestFailover(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")
	objC := env.MockCluster("failover_test")
	_, objS := env.MockTaskWithStage(objC)

	type schema struct {
		stageId       int64
		clusterName   string
		shardList     []*omodel.Shard
		maxConcurnecy int
		intervalTime  int
	}
	tests := []struct {
		name    string
		before  func()
		args    schema
		wantErr bool
		expect  func(*testing.T, error)
	}{
		{
			name: "test1: getAliveSentinel failed",
			args: schema{
				stageId:     objS.ID,
				clusterName: objS.Name,
				shardList: []*omodel.Shard{
					{Name: "redis_public_test-server1", Master: &omodel.Instance{IP: "************", Port: 7000}, Slaves: []*omodel.Instance{{IP: "************", Port: 7000}}},
					{Name: "redis_public_test-server2", Master: &omodel.Instance{IP: "************", Port: 7001}, Slaves: []*omodel.Instance{{IP: "************", Port: 7001}}},
				},
				maxConcurnecy: 2,
				intervalTime:  5,
			},
			wantErr: true,
		},
		{
			name: "test2: cancel failover",
			before: func() {
				db, _ := mysql.Database()
				db.Sentinel.Delete().ExecX(context.Background())
				db.Sentinel.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetBns("failover-test-sentinel.siod-redis").SetIP("************").SetPort(9001).SetIdc("hba").Save(context.Background())
				redisc.Mock().ExpectInfo("Sentinel").SetVal("s:s")

				go func() {
					time.Sleep(100 * time.Millisecond)
					anyCancel, loaded := taskHandler.LoadAndDelete(objS.ID)
					if loaded {
						anyCancel.(context.CancelFunc)()
					} else {
						t.Error("test2 cancel func not found")
					}
				}()

				// server1执行，server2\3\4不执行
				redisc.Mock().MatchExpectationsInOrder(false)
				redisc.Mock().ExpectDo("sentinel", "slaves", "redis_public_test-server1").SetVal([]any{
					[]any{"name", "************:7000", "ip", "************", "port", "7000", "runid", "233cf9d8cc205201523e16d325ab4e653666cba7", "flags", "slave", "link-pending-commands", "0", "link-refcount", "1", "last-ping-sent", "0", "last-ok-ping-reply", "967", "last-ping-reply", "967", "down-after-milliseconds", "30000", "info-refresh", "7805", "role-reported", "slave", "role-reported-time", "4746715447", "master-link-down-time", "0", "master-link-status", "ok", "master-host", "************", "master-port", "7000", "slave-priority", "100", "slave-repl-offset", "412556657252"},
				})

				redisc.Mock().ExpectInfo("Replication").SetVal("role:slave\nmaster_host:************\nmaster_port:7000")
				redisc.Mock().ExpectInfo("Replication").SetVal("master_link_status:down\nmaster_sync_in_progress:1\nrole:slave\nmaster_host:************\nmaster_port:7000")
				redisc.Mock().ExpectInfo("Replication").SetVal("master_link_status:up\nmaster_sync_in_progress:0\nrole:slave\nmaster_host:************\nmaster_port:7000")
				redisc.Mock().ExpectSet("op_sync_check", time.Now().Format("200601021504"), 5*time.Second).SetVal("ok")
				redisc.Mock().ExpectGet("op_sync_check").SetVal(time.Now().Format("200601021504"))

				redisc.Mock().ExpectConfigSet("appendonly", "no").SetVal("ok")
				redisc.Mock().ExpectConfigSet("appendonly", "yes").SetVal("ok")
				redisc.Mock().ExpectConfigRewrite().SetVal("ok")
				redisc.Mock().ExpectConfigRewrite().SetVal("ok")
			},
			args: schema{
				stageId:     objS.ID,
				clusterName: objC.Name,
				shardList: []*omodel.Shard{
					{Name: "redis_public_test-server1", Master: &omodel.Instance{IP: "************", Port: 7000}, Slaves: []*omodel.Instance{{IP: "************", Port: 7000}}},
					{Name: "redis_public_test-server2", Master: &omodel.Instance{IP: "************", Port: 7001}, Slaves: []*omodel.Instance{{IP: "************", Port: 7001}}},
					{Name: "redis_public_test-server3", Master: &omodel.Instance{IP: "************", Port: 7002}, Slaves: []*omodel.Instance{{IP: "************", Port: 7002}}},
					{Name: "redis_public_test-server4", Master: &omodel.Instance{IP: "************", Port: 7003}, Slaves: []*omodel.Instance{{IP: "************", Port: 7003}}},
				},
				maxConcurnecy: 1,
			},
			wantErr: true,
			expect: func(t *testing.T, err error) {
				if err.Error() != "context canceled" {
					t.Errorf("want context canceled, but got %s", err)
				}
			},
		},
		{
			name: "test3: redis_public_test-server3 failover failed",
			before: func() {
				db, _ := mysql.Database()
				db.Sentinel.Delete().ExecX(context.Background())
				db.Sentinel.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetBns("failover-test-sentinel.siod-redis").SetIP("************").SetPort(9001).SetIdc("hba").Save(context.Background())
				redisc.Mock().ExpectInfo("Sentinel").SetVal("s:s")

				// 理论上1、2分片一组，3、4分片一组
				// server1失败，3成功，2、4不执行
				redisc.Mock().MatchExpectationsInOrder(false)
				redisc.Mock().ExpectDo("sentinel", "slaves", "redis_public_test-server1").SetVal([]any{
					[]any{"name", "************:7000", "ip", "************", "port", "7000", "runid", "233cf9d8cc205201523e16d325ab4e653666cba7", "flags", "slave", "link-pending-commands", "0", "link-refcount", "1", "last-ping-sent", "0", "last-ok-ping-reply", "967", "last-ping-reply", "967", "down-after-milliseconds", "30000", "info-refresh", "7805", "role-reported", "slave", "role-reported-time", "4746715447", "master-link-down-time", "0", "master-link-status", "ok", "master-host", "************", "master-port", "7000", "slave-priority", "100", "slave-repl-offset", "412556657252"},
				})
				redisc.Mock().ExpectDo("sentinel", "slaves", "redis_public_test-server3").SetVal([]any{
					[]any{"name", "************:7000", "ip", "************", "port", "7002", "runid", "233cf9d8cc205201523e16d325ab4e653666cba7", "flags", "slave", "link-pending-commands", "0", "link-refcount", "1", "last-ping-sent", "0", "last-ok-ping-reply", "967", "last-ping-reply", "967", "down-after-milliseconds", "30000", "info-refresh", "7805", "role-reported", "slave", "role-reported-time", "4746715447", "master-link-down-time", "0", "master-link-status", "ok", "master-host", "************", "master-port", "7000", "slave-priority", "100", "slave-repl-offset", "412556657252"},
				})

				redisc.Mock().ExpectInfo("Replication").SetVal("role:slave\nmaster_host:************\nmaster_port:7000")
				redisc.Mock().ExpectInfo("Replication").SetVal("master_link_status:up\nmaster_sync_in_progress:0\nrole:slave\nmaster_host:************\nmaster_port:7000")
				redisc.Mock().ExpectSet("op_sync_check", time.Now().Format("200601021504"), 5*time.Second).SetVal("ok")
				redisc.Mock().ExpectGet("op_sync_check").SetVal(time.Now().Format("200601021504"))

				redisc.Mock().ExpectConfigSet("appendonly", "no").SetVal("ok")
				redisc.Mock().ExpectConfigSet("appendonly", "yes").SetVal("ok")
				redisc.Mock().ExpectConfigRewrite().SetVal("ok")
				redisc.Mock().ExpectConfigRewrite().SetVal("ok")
			},
			args: schema{
				stageId:     objS.ID,
				clusterName: objC.Name,
				shardList: []*omodel.Shard{
					{Name: "redis_public_test-server1", Master: &omodel.Instance{IP: "************", Port: 7000}, Slaves: []*omodel.Instance{{IP: "************", Port: 7000}}},
					{Name: "redis_public_test-server2", Master: &omodel.Instance{IP: "************", Port: 7001}, Slaves: []*omodel.Instance{{IP: "************", Port: 7001}}},
					{Name: "redis_public_test-server3", Master: &omodel.Instance{IP: "************", Port: 7002}, Slaves: []*omodel.Instance{{IP: "************", Port: 7002}}},
					{Name: "redis_public_test-server4", Master: &omodel.Instance{IP: "************", Port: 7003}, Slaves: []*omodel.Instance{{IP: "************", Port: 7003}}},
				},
				maxConcurnecy: 2,
			},
			wantErr: true,
			expect: func(t *testing.T, err error) {
				if err.Error() != "[redis_public_test-server3] master host is ************:7000, neither ************:7002 nor ************:7002" {
					t.Errorf("wrong error, expect=\"[redis_public_test-server3] master host is ************:7000, neither ************:7002 nor ************:7002\", bug got %v", err)
				}
			},
		},
	}
	for _, tt := range tests {
		if tt.before != nil {
			tt.before()
		}
		t.Run(tt.name, func(t *testing.T) {
			err := Failover(tt.args.stageId, tt.args.clusterName, tt.args.shardList, tt.args.maxConcurnecy, tt.args.intervalTime)
			if (err != nil) != tt.wantErr {
				t.Errorf("Failover() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, err)
			}
		})
	}
}

func TestStopFailover(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")

	var id int64 = 1
	type schema struct {
		stageId int64
	}
	tests := []struct {
		name    string
		before  func()
		args    schema
		wantErr bool
		expect  func(*testing.T, error)
	}{
		{
			name: "test1: task not found",
			args: schema{
				stageId: id,
			},
			wantErr: true,
		},
		{
			name: "test2: success",
			before: func() {
				_, cancel := context.WithCancel(context.Background())
				taskHandler.Store(id, cancel)
			},
			args: schema{
				stageId: id,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		if tt.before != nil {
			tt.before()
		}
		t.Run(tt.name, func(t *testing.T) {
			err := StopFailover(tt.args.stageId)
			if (err != nil) != tt.wantErr {
				t.Errorf("StopFailover() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, err)
			}
		})
	}
}
