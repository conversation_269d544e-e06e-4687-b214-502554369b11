package component

import (
	"context"
	"testing"
	"time"

	"dt-common/mysql"
	"dt-common/omodel"
	"dt-common/redisc"
	"redis-cmanager/env"
)

func TestSerialSlaveOf(t *testing.T) {
	// 初始化配置
	env.Mock(t, "../../../config/config.yaml")

	// mock data
	objC := env.MockCluster("slave_of_test")
	_, objS := env.MockTaskWithStage(objC)
	db, _ := mysql.Database()
	db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetBns("redis-test-redis.siod-redis").SetIP("************").SetPort(7000).SetIdc("hba").SetName("server1").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).Save(context.Background())
	db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetBns("redis-test-redis.siod-redis").SetIP("************").SetPort(7000).SetIdc("hbb").SetName("server1").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).Save(context.Background())

	type args struct {
		ctx       context.Context
		stageId   int64
		shardList []*omodel.Shard
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test1: master is not a master",
			before: func() {
				redisc.Mock().ExpectInfo("Replication").SetVal("role:slave\nmaster_host:************\nmaster_port:7000")
			},
			args: args{
				ctx:     context.Background(),
				stageId: objS.ID,
				shardList: []*omodel.Shard{
					{Master: &omodel.Instance{IP: "************", Port: 7000}, Slaves: []*omodel.Instance{{IP: "************", Port: 7000}}},
				},
			},
			wantErr: true,
			expect:  func(t *testing.T) {},
		},
		{
			name: "test2: slave is another master's slave",
			before: func() {
				redisc.Mock().ExpectInfo("Replication").SetVal("role:master")
				redisc.Mock().ExpectInfo("Replication").SetVal("role:slave\nmaster_host:************\nmaster_port:7000")
			},
			args: args{
				ctx:     context.Background(),
				stageId: objS.ID,
				shardList: []*omodel.Shard{
					{Master: &omodel.Instance{IP: "************", Port: 7000}, Slaves: []*omodel.Instance{{IP: "************", Port: 7000}}},
				},
			},
			wantErr: true,
			expect:  func(t *testing.T) {},
		},
		{
			name: "test3: slave is a master with a slave",
			before: func() {
				redisc.Mock().ExpectInfo("Replication").SetVal("role:master")
				redisc.Mock().ExpectInfo("Replication").SetVal("role:master\nconnected_slaves:1\nslave0:ip=************,port=7002,state=online,offset=82048074628,lag=1")
			},
			args: args{
				ctx:     context.Background(),
				stageId: objS.ID,
				shardList: []*omodel.Shard{
					{Master: &omodel.Instance{IP: "************", Port: 7000}, Slaves: []*omodel.Instance{{IP: "************", Port: 7000}}},
				},
			},
			wantErr: true,
			expect:  func(t *testing.T) {},
		},
		{
			name: "test4: sync failed 1",
			before: func() {
				redisc.Mock().ExpectInfo("Replication").SetVal("role:master")
				redisc.Mock().ExpectInfo("Replication").SetVal("role:master\n")
				redisc.Mock().ExpectSlaveOf("************", "7000").SetVal("OK")
			},
			args: args{
				ctx:     context.Background(),
				stageId: objS.ID,
				shardList: []*omodel.Shard{
					{Master: &omodel.Instance{IP: "************", Port: 7000}, Slaves: []*omodel.Instance{{IP: "************", Port: 7000}}},
				},
			},
			wantErr: true,
			expect:  func(t *testing.T) {},
		},
		{
			name: "test5: sync failed 2",
			before: func() {
				redisc.Mock().ExpectInfo("Replication").SetVal("role:master")
				redisc.Mock().ExpectInfo("Replication").SetVal("role:slave\nmaster_host:************\nmaster_port:7000")
			},
			args: args{
				ctx:     context.Background(),
				stageId: objS.ID,
				shardList: []*omodel.Shard{
					{Master: &omodel.Instance{IP: "************", Port: 7000}, Slaves: []*omodel.Instance{{IP: "************", Port: 7000}}},
				},
			},
			wantErr: true,
			expect:  func(t *testing.T) {},
		},
		{
			name: "test6: success",
			before: func() {
				redisc.Mock().ExpectInfo("Replication").SetVal("role:master")
				redisc.Mock().ExpectInfo("Replication").SetVal("role:slave\nmaster_host:************\nmaster_port:7000")
				redisc.Mock().ExpectInfo("Replication").SetVal("role:slave\nmaster_host:************\nmaster_port:7000")
				redisc.Mock().ExpectInfo("Replication").SetVal("role:slave\nmaster_host:************\nmaster_port:7000\nmaster_link_status:up\nmaster_sync_in_progress:0")

				redisc.Mock().ExpectSet("op_sync_check", time.Now().Format("200601021504"), 5*time.Second).SetVal("ok")
				redisc.Mock().ExpectGet("op_sync_check").SetVal(time.Now().Format("200601021504"))

				redisc.Mock().ExpectConfigSet("appendonly", "no").SetVal("ok")
				redisc.Mock().ExpectConfigSet("appendonly", "yes").SetVal("ok")
				redisc.Mock().ExpectConfigRewrite().SetVal("ok")
				redisc.Mock().ExpectConfigRewrite().SetVal("ok")
			},
			args: args{
				ctx:     context.Background(),
				stageId: objS.ID,
				shardList: []*omodel.Shard{
					{Master: &omodel.Instance{IP: "************", Port: 7000}, Slaves: []*omodel.Instance{{IP: "************", Port: 7000}}},
				},
			},
			wantErr: false,
			expect:  func(t *testing.T) {},
		},
	}
	for _, tt := range tests {
		if tt.before != nil {
			tt.before()
		}
		t.Run(tt.name, func(t *testing.T) {
			err := SerialSlaveOf(tt.args.ctx, tt.args.stageId, tt.args.shardList)
			if (err != nil) != tt.wantErr {
				t.Errorf("SlaveOf() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

// 单测：从库启动
func TestSlaveOf(t *testing.T) {
	// 初始化配置
	env.Mock(t, "../../../config/config.yaml")

	// mock data
	objC := env.MockCluster("slave_of_test")
	_, objS := env.MockTaskWithStage(objC)
	db, _ := mysql.Database()
	db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetBns("redis-test-redis.siod-redis").SetIP("************").SetPort(7000).SetIdc("hba").SetName("server1").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).Save(context.Background())
	db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetBns("redis-test-redis.siod-redis").SetIP("************").SetPort(7000).SetIdc("hbb").SetName("server1").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).Save(context.Background())
	db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetBns("redis-test-redis.siod-redis").SetIP("************").SetPort(7000).SetIdc("hba").SetName("server2").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).Save(context.Background())
	db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetBns("redis-test-redis.siod-redis").SetIP("************").SetPort(7001).SetIdc("hbb").SetName("server2").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).Save(context.Background())

	type args struct {
		stageId   int64
		shardList []*omodel.Shard
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test1: success",
			before: func() {
				redisc.Mock().ExpectInfo("Replication").SetVal("role:slave\nmaster_host:************\nmaster_port:7000")
			},
			args: args{
				stageId: objS.ID,
				shardList: []*omodel.Shard{
					{Master: &omodel.Instance{IP: "************", Port: 7000}, Slaves: []*omodel.Instance{{IP: "************", Port: 7000}}},
					{Master: &omodel.Instance{IP: "************", Port: 7000}, Slaves: []*omodel.Instance{{IP: "************", Port: 7001}}},
				},
			},
			wantErr: true,
			expect:  func(t *testing.T) {},
		},
	}
	for _, tt := range tests {
		if tt.before != nil {
			tt.before()
		}
		t.Run(tt.name, func(t *testing.T) {
			err := SlaveOf(tt.args.stageId, tt.args.shardList)
			if (err != nil) != tt.wantErr {
				t.Errorf("SlaveOf() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

func TestStopSlaveOf(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")

	var id int64 = 1
	type schema struct {
		stageId int64
	}
	tests := []struct {
		name    string
		before  func()
		args    schema
		wantErr bool
		expect  func(*testing.T, error)
	}{
		{
			name: "test1: task not found",
			args: schema{
				stageId: id,
			},
			wantErr: true,
		},
		{
			name: "test2: success",
			before: func() {
				_, cancel := context.WithCancel(context.Background())
				taskHandler.Store(id, cancel)
			},
			args: schema{
				stageId: id,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		if tt.before != nil {
			tt.before()
		}
		t.Run(tt.name, func(t *testing.T) {
			err := StopSlaveOf(tt.args.stageId)
			if (err != nil) != tt.wantErr {
				t.Errorf("StopSlaveOf() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, err)
			}
		})
	}
}
