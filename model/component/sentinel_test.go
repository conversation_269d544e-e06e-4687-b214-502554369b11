package component

import (
	"context"
	"regexp"
	"testing"

	"github.com/jarcoal/httpmock"

	"dt-common/mysql"
	"dt-common/redisc"
	"redis-cmanager/env"
)

// 单测：sentinel启动
func TestStartSentinel(t *testing.T) {
	// 初始化配置
	env.Mock(t, "../../../config/config.yaml")
	objC := env.MockCluster("test_start_sentinel")
	_, objS := env.MockTaskWithStage(objC)

	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	db, _ := mysql.Database()
	// mock proxy data
	db.Sentinel.Create().SetClusterID(objC.ID).SetClusterName("test_start_sentinel").SetDocker(0).SetBns("r3-test-sentinel.siod-redis").SetIP("************").SetPort(9001).SetIdc("hba").Save(context.Background())
	db.Sentinel.Create().SetClusterID(objC.ID).SetClusterName("test_start_sentinel").SetDocker(0).SetBns("r3-test-sentinel.siod-redis").SetIP("************").SetPort(9001).SetIdc("hba").Save(context.Background())
	db.Sentinel.Create().SetClusterID(objC.ID).SetClusterName("test_start_sentinel").SetDocker(0).SetBns("r3-test-sentinel.siod-redis").SetIP("************").SetPort(9001).SetIdc("hbb").Save(context.Background())
	db.Sentinel.Create().SetClusterID(objC.ID).SetClusterName("test_start_sentinel").SetDocker(1).SetBns("r3-test-sentinel.siod-redis").SetIP("************").SetPort(9001).SetIdc("hbb").Save(context.Background())

	type args struct {
		stageId int64
		bns     string
		idc     string
		ipList  []string
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(t *testing.T)
	}{
		{
			name: "test1",
			before: func() {
				httpmock.RegisterResponder("POST", "http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-redis/apps/r3-test-sentinel/instances/batchBlock",
					httpmock.NewStringResponder(200, `{"success": true, "message": "OK", "data": "ok"}`),
				)

				httpmock.RegisterResponder("GET", "http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-redis/apps/r3-test-sentinel/instances?showPage=0",
					httpmock.NewStringResponder(200, `{"success": true, "message": "OK", "data": [
						{"name": "0.redis-cmanager.siod-kafka","hostName": "kafka0002.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hba"},"disable": false,"instanceType": "host","podId": null,"containerList": []},
						{"name": "1.redis-cmanager.siod-kafka","hostName": "kafka0000.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hbb"},"disable": false,"instanceType": "host","podId": null,"containerList": []},
						{"name": "2.redis-cmanager.siod-kafka","hostName": "kafka0051.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hba"},"disable": false,"instanceType": "host","podId": null,"containerList": []},
						{"name": "3.redis-cmanager.siod-kafka","hostName": "kafka0052.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hbb"},"disable": false,"instanceType": "host","podId": null,"containerList": []}
					]}`),
				)

				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/sentinel/start`),
					httpmock.NewStringResponder(200, `{"code": "00000", "data": "mock ok"}`))
			},
			args: args{
				stageId: objS.ID,
				bns:     "r3-test-sentinel.siod-redis",
				ipList:  []string{"************"},
			},
			wantErr: false,
			expect: func(t *testing.T) {
				info := httpmock.GetCallCountInfo()
				if info["POST =~://[\\w\\W]+/sentinel/start"] != 1 {
					t.Errorf("POST =~://[\\w\\W]+/sentinel/start want 1, but got %d", info["POST =~://[\\w\\W]+/sentinel/start"])
				}
				httpmock.ZeroCallCounters()
			},
		},
		{
			name: "test1.1",
			before: func() {
				httpmock.RegisterResponder("GET", "http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-redis/apps/r3-test-sentinel/instances?showPage=0",
					httpmock.NewStringResponder(200, `{"success": true, "message": "OK", "data": [
						{"name": "0.redis-cmanager.siod-kafka","hostName": "kafka0002.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hba"},"disable": false,"instanceType": "host","podId": null,"containerList": []},
						{"name": "1.redis-cmanager.siod-kafka","hostName": "kafka0000.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hbb"},"disable": false,"instanceType": "host","podId": null,"containerList": []},
						{"name": "2.redis-cmanager.siod-kafka","hostName": "kafka0051.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hba"},"disable": false,"instanceType": "host","podId": null,"containerList": []},
						{"name": "3.redis-cmanager.siod-kafka","hostName": "kafka0052.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hbb"},"disable": false,"instanceType": "host","podId": null,"containerList": []}
					]}`),
				)

				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/sentinel/start`),
					httpmock.NewStringResponder(200, `{"code": "00000", "data": "mock ok"}`))
			},
			args: args{
				stageId: objS.ID,
				bns:     "r3-test-sentinel.siod-redis",
				// ipList:  []string{"************"},
				idc: "hbb",
			},
			wantErr: false,
			expect: func(t *testing.T) {
				info := httpmock.GetCallCountInfo()
				if info["POST =~://[\\w\\W]+/sentinel/start"] != 2 {
					t.Errorf("POST =~://[\\w\\W]+/sentinel/start want 2, but got %d", info["POST =~://[\\w\\W]+/sentinel/start"])
				}
				httpmock.ZeroCallCounters()
			},
		},
		{
			name: "test2",
			args: args{
				stageId: objS.ID,
				bns:     "r3-test-router.siod-redis",
				ipList:  []string{"************", "************"},
			},
			wantErr: true,
		},
		{
			name: "test3",
			args: args{
				stageId: objS.ID,
				bns:     "r3-test-sentinel.siod-redis",
				ipList:  []string{},
			},
			wantErr: false,
		},
		{
			name: "test4",
			args: args{
				stageId: objS.ID,
				bns:     "r3-test-sentinel.siod-redis",
				ipList:  []string{"************", "************"},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		if tt.before != nil {
			tt.before()
		}
		t.Run(tt.name, func(t *testing.T) {
			err := StartSentinel(tt.args.stageId, tt.args.bns, tt.args.idc, tt.args.ipList)
			if (err != nil) != tt.wantErr {
				t.Errorf("StartSentinel() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

// 单测：sentinel关停
func TestStopSentinel(t *testing.T) {
	// 初始化配置
	env.Mock(t, "../../../config/config.yaml")
	objC := env.MockCluster("test_stop_sentinel")
	_, objS := env.MockTaskWithStage(objC)

	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	db, _ := mysql.Database()
	// mock proxy data
	db.Sentinel.Create().SetClusterID(objC.ID).SetClusterName("test_start_sentinel").SetDocker(0).SetBns("r3-test-sentinel.siod-redis").SetIP("************").SetPort(9001).SetIdc("hba").Save(context.Background())
	db.Sentinel.Create().SetClusterID(objC.ID).SetClusterName("test_start_sentinel").SetDocker(0).SetBns("r3-test-sentinel.siod-redis").SetIP("************").SetPort(9001).SetIdc("hba").Save(context.Background())
	db.Sentinel.Create().SetClusterID(objC.ID).SetClusterName("test_start_sentinel").SetDocker(0).SetBns("r3-test-sentinel.siod-redis").SetIP("************").SetPort(9001).SetIdc("hbb").Save(context.Background())
	db.Sentinel.Create().SetClusterID(objC.ID).SetClusterName("test_start_sentinel").SetDocker(1).SetBns("r3-test-sentinel.siod-redis").SetIP("************").SetPort(9001).SetIdc("hbb").Save(context.Background())

	type args struct {
		stageId int64
		bns     string
		idc     string
		ipList  []string
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(t *testing.T)
	}{
		{
			name: "test1",
			before: func() {
				httpmock.RegisterResponder("POST", "http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-redis/apps/r3-test-sentinel/instances/batchBlock",
					httpmock.NewStringResponder(200, `{"success": true, "message": "OK", "data": "ok"}`),
				)

				httpmock.RegisterResponder("GET", "http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-redis/apps/r3-test-sentinel/instances?showPage=0",
					httpmock.NewStringResponder(200, `{"success": true, "message": "OK", "data": [
						{"name": "0.redis-cmanager.siod-kafka","hostName": "kafka0002.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hba"},"disable": false,"instanceType": "host","podId": null,"containerList": []},
						{"name": "1.redis-cmanager.siod-kafka","hostName": "kafka0000.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hbb"},"disable": false,"instanceType": "host","podId": null,"containerList": []},
						{"name": "2.redis-cmanager.siod-kafka","hostName": "kafka0051.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hba"},"disable": false,"instanceType": "host","podId": null,"containerList": []},
						{"name": "3.redis-cmanager.siod-kafka","hostName": "kafka0052.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hbb"},"disable": false,"instanceType": "host","podId": null,"containerList": []}
					]}`),
				)

				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/sentinel/stop`),
					httpmock.NewStringResponder(200, `{"code": "00000", "data": "mock ok"}`))
			},
			args: args{
				stageId: objS.ID,
				bns:     "r3-test-sentinel.siod-redis",
				ipList:  []string{"************"},
			},
			wantErr: false,
			expect: func(t *testing.T) {
				info := httpmock.GetCallCountInfo()
				if info["POST =~://[\\w\\W]+/sentinel/stop"] != 1 {
					t.Errorf("POST =~://[\\w\\W]+/sentinel/stop want 1, but got %d", info["POST =~://[\\w\\W]+/sentinel/stop"])
				}
				httpmock.ZeroCallCounters()
			},
		},
		{
			name: "test1.1",
			before: func() {
				httpmock.RegisterResponder("GET", "http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-redis/apps/r3-test-sentinel/instances?showPage=0",
					httpmock.NewStringResponder(200, `{"success": true, "message": "OK", "data": [
						{"name": "0.redis-cmanager.siod-kafka","hostName": "kafka0002.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hba"},"disable": false,"instanceType": "host","podId": null,"containerList": []},
						{"name": "1.redis-cmanager.siod-kafka","hostName": "kafka0000.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hbb"},"disable": false,"instanceType": "host","podId": null,"containerList": []},
						{"name": "2.redis-cmanager.siod-kafka","hostName": "kafka0051.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hba"},"disable": false,"instanceType": "host","podId": null,"containerList": []},
						{"name": "3.redis-cmanager.siod-kafka","hostName": "kafka0052.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hbb"},"disable": false,"instanceType": "host","podId": null,"containerList": []}
					]}`),
				)

				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/sentinel/stop`),
					httpmock.NewStringResponder(200, `{"code": "00000", "data": "mock ok"}`))
			},
			args: args{
				stageId: objS.ID,
				bns:     "r3-test-sentinel.siod-redis",
				// ipList:  []string{"************"},
				idc: "hbb",
			},
			wantErr: false,
			expect: func(t *testing.T) {
				info := httpmock.GetCallCountInfo()
				if info["POST =~://[\\w\\W]+/sentinel/stop"] != 2 {
					t.Errorf("POST =~://[\\w\\W]+/sentinel/stop want 2, but got %d", info["POST =~://[\\w\\W]+/sentinel/stop"])
				}
				httpmock.ZeroCallCounters()
			},
		},
		{
			name: "test2",
			args: args{
				stageId: objS.ID,
				bns:     "r3-test-router.siod-redis",
				ipList:  []string{"************", "************"},
			},
			wantErr: true,
		},
		{
			name: "test3",
			args: args{
				stageId: objS.ID,
				bns:     "r3-test-sentinel.siod-redis",
				ipList:  []string{},
			},
			wantErr: false,
		},
		{
			name: "test4",
			args: args{
				stageId: objS.ID,
				bns:     "r3-test-sentinel.siod-redis",
				ipList:  []string{"************", "************"},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		if tt.before != nil {
			tt.before()
		}
		t.Run(tt.name, func(t *testing.T) {
			err := StopSentinel(tt.args.stageId, tt.args.bns, tt.args.idc, tt.args.ipList)
			if (err != nil) != tt.wantErr {
				t.Errorf("StopSentinel() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

func TestUpdateQuorum(t *testing.T) {
	// 初始化配置
	env.Mock(t, "../../config/config.yaml")

	// objC := env.MockCluster("sentinel_quorum")
	// _, objS := env.MockTaskWithStage(objC)

	// httpmock.Activate()
	// defer httpmock.DeactivateAndReset()

	// // mock proxy data
	// db, _ := mysql.Database()
	// db.Sentinel.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(0).SetBns("r3-test-sentinel.siod-redis").SetIP("************").SetPort(8999).SetIdc("hba").Save(context.Background())

	type args struct {
		stageId int64
		bns     string
		quorum  int
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(t *testing.T)
	}{
		{
			name: "test1",
			before: func() {
				redisc.Mock().ExpectDo("SENTINEL", "MASTERS").SetVal([]any{
					[]any{"name", "redis_public_test-server1", "ip", "************", "port", "7000", "runid", "403fddb743cd80a2ae393a23fe6e0bc37161820d", "flags", "master", "link-pending-commands", "0", "link-refcount", "1", "last-ping-sent", "0", "last-ok-ping-reply", "617", "last-ping-reply", "617", "down-after-milliseconds", "30000", "info-refresh", "132", "role-reported master", "role-reported-time", "3630037744", "config-epoch", "0", "num-slaves", "1", "num-other-sentinels", "2", "quorum", "2", "failover-timeout", "180000", "parallel-syncs", "1"},
					[]any{"name", "redis_public_test-server2", "ip", "************", "port", "7001", "runid", "369fe0655a2be709fe957ca8a4ed6a628a961cf7", "flags", "master", "link-pending-commands", "0", "link-refcount", "1", "last-ping-sent", "0", "last-ok-ping-reply", "617", "last-ping-reply", "617", "down-after-milliseconds", "30000", "info-refresh", "132", "role-reported master", "role-reported-time", "2689331234", "config-epoch", "0", "num-slaves", "1", "num-other-sentinels", "2", "quorum", "2", "failover-timeout", "180000", "parallel-syncs", "1"},
				})

				redisc.Mock().ExpectDo("SENTINEL", "SET", "redis_public_test-server1", "quorum", 2).SetVal("ok")
				redisc.Mock().ExpectDo("SENTINEL", "SET", "redis_public_test-server2", "quorum", 2).SetVal("ok")
			},
			args: args{
				stageId: 1,
				bns:     "r3-test-sentinel.siod-redis",
				quorum:  2,
			},
			wantErr: false,
			expect:  func(t *testing.T) {},
		},
	}
	for _, tt := range tests {
		if tt.before != nil {
			tt.before()
		}
		t.Run(tt.name, func(t *testing.T) {
			err := UpdateQuorum(tt.args.stageId, tt.args.bns, tt.args.quorum)
			if (err != nil) != tt.wantErr {
				t.Errorf("UpdateQuorum() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}
