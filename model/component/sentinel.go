package component

import (
	"fmt"
	"strings"

	"dt-common/ent/sentinel"
	"dt-common/mysql"
	"dt-common/noah"
	"dt-common/omodel"
	"dt-common/redisc"
	"redis-cmanager/library/ragent"
)

// 启动sentinel
func StartSentinel(stageId int64, bns string, idc string, ipList []string) error {
	// 调用noah接口获取sentinel实例列表
	tmp := strings.Split(bns, ".")
	if len(tmp) != 2 {
		return fmt.Errorf("the format of bns %s is incorrect", bns)
	}

	product, app := tmp[1], tmp[0]
	instances, err := noah.GetInstances(product, app)
	if err != nil {
		omodel.StageAppendErrorLog(stageId, "failed to get instances from noah bns, bns=%s, error=(%v)", bns, err)
		return err
	}

	// 获取最终操作的iplist
	names := []string{}
	ipMap := make(map[string]int)
	if idc != "" {
		for _, instance := range instances {
			if instance.Tags["idc"] == idc {
				ipMap[instance.IP] = instance.PortInfo.Main
				names = append(names, instance.Name)
			}
		}
	} else {
		for _, ip := range ipList {
			ipMap[ip] = -1
		}
		for _, instance := range instances {
			if _, exist := ipMap[instance.IP]; exist {
				ipMap[instance.IP] = instance.PortInfo.Main
				names = append(names, instance.Name)
			}
		}
		// 检查参数是否都遍历到了
		for ip, port := range ipMap {
			if port == -1 {
				omodel.StageAppendWarnLog(stageId, "sentinel %s:%d not in bns %s", ip, port, bns)
				return fmt.Errorf("sentinel %s:%d is not in bns %s", ip, port, bns)
			}
		}
	}

	// 遍历sentinel实例并启动
	db, err := mysql.Database()
	if err != nil {
		omodel.StageAppendErrorLog(stageId, "failed to connect mysql, error=(%v)", err)
		return err
	}
	for ip, port := range ipMap {
		// 1 调用agent启动sentinel
		err = ragent.SentinelStart(ip, port)
		if err != nil {
			omodel.StageAppendErrorLog(stageId, "failed to start sentinel %s:%d, error=(%v)", ip, port, err)
			return err
		}
		omodel.StageAppendErrorLog(stageId, "succeed to start sentinel %s:%d, proceed to update metadata", ip, port)

		// 2 更新状态
		ctx, cancel := mysql.ContextWithTimeout()
		_, err = db.Sentinel.Update().
			SetStatus(omodel.INSTANCE_STATUS_NORMAL).
			Where(sentinel.IP(ip), sentinel.Port(port)).
			Save(ctx)
		cancel()
		if err != nil {
			omodel.StageAppendErrorLog(stageId, "failed to update the database, error=(%v)", err)
			return err
		}
		omodel.StageAppendInfoLog(stageId, "succeed to update %s:%d metadata status to normal", ip, port)
	}

	// 启动sentinel后解屏蔽实例
	err = noah.EnableInstances(product, app, names)
	if err != nil {
		omodel.StageAppendWarnLog(stageId, "failed to enable sentinels, hostNames=%v, error=(%v)", strings.Join(names, ","), err)
		return err
	}
	omodel.StageAppendInfoLog(stageId, "succeed to start all sentinels")

	return nil
}

// 关停sentinel
func StopSentinel(stageId int64, bns string, idc string, ipList []string) error {
	// 调用noah接口获取sentinel实例列表
	tmp := strings.Split(bns, ".")
	if len(tmp) != 2 {
		return fmt.Errorf("the format of bns %s is incorrect", bns)
	}

	product, app := tmp[1], tmp[0]
	instances, err := noah.GetInstances(product, app)
	if err != nil {
		omodel.StageAppendErrorLog(stageId, "failed to get instances from noah bns, bns=%s, error=(%v)", bns, err)
		return err
	}

	// 获取最终操作的iplist
	names := []string{}
	ipMap := make(map[string]int)
	if idc != "" {
		for _, instance := range instances {
			if instance.Tags["idc"] == idc {
				ipMap[instance.IP] = instance.PortInfo.Main
				names = append(names, instance.Name)
			}
		}
	} else {
		for _, ip := range ipList {
			ipMap[ip] = -1
		}
		for _, instance := range instances {
			if _, exist := ipMap[instance.IP]; exist {
				ipMap[instance.IP] = instance.PortInfo.Main
				names = append(names, instance.Name)
			}
		}
		// 检查参数是否都遍历到了
		for ip, port := range ipMap {
			if port == -1 {
				omodel.StageAppendWarnLog(stageId, "sentinel %s:%d not in bns %s", ip, port, bns)
				return fmt.Errorf("sentinel %s:%d is not in bns %s", ip, port, bns)
			}
		}
	}

	// 关停sentinel前屏蔽实例
	err = noah.DisableInstances(product, app, names)
	if err != nil {
		omodel.StageAppendWarnLog(stageId, "failed to disable sentinels, hostNames=%v, error=(%v)", strings.Join(names, ","), err)
		return err
	}
	omodel.StageAppendErrorLog(stageId, "succeed to disable sentinels, hostNames=%v, proceed to stop", strings.Join(names, ","))

	db, err := mysql.Database()
	if err != nil {
		omodel.StageAppendErrorLog(stageId, "failed to connect mysql, error=(%v)", err)
		return err
	}
	for ip, port := range ipMap {
		// 1 关停该sentinel
		err = ragent.SentinelStop(ip, port)
		if err != nil {
			omodel.StageAppendErrorLog(stageId, "failed to stop sentinel %s:%d, error=(%v)", ip, port, err)
			return err
		}
		omodel.StageAppendErrorLog(stageId, "succeed to stop sentinel %s:%d, proceed to update metadata", ip, port)

		// 2 更新状态
		ctx, cancel := mysql.ContextWithTimeout()
		_, err = db.Sentinel.Update().
			SetStatus(omodel.INSTANCE_STATUS_STOPPED).
			Where(sentinel.IP(ip), sentinel.Port(port)).
			Save(ctx)
		cancel()
		if err != nil {
			omodel.StageAppendErrorLog(stageId, "failed to update the database, error=(%v)", err)
			return err
		}
		omodel.StageAppendInfoLog(stageId, "succeed to update %s:%d metadata status to stopped", ip, port)
	}
	omodel.StageAppendInfoLog(stageId, "succeed to stop all sentinels")

	return nil
}

// 调整sentinel的quorum数量
func UpdateQuorum(stageId int64, bns string, quorum int) error {
	// 获取集群的sentinel
	db, err := mysql.Database()
	if err != nil {
		omodel.StageAppendErrorLog(stageId, "failed to connect mysql, error=(%v)", err)
		return err
	}

	ctx, cancel := mysql.ContextWithTimeout()
	sentinelList, err := db.Sentinel.Query().Where(
		sentinel.Bns(bns),
		sentinel.Docker(omodel.DEPLOY_ENV_BBC),
	).All(ctx)
	cancel()
	if err != nil {
		return err
	}

	if len(sentinelList) == 0 {
		err = fmt.Errorf("no sentinel found in %s", bns)
		omodel.StageAppendErrorLog(stageId, err.Error())
		return err
	}

	for _, obj := range sentinelList {
		omodel.StageAppendInfoLog(stageId, "start to update sentinel %s:%d", obj.IP, obj.Port)

		// 获取所有的masterName，遍历执行sentinel set quorum 2
		client, err := redisc.Client(obj.IP, obj.Port)
		if err != nil {
			return err
		}
		ctx, cancel := redisc.ReadTimeout()
		masters, err := client.Do(ctx, "SENTINEL", "MASTERS").Slice()
		cancel()
		if err != nil {
			continue
		}

		for _, masterInfo := range masters {
			infoKV := masterInfo.([]any)
			for i, item := range infoKV {
				if item.(string) == "name" {
					masterName := infoKV[i+1]
					ctx, cancel = redisc.WriteTimeout()
					err = client.Do(ctx, "SENTINEL", "SET", masterName, "quorum", quorum).Err()
					cancel()
					if err != nil {
						omodel.StageAppendErrorLog(stageId, "failed to update master %s's quorum to %d, error=(%v)", masterName, quorum, err)
						return err
					}
					omodel.StageAppendInfoLog(stageId, "succeed to update master %s quorum to %d, sentinel=[%s:%d]", masterName, quorum, obj.IP, obj.Port)
					break
				}
			}
		}
	}

	return nil
}
