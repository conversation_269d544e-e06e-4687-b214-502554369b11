package workflow

import (
	"context"
	"errors"
	"testing"

	"dt-common/ent"
	"dt-common/errs"
	"dt-common/omodel"
	"redis-xweb/env"
)

func TestAfterExec(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")
	objC := env.MockCluster("exec_test")
	objT, objS := env.MockTaskWithStage(objC)

	type args struct {
		taskID      int64
		stageID     int64
		curSequence int
		result      error
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test1: nil result",
			args: args{
				taskID:      objT.ID,
				stageID:     objS.ID,
				curSequence: 1,
				result:      nil,
			},
			wantErr: false,
		},
		{
			name: "test2: task not found",
			args: args{
				taskID:      99,
				stageID:     objS.ID,
				curSequence: 1,
				result:      errs.Success,
			},
			wantErr: true,
		},
		{
			name: "test3: exec failed",
			args: args{
				taskID:      objT.ID,
				stageID:     objS.ID,
				curSequence: 1,
				result:      errs.CodeAuthenticationFailed,
			},
			wantErr: false,
		},
		{
			name: "test4: exec success",
			before: func() {
				objS.Update().SetStatus(string(omodel.STAGE_STATUS_NORMAL)).SetType(omodel.STAGE_TYPE_FAILOVER).ExecX(context.Background())
			},
			args: args{
				taskID:      objT.ID,
				stageID:     objS.ID,
				curSequence: 1,
				result:      errs.Success,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			afterExec(tt.args.taskID, tt.args.stageID, tt.args.curSequence, &tt.args.result)
			// if (err != nil) != tt.wantErr {
			// 	t.Errorf("afterExec() error = %v, wantErr %v", err, tt.wantErr)
			// }
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

func TestExec(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")
	objC := env.MockCluster("exec_test")
	_, objS := env.MockTaskWithStage(objC)

	type args struct {
		stageId int64
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test1: nil stage",
			args: args{
				stageId: 99,
			},
			wantErr: true,
		},
		{
			name: "test2: undefined type",
			args: args{
				stageId: objS.ID,
			},
			wantErr: true,
		},
		{
			name: "test3: start failed",
			before: func() {
				objS.Update().SetStatus(string(omodel.STAGE_STATUS_RUNNING)).SetType(omodel.STAGE_TYPE_FAILOVER).ExecX(context.Background())
			},
			args: args{
				stageId: objS.ID,
			},
			wantErr: true,
		},
		{
			name: "test4: success",
			before: func() {
				objS.Update().SetStatus(string(omodel.STAGE_STATUS_NORMAL)).SetType(omodel.STAGE_TYPE_FAILOVER).ExecX(context.Background())
			},
			args: args{
				stageId: objS.ID,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := Exec(tt.args.stageId)
			if (err != nil) != tt.wantErr {
				t.Errorf("Exec() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

func TestRollback(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")
	objC := env.MockCluster("stage_control_test")
	_, objS := env.MockTaskWithStage(objC)
	// db, _ := mysql.Database()

	type args struct {
		stageId int64
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test1: nil stage",
			args: args{
				stageId: 99,
			},
			wantErr: true,
		},
		{
			name: "test2: undefined type",
			args: args{
				stageId: objS.ID,
			},
			wantErr: true,
		},
		{
			name: "test3: start failed",
			before: func() {
				objS.Update().SetStatus(string(omodel.STAGE_STATUS_ROLLBACK_RUNNING)).SetType(omodel.STAGE_TYPE_FAILOVER).ExecX(context.Background())
			},
			args: args{
				stageId: objS.ID,
			},
			wantErr: true,
		},
		{
			name: "test4: success",
			before: func() {
				objS.Update().SetStatus(string(omodel.STAGE_STATUS_DONE)).SetType(omodel.STAGE_TYPE_FAILOVER).ExecX(context.Background())
			},
			args: args{
				stageId: objS.ID,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := Rollback(tt.args.stageId)
			if (err != nil) != tt.wantErr {
				t.Errorf("rollback() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

func TestStop(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")
	objC := env.MockCluster("stage_stop_test")
	_, objS := env.MockTaskWithStage(objC)
	// db, _ := mysql.Database()

	type args struct {
		stageId int64
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test1: nil stage",
			args: args{
				stageId: 99,
			},
			wantErr: true,
		},
		{
			name: "test2: undefined type",
			args: args{
				stageId: objS.ID,
			},
			wantErr: true,
		},
		{
			name: "test3: not running",
			before: func() {
				objS.Update().SetStatus(string(omodel.STAGE_STATUS_NORMAL)).SetType(omodel.STAGE_TYPE_FAILOVER).ExecX(context.Background())
			},
			args: args{
				stageId: objS.ID,
			},
			wantErr: true,
		},
		{
			name: "test4: stop failed",
			before: func() {
				objS.Update().SetStatus(string(omodel.STAGE_STATUS_RUNNING)).SetType(omodel.STAGE_TYPE_FAILOVER).ExecX(context.Background())
				stageTypeMap[omodel.STAGE_TYPE_FAILOVER] = StageTypeFunc{Stop: func(s *ent.Stage) error { return errors.New("test") }}
			},
			args: args{
				stageId: objS.ID,
			},
			wantErr: false,
		},
		{
			name: "test4: success",
			before: func() {
				objS.Update().SetStatus(string(omodel.STAGE_STATUS_RUNNING)).SetType(omodel.STAGE_TYPE_FAILOVER).ExecX(context.Background())
				stageTypeMap[omodel.STAGE_TYPE_FAILOVER] = StageTypeFunc{Stop: func(s *ent.Stage) error { return nil }}
			},
			args: args{
				stageId: objS.ID,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := Stop(tt.args.stageId)
			if (err != nil) != tt.wantErr {
				t.Errorf("stop() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}
