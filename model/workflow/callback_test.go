package workflow

import (
	"context"
	"fmt"
	"testing"

	"dt-common/ent"
	"dt-common/ent/stage"
	"dt-common/mysql"
	"dt-common/omodel"
	"redis-xweb/env"
)

func TestCallbackExec(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")
	objC := env.MockCluster("callback_exec")
	_, objS := env.MockTaskWithStage(objC)
	db, _ := mysql.Database()

	type args struct {
		stageData *ent.Stage
		success   bool
		message   string
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test1: not success",
			before: func() {
				db.Stage.Update().SetStatus("running").Where(stage.ID(objS.ID)).ExecX(context.Background())
			},
			args: args{
				stageData: objS,
				success:   false,
				message:   "test1",
			},
			expect: func(t *testing.T) {
				objS, _ := db.Stage.Query().Where(stage.ID(objS.ID)).First(context.Background())
				if objS.Status != string(omodel.STAGE_STATUS_ERROR) {
					t.Errorf("callbackExec() error = %v, wantErr %v", objS.Status, omodel.STAGE_STATUS_ERROR)
				}
			},
		},
		{
			name: "test2: success but no exec func",
			before: func() {
				db.Stage.Update().SetStatus("running").Where(stage.ID(objS.ID)).ExecX(context.Background())
			},
			args: args{
				stageData: objS,
				success:   true,
				message:   "test1",
			},
			expect: func(t *testing.T) {
				objS, _ := db.Stage.Query().Where(stage.ID(objS.ID)).First(context.Background())
				if objS.Status != string(omodel.STAGE_STATUS_DONE) {
					t.Errorf("callbackExec() error = %v, wantErr %v", objS.Status, omodel.STAGE_STATUS_DONE)
				}
			},
		},
		{
			name: "test3: success and exec func",
			before: func() {
				db.Stage.Update().SetStatus("running").Where(stage.ID(objS.ID)).ExecX(context.Background())
				stageCallbackMap[objS.Type] = CallbackFunc{Exec: func(stageData *ent.Stage) error {
					db.Stage.Update().SetParameter("123").Where(stage.ID(objS.ID)).ExecX(context.Background())
					return nil
				}}
			},
			args: args{
				stageData: objS,
				success:   true,
				message:   "test1",
			},
			expect: func(t *testing.T) {
				objS, _ := db.Stage.Query().Where(stage.ID(objS.ID)).First(context.Background())
				if *objS.Parameter != "123" {
					t.Errorf("callbackExec() error = %v, wantErr %v", *objS.Parameter, "123")
				}
				if objS.Status != string(omodel.STAGE_STATUS_DONE) {
					t.Errorf("callbackExec() error = %v, wantErr %v", objS.Status, omodel.STAGE_STATUS_DONE)
				}
			},
		},
		{
			name: "test4: success and exec func failed",
			before: func() {
				db.Stage.Update().SetStatus("running").Where(stage.ID(objS.ID)).ExecX(context.Background())
				stageCallbackMap[objS.Type] = CallbackFunc{Exec: func(stageData *ent.Stage) error {
					db.Stage.Update().SetParameter("123").Where(stage.ID(objS.ID)).ExecX(context.Background())
					return fmt.Errorf("test")
				}}
			},
			args: args{
				stageData: objS,
				success:   true,
				message:   "test1",
			},
			expect: func(t *testing.T) {
				objS, _ := db.Stage.Query().Where(stage.ID(objS.ID)).First(context.Background())
				if *objS.Parameter != "123" {
					t.Errorf("callbackExec() error = %v, wantErr %v", objS.Parameter, "123")
				}
				if objS.Status != string(omodel.STAGE_STATUS_ERROR) {
					t.Errorf("callbackExec() error = %v, wantErr %v", objS.Status, omodel.STAGE_STATUS_ERROR)
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			CallbackExec(tt.args.stageData, tt.args.success, tt.args.message)
			// if (err != nil) != tt.wantErr {
			// 	t.Errorf("callbackExec() error = %v, wantErr %v", err, tt.wantErr)
			// }
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

func TestCallbackRollback(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")
	objC := env.MockCluster("callback_rollback")
	_, objS := env.MockTaskWithStage(objC)
	db, _ := mysql.Database()

	type args struct {
		stageData *ent.Stage
		success   bool
		message   string
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test1: not success",
			before: func() {
				db.Stage.Update().SetStatus(string(omodel.STAGE_STATUS_ROLLBACK_RUNNING)).Where(stage.ID(objS.ID)).ExecX(context.Background())
			},
			args: args{
				stageData: objS,
				success:   false,
				message:   "test1",
			},
			expect: func(t *testing.T) {
				objS, _ := db.Stage.Query().Where(stage.ID(objS.ID)).First(context.Background())
				if objS.Status != string(omodel.STAGE_STATUS_ROLLBACK_ERROR) {
					t.Errorf("callbackRollback() error = %v, wantErr %v", objS.Status, omodel.STAGE_STATUS_ROLLBACK_ERROR)
				}
			},
		},
		{
			name: "test2: success but no exec func",
			before: func() {
				db.Stage.Update().SetStatus(string(omodel.STAGE_STATUS_ROLLBACK_RUNNING)).Where(stage.ID(objS.ID)).ExecX(context.Background())
			},
			args: args{
				stageData: objS,
				success:   true,
				message:   "test1",
			},
			expect: func(t *testing.T) {
				objS, _ := db.Stage.Query().Where(stage.ID(objS.ID)).First(context.Background())
				if objS.Status != string(omodel.STAGE_STATUS_ROLLBACK_DONE) {
					t.Errorf("callbackRollback() error = %v, wantErr %v", objS.Status, omodel.STAGE_STATUS_ROLLBACK_DONE)
				}
			},
		},
		{
			name: "test3: success and exec func",
			before: func() {
				db.Stage.Update().SetStatus(string(omodel.STAGE_STATUS_ROLLBACK_RUNNING)).Where(stage.ID(objS.ID)).ExecX(context.Background())
				stageCallbackMap[objS.Type] = CallbackFunc{Rollback: func(stageData *ent.Stage) error {
					db.Stage.Update().SetParameter("123").Where(stage.ID(objS.ID)).ExecX(context.Background())
					return nil
				}}
			},
			args: args{
				stageData: objS,
				success:   true,
				message:   "test1",
			},
			expect: func(t *testing.T) {
				objS, _ := db.Stage.Query().Where(stage.ID(objS.ID)).First(context.Background())
				if *objS.Parameter != "123" {
					t.Errorf("callbackRollback() error = %v, wantErr %v", *objS.Parameter, "123")
				}
				if objS.Status != string(omodel.STAGE_STATUS_ROLLBACK_DONE) {
					t.Errorf("callbackRollback() error = %v, wantErr %v", objS.Status, omodel.STAGE_STATUS_ROLLBACK_DONE)
				}
			},
		},
		{
			name: "test4: success and exec func failed",
			before: func() {
				db.Stage.Update().SetStatus(string(omodel.STAGE_STATUS_ROLLBACK_RUNNING)).Where(stage.ID(objS.ID)).ExecX(context.Background())
				stageCallbackMap[objS.Type] = CallbackFunc{Rollback: func(stageData *ent.Stage) error {
					db.Stage.Update().SetParameter("123").Where(stage.ID(objS.ID)).ExecX(context.Background())
					return fmt.Errorf("test")
				}}
			},
			args: args{
				stageData: objS,
				success:   true,
				message:   "test1",
			},
			expect: func(t *testing.T) {
				objS, _ := db.Stage.Query().Where(stage.ID(objS.ID)).First(context.Background())
				if *objS.Parameter != "123" {
					t.Errorf("callbackRollback() error = %v, wantErr %v", *objS.Parameter, "123")
				}
				if objS.Status != string(omodel.STAGE_STATUS_ROLLBACK_ERROR) {
					t.Errorf("callbackRollback() error = %v, wantErr %v", objS.Status, omodel.STAGE_STATUS_ROLLBACK_ERROR)
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			CallbackRollback(tt.args.stageData, tt.args.success, tt.args.message)
			// if (err != nil) != tt.wantErr {
			// 	t.Errorf("callbackRollback() error = %v, wantErr %v", err, tt.wantErr)
			// }
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}
