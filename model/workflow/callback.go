package workflow

import (
	"fmt"

	"dt-common/ent"
	"dt-common/errs"
	"dt-common/logger"
	"dt-common/omodel"
)

// 阶段类型和执行函数定义
type CallbackFunc struct {
	Exec     StageFunc
	Rollback StageFunc
}

// 异步阶段处理
func CallbackExec(stageData *ent.Stage, success bool, message string) {
	var result error

	if !success {
		logger.Info("[STAGE %d] stage exec failed", stageData.ID)
		result = fmt.Errorf("StageID: %d, 执行失败, error=(%v)", stageData.ID, message)
	} else {
		funcs, exist := stageCallbackMap[stageData.Type]
		if exist && funcs.Exec != nil {
			omodel.StageAppendInfoLog(stageData.ID, "[STAGE %d] start to run callback exec func", stageData.ID)
			result = funcs.Exec(stageData)
		}

		// 异步任务完成，将err赋值errs.Success结束阶段
		if result == nil {
			result = errs.Success
		} else if result != errs.Success {
			logger.Error("[STAGE %d] failed to exec stage callback, error=(%v)", stageData.ID, result)
		}
		logger.Info("[STAGE %d] succeed to exec stage callback", stageData.ID)
	}

	err := omodel.StageFinish(stageData.ID, &result)
	if err != nil {
		omodel.StageAppendWarnLog(stageData.ID, "[STAGE %d] failed to update stage status, error=(%v)", stageData.ID, err)
	}
	afterExec(stageData.TaskID, stageData.ID, stageData.Sequence, &result)
}

// 异步回滚回调
// - 回滚不影响工单结果
func CallbackRollback(stageData *ent.Stage, success bool, message string) {
	var result error

	if !success {
		logger.Info("[STAGE %d] stage rollback failed", stageData.ID)
		result = fmt.Errorf("exception occurred before callback, error=(%v)", message)
	} else {
		funcs, exist := stageCallbackMap[stageData.Type]
		if exist && funcs.Rollback != nil {
			omodel.StageAppendInfoLog(stageData.ID, "[STAGE %d] start to run rollback callback func", stageData.ID)
			result = funcs.Rollback(stageData)
		}
		// 异步任务完成，将err赋值errs.Success结束阶段
		if result == nil {
			result = errs.Success
		} else if result != errs.Success {
			logger.Error("[STAGE %d] to run stage rollback callback, error=(%v)", stageData.ID, result)
		}
		logger.Info("[STAGE %d] succeed to run rollback callback", stageData.ID)
	}

	// 无论成功都需要更新阶段状态，所以result不能为nil，Order:1
	err := omodel.StageFinish(stageData.ID, &result)
	if err != nil {
		omodel.StageAppendWarnLog(stageData.ID, "[STAGE %d] failed to update stage status, error=(%v)", stageData.ID, err)
	}
}
