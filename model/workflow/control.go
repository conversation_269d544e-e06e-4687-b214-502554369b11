package workflow

import (
	"fmt"

	"dt-common/ent"
	"dt-common/ent/stage"
	"dt-common/ent/task"
	"dt-common/errs"
	"dt-common/logger"
	"dt-common/mysql"
	"dt-common/omodel"
	"redis-xweb/library/errc"
	"redis-xweb/library/userc"
)

// ============================================
// 					Exec
// ============================================

// 执行阶段
func Exec(stageID int64) error {
	stageData, err := omodel.GetStageDetail(stageID)
	if err != nil {
		logger.Warn("[STAGE %d] failed to get Stage detail, error=(%v)", stageID, err)
		return errs.CodeDatabase.Detail("阶段详情获取失败")
	}

	stageFuncs, exist := stageTypeMap[stageData.Type]
	if !exist || stageFuncs.Exec == nil {
		// 一般不会走到这
		logger.Warn("[STAGE %d] undefined stage type, type is %s", stageID, stageData.Type)
		return errc.CodeUndefinedValue.Detail("未定义的阶段类型")
	}

	stageData, err = omodel.StageStart(stageID)
	if err != nil {
		logger.Warn("[STAGE %d] failed to start stage, error=(%v)", stageID, err)
		return errs.CodeRequestFailed.Detail("阶段启动失败")
	}
	logger.Info("[STAGE %d] stage start", stageID)

	// 后台执行
	go func() {
		var result error
		defer afterExec(stageData.TaskID, stageID, stageData.Sequence, &result)
		defer omodel.StageFinish(stageID, &result)
		defer AsyncRecovery(stageID, &result)

		result = stageFuncs.Exec(stageData)
	}()
	return nil
}

// 阶段执行结束后的处理，阶段执行有三个结果：同步成功errs.Success/异步调用成功nil/失败!=nil
// 异步调用成功不做任何操作
// 失败1、修改工单状态(如果有)
// 成功1、自动执行下一个阶段 2、修改工单状态(如果有)
func afterExec(taskID int64, stageId int64, curSequence int, result *error) {
	if *result == nil {
		return
	}

	// 获取所属任务的所有阶段
	db, err := mysql.Database()
	if err != nil {
		logger.Error("[STAGE %d] failed to get mysql connection, error=(%v)", stageId, err)
		return
	}
	// 查询task & stage
	ctx, cancel := mysql.ContextWithTimeout()
	taskData, err := db.Task.Query().Where(task.ID(taskID)).WithStages(func(q *ent.StageQuery) {
		q.Order(stage.BySequence(), ent.Asc())
	}).Only(ctx)
	cancel()
	if err != nil {
		logger.Error("[STAGE %d] failed to query task's all stages, taskId=%d, error=(%v)", stageId, taskID, err)
		return
	}

	finishFlow := true
	defer userc.SetFlowResultIfNeeded(taskData.FlowInstanceID, taskData.NodeInstanceID, &finishFlow, result)
	// 阶段失败结束工单(如果有)
	if *result != errs.Success {
		return
	}

	// 如果全部阶段都完成了，尝试结束工单
	// 阶段成功尝试自动执行下一个阶段
	for _, s := range taskData.Edges.Stages {
		// 执行完成的不用管
		if s.Status == string(omodel.STAGE_STATUS_DONE) {
			continue
		}

		finishFlow = false
		switch true {
		case s.Sequence <= curSequence: // 当前阶段之前有非done阶段，说明有非预期介入中断了原本流程
		case !s.Automate: // 不允许自动执行，后续的阶段也不用看了
		case s.Status != string(omodel.STAGE_STATUS_NORMAL): // 不是初始状态
		default:
			logger.Info("[STAGE %d] automate exec next stage %d", stageId, s.ID)
			go Exec(s.ID)
		}
		break
	}
}

// ======================================
//  	         阶段回滚
// ======================================

// 回滚阶段
func Rollback(stageID int64) error {
	stageData, err := omodel.GetStageDetail(stageID)
	if err != nil {
		logger.Warn("[STAGE %d] failed to get Stage detail, error=(%v)", stageID, err)
		return errs.CodeRequestFailed.Detail("阶段详情获取失败")
	}

	stageFuncs, exist := stageTypeMap[stageData.Type]
	if !exist || stageFuncs.Rollback == nil {
		// 没有回滚函数，则直接报错返回
		logger.Warn("[STAGE %d] stage %s does not support rollback", stageID, stageData.Type)
		return errc.CodeRollbackUnsupported
	}

	// 尝试更新状态
	stageData, err = omodel.StageRollback(stageID)
	if err != nil {
		logger.Warn("[STAGE %d] failed to start rollback, error=(%v)", stageID, err)
		return errs.CodeRequestFailed.Detail("阶段回滚启动失败")
	}
	logger.Info("[STAGE %d] stage start rollback", stageID)

	// 后台执行回滚函数
	go func() {
		var result error
		defer omodel.StageFinish(stageData.ID, &result)
		defer AsyncRecovery(stageID, &result)

		result = stageFuncs.Rollback(stageData)
	}()
	return nil
}

// ======================================
//  	         阶段停止
// ======================================

// 中止正在执行的阶段
func Stop(stageID int64) error {
	// 获取stage详情
	stageData, err := omodel.GetStageDetail(stageID)
	if err != nil {
		logger.Warn("[STAGE %d] failed to get stage detail, error=(%v)", stageID, err)
		return errs.CodeRequestFailed.Detail("阶段详情获取失败")
	}

	stageFuncs, exist := stageTypeMap[stageData.Type]
	if !exist || stageFuncs.Stop == nil {
		logger.Warn("[STAGE %d] stage %s does not support stop", stageID, stageData.Type)
		return errs.CodeRequestParameter.Detail("阶段不支持取消")
	}

	// 只能取消执行中的阶段
	if stageData.Status != string(omodel.STAGE_STATUS_RUNNING) {
		logger.Warn("[STAGE %d] stage %s can not stop right now", stageID, stageData.Type)
		return errs.CodeRequestParameter.Detail("阶段未在执行中，无法取消")
	}
	logger.Info("[STAGE %d] stage start to stop", stageID)

	go func() {
		var result error
		defer omodel.StageFinish(stageID, &result)
		defer AsyncRecovery(stageID, &result)

		result = stageFuncs.Stop(stageData)
		// 取消失败do nothing
		if result != nil {
			omodel.StageAppendErrorLog(stageID, "[STAGE %d] failed to stop stage, error=(%v)", stageID, err)
			return
		}
		// 取消成功时结束阶段，将stage状态置为error
		result = fmt.Errorf("阶段取消")
	}()
	return nil
}
