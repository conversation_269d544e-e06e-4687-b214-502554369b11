package process

import (
	"encoding/json"
	"fmt"

	"dt-common/ent"
	"dt-common/errs"
	"dt-common/logger"
	"dt-common/omodel"
	"redis-xweb/library/ras"
)

// 进程启停，module与bns必填，物理redis的启停由单独的类型控制，不再此考虑。
type StageSchemaOptInstance struct {
	Module string   `json:"module"`        // 进程类型
	BNS    string   `json:"bns"`           // BNS
	IDC    string   `json:"idc,omitempty"` // 指定idc
	IPs    []string `json:"ips,omitempty"` // 指定ip
}

// 关停服务，由cmanager提供接口
func Stop(stageData *ent.Stage) error {
	// 参数解析
	var params StageSchemaOptInstance
	err := json.Unmarshal([]byte(*stageData.Parameter), &params)
	if err != nil {
		omodel.StageAppendErrorLog(stageData.ID, "failed to parse stage parameter, value=%v, error=(%v)", stageData.Parameter, err)
		return errs.CodeUnmarshalError
	}
	// 空值判断
	if params.Module == "" {
		omodel.StageAppendErrorLog(stageData.ID, "Expect parameter module but got empty value")
		return fmt.Errorf("module参数错误")
	}
	if params.BNS == "" {
		omodel.StageAppendErrorLog(stageData.ID, "Expect parameter bns but got empty value")
		return fmt.Errorf("bns参数错误")
	}

	// 请求参数
	requestParams := ras.InstanceOperateParams{
		StageId:     stageData.ID,
		ClusterName: stageData.ClusterName,
		BNS:         params.BNS,
		IDC:         params.IDC,
		IPList:      params.IPs,
	}
	switch params.Module {
	case "proxy":
		err = ras.StopProxy(&requestParams)
		if err != nil {
			omodel.StageAppendErrorLog(stageData.ID, "failed to stop proxy, error=(%v)", err)
			return err
		}
		logger.Debug("call to stop proxy, cluster=%s, instance=%v", params.IPs, stageData.ClusterName)
	case "sentinel":
		err = ras.StopSentinel(&requestParams)
		if err != nil {
			omodel.StageAppendErrorLog(stageData.ID, "failed to stop sentinel, error=(%v)", err)
			return err
		}
		logger.Debug("call to stop sentinel, cluster=%s, instance=%v", params.IPs, stageData.ClusterName)
	default:
		err = fmt.Errorf("undefined module %s for stop instances", params.Module)
		return err
	}

	return nil
}

// 启动服务，由cmanager提供接口
func Start(stageData *ent.Stage) error {
	// 参数解析
	var params StageSchemaOptInstance
	err := json.Unmarshal([]byte(*stageData.Parameter), &params)
	if err != nil {
		omodel.StageAppendErrorLog(stageData.ID, "failed to parse stage parameter, value=%v, error=(%v)", stageData.Parameter, err)
		return errs.CodeUnmarshalError
	}
	// 空值判断
	if params.Module == "" {
		omodel.StageAppendErrorLog(stageData.ID, "expect parameter module but got empty value")
		return fmt.Errorf("module参数错误")
	}
	if params.BNS == "" {
		omodel.StageAppendErrorLog(stageData.ID, "expect parameter bns but got empty value")
		return fmt.Errorf("bns参数错误")
	}

	// 请求参数
	requestParams := ras.InstanceOperateParams{
		StageId:     stageData.ID,
		ClusterName: stageData.ClusterName,
		BNS:         params.BNS,
		IDC:         params.IDC,
		IPList:      params.IPs,
	}
	switch params.Module {
	case "proxy":
		err = ras.StartProxy(&requestParams)
		if err != nil {
			omodel.StageAppendErrorLog(stageData.ID, "failed to start proxy, error=(%v)", err)
			return err
		}
		omodel.StageAppendInfoLog(stageData.ID, "succeed to start proxy, instance=%v, cluster=%s", params.IPs, stageData.ClusterName)
	case "sentinel":
		err = ras.StartSentinel(&requestParams)
		if err != nil {
			omodel.StageAppendErrorLog(stageData.ID, "failed to start sentinel, error=(%v)", err)
			return err
		}
		omodel.StageAppendInfoLog(stageData.ID, "succeed to start sentinel, instance=%v, cluster=%s", params.IPs, stageData.ClusterName)
	default:
		err = fmt.Errorf("undefined module %s for start instances", params.Module)
		return err
	}

	return nil
}
