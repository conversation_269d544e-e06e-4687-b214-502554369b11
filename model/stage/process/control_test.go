package process

import (
	"encoding/json"
	"regexp"
	"testing"

	"github.com/jarcoal/httpmock"

	"dt-common/ent"
	"redis-xweb/env"
)

func TestStop(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")
	objC := env.MockCluster("stop_instances_test")
	objT, objS := env.MockTaskWithStage(objC)

	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	tests := []struct {
		name    string
		before  func()
		args    *ent.Stage
		wantErr bool
		expect  func(*testing.T, error)
	}{
		{
			name: "test1: check params",
			args: func() *ent.Stage {
				params := ""
				objS.Parameter = &params
				return objS
			}(),
			wantErr: true,
		},
		{
			name: "test2: check module",
			args: func() *ent.Stage {
				params := StageSchemaOptInstance{
					Module: "",
					BNS:    "",
				}
				b, _ := json.Marshal(params)
				s := string(b)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: true,
		},
		{
			name: "test3: check bns",
			args: func() *ent.Stage {
				params := StageSchemaOptInstance{
					Module: "proxy",
					BNS:    "",
				}
				b, _ := json.Marshal(params)
				s := string(b)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: true,
		},
		{
			name: "test4: request failed",
			args: func() *ent.Stage {
				obj := StageSchemaOptInstance{
					Module: "proxy",
					BNS:    "unit.test",
					IPs:    []string{"************"},
				}
				params, _ := json.Marshal(obj)
				s := string(params)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: true,
		},
		{
			name: "test5: success proxy",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/process/proxy/stop`),
					httpmock.ResponderFromResponse(httpmock.NewStringResponse(200, `{"code": "00000", "data": "ok"}`)))
			},
			args: func() *ent.Stage {
				obj := StageSchemaOptInstance{
					Module: "proxy",
					BNS:    "unit.test",
					IPs:    []string{"************"},
				}
				params, _ := json.Marshal(obj)
				s := string(params)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: false,
		},
		{
			name: "test5: success sentinel",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/process/sentinel/stop`),
					httpmock.ResponderFromResponse(httpmock.NewStringResponse(200, `{"code": "00000", "data": "ok"}`)))
			},
			args: func() *ent.Stage {
				obj := StageSchemaOptInstance{
					Module: "sentinel",
					BNS:    "unit.test",
					IPs:    []string{"************"},
				}
				params, _ := json.Marshal(obj)
				s := string(params)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := Stop(tt.args)
			if (err != nil) != tt.wantErr {
				t.Errorf("Stop() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, err)
			}
		})
	}
}

func TestStart(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")
	objC := env.MockCluster("proxy_start_test")
	objT, objS := env.MockTaskWithStage(objC)

	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	tests := []struct {
		name    string
		before  func()
		args    *ent.Stage
		wantErr bool
		expect  func(*testing.T, error)
	}{
		{
			name: "test1: check params",
			args: func() *ent.Stage {
				params := ""
				objS.Parameter = &params
				return objS
			}(),
			wantErr: true,
		},
		{
			name: "test2: check module",
			args: func() *ent.Stage {
				params := StageSchemaOptInstance{
					Module: "",
					BNS:    "",
				}
				b, _ := json.Marshal(params)
				s := string(b)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: true,
		},
		{
			name: "test3: check bns",
			args: func() *ent.Stage {
				params := StageSchemaOptInstance{
					Module: "proxy",
					BNS:    "",
				}
				b, _ := json.Marshal(params)
				s := string(b)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: true,
		},
		{
			name: "test4: request failed",
			args: func() *ent.Stage {
				obj := StageSchemaOptInstance{
					Module: "proxy",
					BNS:    "unit.test",
					IPs:    []string{"************"},
				}
				params, _ := json.Marshal(obj)
				s := string(params)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: true,
		},
		{
			name: "test5: success proxy",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/process/proxy/start`),
					httpmock.ResponderFromResponse(httpmock.NewStringResponse(200, `{"code": "00000", "data": "ok"}`)))
			},
			args: func() *ent.Stage {
				obj := StageSchemaOptInstance{
					Module: "proxy",
					BNS:    "unit.test",
					IPs:    []string{"************"},
				}
				params, _ := json.Marshal(obj)
				s := string(params)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: false,
		},
		{
			name: "test6: success sentinel",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/process/sentinel/start`),
					httpmock.ResponderFromResponse(httpmock.NewStringResponse(200, `{"code": "00000", "data": "ok"}`)))
			},
			args: func() *ent.Stage {
				obj := StageSchemaOptInstance{
					Module: "sentinel",
					BNS:    "unit.test",
					IPs:    []string{"************"},
				}
				params, _ := json.Marshal(obj)
				s := string(params)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := Start(tt.args)
			if (err != nil) != tt.wantErr {
				t.Errorf("Start() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, err)
			}
		})
	}
}
