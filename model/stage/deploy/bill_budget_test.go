package deploy

import (
	"encoding/json"
	"regexp"
	"testing"

	"github.com/jarcoal/httpmock"

	"dt-common/ent"
	"redis-xweb/env"
)

// 单测：Flow自动执行 - 创建物理订单
func Test_createBill(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")
	objC := env.MockCluster("deploy_test")
	objT, objS := env.MockTaskWithStage(objC)
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	tests := []struct {
		name    string
		before  func()
		args    *ent.Stage
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name:   "test1: unmarshal error",
			before: func() {},
			args: func() *ent.Stage {
				paramStr := "-1"
				objS.Parameter = &paramStr
				return objS
			}(),
			wantErr: true,
		},
		{
			name: "test2: empty params",
			args: func() *ent.Stage {
				obj := StageSchemaBill{}
				params, _ := json.Marshal(obj)
				str := string(params)
				s := env.MockStage(objT)
				s.Parameter = &str
				return s
			}(),
			wantErr: true,
		},
		{
			name: "test3: success",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/api/billing/business/list`),
					httpmock.ResponderFromResponse(httpmock.NewStringResponse(200, "{\"errno\":0,\"errmsg\":\"success\",\"data\":[\"信贷\",\"金科\",\"理财\",\"支付\",\"保险\",\"供应链金融\",\"RPA\",\"GAI\"]}")))
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/api/billing/order/create`),
					httpmock.ResponderFromResponse(httpmock.NewStringResponse(200, "{\"errno\":0,\"errmsg\":\"success\",\"data\":{}}")))
			},
			args: func() *ent.Stage {
				obj := StageSchemaBill{
					Business: map[string]float64{
						"金科": 100,
					},
				}
				params, _ := json.Marshal(obj)
				str := string(params)
				objS.Parameter = &str
				return objS
			}(),
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := CreateBill(tt.args)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateBill() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}
