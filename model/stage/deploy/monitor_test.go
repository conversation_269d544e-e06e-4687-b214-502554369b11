package deploy

import (
	"context"
	"encoding/json"
	"regexp"
	"testing"

	"github.com/jarcoal/httpmock"

	"dt-common/ent"
	"dt-common/errs"
	"dt-common/mysql"
	"dt-common/omodel"
	"redis-xweb/env"
	"redis-xweb/library/monitor"
)

func TestGetApp(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")

	type args struct {
		bns  string
		port int
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, *monitor.App)
	}{
		{
			name: "test1: success",
			args: args{
				bns:  "r3-test-router.siod-redis",
				port: 8001,
			},
			wantErr: false,
			expect: func(t *testing.T, app *monitor.App) {
				if app.ProductName != "siod-redis" {
					t.Errorf("app.ProductName = %v, want siod-redis", app.ProductName)
				}
				if app.AppName != "r3-test-router" {
					t.Errorf("app.AppName = %v, want r3-test-router", app.AppName)
				}
				if app.Port != 8001 {
					t.Errorf("app.Port = %v, want 8001", app.Port)
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			app := getApp(tt.args.bns, tt.args.port)
			// if (err != nil) != tt.wantErr {
			// 	t.Errorf("SyncInitMonitors() error = %v, wantErr %v", err, tt.wantErr)
			// }
			if tt.expect != nil {
				tt.expect(t, app)
			}
		})
	}
}

func TestGetClusterBnsApps(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")
	objC := env.MockCluster("monitor_test")
	db, _ := mysql.Database()

	type args struct {
		clusterName string
		component   []string
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, *[]monitor.M)
	}{
		{
			name: "test1: GetClusterModules proxy failed",
			args: args{
				clusterName: objC.Name,
				component:   []string{omodel.PROCESS_TYPE_REDIS, omodel.PROCESS_TYPE_PROXY, omodel.PROCESS_TYPE_SENTINEL},
			},
			wantErr: true,
		},
		{
			name: "test2: GetClusterModules redis failed",
			before: func() {
				db.Proxy.Delete().Exec(context.Background())
				db.Proxy.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("monitor-test-router.siod-redis").SetIP("************").SetPort(8001).SetIdc("hba").Save(context.Background())
				db.Proxy.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("monitor-test-router.siod-redis").SetIP("************").SetPort(8001).SetIdc("hbb").Save(context.Background())
			},
			args: args{
				clusterName: objC.Name,
				component:   []string{omodel.PROCESS_TYPE_REDIS, omodel.PROCESS_TYPE_PROXY, omodel.PROCESS_TYPE_SENTINEL},
			},
			wantErr: true,
		},
		{
			name: "test3: GetClusterModules sentinel failed",
			before: func() {
				db.Redis.Delete().Exec(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("monitor-test-redis.siod-redis").SetIP("************").SetPort(7000).SetIdc("hba").SetName("server1").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).Save(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("monitor-test-redis.siod-redis").SetIP("************").SetPort(7000).SetIdc("hbb").SetName("server1").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).Save(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("monitor-test-redis.siod-redis").SetIP("************").SetPort(7000).SetIdc("hba").SetName("server2").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).Save(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("monitor-test-redis.siod-redis").SetIP("************").SetPort(7000).SetIdc("hbb").SetName("server2").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).Save(context.Background())
			},
			args: args{
				clusterName: objC.Name,
				component:   []string{omodel.PROCESS_TYPE_REDIS, omodel.PROCESS_TYPE_PROXY, omodel.PROCESS_TYPE_SENTINEL},
			},
			wantErr: true,
		},
		{
			name: "test4: success",
			before: func() {
				db.Sentinel.Delete().Exec(context.Background())
				db.Sentinel.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("monitor-test-sentinel.siod-redis").SetIP("************").SetPort(9001).SetIdc("hba").Save(context.Background())
				db.Sentinel.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("monitor-test-sentinel.siod-redis").SetIP("************").SetPort(9001).SetIdc("hbb").Save(context.Background())
				db.Sentinel.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("monitor-test-sentinel.siod-redis").SetIP("************").SetPort(9001).SetIdc("hbb").Save(context.Background())
			},
			args: args{
				clusterName: objC.Name,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			apps, err := GetClusterBnsApps(tt.args.clusterName, tt.args.component)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetClusterBnsApps() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, apps)
			}
		})
	}
}

// 单测：添加监控任务
func TestSyncInitMonitors(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")
	objC := env.MockCluster("init_monitor_test")
	_, objS := env.MockTaskWithStage(objC)
	db, _ := mysql.Database()

	tests := []struct {
		name    string
		before  func()
		args    *ent.Stage
		wantErr bool
		expect  func(*testing.T, error)
	}{
		{
			name:    "test1: getClusterBnsApps failed",
			args:    objS,
			wantErr: true,
		},
		{
			name: "test2: add monitor failed",
			before: func() {
				db.Proxy.Delete().Exec(context.Background())
				db.Proxy.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("init-monitor-test-router.siod-redis").SetIP("************").SetPort(8001).SetIdc("hba").Save(context.Background())
				db.Proxy.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("init-monitor-test-router.siod-redis").SetIP("************").SetPort(8001).SetIdc("hbb").Save(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("init-monitor-test-redis.siod-redis").SetIP("************").SetPort(7000).SetIdc("hba").SetName("server1").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).Save(context.Background())
				db.Redis.Delete().Exec(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("init-monitor-test-redis.siod-redis").SetIP("************").SetPort(7000).SetIdc("hbb").SetName("server1").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).Save(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("init-monitor-test-redis.siod-redis").SetIP("************").SetPort(7000).SetIdc("hba").SetName("server2").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).Save(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("init-monitor-test-redis.siod-redis").SetIP("************").SetPort(7000).SetIdc("hbb").SetName("server2").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).Save(context.Background())
				db.Sentinel.Delete().Exec(context.Background())
				db.Sentinel.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("init-monitor-test-sentinel.siod-redis").SetIP("************").SetPort(9001).SetIdc("hba").Save(context.Background())
				db.Sentinel.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("init-monitor-test-sentinel.siod-redis").SetIP("************").SetPort(9001).SetIdc("hbb").Save(context.Background())
				db.Sentinel.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("init-monitor-test-sentinel.siod-redis").SetIP("************").SetPort(9001).SetIdc("hbb").Save(context.Background())
			},
			args:    objS,
			wantErr: true,
		},
		{
			name: "test3: add alarms failed",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+Tasks`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": "{}"}`))
			},
			args:    objS,
			wantErr: true,
		},
		{
			name: "test4: success",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+/policies`),
					httpmock.NewStringResponder(200, "{\"success\":true,\"message\":\"\",\"data\":{\"id\":1}}"))
			},
			args:    objS,
			wantErr: true,
			expect: func(t *testing.T, err error) {
				if err != errs.Success {
					t.Errorf("SyncInitMonitors() error = %v, wantErr %v", err, true)
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := SyncInitMonitors(tt.args)
			if (err != nil) != tt.wantErr {
				t.Errorf("SyncInitMonitors() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, err)
			}
		})
	}
	httpmock.Reset()
}

// 单测：删除监控任务
func TestInitMonitorRollback(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")
	objC := env.MockCluster("rollback_monitor_test")
	_, objS := env.MockTaskWithStage(objC)
	db, _ := mysql.Database()

	tests := []struct {
		name    string
		before  func()
		args    *ent.Stage
		wantErr bool
		expect  func(*testing.T, error)
	}{
		{
			name:    "test1: getClusterBnsApps failed",
			args:    objS,
			wantErr: true,
		},
		{
			name: "test2: del monitor failed",
			before: func() {
				db.Proxy.Delete().Exec(context.Background())
				db.Proxy.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("rollback-monitor-test-router.siod-redis").SetIP("************").SetPort(8001).SetIdc("hba").Save(context.Background())
				db.Proxy.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("rollback-monitor-test-router.siod-redis").SetIP("************").SetPort(8001).SetIdc("hbb").Save(context.Background())
				db.Redis.Delete().Exec(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("rollback-monitor-test-redis.siod-redis").SetIP("************").SetPort(7000).SetIdc("hba").SetName("server1").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).Save(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("rollback-monitor-test-redis.siod-redis").SetIP("************").SetPort(7000).SetIdc("hbb").SetName("server1").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).Save(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("rollback-monitor-test-redis.siod-redis").SetIP("************").SetPort(7000).SetIdc("hba").SetName("server2").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).Save(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("rollback-monitor-test-redis.siod-redis").SetIP("************").SetPort(7000).SetIdc("hbb").SetName("server2").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).Save(context.Background())
				db.Sentinel.Delete().Exec(context.Background())
				db.Sentinel.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("rollback-monitor-test-sentinel.siod-redis").SetIP("************").SetPort(9001).SetIdc("hba").Save(context.Background())
				db.Sentinel.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("rollback-monitor-test-sentinel.siod-redis").SetIP("************").SetPort(9001).SetIdc("hbb").Save(context.Background())
				db.Sentinel.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("rollback-monitor-test-sentinel.siod-redis").SetIP("************").SetPort(9001).SetIdc("hbb").Save(context.Background())
			},
			args:    objS,
			wantErr: true,
		},
		{
			name: "test3: del alarms failed",
			before: func() {
				httpmock.RegisterRegexpResponder("DELETE", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+Tasks`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": "{}"}`))
			},
			args:    objS,
			wantErr: true,
		},
		{
			name: "test4: success",
			before: func() {
				httpmock.RegisterRegexpResponder("DELETE", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+/policies`),
					httpmock.NewStringResponder(200, "{\"success\":true,\"message\":\"\",\"data\":{\"id\":1}}"))
			},
			args:    objS,
			wantErr: true,
			expect: func(t *testing.T, err error) {
				if err != errs.Success {
					t.Errorf("SyncDeleteMonitors() error = %v, wantErr %v", err, true)
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := SyncDeleteMonitors(tt.args)
			if (err != nil) != tt.wantErr {
				t.Errorf("SyncDeleteMonitors() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, err)
			}
		})
	}
	httpmock.Reset()
}

// 单测：清理sentinel监控采集和报警策略（物理实例）
func TestSyncCleanSentinelMonitors(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")
	objC := env.MockCluster("r3_test")
	objT, objS := env.MockTaskWithStage(objC)

	tests := []struct {
		name    string
		before  func()
		args    *ent.Stage
		wantErr bool
		expect  func(*testing.T, error)
	}{
		{
			name: "test1: check params",
			args: func() *ent.Stage {
				paramStr := "-1"
				objS.Parameter = &paramStr
				return objS
			}(),
			wantErr: true,
		},
		{
			name: "test2: check sentinel and port",
			args: func() *ent.Stage {
				params := StageSchemaMonitor{}
				b, _ := json.Marshal(params)
				s := string(b)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: true,
		},
		{
			name: "test3: clean alarm failed",
			args: func() *ent.Stage {
				params := StageSchemaMonitor{
					Bns:  "r3-test-sentinel.siod-redis",
					Port: 9001,
				}
				b, _ := json.Marshal(params)
				s := string(b)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: true,
		},
		{
			name: "test4: clean monitor failed",
			before: func() {
				httpmock.RegisterRegexpResponder("DELETE", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+/policies`),
					httpmock.NewStringResponder(200, "{\"success\":true,\"message\":\"\",\"data\":{\"id\":1}}"))
			},
			args: func() *ent.Stage {
				params := StageSchemaMonitor{
					Bns:  "r3-test-sentinel.siod-redis",
					Port: 9001,
				}
				b, _ := json.Marshal(params)
				s := string(b)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: true,
		},
		{
			name: "test4: success",
			before: func() {
				httpmock.RegisterRegexpResponder("DELETE", regexp.MustCompile(`://[\w\W]+/inspectProxy/api/v1/products/[\w\W]+/apps/[\w\W]+Tasks`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": "{}"}`))
			},
			args: func() *ent.Stage {
				params := StageSchemaMonitor{
					Bns:  "r3-test-sentinel.siod-redis",
					Port: 9001,
				}
				b, _ := json.Marshal(params)
				s := string(b)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: true,
			expect: func(t *testing.T, err error) {
				if err != errs.Success {
					t.Errorf("SyncDeleteMonitors() error = %v, wantErr %v", err, true)
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := SyncCleanSentinelMonitors(tt.args)
			if (err != nil) != tt.wantErr {
				t.Errorf("SyncCleanSentinelMonitors() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, err)
			}
		})
	}
}
