package deploy

import (
	"encoding/json"

	"dt-common/ent"
	"dt-common/ent/cluster"
	"dt-common/errs"
	"dt-common/mysql"
	"dt-common/omodel"
	"redis-xweb/library/billing"
)

// ======================================
// 			       预算
// ======================================

// ======================================
// 			       账单
// ======================================

type StageSchemaBill struct {
	Business map[string]float64 `json:"business"`          // 业务属性
	Product  map[string]float64 `json:"product,omitempty"` // 产品属性
}

// 成本中心创建订单（容器集群）
func CreateBill(stageData *ent.Stage) error {
	// 参数解析
	var params StageSchemaBill
	err := json.Unmarshal([]byte(*stageData.Parameter), &params)
	if err != nil {
		omodel.StageAppendErrorLog(stageData.ID, "failed to parse stage parameter, value=%v, error=(%v)", stageData.Parameter, err)
		return err
	}

	// 获取集群信息
	db, err := mysql.Database()
	if err != nil {
		omodel.StageAppendErrorLog(stageData.ID, "failed to get mysql connection, error=(%v)", err)
		return err
	}
	ctx, cancel := mysql.ContextWithTimeout()
	clusterData, err := db.Cluster.Query().Where(cluster.Name(stageData.ClusterName)).Only(ctx)
	if err != nil {
		return err
	}
	cancel()

	// 创建物理集群内存订单
	err = billing.CreateClusterOrder(&billing.CreateOrderParams{
		ClusterName: clusterData.Name,
		ProxyNum:    clusterData.ProxyNum,
		ShardNum:    float64(clusterData.ShardNum),
		StorageSize: float64(clusterData.StorageSize),
		SentinelNum: 3,
		DepartCode:  clusterData.DepartmentID,
		Department:  clusterData.Department,
		Creator:     clusterData.Owner,
		Business:    params.Business,
	})
	if err != nil {
		omodel.StageAppendWarnLog(stageData.ID, "failed to create order, cluster=%s, error=(%v)", clusterData.Name, err)
		return err
	}

	// 因为是部署的最后一步，所以更新集群的状态从deploying -> normal
	if clusterData.Status == omodel.CLUSTER_STATUS_DEPLOYING {
		ctx, cancel = mysql.ContextWithTimeout()
		err = clusterData.Update().SetStatus(omodel.CLUSTER_STATUS_NORMAL).Exec(ctx)
		if err != nil {
			return err
		}
		cancel()
	}

	// 同步任务返回 errs.Success
	return errs.Success
}
