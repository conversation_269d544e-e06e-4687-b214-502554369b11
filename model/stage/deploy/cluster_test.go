package deploy

import (
	"encoding/json"
	"regexp"
	"testing"

	"github.com/jarcoal/httpmock"

	"dt-common/ent"
	"dt-common/omodel"
	"redis-xweb/env"
)

// 单测：实例解挂载
func TestCreateCluster(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")
	objC := env.MockCluster("deploy_test")
	objT, objS := env.MockTaskWithStage(objC)

	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	tests := []struct {
		name    string
		before  func()
		args    *ent.Stage
		wantErr bool
		expect  func(*testing.T, error)
	}{
		{
			name:   "test1: unmarshal error",
			before: func() {},
			args: func() *ent.Stage {
				paramStr := "-1"
				objS.Parameter = &paramStr
				return objS
			}(),
			wantErr: true,
		},
		{
			name: "test2: empty params",
			args: func() *ent.Stage {
				obj := omodel.DeployOptions{}
				params, _ := json.Marshal(obj)
				str := string(params)
				s := env.MockStage(objT)
				s.Parameter = &str
				return s
			}(),
			wantErr: true,
		},
		{
			name: "test3: success",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/cluster/deploy`),
					httpmock.ResponderFromResponse(httpmock.NewStringResponse(200, `{"code": "00000", "data": "ok"}`)))
			},
			args: func() *ent.Stage {
				obj := omodel.DeployOptions{
					Name:      objC.Name,
					ProxyPort: 8001,
					ProxyReplicas: map[string]int{
						"hba": 2,
						"hbb": 2,
					},
					ShardNum:  2,
					ShardMem:  4,
					ShardPort: 7001,
					ShardReplicas: map[string]int{
						"hba": 2,
						"hbb": 2,
					},
					SentinelPort: 9001,
					SentinelReplicas: map[string]int{
						"hba": 1,
						"hbb": 1,
						"hbc": 1,
					},
					EnabledAZ: []string{"hbb"},
				}
				params, _ := json.Marshal(obj)
				str := string(params)
				objS.Parameter = &str
				return objS
			}(),
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := CreateCluster(tt.args)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateCluster() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, err)
			}
		})
	}
}
