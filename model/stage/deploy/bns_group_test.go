package deploy

import (
	"encoding/json"
	"regexp"
	"slices"
	"testing"

	"github.com/jarcoal/httpmock"

	"dt-common/ent"
	"dt-common/errs"
	"redis-xweb/env"
)

// 获取proxy的bns和实例
func TestCreateGroup(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")
	objC := env.MockCluster("smart_test")
	objT, objS := env.MockTaskWithStage(objC)

	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	tests := []struct {
		name    string
		before  func()
		args    *ent.Stage
		wantErr bool
		expect  func(*testing.T, error)
	}{
		{
			name:   "test1: check args",
			before: func() {},
			args: func() *ent.Stage {
				paramStr := "-1"
				objS.Parameter = &paramStr
				return objS
			}(),
			wantErr: true,
		},
		{
			name:   "test2: empty params",
			before: func() {},
			args: func() *ent.Stage {
				params := StageSchemaGroup{
					Group:         "",
					TargetAppList: []string{},
				}
				b, _ := json.Marshal(params)
				s := string(b)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: true,
		},
		{
			name:   "test3: empty params 2",
			before: func() {},
			args: func() *ent.Stage {
				params := StageSchemaGroup{
					Group: "group.blind-router-router.siod-redis",
				}
				b, _ := json.Marshal(params)
				s := string(b)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: true,
		},
		{
			name:   "test4: empty params 3",
			before: func() {},
			args: func() *ent.Stage {
				params := StageSchemaGroup{
					Group: "group.blind-router-router.siod-redis",
					Owner: "jiayiming_dxm",
				}
				b, _ := json.Marshal(params)
				s := string(b)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: true,
		},
		{
			name: "test6: call failed",
			args: func() *ent.Stage {
				params := StageSchemaGroup{
					Group:         "group.blind-router-router.siod-redis",
					Owner:         "jiayiming_dxm",
					TargetAppList: []string{"r3-test-router.siod-redis", "r3-test-1-router.siod-redis"},
				}
				b, _ := json.Marshal(params)
				s := string(b)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: true,
		},
		{
			name: "test7: success",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/bns-group/api/v1/groups/add`),
					httpmock.ResponderFromResponse(httpmock.NewStringResponse(200, "{\"errno\":0,\"errmsg\":\"success\",\"data\":{}}")))
			},
			args: func() *ent.Stage {
				params := StageSchemaGroup{
					Group:         "group.blind-router-router.siod-redis",
					Owner:         "jiayiming_dxm",
					TargetAppList: []string{"r3-test-router.siod-redis", "r3-test-1-router.siod-redis"},
				}
				b, _ := json.Marshal(params)
				s := string(b)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: true,
			expect: func(t *testing.T, err error) {
				if err != errs.Success {
					t.Errorf("expect success but got %v", err)
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := CreateGroup(tt.args)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateGroup() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, err)
			}
		})
	}
}

func Test_diffWhitelist(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")

	type schema struct {
		arr1 []string
		arr2 []string
	}
	tests := []struct {
		name    string
		before  func()
		args    schema
		wantErr bool
		expect  func(*testing.T, []string)
	}{
		{
			name: "test1: add new item",
			args: schema{
				arr1: []string{"127.0.0.1", "192.168.3.11"},
				arr2: []string{"127.0.0.1", "192.168.3.11", "192.168.3.12"},
			},
			expect: func(t *testing.T, diff []string) {
				if !slices.Contains(diff, "192.168.3.12") {
					t.Errorf("test1: expected 192.168.3.12, but not get")
				}
			},
		},
		{
			name: "test2: del item",
			args: schema{
				arr1: []string{"127.0.0.1", "192.168.3.11", "127.0.0.3"},
				arr2: []string{"127.0.0.1"},
			},
			expect: func(t *testing.T, diff []string) {
				if !slices.Contains(diff, "192.168.3.11") {
					t.Errorf("test1: expected 192.168.3.11, but not get")
				}
				if !slices.Contains(diff, "127.0.0.3") {
					t.Errorf("test1: expected 127.0.0.3, but not get")
				}
			},
		},
		{
			name: "test3: add and del item",
			args: schema{
				arr1: []string{"127.0.0.1", "192.168.3.11"},
				arr2: []string{"127.0.0.1", "127.0.0.3"},
			},
			expect: func(t *testing.T, diff []string) {
				if !slices.Contains(diff, "192.168.3.11") {
					t.Errorf("test1: expected 192.168.3.11, but not get")
				}
				if !slices.Contains(diff, "127.0.0.3") {
					t.Errorf("test1: expected 127.0.0.3, but not get")
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			appList := diff(tt.args.arr1, tt.args.arr2)
			// if (err != nil) != tt.wantErr {
			// 	t.Errorf("diffWhitelist() error = %v, wantErr %v", err, tt.wantErr)
			// }
			if tt.expect != nil {
				tt.expect(t, appList)
			}
		})
	}
}

func Test_isInstanesDisabled(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")

	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type schema struct {
		stageId int64
		appList []string
	}
	tests := []struct {
		name    string
		before  func()
		args    schema
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test1: not ok",
			before: func() {
				httpmock.RegisterResponder("GET", "http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-redis/apps/r3-test-sentinel/instances?showPage=0",
					httpmock.NewStringResponder(200, `{"success": true, "message": "OK", "data": [
						{"name": "0.redis-cmanager.siod-kafka","hostName": "kafka0002.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hba"},"disable": false,"instanceType": "host","podId": null,"containerList": []},
						{"name": "1.redis-cmanager.siod-kafka","hostName": "kafka0000.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hbb"},"disable": true,"instanceType": "host","podId": null,"containerList": []},
						{"name": "2.redis-cmanager.siod-kafka","hostName": "kafka0051.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hba"},"disable": false,"instanceType": "host","podId": null,"containerList": []},
						{"name": "3.redis-cmanager.siod-kafka","hostName": "kafka0052.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hbb"},"disable": false,"instanceType": "host","podId": null,"containerList": []}
					]}`),
				)
			},
			args: schema{
				stageId: 1,
				appList: []string{"r3-test-sentinel.siod-redis"},
			},
			wantErr: true,
		},
		{
			name: "test2: is ok",
			before: func() {
				httpmock.RegisterResponder("GET", "http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-redis/apps/r3-test-sentinel/instances?showPage=0",
					httpmock.NewStringResponder(200, `{"success": true, "message": "OK", "data": [
						{"name": "0.redis-cmanager.siod-kafka","hostName": "kafka0002.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hba"},"disable": true,"instanceType": "host","podId": null,"containerList": []},
						{"name": "1.redis-cmanager.siod-kafka","hostName": "kafka0000.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hbb"},"disable": true,"instanceType": "host","podId": null,"containerList": []},
						{"name": "2.redis-cmanager.siod-kafka","hostName": "kafka0051.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hba"},"disable": true,"instanceType": "host","podId": null,"containerList": []},
						{"name": "3.redis-cmanager.siod-kafka","hostName": "kafka0052.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hbb"},"disable": true,"instanceType": "host","podId": null,"containerList": []}
					]}`),
				)
			},
			args: schema{
				stageId: 1,
				appList: []string{"r3-test-sentinel.siod-redis"},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := isInstanesDisabled(tt.args.stageId, tt.args.appList)
			if (err != nil) != tt.wantErr {
				t.Errorf("IsInstanesDisabled() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

// 更新BNS_GROUP的APPLIST
func TestUpdateGroupAppList(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")
	objC := env.MockCluster("smart_test")
	objT, objS := env.MockTaskWithStage(objC)

	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	tests := []struct {
		name    string
		before  func()
		args    *ent.Stage
		wantErr bool
		expect  func(*testing.T, error)
	}{
		{
			name:   "test1: check args",
			before: func() {},
			args: func() *ent.Stage {
				paramStr := "-1"
				objS.Parameter = &paramStr
				return objS
			}(),
			wantErr: true,
		},
		{
			name:   "test2: empty params",
			before: func() {},
			args: func() *ent.Stage {
				params := StageSchemaGroup{
					Group:         "",
					SourceAppList: []string{},
					TargetAppList: []string{},
				}
				b, _ := json.Marshal(params)
				s := string(b)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: true,
		},
		{
			name:   "test3: empty params 2",
			before: func() {},
			args: func() *ent.Stage {
				params := StageSchemaGroup{
					Group:         "group.blind-router-router.siod-redis",
					TargetAppList: []string{},
					SourceAppList: []string{},
				}
				b, _ := json.Marshal(params)
				s := string(b)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: true,
		},
		{
			name:   "test4: empty params 3",
			before: func() {},
			args: func() *ent.Stage {
				params := StageSchemaGroup{
					Group:         "group.blind-router-router.siod-redis",
					TargetAppList: []string{"r3-test-router.siod-redis", "r3-test-1-router.siod-redis"},
					SourceAppList: []string{},
				}
				b, _ := json.Marshal(params)
				s := string(b)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: true,
		},
		{
			name: "test5: not disable",
			before: func() {
				httpmock.RegisterResponder("GET", "http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-redis/apps/r3-test-router/instances?showPage=0",
					httpmock.NewStringResponder(200, `{"success": true, "message": "OK", "data": [
						{"name": "0.redis-cmanager.siod-kafka","hostName": "kafka0002.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hba"},"disable": true,"instanceType": "host","podId": null,"containerList": []},
						{"name": "1.redis-cmanager.siod-kafka","hostName": "kafka0000.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hbb"},"disable": false,"instanceType": "host","podId": null,"containerList": []},
						{"name": "2.redis-cmanager.siod-kafka","hostName": "kafka0051.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hba"},"disable": true,"instanceType": "host","podId": null,"containerList": []},
						{"name": "3.redis-cmanager.siod-kafka","hostName": "kafka0052.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hbb"},"disable": true,"instanceType": "host","podId": null,"containerList": []}
					]}`),
				)
			},
			args: func() *ent.Stage {
				params := StageSchemaGroup{
					Group:         "group.blind-router-router.siod-redis",
					TargetAppList: []string{"r3-test-router.siod-redis", "r3-test-1-router.siod-redis"},
					SourceAppList: []string{"r3-test-1-router.siod-redis"},
				}
				b, _ := json.Marshal(params)
				s := string(b)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: true,
		},
		{
			name: "test6: call failed",
			before: func() {
				httpmock.RegisterResponder("GET", "http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-redis/apps/r3-test-router/instances?showPage=0",
					httpmock.NewStringResponder(200, `{"success": true, "message": "OK", "data": [
						{"name": "0.redis-cmanager.siod-kafka","hostName": "kafka0002.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hba"},"disable": true,"instanceType": "host","podId": null,"containerList": []},
						{"name": "1.redis-cmanager.siod-kafka","hostName": "kafka0000.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hbb"},"disable": true,"instanceType": "host","podId": null,"containerList": []},
						{"name": "2.redis-cmanager.siod-kafka","hostName": "kafka0051.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hba"},"disable": true,"instanceType": "host","podId": null,"containerList": []},
						{"name": "3.redis-cmanager.siod-kafka","hostName": "kafka0052.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hbb"},"disable": true,"instanceType": "host","podId": null,"containerList": []}
					]}`),
				)
			},
			args: func() *ent.Stage {
				params := StageSchemaGroup{
					Group:         "group.blind-router-router.siod-redis",
					TargetAppList: []string{"r3-test-router.siod-redis", "r3-test-1-router.siod-redis"},
					SourceAppList: []string{"r3-test-1-router.siod-redis"},
				}
				b, _ := json.Marshal(params)
				s := string(b)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: true,
		},
		{
			name: "test7: success",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/api/v1/groups/getGroupConfigByName`),
					httpmock.NewStringResponder(200, `{"errcode": 0, "data": {"id": 459,"middleName": "licai-alchemy-router-all","name": "group.licai-alchemy-router-all.siod-redis","productName": "siod-redis","owner": "jiayiming_dxm","appList": "licai-alchemy-router.siod-redis,licai-alchemy-router.tech-mixed-b","desc": "REDIS集群实现智能路由","sidecarSwitch": 0,"config": "{\"enable\":1,\"idc_mapping_rule\":{\"hba\":{\"prefer\":[\"hba\"],\"backup\":[\"hbb\"],\"min_instance_need\":1},\"hbb\":{\"prefer\":[\"hbb\"],\"backup\":[\"hba\"],\"min_instance_need\":1},\"hbf\":{\"prefer\":[\"hbb\"],\"backup\":[\"hba\"],\"min_instance_need\":1},\"hbg\":{\"prefer\":[\"hba\"],\"backup\":[\"hbb\"],\"min_instance_need\":1},\"default\":{\"prefer\":[\"hba\",\"hbb\"]}}}","create_time": "2020-03-05T11:32:00+08:00","DFlag": 0,"LastModifyTime": "2023-08-05T23:14:21+08:00"}}`))
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/bns-group/api/v1/groups/update`),
					httpmock.ResponderFromResponse(httpmock.NewStringResponse(200, "{\"errno\":0,\"errmsg\":\"success\",\"data\":{}}")))
			},
			args: func() *ent.Stage {
				params := StageSchemaGroup{
					Group:         "group.blind-router-router.siod-redis",
					TargetAppList: []string{"r3-test-router.siod-redis", "r3-test-1-router.siod-redis"},
					SourceAppList: []string{"r3-test-1-router.siod-redis"},
				}
				b, _ := json.Marshal(params)
				s := string(b)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: true,
			expect: func(t *testing.T, err error) {
				if err != errs.Success {
					t.Errorf("expect success but got %v", err)
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := UpdateGroupAppList(tt.args)
			if (err != nil) != tt.wantErr {
				t.Errorf("UpdateGroupAppList() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, err)
			}
		})
	}
}

// 回滚APPLIST变更
func TestRollbackGroupAppList(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")
	objC := env.MockCluster("smart_test")
	objT, objS := env.MockTaskWithStage(objC)

	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	tests := []struct {
		name    string
		before  func()
		args    *ent.Stage
		wantErr bool
		expect  func(*testing.T, error)
	}{
		{
			name:   "test1: check args",
			before: func() {},
			args: func() *ent.Stage {
				paramStr := "-1"
				objS.Parameter = &paramStr
				return objS
			}(),
			wantErr: true,
		},
		{
			name:   "test2: empty params",
			before: func() {},
			args: func() *ent.Stage {
				params := StageSchemaGroup{
					Group:         "",
					SourceAppList: []string{},
				}
				b, _ := json.Marshal(params)
				s := string(b)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: true,
		},
		{
			name:   "test3: empty params 2",
			before: func() {},
			args: func() *ent.Stage {
				params := StageSchemaGroup{
					Group:         "group.blind-router-router.siod-redis",
					SourceAppList: []string{},
				}
				b, _ := json.Marshal(params)
				s := string(b)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: true,
		},
		{
			name: "test4: call failed",
			args: func() *ent.Stage {
				params := StageSchemaGroup{
					Group:         "group.blind-router-router.siod-redis",
					SourceAppList: []string{"r3-test-router.siod-redis"},
				}
				b, _ := json.Marshal(params)
				s := string(b)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: true,
		},
		{
			name: "test5: not disable",
			before: func() {
				httpmock.RegisterResponder("GET", "http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-redis/apps/r3-test-router/instances?showPage=0",
					httpmock.NewStringResponder(200, `{"success": true, "message": "OK", "data": [
						{"name": "0.redis-cmanager.siod-kafka","hostName": "kafka0002.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hba"},"disable": true,"instanceType": "host","podId": null,"containerList": []},
						{"name": "1.redis-cmanager.siod-kafka","hostName": "kafka0000.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hbb"},"disable": false,"instanceType": "host","podId": null,"containerList": []},
						{"name": "2.redis-cmanager.siod-kafka","hostName": "kafka0051.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hba"},"disable": true,"instanceType": "host","podId": null,"containerList": []},
						{"name": "3.redis-cmanager.siod-kafka","hostName": "kafka0052.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hbb"},"disable": true,"instanceType": "host","podId": null,"containerList": []}
					]}`),
				)
			},
			args: func() *ent.Stage {
				params := StageSchemaGroup{
					Group:         "group.blind-router-router.siod-redis",
					TargetAppList: []string{"r3-test-router.siod-redis", "r3-test-1-router.siod-redis"},
					SourceAppList: []string{"r3-test-1-router.siod-redis"},
				}
				b, _ := json.Marshal(params)
				s := string(b)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: true,
		},
		{
			name: "test6: success",
			before: func() {
				httpmock.RegisterResponder("GET", "http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-redis/apps/r3-test-router/instances?showPage=0",
					httpmock.NewStringResponder(200, `{"success": true, "message": "OK", "data": [
						{"name": "0.redis-cmanager.siod-kafka","hostName": "kafka0002.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hba"},"disable": true,"instanceType": "host","podId": null,"containerList": []},
						{"name": "1.redis-cmanager.siod-kafka","hostName": "kafka0000.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hbb"},"disable": true,"instanceType": "host","podId": null,"containerList": []},
						{"name": "2.redis-cmanager.siod-kafka","hostName": "kafka0051.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hba"},"disable": true,"instanceType": "host","podId": null,"containerList": []},
						{"name": "3.redis-cmanager.siod-kafka","hostName": "kafka0052.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hbb"},"disable": true,"instanceType": "host","podId": null,"containerList": []}
					]}`),
				)

				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/api/v1/groups/getGroupConfigByName`),
					httpmock.NewStringResponder(200, `{"errcode": 0, "data": {"id": 459,"middleName": "licai-alchemy-router-all","name": "group.licai-alchemy-router-all.siod-redis","productName": "siod-redis","owner": "jiayiming_dxm","appList": "licai-alchemy-router.siod-redis,licai-alchemy-router.tech-mixed-b","desc": "REDIS集群实现智能路由","sidecarSwitch": 0,"config": "{\"enable\":1,\"idc_mapping_rule\":{\"hba\":{\"prefer\":[\"hba\"],\"backup\":[\"hbb\"],\"min_instance_need\":1},\"hbb\":{\"prefer\":[\"hbb\"],\"backup\":[\"hba\"],\"min_instance_need\":1},\"hbf\":{\"prefer\":[\"hbb\"],\"backup\":[\"hba\"],\"min_instance_need\":1},\"hbg\":{\"prefer\":[\"hba\"],\"backup\":[\"hbb\"],\"min_instance_need\":1},\"default\":{\"prefer\":[\"hba\",\"hbb\"]}}}","create_time": "2020-03-05T11:32:00+08:00","DFlag": 0,"LastModifyTime": "2023-08-05T23:14:21+08:00"}}`))
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/bns-group/api/v1/groups/update`),
					httpmock.ResponderFromResponse(httpmock.NewStringResponse(200, "{\"errno\":0,\"errmsg\":\"success\",\"data\":{}}")))
			},
			args: func() *ent.Stage {
				params := StageSchemaGroup{
					Group:         "group.blind-router-router.siod-redis",
					TargetAppList: []string{"r3-test-router.siod-redis", "r3-test-1-router.siod-redis"},
					SourceAppList: []string{"r3-test-1-router.siod-redis"},
				}
				b, _ := json.Marshal(params)
				s := string(b)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: true,
			expect: func(t *testing.T, err error) {
				if err != errs.Success {
					t.Errorf("expect success but got %v", err)
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := RollbackGroupAppList(tt.args)
			if (err != nil) != tt.wantErr {
				t.Errorf("RollbackGroupAppList() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, err)
			}
		})
	}
}
