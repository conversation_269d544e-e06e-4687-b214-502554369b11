package deploy

import (
	"encoding/json"
	"fmt"
	"strings"

	"dt-common/bnsgroup"
	"dt-common/ent"
	"dt-common/errs"
	"dt-common/noah"
	"dt-common/omodel"
)

type StageSchemaGroup struct {
	Group         string   `json:"group"`
	Owner         string   `json:"owner,omitempty"`
	SourceAppList []string `json:"sourceAppList,omitempty"`
	TargetAppList []string `json:"targetAppList"`
}

// ======================================
// 				 创建SMART
// ======================================

// 权限、账单、Smart
func CreateGroup(stageData *ent.Stage) error {
	// 参数解析
	var params StageSchemaGroup
	err := json.Unmarshal([]byte(*stageData.Parameter), &params)
	if err != nil {
		omodel.StageAppendErrorLog(stageData.ID, "failed to parse stage parameter, value=%v, error=(%v)", stageData.Parameter, err)
		return err
	}
	// 空值判断
	if params.Group == "" {
		err := fmt.Errorf("stage parameter group should not be empty")
		omodel.StageAppendErrorLog(stageData.ID, "%v", err)
		return err
	}
	if params.Owner == "" {
		err := fmt.Errorf("stage parameter owner should not be empty")
		omodel.StageAppendErrorLog(stageData.ID, "%v", err)
		return err
	}
	if len(params.TargetAppList) == 0 {
		err := fmt.Errorf("stage parameter targetAppList should not be empty")
		omodel.StageAppendErrorLog(stageData.ID, "%v", err)
		return err
	}

	// 创建Smart
	err = bnsgroup.Create(&bnsgroup.RequestParams{
		Group:   params.Group,
		Owner:   params.Owner,
		Desc:    "REDIS集群智能路由",
		AppList: params.TargetAppList,
	})
	if err != nil {
		omodel.StageAppendErrorLog(stageData.ID, "%v", err)
		return err
	}

	// 同步任务返回 errs.Success
	return errs.Success
}

// ======================================
// 				 更新SMART
// ======================================

// 获取数组1和2的补集
func diff(arr1, arr2 []string) []string {
	elementsMap := make(map[string]bool)
	for _, element := range arr1 {
		elementsMap[element] = true
	}

	var result []string
	for _, element := range arr2 {
		if _, ok := elementsMap[element]; ok {
			elementsMap[element] = false
		} else {
			elementsMap[element] = true
		}
	}

	for element, state := range elementsMap {
		if state {
			result = append(result, element)
		}
	}
	return result
}

// 检查新扩容BNS是否全部屏蔽了
func isInstanesDisabled(stageId int64, appList []string) error {
	isOk := true
	for _, bns := range appList {
		// 获取bns实例列表
		tmp := strings.Split(bns, ".")
		if len(tmp) != 2 {
			err := fmt.Errorf("bns %s is not valid", bns)
			omodel.StageAppendErrorLog(stageId, "%v", err)
			return err
		}
		instances, err := noah.GetInstances(tmp[1], tmp[0])
		if err != nil {
			omodel.StageAppendErrorLog(stageId, "failed to get instances from noah, error=(%v)", err)
			return err
		}

		// 打印没有屏蔽的实例
		for _, instance := range instances {
			if !instance.Disable {
				isOk = false
				omodel.StageAppendWarnLog(stageId, "instance %s of bns %s is not disable", instance.Name, bns)
			}
		}
	}
	if !isOk {
		return fmt.Errorf("some bns instances is not disable")
	}

	return nil
}

// 更新smart成员列表
func UpdateGroupAppList(stageData *ent.Stage) error {
	// 参数解析
	var params StageSchemaGroup
	err := json.Unmarshal([]byte(*stageData.Parameter), &params)
	if err != nil {
		omodel.StageAppendErrorLog(stageData.ID, "failed to parse stage parameter, value=%v, error=(%v)", stageData.Parameter, err)
		return err
	}
	// 空值判断
	if params.Group == "" {
		err := fmt.Errorf("stage parameter group should not be empty")
		omodel.StageAppendErrorLog(stageData.ID, "%v", err)
		return err
	}
	if len(params.TargetAppList) == 0 {
		err := fmt.Errorf("stage parameter targetAppList should not be empty")
		omodel.StageAppendErrorLog(stageData.ID, "%v", err)
		return err
	}
	if len(params.SourceAppList) == 0 {
		err := fmt.Errorf("stage parameter sourceAppList should not be empty")
		omodel.StageAppendErrorLog(stageData.ID, "%v", err)
		return err
	}

	// 检查屏蔽
	diffApps := diff(params.SourceAppList, params.TargetAppList)
	if err := isInstanesDisabled(stageData.ID, diffApps); err != nil {
		omodel.StageAppendErrorLog(stageData.ID, "%v", err)
		return err
	}

	// 更新applist
	err = bnsgroup.UpdateAppList(params.Group, params.TargetAppList)
	if err != nil {
		omodel.StageAppendErrorLog(stageData.ID, "%v", err)
		return err
	}

	// 同步任务返回 errs.Success
	return errs.Success
}

// 回滚smart成员修改
func RollbackGroupAppList(stageData *ent.Stage) error {
	// 参数解析
	var params StageSchemaGroup
	err := json.Unmarshal([]byte(*stageData.Parameter), &params)
	if err != nil {
		omodel.StageAppendErrorLog(stageData.ID, "failed to parse stage parameter, value=%v, error=(%v)", stageData.Parameter, err)
		return err
	}
	// 空值判断
	if params.Group == "" {
		err := fmt.Errorf("stage parameter group should not be empty")
		omodel.StageAppendErrorLog(stageData.ID, "%v", err)
		return err
	}
	if len(params.SourceAppList) == 0 {
		err := fmt.Errorf("stage parameter sourceAppList should not be empty")
		omodel.StageAppendErrorLog(stageData.ID, "%v", err)
		return err
	}

	// 检查屏蔽
	diffApps := diff(params.SourceAppList, params.TargetAppList)
	if err := isInstanesDisabled(stageData.ID, diffApps); err != nil {
		omodel.StageAppendErrorLog(stageData.ID, "%v", err)
		return err
	}

	// 更新applist
	err = bnsgroup.UpdateAppList(params.Group, params.SourceAppList)
	if err != nil {
		omodel.StageAppendErrorLog(stageData.ID, "%v", err)
		return err
	}

	// 同步任务返回 errs.Success
	return errs.Success
}
