package deploy

import (
	"encoding/json"
	"errors"
	"fmt"
	"strings"

	"dt-common/ent"
	"dt-common/errs"
	"dt-common/logger"
	"dt-common/omodel"
	"redis-xweb/library/monitor"
)

// 将bns解析成monitor.App
func getApp(bns string, port int) *monitor.App {
	strs := strings.Split(bns, ".")
	return &monitor.App{
		ProductName: strs[1],
		AppName:     strs[0],
		Port:        port,
	}
}

// 获取集群的bns，组装monitor.M
func GetClusterBnsApps(clusterName string, components []string) (*[]monitor.M, error) {
	modules, err := omodel.GetClusterModules(clusterName, omodel.DEPLOY_ENV_DOCKER)
	if err != nil {
		logger.Error("failed to get docker bns, cluster=%s, error=(%v)", clusterName, err)
		return nil, err
	}

	// 组装apps
	clusterPrex := strings.ReplaceAll(clusterName, "_", "-")
	apps := []monitor.M{}
	for _, component := range components {
		switch component {
		case omodel.PROCESS_TYPE_REDIS:
			if len(*modules.Redis) != 1 {
				logger.Error("the number of redis bns abnormal, current is %d, should be 1, cluster=%s, bns=[%v]", len(*modules.Redis), clusterName, modules.Redis)
				return nil, errors.New("docker redis bns abnormal")
			}
			apps = append(apps, &monitor.Redis{App: getApp((*modules.Redis)[0].Bns, (*modules.Redis)[0].Port), ClusterName: clusterPrex})
		case omodel.PROCESS_TYPE_PROXY:
			if len(*modules.Proxy) != 1 {
				logger.Error("the number of proxy bns abnormal, current is %d, should be 1, cluster=%s, bns=[%v]", len(*modules.Proxy), clusterName, modules.Proxy)
				return nil, errors.New("docker proxy bns abnormal")
			}
			apps = append(apps, &monitor.Proxy{App: getApp((*modules.Proxy)[0].Bns, (*modules.Proxy)[0].Port), ClusterName: clusterPrex})
		case omodel.PROCESS_TYPE_SENTINEL:
			if len(*modules.Sentinel) != 1 {
				logger.Error("the number of sentinel bns abnormal, current is %d, should be 1, cluster=%s, bns=[%v]", len(*modules.Sentinel), clusterName, modules.Sentinel)
				return nil, errors.New("docker sentinel bns abnormal")
			}
			apps = append(apps, &monitor.Sentinel{App: getApp((*modules.Sentinel)[0].Bns, (*modules.Sentinel)[0].Port), ClusterName: clusterPrex})
		}
	}

	return &apps, nil
}

// 初始化集群监控&策略
// 仅针对容器集群和容器的bns
func SyncInitMonitors(stageData *ent.Stage) error {
	// 组装apps
	apps, err := GetClusterBnsApps(stageData.ClusterName, []string{omodel.PROCESS_TYPE_REDIS, omodel.PROCESS_TYPE_PROXY, omodel.PROCESS_TYPE_SENTINEL})
	if err != nil {
		return err
	}

	for _, app := range *apps {
		err := app.Monitors("add")
		omodel.StageAppendInfoLog(stageData.ID, app.RunMessage())
		if err != nil {
			logger.Error("failed to add monitor task, bns=%s, error=(%v)", app.BNS(), err)
			return err
		}

		err = app.Alarms("add")
		omodel.StageAppendInfoLog(stageData.ID, app.RunMessage())
		if err != nil {
			logger.Error("failed to add alarms, bns=%s, error=(%v)", app.BNS(), err)
			return err
		}
	}

	// 同步任务返回 errs.Success
	return errs.Success
}

// 清理集群监控&策略
// 仅针对容器集群和容器的bns
func SyncDeleteMonitors(stageData *ent.Stage) error {
	// 组装apps
	apps, err := GetClusterBnsApps(stageData.ClusterName, []string{omodel.PROCESS_TYPE_REDIS, omodel.PROCESS_TYPE_PROXY, omodel.PROCESS_TYPE_SENTINEL})
	if err != nil {
		return err
	}

	for _, app := range *apps {
		err := app.Monitors("del")
		omodel.StageAppendInfoLog(stageData.ID, app.RunMessage())
		if err != nil {
			logger.Error("failed to del monitor task, bns=%s, error=(%v)", app.BNS(), err)
			return err
		}

		err = app.Alarms("del")
		omodel.StageAppendInfoLog(stageData.ID, app.RunMessage())
		if err != nil {
			logger.Error("failed to del alarms, bns=%s, error=(%v)", app.BNS(), err)
			return err
		}
	}

	// 同步任务返回 errs.Success
	return errs.Success
}

// ====================================================================
// 					清理Sentinel物理实例监控和报警
// ====================================================================

type StageSchemaMonitor struct {
	Bns  string `json:"bns"`
	Port int    `json:"port"`
}

// 删除sentinel bns上物理监控和报警
func SyncCleanSentinelMonitors(stageData *ent.Stage) error {
	var sentinel StageSchemaMonitor
	err := json.Unmarshal([]byte(*stageData.Parameter), &sentinel)
	if err != nil {
		return fmt.Errorf("failed to unmarshal stage parameter, error=(%v)", err)
	}
	if sentinel.Bns == "" || sentinel.Port == 0 {
		return fmt.Errorf("invalid sentinel bns or port, bns=%s, port=%d", sentinel.Bns, sentinel.Port)
	}

	app := monitor.Sentinel{
		App:         getApp(sentinel.Bns, sentinel.Port),
		ClusterName: stageData.ClusterName,
	}

	err = app.Clean("alarm")
	omodel.StageAppendInfoLog(stageData.ID, app.RunMessage())
	if err != nil {
		logger.Error("failed to clean sentinel bbc alarms, bns=%s, error=(%v)", app.BNS(), err)
		return err
	}

	err = app.Clean("monitor")
	omodel.StageAppendInfoLog(stageData.ID, app.RunMessage())
	if err != nil {
		logger.Error("failed to clean sentinel bbc monitor task, bns=%s, error=(%v)", app.BNS(), err)
		return err
	}

	// 同步任务返回 errs.Success
	return errs.Success
}
