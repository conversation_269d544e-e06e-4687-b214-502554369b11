package deploy

import (
	"encoding/json"
	"fmt"

	"dt-common/ent"
	"dt-common/logger"
	"dt-common/omodel"
	"redis-xweb/library/ras"
)

// ======================================
// 				 申请实例
// ======================================

// CreateCluster 创建集群
func CreateCluster(stageData *ent.Stage) error {
	// 参数解析
	var params omodel.DeployOptions
	err := json.Unmarshal([]byte(*stageData.Parameter), &params)
	if err != nil {
		omodel.StageAppendErrorLog(stageData.ID, "failed to parse stage parameter, value=%v, error=(%v)", stageData.Parameter, err)
		return err
	}

	// 空值判断
	paramsCheck := true
	if params.ClusterID == 0 {
		omodel.StageAppendErrorLog(stageData.ID, "cluster id should not be empty")
		paramsCheck = false
	}
	if params.Name == "" {
		omodel.StageAppendErrorLog(stageData.ID, "cluster name should not be empty")
		paramsCheck = false
	}
	if params.ProxyPort == 0 {
		omodel.StageAppendErrorLog(stageData.ID, "proxy port should not be 0")
		paramsCheck = false
	}
	if len(params.ProxyReplicas) == 0 {
		omodel.StageAppendErrorLog(stageData.ID, "proxy replicas map should not be empty")
		paramsCheck = false
	}
	if params.ShardNum == 0 {
		omodel.StageAppendErrorLog(stageData.ID, "shard num should not be 0")
		paramsCheck = false
	}
	if params.ShardMem < 1 || params.ShardMem > 16 {
		omodel.StageAppendErrorLog(stageData.ID, "shard memory size should between 1 and 16")
		paramsCheck = false
	}
	if params.ShardPort == 0 {
		omodel.StageAppendErrorLog(stageData.ID, "shard port should not be 0")
		paramsCheck = false
	}
	if len(params.ShardReplicas) == 0 {
		omodel.StageAppendErrorLog(stageData.ID, "shard replicas map should not be empty")
		paramsCheck = false
	}
	if params.SentinelPort == 0 {
		omodel.StageAppendErrorLog(stageData.ID, "sentinel port should not be 0")
		paramsCheck = false
	}
	if len(params.SentinelReplicas) == 0 {
		omodel.StageAppendErrorLog(stageData.ID, "sentinel replicas map should not be empty")
		paramsCheck = false
	}
	if len(params.EnabledAZ) == 0 {
		omodel.StageAppendErrorLog(stageData.ID, "enabledAZ should not be empty")
		paramsCheck = false
	}
	if !paramsCheck {
		return fmt.Errorf("cluster parameters got wrong item")
	}
	params.StageID = stageData.ID

	// 调用cmanager开始创建集群
	err = ras.CreateCluster(&params)
	if err != nil {
		logger.Error("failed to call cmanager to create cluster, params=%v, error=(%v)", params, err)
		return err
	}

	return nil
}
