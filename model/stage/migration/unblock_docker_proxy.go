package migration

import (
	"encoding/json"
	"fmt"
	"strings"

	"dt-common/ent"
	"dt-common/errs"
	"dt-common/noah"
	"dt-common/omodel"
)

// ===============================
//          解屏蔽容器proxy
// ===============================

// 用于解析stage.Parameter
type StageSchemaBns struct {
	BNS string `json:"bns"`
	IDC string `json:"idc"`
}

// UnblockProxyOneByOne 解屏蔽所有容器Proxy
// - 每次执行解开bns中1个proxy的屏蔽
// - 整个迁移流程中会调用2次本阶段
func UnblockProxyOneByOne(stageData *ent.Stage) error {
	// 参数解析
	var params StageSchemaBns
	err := json.Unmarshal([]byte(*stageData.Parameter), &params)
	if err != nil {
		omodel.StageAppendErrorLog(stageData.ID, "failed to parse stage parameter, value=%v, error=(%v)", stageData.Parameter, err)
		return err
	}
	// 空值判断
	if params.BNS == "" {
		err := fmt.Errorf("stage parameter bns is empty")
		omodel.StageAppendErrorLog(stageData.ID, "%v", err)
		return err
	}

	// 通过noah获取实例
	strs := strings.Split(params.BNS, ".")
	if len(strs) < 2 {
		err := fmt.Errorf("parameter bns %s format is incorrect", params.BNS)
		omodel.StageAppendErrorLog(stageData.ID, "%v", err)
		return err
	}
	productLine := strs[1]
	appName := strs[0]
	instances, err := noah.GetInstances(productLine, appName)
	if err != nil {
		return err
	}

	unblockNum := 0
	for _, instance := range instances {
		if len(instance.Tags) == 0 {
			omodel.StageAppendErrorLog(stageData.ID, "proxy %s:%d has no tags", instance.IP, instance.PortInfo.Main)
			continue
		}
		// 跳过非指定机房的proxy
		if params.IDC != "" && instance.Tags["idc"] != params.IDC {
			continue
		}
		// 跳过已解除屏蔽的proxy
		if !instance.Disable {
			omodel.StageAppendInfoLog(stageData.ID, "proxy %s:%d already unblocked", instance.IP, instance.PortInfo.Main)
			continue
		}
		if unblockNum != 0 {
			omodel.StageAppendErrorLog(stageData.ID, "proxy %s:%d is still blocked", instance.IP, instance.PortInfo.Main)
			return fmt.Errorf("阶段重试继续解屏蔽")
		}

		// 解屏蔽一个proxy
		err = noah.EnableInstances(productLine, appName, []string{instance.Name})
		if err != nil {
			omodel.StageAppendErrorLog(stageData.ID, "failed to unblock proxy %s:%d, error=(%v)", instance.IP, instance.PortInfo.Main, err)
			return err
		}
		omodel.StageAppendInfoLog(stageData.ID, "succeed to unblock proxy %s:%d", instance.IP, instance.PortInfo.Main)
		unblockNum++
	}
	omodel.StageAppendInfoLog(stageData.ID, "all proxy instance unblocked")

	return errs.Success
}

// 屏蔽实例
func DisableInstances(stageData *ent.Stage) error {
	// 参数解析
	var params StageSchemaBns
	err := json.Unmarshal([]byte(*stageData.Parameter), &params)
	if err != nil {
		omodel.StageAppendErrorLog(stageData.ID, "failed to parse stage parameter, value=%v, error=(%v)", stageData.Parameter, err)
		return err
	}
	// 空值判断
	if params.BNS == "" {
		err := fmt.Errorf("stage parameter bns is empty")
		omodel.StageAppendErrorLog(stageData.ID, "%v", err)
		return err
	}

	// 通过noah获取实例
	strs := strings.Split(params.BNS, ".")
	if len(strs) < 2 {
		err := fmt.Errorf("parameter bns %s format is incorrect", params.BNS)
		omodel.StageAppendErrorLog(stageData.ID, "%v", err)
		return err
	}
	productLine := strs[1]
	appName := strs[0]
	instances, err := noah.GetInstances(productLine, appName)
	if err != nil {
		return err
	}

	instanceNames := []string{}
	for _, instance := range instances {
		if len(instance.Tags) == 0 {
			omodel.StageAppendErrorLog(stageData.ID, "proxy %s:%d has no tags", instance.IP, instance.PortInfo.Main)
			continue
		}
		// 跳过非指定机房的proxy
		if params.IDC != "" && instance.Tags["idc"] != params.IDC {
			continue
		}
		// 已经屏蔽过了不再屏蔽
		if instance.Disable {
			continue
		}
		instanceNames = append(instanceNames, instance.Name)
	}
	err = noah.DisableInstances(productLine, appName, instanceNames)
	if err != nil {
		omodel.StageAppendErrorLog(stageData.ID, "failed to block instances, bns=%s, instances=%s, error=(%v)", params.BNS, strings.Join(instanceNames, ","), err)
		return err
	}
	omodel.StageAppendInfoLog(stageData.ID, "succeed to block instances, bns=%s, instances=%s", params.BNS, strings.Join(instanceNames, ","))

	return errs.Success
}
