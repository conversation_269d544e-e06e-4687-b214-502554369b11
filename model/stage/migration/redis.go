package migration

import (
	"encoding/json"
	"fmt"

	"dt-common/ent"
	"dt-common/ent/redis"
	"dt-common/errs"
	"dt-common/mysql"
	"dt-common/omodel"
	"redis-xweb/library/errc"
	"redis-xweb/library/ras"
)

type StageSchemaOperateRedis struct {
	BNS       string             `json:"bns"`
	IDC       string             `json:"idc"`                 // 从库必须都在指定的idc
	Instances []*omodel.Instance `json:"instances,omitempty"` // 回滚用
}

// 异步关停从库
func StopSlaves(stageData *ent.Stage) error {
	// 参数解析
	var params StageSchemaOperateRedis
	err := json.Unmarshal([]byte(*stageData.Parameter), &params)
	if err != nil {
		omodel.StageAppendErrorLog(stageData.ID, "failed to parse stage parameter, value=%v, error=(%v)", stageData.Parameter, err)
		return errs.CodeUnmarshalError
	}
	// 空值判断
	if params.BNS == "" {
		omodel.StageAppendErrorLog(stageData.ID, "Expect parameter bns but got empty value")
		return errs.CodeRequestParameter.Detail("BNS参数错误")
	}
	if params.IDC == "" {
		omodel.StageAppendErrorLog(stageData.ID, "Expect parameter idc but got empty value")
		return errs.CodeRequestParameter.Detail("IDC参数错误")
	}

	// 获取从库列表，判断是否都在指定的idc
	db, err := mysql.Database()
	if err != nil {
		return err
	}
	ctx, cancel := mysql.ContextWithTimeout()
	slaves, err := db.Redis.Query().Where(
		redis.ClusterName(stageData.ClusterName),
		redis.Idc(params.IDC),
		redis.Role(omodel.REDIS_ROLE_SLAVE),
		redis.Docker(omodel.DEPLOY_ENV_BBC),
	).All(ctx)
	cancel()
	if err != nil {
		return err
	}
	if len(slaves) == 0 {
		omodel.StageAppendWarnLog(stageData.ID, "No slaves found in db, cluster=%s, bns=%s, idc=%s", stageData.ClusterName, params.BNS, params.IDC)
		return errc.CodeClusterAbnormal.Detail("slaves not found")
	}

	instances := make([]*omodel.Instance, len(slaves))
	for i, slave := range slaves {
		if slave.Idc != params.IDC {
			return fmt.Errorf("slave %s is not in idc %s", slave.Name, params.IDC)
		}
		instances[i] = &omodel.Instance{IP: slave.IP, Port: slave.Port, Name: slave.Name}
	}
	// 将Instance参数写回stage便于回滚
	err = omodel.StageAppendParameter(stageData.ID, map[string]any{
		"instances": instances,
	})
	if err != nil {
		omodel.StageAppendErrorLog(stageData.ID, "failed to save parameter instances to stage, error=(%v)", err)
		return err
	}

	// 调用manager接口关停物理从库
	err = ras.StopSlaves(&ras.RedisOperateParams{
		StageId:     stageData.ID,
		ClusterName: stageData.ClusterName,
		BNS:         params.BNS,
		Instances:   instances,
	})
	if err != nil {
		omodel.StageAppendErrorLog(stageData.ID, "failed to call redis-manager to stop slaves, cluster=%s, error=(%v)", stageData.ClusterName, err)
		return errs.CodeRequestFailed.Detail("物理从库关停接口请求失败")
	}
	// 异步执行
	omodel.StageAppendInfoLog(stageData.ID, "succeed to call redis-manager to stop slaves, cluster=%s", stageData.ClusterName)
	return nil
}

// 调用manager接口启动物理从库，作为异步关停方法的回滚
func StartSlaves(stageData *ent.Stage) error {
	// 参数解析
	var params StageSchemaOperateRedis
	err := json.Unmarshal([]byte(*stageData.Parameter), &params)
	if err != nil {
		omodel.StageAppendErrorLog(stageData.ID, "failed to parse stage parameter, value=%v, error=(%v)", stageData.Parameter, err)
		return errs.CodeUnmarshalError
	}
	// 空值判断
	if params.BNS == "" {
		omodel.StageAppendErrorLog(stageData.ID, "Expect parameter bns but got empty value")
		return errs.CodeRequestParameter.Detail("BNS参数错误")
	}
	if len(params.Instances) == 0 {
		omodel.StageAppendErrorLog(stageData.ID, "Expect parameter instanes but got empty array")
		return errs.CodeRequestParameter.Detail("Instances参数错误")
	}

	// 调用manager接口启动物理从库
	err = ras.StartSlaves(&ras.RedisOperateParams{
		StageId:     stageData.ID,
		ClusterName: stageData.ClusterName,
		BNS:         params.BNS,
		Instances:   params.Instances,
	})
	if err != nil {
		omodel.StageAppendErrorLog(stageData.ID, "failed to call redis-manager to start slaves, cluster=%s, error=(%v)", stageData.ClusterName, err)
		return errs.CodeRequestFailed.Detail("从库启动API请求失败")
	}
	// 异步执行
	omodel.StageAppendInfoLog(stageData.ID, "succeed to call redis-manager to start slaves, cluster=%s", stageData.ClusterName)
	return nil
}
