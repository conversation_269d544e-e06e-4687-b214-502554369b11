package migration

import (
	"encoding/json"
	"fmt"

	"dt-common/ent"
	"dt-common/errs"
	"dt-common/omodel"
	"redis-xweb/library/ras"
)

// 用于解析stage.Parameter
type StageSchemaTag struct {
	IDC string `json:"idc"`
}

// 从BNS中摘除已屏蔽、无效的redis、proxy、sentinel实例
// 从stage.parameter中解析出idc标签，按机房维度摘
// 实例存活状态由manager判断，存在未关停、未屏蔽的实例报错返回
// 阶段不设置回滚
func UnmountInstance(stageData *ent.Stage) error {
	// 参数解析
	var params StageSchemaTag
	err := json.Unmarshal([]byte(*stageData.Parameter), &params)
	if err != nil {
		omodel.StageAppendErrorLog(stageData.ID, "failed to parse stage parameter, value=%v, error=(%v)", stageData.Parameter, err)
		return errs.CodeUnmarshalError
	}
	// 空值判断
	if params.IDC == "" {
		omodel.StageAppendErrorLog(stageData.ID, "Empty value, idc=%v", params.IDC)
		return fmt.Errorf("empty value")
	}

	err = ras.UnmountInstance(&ras.InstanceOfflineRequest{
		StageId:     stageData.ID,
		ClusterName: stageData.ClusterName,
		IDC:         params.IDC,
	})
	if err != nil {
		omodel.StageAppendErrorLog(stageData.ID, "failed to call redis-manager to delete instance of BBC, taskId=%d, stageId=%d, error=(%v)", stageData.TaskID, stageData.ID, err)
		return errs.CodeRequestFailed.Detail("删除物理集群实例失败")
	}
	omodel.StageAppendInfoLog(stageData.ID, "succeed to call redis-manager to delete instance of BBC, taskId=%d, stageId=%d", stageData.TaskID, stageData.ID)

	return nil
}
