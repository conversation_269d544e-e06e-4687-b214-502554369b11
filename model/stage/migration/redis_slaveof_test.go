package migration

import (
	"context"
	"encoding/json"
	"regexp"
	"testing"

	"github.com/jarcoal/httpmock"

	"dt-common/ent"
	"dt-common/mysql"
	"dt-common/omodel"
	"redis-xweb/env"
)

// 单测：容器实例slaveof物理实例
func TestMigrationSlaveOf(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")
	objC := env.MockCluster("slaveof_test")
	_, objS := env.MockTaskWithStage(objC)
	db, _ := mysql.Database()

	type args struct {
		stageId     int64
		clusterName string
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, error, []*omodel.Shard)
	}{
		{
			name: "test1: metadata empty",
			args: args{
				stageId:     objS.ID,
				clusterName: objS.ClusterName,
			},
			wantErr: true,
		},
		{
			name: "test2: multi master",
			before: func() {
				db.Redis.Delete().Exec(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(0).SetBns("r3-test-router.siod-redis").SetName("r3-test_server1").SetIP("************").SetPort(7001).SetIdc("hba").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(0).SetBns("r3-test-router.siod-redis").SetName("r3-test_server1").SetIP("************").SetPort(7001).SetIdc("hba").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test-router.siod-redis").SetName("r3-test_server1").SetIP("************").SetPort(7048).SetIdc("hbb").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())

				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(0).SetBns("r3-test-router.siod-redis").SetName("r3-test_server2").SetIP("************").SetPort(7002).SetIdc("hba").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(0).SetBns("r3-test-router.siod-redis").SetName("r3-test_server2").SetIP("************").SetPort(7002).SetIdc("hbb").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test-router.siod-redis").SetName("r3-test_server2").SetIP("************").SetPort(7048).SetIdc("hbb").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
			},
			args: args{
				stageId:     objS.ID,
				clusterName: objS.ClusterName,
			},
			wantErr: true,
		},
		{
			name: "test3: multi slave",
			before: func() {
				db.Redis.Delete().Exec(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(0).SetBns("r3-test-router.siod-redis").SetName("r3-test_server1").SetIP("************").SetPort(7001).SetIdc("hba").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test-router.siod-redis").SetName("r3-test_server1").SetIP("************").SetPort(7001).SetIdc("hbb").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test-router.siod-redis").SetName("r3-test_server1").SetIP("************").SetPort(7048).SetIdc("hbb").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())

				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(0).SetBns("r3-test-router.siod-redis").SetName("r3-test_server2").SetIP("************").SetPort(7002).SetIdc("hba").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(0).SetBns("r3-test-router.siod-redis").SetName("r3-test_server2").SetIP("************").SetPort(7002).SetIdc("hbb").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test-router.siod-redis").SetName("r3-test_server2").SetIP("************").SetPort(7048).SetIdc("hbb").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
			},
			args: args{
				stageId:     objS.ID,
				clusterName: objS.ClusterName,
			},
			wantErr: true,
		},

		{
			name: "test4: missing master",
			before: func() {
				db.Redis.Delete().ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(0).SetBns("r3-test-router.siod-redis").SetName("r3-test_server1").SetIP("************").SetPort(7001).SetIdc("hbb").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(0).SetBns("r3-test-router.siod-redis").SetName("r3-test_server1").SetIP("************").SetPort(7001).SetIdc("hbb").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test-router.siod-redis").SetName("r3-test_server1").SetIP("************").SetPort(7048).SetIdc("hbb").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())

				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(0).SetBns("r3-test-router.siod-redis").SetName("r3-test_server2").SetIP("************").SetPort(7002).SetIdc("hba").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(0).SetBns("r3-test-router.siod-redis").SetName("r3-test_server2").SetIP("************").SetPort(7002).SetIdc("hbb").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test-router.siod-redis").SetName("r3-test_server2").SetIP("************").SetPort(7048).SetIdc("hbb").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
			},
			args: args{
				stageId:     objS.ID,
				clusterName: objS.ClusterName,
			},
			wantErr: true,
		},
		{
			name: "test5: missing slave",
			before: func() {
				db.Redis.Delete().ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(0).SetBns("r3-test-router.siod-redis").SetName("r3-test_server1").SetIP("************").SetPort(7001).SetIdc("hba").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(0).SetBns("r3-test-router.siod-redis").SetName("r3-test_server1").SetIP("************").SetPort(7001).SetIdc("hbb").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())

				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(0).SetBns("r3-test-router.siod-redis").SetName("r3-test_server2").SetIP("************").SetPort(7002).SetIdc("hba").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(0).SetBns("r3-test-router.siod-redis").SetName("r3-test_server2").SetIP("************").SetPort(7002).SetIdc("hbb").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test-router.siod-redis").SetName("r3-test_server2").SetIP("************").SetPort(7048).SetIdc("hbb").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
			},
			args: args{
				stageId:     objS.ID,
				clusterName: objS.ClusterName,
			},
			wantErr: true,
		},
		{
			name: "test6: success",
			before: func() {
				db.Redis.Delete().Exec(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(0).SetBns("r3-test-router.siod-redis").SetName("r3-test_server1").SetIP("************").SetPort(7001).SetIdc("hba").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(0).SetBns("r3-test-router.siod-redis").SetName("r3-test_server1").SetIP("************").SetPort(7001).SetIdc("hbb").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test-router.siod-redis").SetName("r3-test_server1").SetIP("************").SetPort(7048).SetIdc("hbb").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(0).SetBns("r3-test-router.siod-redis").SetName("r3-test_server2").SetIP("************").SetPort(7002).SetIdc("hba").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(0).SetBns("r3-test-router.siod-redis").SetName("r3-test_server2").SetIP("************").SetPort(7002).SetIdc("hbb").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test-router.siod-redis").SetName("r3-test_server2").SetIP("************").SetPort(7048).SetIdc("hbb").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
			},
			args: args{
				stageId:     objS.ID,
				clusterName: objS.ClusterName,
			},
			wantErr: false,
		},
		{
			name: "test7: recall success",
			before: func() {
				db.Redis.Delete().Exec(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(0).SetBns("r3-test-router.siod-redis").SetName("r3-test_server1").SetIP("************").SetPort(7001).SetIdc("hba").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(0).SetBns("r3-test-router.siod-redis").SetName("r3-test_server1").SetIP("************").SetPort(7001).SetIdc("hbb").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test-router.siod-redis").SetName("r3-test_server1").SetIP("************").SetPort(7048).SetIdc("hbb").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(0).SetBns("r3-test-router.siod-redis").SetName("r3-test_server2").SetIP("************").SetPort(7002).SetIdc("hba").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(0).SetBns("r3-test-router.siod-redis").SetName("r3-test_server2").SetIP("************").SetPort(7002).SetIdc("hbb").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test-router.siod-redis").SetName("r3-test_server2").SetIP("************").SetPort(7048).SetIdc("hbb").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
			},
			args: args{
				stageId:     objS.ID,
				clusterName: objS.ClusterName,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			shardList, err := MigrationSlaveOf(tt.args.stageId, tt.args.clusterName)
			if (err != nil) != tt.wantErr {
				t.Errorf("MigrationSlaveOf() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, err, shardList)
			}
		})
	}

}

// 单测：容器实例slaveof容器实例
func TestDockerSlaveOf(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")
	objC := env.MockCluster("slaveof_test")
	_, objS := env.MockTaskWithStage(objC)
	db, _ := mysql.Database()

	type args struct {
		stageId     int64
		clusterName string
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, error, []*omodel.Shard)
	}{
		{
			name: "test1: metadata empty",
			args: args{
				stageId:     objS.ID,
				clusterName: objS.ClusterName,
			},
			wantErr: true,
		},
		{
			name: "test2: multi docker master",
			before: func() {
				db.Redis.Delete().Exec(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(0).SetBns("r3-test-router.siod-redis").SetName("r3-test_server1").SetIP("************").SetPort(7001).SetIdc("hbb").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test-router.siod-redis").SetName("r3-test_server1").SetIP("************").SetPort(7048).SetIdc("hba").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test-router.siod-redis").SetName("r3-test_server1").SetIP("************").SetPort(7048).SetIdc("hbb").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(0).SetBns("r3-test-router.siod-redis").SetName("r3-test_server2").SetIP("************").SetPort(7002).SetIdc("hbb").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test-router.siod-redis").SetName("r3-test_server2").SetIP("************").SetPort(7048).SetIdc("hba").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test-router.siod-redis").SetName("r3-test_server2").SetIP("************").SetPort(7048).SetIdc("hbb").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
			},
			args: args{
				stageId:     objS.ID,
				clusterName: objS.ClusterName,
			},
			wantErr: true,
		},
		{
			name: "test3: multi docker slave",
			before: func() {
				db.Redis.Delete().Exec(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(0).SetBns("r3-test-router.siod-redis").SetName("r3-test_server1").SetIP("************").SetPort(7001).SetIdc("hbb").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test-router.siod-redis").SetName("r3-test_server1").SetIP("************").SetPort(7048).SetIdc("hba").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test-router.siod-redis").SetName("r3-test_server1").SetIP("************").SetPort(7048).SetIdc("hbb").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(0).SetBns("r3-test-router.siod-redis").SetName("r3-test_server2").SetIP("************").SetPort(7002).SetIdc("hbb").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test-router.siod-redis").SetName("r3-test_server2").SetIP("************").SetPort(7048).SetIdc("hba").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test-router.siod-redis").SetName("r3-test_server2").SetIP("************").SetPort(7048).SetIdc("hbb").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
			},
			args: args{
				stageId:     objS.ID,
				clusterName: objS.ClusterName,
			},
			wantErr: true,
		},
		{
			name: "test4: shard missing master",
			before: func() {
				db.Redis.Delete().Exec(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test-router.siod-redis").SetName("r3-test_server1").SetIP("************").SetPort(7048).SetIdc("hba").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test-router.siod-redis").SetName("r3-test_server2").SetIP("************").SetPort(7048).SetIdc("hba").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test-router.siod-redis").SetName("r3-test_server3").SetIP("************").SetPort(7048).SetIdc("hba").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test-router.siod-redis").SetName("r3-test_server4").SetIP("************").SetPort(7048).SetIdc("hbb").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
			},
			args: args{
				stageId:     objS.ID,
				clusterName: objS.ClusterName,
			},
			wantErr: true,
		},
		{
			name: "test4: shard missing slave",
			before: func() {
				db.Redis.Delete().Exec(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test-router.siod-redis").SetName("r3-test_server1").SetIP("************").SetPort(7048).SetIdc("hba").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test-router.siod-redis").SetName("r3-test_server2").SetIP("************").SetPort(7048).SetIdc("hba").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test-router.siod-redis").SetName("r3-test_server3").SetIP("************").SetPort(7048).SetIdc("hba").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test-router.siod-redis").SetName("r3-test_server4").SetIP("************").SetPort(7048).SetIdc("hbb").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
			},
			args: args{
				stageId:     objS.ID,
				clusterName: objS.ClusterName,
			},
			wantErr: true,
		},
		{
			name: "test5: success",
			before: func() {
				db.Redis.Delete().Exec(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(0).SetBns("r3-test-router.siod-redis").SetName("r3-test_server1").SetIP("************").SetPort(7001).SetIdc("hbb").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test-router.siod-redis").SetName("r3-test_server1").SetIP("************").SetPort(7048).SetIdc("hba").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test-router.siod-redis").SetName("r3-test_server1").SetIP("************").SetPort(7048).SetIdc("hbb").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(0).SetBns("r3-test-router.siod-redis").SetName("r3-test_server2").SetIP("************").SetPort(7002).SetIdc("hbb").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test-router.siod-redis").SetName("r3-test_server2").SetIP("************").SetPort(7048).SetIdc("hba").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test-router.siod-redis").SetName("r3-test_server2").SetIP("************").SetPort(7048).SetIdc("hbb").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
			},
			args: args{
				stageId:     objS.ID,
				clusterName: objS.ClusterName,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			shardList, err := DockerSlaveOf(tt.args.stageId, tt.args.clusterName)
			if (err != nil) != tt.wantErr {
				t.Errorf("DockerSlaveOf() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, err, shardList)
			}
		})
	}
}

func TestSlaveOf(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")
	objC := env.MockCluster("slaveof_test")
	objT, objS := env.MockTaskWithStage(objC)
	db, _ := mysql.Database()
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	tests := []struct {
		name    string
		before  func()
		args    *ent.Stage
		wantErr bool
		expect  func(*testing.T, error)
	}{
		{
			name:    "test1: parameter check failed",
			args:    objS,
			wantErr: true,
		},
		{
			name: "test2: undefined parameter",
			args: func() *ent.Stage {
				params := StageSchemaSlaveOf{Type: "docker3bbc"}
				b, _ := json.Marshal(params)
				s := string(b)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: true,
		},
		{
			name: "test3: request failed",
			before: func() {
				db.Redis.Delete().Exec(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(0).SetBns("r3-test-router.siod-redis").SetName("r3-test_server1").SetIP("************").SetPort(7001).SetIdc("hba").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(0).SetBns("r3-test-router.siod-redis").SetName("r3-test_server1").SetIP("************").SetPort(7001).SetIdc("hbb").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test-router.siod-redis").SetName("r3-test_server1").SetIP("************").SetPort(7048).SetIdc("hbb").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(0).SetBns("r3-test-router.siod-redis").SetName("r3-test_server2").SetIP("************").SetPort(7002).SetIdc("hba").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(0).SetBns("r3-test-router.siod-redis").SetName("r3-test_server2").SetIP("************").SetPort(7002).SetIdc("hbb").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test-router.siod-redis").SetName("r3-test_server2").SetIP("************").SetPort(7048).SetIdc("hbb").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
			},
			args: func() *ent.Stage {
				params := StageSchemaSlaveOf{Type: "docker2bbc"}
				b, _ := json.Marshal(params)
				s := string(b)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: true,
		},
		{
			name: "test4: success",
			before: func() {
				db.Redis.Delete().Exec(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(0).SetBns("r3-test-router.siod-redis").SetName("r3-test_server1").SetIP("************").SetPort(7001).SetIdc("hba").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(0).SetBns("r3-test-router.siod-redis").SetName("r3-test_server1").SetIP("************").SetPort(7001).SetIdc("hbb").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test-router.siod-redis").SetName("r3-test_server1").SetIP("************").SetPort(7048).SetIdc("hbb").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(0).SetBns("r3-test-router.siod-redis").SetName("r3-test_server2").SetIP("************").SetPort(7002).SetIdc("hba").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(0).SetBns("r3-test-router.siod-redis").SetName("r3-test_server2").SetIP("************").SetPort(7002).SetIdc("hbb").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test-router.siod-redis").SetName("r3-test_server2").SetIP("************").SetPort(7048).SetIdc("hbb").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())

				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/migration/slaveOf`),
					httpmock.ResponderFromResponse(httpmock.NewStringResponse(200, `{"code": "00000", "data": "ok"}`)))
			},
			args: func() *ent.Stage {
				params := StageSchemaSlaveOf{Type: "docker2bbc"}
				b, _ := json.Marshal(params)
				s := string(b)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := SlaveOf(tt.args)
			if (err != nil) != tt.wantErr {
				t.Errorf("SlaveOf() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, err)
			}
		})
	}
}

func TestStopSlaveOf(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")
	objC := env.MockCluster("stop_slaveof_test")
	_, objS := env.MockTaskWithStage(objC)

	tests := []struct {
		name    string
		before  func()
		args    *ent.Stage
		wantErr bool
		expect  func(*testing.T, error)
	}{
		{
			name:    "test1: request failed",
			args:    objS,
			wantErr: true,
		},
		{
			name: "test2: success",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/migration/stopSlaveOf`),
					httpmock.ResponderFromResponse(httpmock.NewStringResponse(200, `{"code": "00000", "data": "ok"}`)))
			},
			args:    objS,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := StopSlaveOf(tt.args)
			if (err != nil) != tt.wantErr {
				t.Errorf("StopSlaveOf() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, err)
			}
		})
	}
	httpmock.Reset()
}
