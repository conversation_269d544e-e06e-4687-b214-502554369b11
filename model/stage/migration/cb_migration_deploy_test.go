package migration

import (
	"context"
	"encoding/json"
	"testing"

	"dt-common/ent"
	"dt-common/mysql"
	"dt-common/omodel"
	"redis-xweb/controller/migration"
	"redis-xweb/env"
)

// 获取proxy的bns和实例
func TestGetProxyInfo(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")
	objC := env.MockCluster("proxy_info_test")

	type args struct {
		clusterName string
		proxyList   []*ent.Proxy
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, *Module)
	}{
		{
			name:   "test1: check args",
			before: func() {},
			args: args{
				clusterName: objC.Name,
				proxyList:   []*ent.Proxy{},
			},
			wantErr: true,
		},
		{
			name:   "test2: undefined env",
			before: func() {},
			args: args{
				clusterName: objC.Name,
				proxyList: []*ent.Proxy{
					{ID: 1, ClusterName: objC.Name, IP: "*********", Port: 8001, Bns: "r3-test-bbc-router.test", Docker: 3},
				},
			},
			wantErr: true,
		},
		{
			name:   "test3: bbc proxy has no idc",
			before: func() {},
			args: args{
				clusterName: objC.Name,
				proxyList: []*ent.Proxy{
					{ID: 1, ClusterName: objC.Name, IP: "*********", Port: 8001, Bns: "r3-test-bbc-router.test", Docker: 0},
				},
			},
			wantErr: true,
		},
		{
			name:   "test4: multi bns of bbc proxy",
			before: func() {},
			args: args{
				clusterName: objC.Name,
				proxyList: []*ent.Proxy{
					{ID: 1, ClusterName: "r3_test", IP: "*********", Port: 8001, Idc: "hba", Bns: "r3-test-bbc-router.test", Docker: 0},
					{ID: 2, ClusterName: "r3_test", IP: "*********", Port: 8001, Idc: "hbb", Bns: "r3-test-bbc1-router.test", Docker: 0},
				},
			},
			wantErr: true,
		},
		{
			name:   "test5: docker proxy has no idc",
			before: func() {},
			args: args{
				clusterName: objC.Name,
				proxyList: []*ent.Proxy{
					{ID: 1, ClusterName: "r3_test", IP: "*********", Port: 8001, Bns: "r3-test-bbc-router.test", Docker: 1},
				},
			},
			wantErr: true,
		},
		{
			name:   "test6: multi bns of docker proxy",
			before: func() {},
			args: args{
				clusterName: objC.Name,
				proxyList: []*ent.Proxy{
					{ID: 1, ClusterName: "r3_test", IP: "*********", Port: 8001, Idc: "hba", Bns: "r3-test-docker-router.test", Docker: 1},
					{ID: 2, ClusterName: "r3_test", IP: "*********", Port: 8001, Idc: "hbb", Bns: "r3-test-docker1-router.test", Docker: 1},
				},
			},
			wantErr: true,
		},
		{
			name:   "test7: missing bbc proxy",
			before: func() {},
			args: args{
				clusterName: objC.Name,
				proxyList: []*ent.Proxy{
					{ID: 3, ClusterName: "r3_test", IP: "*********", Port: 8001, Idc: "hba", Bns: "r3-test-docker-router.test", Docker: 1},
					{ID: 4, ClusterName: "r3_test", IP: "*********", Port: 8001, Idc: "hbb", Bns: "r3-test-docker-router.test", Docker: 1},
				},
			},
			wantErr: true,
		},
		{
			name:   "test8: missing docker proxy",
			before: func() {},
			args: args{
				clusterName: objC.Name,
				proxyList: []*ent.Proxy{
					{ID: 1, ClusterName: "r3_test", IP: "*********", Port: 8001, Idc: "hba", Bns: "r3-test-bbc-router.test", Docker: 0},
					{ID: 2, ClusterName: "r3_test", IP: "*********", Port: 8001, Idc: "hbb", Bns: "r3-test-bbc-router.test", Docker: 0},
				},
			},
			wantErr: true,
		},
		{
			name:   "test9: success",
			before: func() {},
			args: args{
				clusterName: objC.Name,
				proxyList: []*ent.Proxy{
					{ID: 1, ClusterName: "r3_test", IP: "*********", Port: 8001, Idc: "hba", Bns: "r3-test-bbc-router.test", Docker: 0},
					{ID: 2, ClusterName: "r3_test", IP: "*********", Port: 8001, Idc: "hbb", Bns: "r3-test-bbc-router.test", Docker: 0},
					{ID: 3, ClusterName: "r3_test", IP: "*********", Port: 8001, Idc: "hba", Bns: "r3-test-docker-router.test", Docker: 1},
					{ID: 4, ClusterName: "r3_test", IP: "*********", Port: 8001, Idc: "hbb", Bns: "r3-test-docker-router.test", Docker: 1},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			proxyInfo, err := getProxyInfo(tt.args.clusterName, tt.args.proxyList)
			if (err != nil) != tt.wantErr {
				t.Errorf("getProxyInfo() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, proxyInfo)
			}
		})
	}
}

// 获取redis的bns和实例
func TestGetRedisInfo(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")
	objC := env.MockCluster("redis_info_test")

	type args struct {
		clusterName string
		redisList   []*ent.Redis
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, *Module)
	}{
		{
			name:   "test1: check args",
			before: func() {},
			args: args{
				clusterName: objC.Name,
				redisList:   []*ent.Redis{},
			},
			wantErr: true,
		},
		{
			name:   "test2: undefined env",
			before: func() {},
			args: args{
				clusterName: objC.Name,
				redisList: []*ent.Redis{
					{ID: 1, ClusterName: objC.Name, IP: "*********", Port: 8001, Bns: "r3-test-bbc-router.test", Docker: 3},
				},
			},
			wantErr: true,
		},
		{
			name:   "test3: multi bns of bbc redis",
			before: func() {},
			args: args{
				clusterName: objC.Name,
				redisList: []*ent.Redis{
					{ID: 1, ClusterName: "r3_test", IP: "*********", Port: 8001, Idc: "hba", Bns: "r3-test-bbc-router.test", Docker: 0},
					{ID: 2, ClusterName: "r3_test", IP: "*********", Port: 8001, Idc: "hbb", Bns: "r3-test-bbc1-router.test", Docker: 0},
				},
			},
			wantErr: true,
		},
		{
			name:   "test4: multi bns of docker redis",
			before: func() {},
			args: args{
				clusterName: objC.Name,
				redisList: []*ent.Redis{
					{ID: 1, ClusterName: "r3_test", IP: "*********", Port: 8001, Idc: "hba", Bns: "r3-test-docker-router.test", Docker: 1},
					{ID: 2, ClusterName: "r3_test", IP: "*********", Port: 8001, Idc: "hbb", Bns: "r3-test-docker1-router.test", Docker: 1},
				},
			},
			wantErr: true,
		},
		{
			name:   "test5: missing bbc redis",
			before: func() {},
			args: args{
				clusterName: objC.Name,
				redisList: []*ent.Redis{
					{ID: 3, ClusterName: "r3_test", IP: "*********", Port: 8001, Idc: "hba", Bns: "r3-test-docker-router.test", Docker: 1},
					{ID: 4, ClusterName: "r3_test", IP: "*********", Port: 8001, Idc: "hbb", Bns: "r3-test-docker-router.test", Docker: 1},
				},
			},
			wantErr: true,
		},
		{
			name:   "test6: missing docker redis",
			before: func() {},
			args: args{
				clusterName: objC.Name,
				redisList: []*ent.Redis{
					{ID: 1, ClusterName: "r3_test", IP: "*********", Port: 8001, Idc: "hba", Bns: "r3-test-bbc-router.test", Docker: 0},
					{ID: 2, ClusterName: "r3_test", IP: "*********", Port: 8001, Idc: "hbb", Bns: "r3-test-bbc-router.test", Docker: 0},
				},
			},
			wantErr: true,
		},
		{
			name:   "test7: success",
			before: func() {},
			args: args{
				clusterName: objC.Name,
				redisList: []*ent.Redis{
					{ID: 1, ClusterName: "r3_test", IP: "*********", Port: 8001, Idc: "hba", Bns: "r3-test-bbc-router.test", Docker: 0},
					{ID: 2, ClusterName: "r3_test", IP: "*********", Port: 8001, Idc: "hbb", Bns: "r3-test-bbc-router.test", Docker: 0},
					{ID: 3, ClusterName: "r3_test", IP: "*********", Port: 8001, Idc: "hba", Bns: "r3-test-docker-router.test", Docker: 1},
					{ID: 4, ClusterName: "r3_test", IP: "*********", Port: 8001, Idc: "hbb", Bns: "r3-test-docker-router.test", Docker: 1},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			proxyInfo, err := getRedisInfo(tt.args.clusterName, tt.args.redisList)
			if (err != nil) != tt.wantErr {
				t.Errorf("getProxyInfo() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, proxyInfo)
			}
		})
	}
}

func TestGetSentinelInfo(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")
	objC := env.MockCluster("sentinel_info_test")

	type args struct {
		clusterName  string
		sentinelList []*ent.Sentinel
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, *ModuleSentinel)
	}{
		{
			name:   "test1: check args",
			before: func() {},
			args: args{
				clusterName:  objC.Name,
				sentinelList: []*ent.Sentinel{},
			},
			wantErr: true,
		},
		{
			name:   "test2: undefined env",
			before: func() {},
			args: args{
				clusterName: objC.Name,
				sentinelList: []*ent.Sentinel{
					{ID: 1, ClusterName: objC.Name, IP: "*********", Port: 8001, Bns: "r3-test-bbc-router.test", Docker: 3},
				},
			},
			wantErr: true,
		},
		{
			name:   "test3: bbc proxy has no idc",
			before: func() {},
			args: args{
				clusterName: objC.Name,
				sentinelList: []*ent.Sentinel{
					{ID: 1, ClusterName: objC.Name, IP: "*********", Port: 8001, Bns: "r3-test-bbc-router.test", Docker: 0},
				},
			},
			wantErr: true,
		},
		{
			name:   "test4: multi bns of bbc proxy",
			before: func() {},
			args: args{
				clusterName: objC.Name,
				sentinelList: []*ent.Sentinel{
					{ID: 1, ClusterName: "r3_test", IP: "*********", Port: 8001, Idc: "hba", Bns: "r3-test-bbc-router.test", Docker: 0},
					{ID: 2, ClusterName: "r3_test", IP: "*********", Port: 8001, Idc: "hbb", Bns: "r3-test-bbc1-router.test", Docker: 0},
				},
			},
			wantErr: true,
		},
		{
			name:   "test5: docker proxy has no idc",
			before: func() {},
			args: args{
				clusterName: objC.Name,
				sentinelList: []*ent.Sentinel{
					{ID: 1, ClusterName: "r3_test", IP: "*********", Port: 8001, Bns: "r3-test-bbc-router.test", Docker: 1},
				},
			},
			wantErr: true,
		},
		{
			name:   "test6: multi bns of docker proxy",
			before: func() {},
			args: args{
				clusterName: objC.Name,
				sentinelList: []*ent.Sentinel{
					{ID: 1, ClusterName: "r3_test", IP: "*********", Port: 8001, Idc: "hba", Bns: "r3-test-docker-router.test", Docker: 1},
					{ID: 2, ClusterName: "r3_test", IP: "*********", Port: 8001, Idc: "hbb", Bns: "r3-test-docker1-router.test", Docker: 1},
				},
			},
			wantErr: true,
		},
		{
			name:   "test7: missing bbc proxy",
			before: func() {},
			args: args{
				clusterName: objC.Name,
				sentinelList: []*ent.Sentinel{
					{ID: 3, ClusterName: "r3_test", IP: "*********", Port: 8001, Idc: "hba", Bns: "r3-test-docker-router.test", Docker: 1},
					{ID: 4, ClusterName: "r3_test", IP: "*********", Port: 8001, Idc: "hbb", Bns: "r3-test-docker-router.test", Docker: 1},
				},
			},
			wantErr: true,
		},
		{
			name:   "test8: missing docker proxy",
			before: func() {},
			args: args{
				clusterName: objC.Name,
				sentinelList: []*ent.Sentinel{
					{ID: 1, ClusterName: "r3_test", IP: "*********", Port: 8001, Idc: "hba", Bns: "r3-test-bbc-router.test", Docker: 0},
					{ID: 2, ClusterName: "r3_test", IP: "*********", Port: 8001, Idc: "hbb", Bns: "r3-test-bbc-router.test", Docker: 0},
				},
			},
			wantErr: true,
		},
		{
			name:   "test9: success",
			before: func() {},
			args: args{
				clusterName: objC.Name,
				sentinelList: []*ent.Sentinel{
					{ID: 1, ClusterName: "r3_test", IP: "*********", Port: 8001, Idc: "hba", Bns: "r3-test-bbc-router.test", Docker: 0},
					{ID: 2, ClusterName: "r3_test", IP: "*********", Port: 8001, Idc: "hbb", Bns: "r3-test-bbc-router.test", Docker: 0},
					{ID: 3, ClusterName: "r3_test", IP: "*********", Port: 8001, Idc: "hba", Bns: "r3-test-docker-router.test", Docker: 1},
					{ID: 4, ClusterName: "r3_test", IP: "*********", Port: 8001, Idc: "hbb", Bns: "r3-test-docker-router.test", Docker: 1},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			proxyInfo, err := getSentinelInfo(tt.args.clusterName, tt.args.sentinelList)
			if (err != nil) != tt.wantErr {
				t.Errorf("getSentinelInfo() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, proxyInfo)
			}
		})
	}
}

// 单测 GetClusterModuleInfos
func TestGetClusterModuleInfos(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")
	objC := env.MockCluster("cluster_test")
	db, _ := mysql.Database()

	type args struct {
		clusterName string
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name:   "test1: check args",
			before: func() {},
			args: args{
				clusterName: "not_exist",
			},
			wantErr: true,
		},
		{
			name: "test2: getProxyInfo error",
			args: args{
				clusterName: objC.Name,
			},
			wantErr: true,
		},
		{
			name: "test3: getRedisInfo error",
			before: func() {
				db.Proxy.Delete().Exec(context.Background())
				db.Proxy.Create().SetCluster(objC).SetClusterName(objC.Name).SetIP("*********").SetPort(8001).SetIdc("hba").SetBns("r3-test-bbc-router.test").SetDocker(0).Save(context.Background())
				db.Proxy.Create().SetCluster(objC).SetClusterName(objC.Name).SetIP("*********").SetPort(8001).SetIdc("hbb").SetBns("r3-test-bbc-router.test").SetDocker(0).Save(context.Background())
				db.Proxy.Create().SetCluster(objC).SetClusterName(objC.Name).SetIP("*********").SetPort(8001).SetIdc("hba").SetBns("r3-test-docker-router.test").SetDocker(1).Save(context.Background())
				db.Proxy.Create().SetCluster(objC).SetClusterName(objC.Name).SetIP("*********").SetPort(8001).SetIdc("hbb").SetBns("r3-test-docker-router.test").SetDocker(1).Save(context.Background())
			},
			args: args{
				clusterName: objC.Name,
			},
			wantErr: true,
		},
		{
			name: "test4: getSentinelInfo error",
			before: func() {
				db.Redis.Delete().Exec(context.Background())
				db.Redis.Create().SetCluster(objC).SetClusterName(objC.Name).SetName("r3-test_server1").SetIP("************").SetPort(7001).SetIdc("hba").SetRole("master").SetBns("r3-test-bbc-redis.test").SetDocker(0).SetMaxmemory(1).SetUsedMemory(1).Save(context.Background())
				db.Redis.Create().SetCluster(objC).SetClusterName(objC.Name).SetName("r3-test_server1").SetIP("************").SetPort(7001).SetIdc("hbb").SetRole("slave").SetBns("r3-test-bbc-redis.test").SetDocker(0).SetMaxmemory(1).SetUsedMemory(1).Save(context.Background())
				db.Redis.Create().SetCluster(objC).SetClusterName(objC.Name).SetName("r3-test_server2").SetIP("************").SetPort(7002).SetIdc("hba").SetRole("master").SetBns("r3-test-docker-redis.test").SetDocker(1).SetMaxmemory(1).SetUsedMemory(1).Save(context.Background())
				db.Redis.Create().SetCluster(objC).SetClusterName(objC.Name).SetName("r3-test_server2").SetIP("************").SetPort(7002).SetIdc("hbb").SetRole("slave").SetBns("r3-test-docker-redis.test").SetDocker(1).SetMaxmemory(1).SetUsedMemory(1).Save(context.Background())
			},
			args: args{
				clusterName: objC.Name,
			},
			wantErr: true,
		},
		{
			name: "test6: success",
			before: func() {
				db.Sentinel.Delete().Exec(context.Background())
				db.Sentinel.Create().SetCluster(objC).SetClusterName(objC.Name).SetIP("************").SetPort(9001).SetIdc("hba").SetBns("r3-test-bbc-sentinel.test").SetDocker(0).Save(context.Background())
				db.Sentinel.Create().SetCluster(objC).SetClusterName(objC.Name).SetIP("************").SetPort(9001).SetIdc("hba").SetBns("r3-test-bbc-sentinel.test").SetDocker(0).Save(context.Background())
				db.Sentinel.Create().SetCluster(objC).SetClusterName(objC.Name).SetIP("************").SetPort(9001).SetIdc("hbb").SetBns("r3-test-bbc-sentinel.test").SetDocker(0).Save(context.Background())
				db.Sentinel.Create().SetCluster(objC).SetClusterName(objC.Name).SetIP("************").SetPort(9001).SetIdc("hba").SetBns("r3-test-bbc-sentinel.test").SetDocker(1).Save(context.Background())
			},
			args: args{
				clusterName: objC.Name,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			_, _, err := getClusterModuleInfos(tt.args.clusterName)
			if (err != nil) != tt.wantErr {
				t.Errorf("getClusterModuleInfos() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

// 单测 CallbackAfterDeployCluster
func TestCallbackAfterDeployCluster(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")
	objC := env.MockCluster("callback_after_deploy")
	db, _ := mysql.Database()

	type args struct {
		stageData *ent.Stage
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test1: success",
			before: func() {
				db.Proxy.Delete().Exec(context.Background())
				db.Proxy.Create().SetCluster(objC).SetClusterName(objC.Name).SetIP("*********").SetPort(8001).SetIdc("hba").SetBns("r3-test-bbc-router.test").SetDocker(0).Save(context.Background())
				db.Proxy.Create().SetCluster(objC).SetClusterName(objC.Name).SetIP("*********").SetPort(8001).SetIdc("hbb").SetBns("r3-test-bbc-router.test").SetDocker(0).Save(context.Background())
				db.Proxy.Create().SetCluster(objC).SetClusterName(objC.Name).SetIP("*********").SetPort(8001).SetIdc("hbb").SetBns("r3-test-docker-router.test").SetDocker(1).Save(context.Background())

				db.Redis.Delete().Exec(context.Background())
				db.Redis.Create().SetCluster(objC).SetClusterName(objC.Name).SetName("r3-test_server1").SetIP("************").SetPort(7001).SetIdc("hba").SetRole("master").SetBns("r3-test-bbc-redis.test").SetDocker(0).SetMaxmemory(1).SetUsedMemory(1).Save(context.Background())
				db.Redis.Create().SetCluster(objC).SetClusterName(objC.Name).SetName("r3-test_server1").SetIP("************").SetPort(7001).SetIdc("hbb").SetRole("slave").SetBns("r3-test-bbc-redis.test").SetDocker(0).SetMaxmemory(1).SetUsedMemory(1).Save(context.Background())
				db.Redis.Create().SetCluster(objC).SetClusterName(objC.Name).SetName("r3-test_server1").SetIP("************").SetPort(7001).SetIdc("hbb").SetRole("slave").SetBns("r3-test-docker-redis.test").SetDocker(1).SetMaxmemory(1).SetUsedMemory(1).Save(context.Background())
				db.Redis.Create().SetCluster(objC).SetClusterName(objC.Name).SetName("r3-test_server2").SetIP("************").SetPort(7002).SetIdc("hba").SetRole("master").SetBns("r3-test-bbc-redis.test").SetDocker(0).SetMaxmemory(1).SetUsedMemory(1).Save(context.Background())
				db.Redis.Create().SetCluster(objC).SetClusterName(objC.Name).SetName("r3-test_server2").SetIP("************").SetPort(7002).SetIdc("hbb").SetRole("slave").SetBns("r3-test-bbc-redis.test").SetDocker(0).SetMaxmemory(1).SetUsedMemory(1).Save(context.Background())
				db.Redis.Create().SetCluster(objC).SetClusterName(objC.Name).SetName("r3-test_server2").SetIP("************").SetPort(7002).SetIdc("hbb").SetRole("slave").SetBns("r3-test-docker-redis.test").SetDocker(1).SetMaxmemory(1).SetUsedMemory(1).Save(context.Background())

				db.Sentinel.Delete().Exec(context.Background())
				db.Sentinel.Create().SetCluster(objC).SetClusterName(objC.Name).SetIP("************").SetPort(9001).SetIdc("hba").SetBns("r3-test-bbc-sentinel.test").SetDocker(0).Save(context.Background())
				db.Sentinel.Create().SetCluster(objC).SetClusterName(objC.Name).SetIP("************").SetPort(9001).SetIdc("hba").SetBns("r3-test-bbc-sentinel.test").SetDocker(0).Save(context.Background())
				db.Sentinel.Create().SetCluster(objC).SetClusterName(objC.Name).SetIP("************").SetPort(9001).SetIdc("hbb").SetBns("r3-test-bbc-sentinel.test").SetDocker(0).Save(context.Background())
				db.Sentinel.Create().SetCluster(objC).SetClusterName(objC.Name).SetIP("************").SetPort(9001).SetIdc("hbb").SetBns("r3-test-bbc-sentinel.test").SetDocker(1).Save(context.Background())

				stageRawDatas := append([]*omodel.Stage{}, migration.StageList...)

				// 创建task
				taskData, _ := db.Task.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetType(omodel.TASK_TYPE_DOCKER_MIGRATION).SetName("容器迁移").SetDescription("容器迁移").SetApplicant("jiayiming_dxm").Save(context.Background())
				// 创建Stages
				bulkArray := make([]*ent.StageCreate, len(stageRawDatas))
				emptyParams := map[string]any{}
				for i, rawData := range stageRawDatas {
					if rawData.Parameter == nil {
						rawData.Parameter = emptyParams
					}
					params, _ := json.Marshal(rawData.Parameter)
					bulkArray[i] = db.Stage.Create().
						SetTask(taskData).
						SetClusterName(objC.Name).
						SetName(rawData.Name).
						SetType(rawData.Type).
						SetSequence(i + 1).
						SetOperator("").
						SetLog("").
						SetParameter(string(params))
				}
				db.Stage.CreateBulk(bulkArray...).Save(context.Background())
			},
			args: args{
				stageData: &ent.Stage{TaskID: 1, ClusterName: objC.Name},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := CallbackAfterDeployCluster(tt.args.stageData)
			if (err != nil) != tt.wantErr {
				t.Errorf("CallbackAfterDeployCluster() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}
