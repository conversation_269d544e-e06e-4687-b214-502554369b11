package migration

import (
	"regexp"
	"testing"

	"github.com/jarcoal/httpmock"

	"dt-common/ent"
	"redis-xweb/env"
)

func TestCompleteCluster(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")
	objC := env.MockCluster("complete_cluster_test")
	_, objS := env.MockTaskWithStage(objC)

	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	tests := []struct {
		name    string
		before  func()
		args    *ent.Stage
		wantErr bool
		expect  func(*testing.T, error)
	}{
		{
			name:    "test1: request failed",
			args:    objS,
			wantErr: true,
		},
		{
			name: "test2: success",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/migration/changeEnabledAZ`),
					httpmock.ResponderFromResponse(httpmock.NewStringResponse(200, `{"code": "00000", "data": "ok"}`)))
			},
			args:    objS,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := CompleteCluster(tt.args)
			if (err != nil) != tt.wantErr {
				t.Errorf("CompleteCluster() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, err)
			}
		})
	}
}
