package migration

import (
	"encoding/json"
	"regexp"
	"testing"

	"github.com/jarcoal/httpmock"

	"dt-common/ent"
	"redis-xweb/env"
)

// 单测：实例解挂载
func TestUnmountInstance(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")
	objC := env.MockCluster("unmount_test")
	objT, objS := env.MockTaskWithStage(objC)

	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	tests := []struct {
		name    string
		before  func()
		args    *ent.Stage
		wantErr bool
		expect  func(*testing.T, error)
	}{
		{
			name: "test1: check params",
			args: func() *ent.Stage {
				paramStr := "-1"
				objS.Parameter = &paramStr
				return objS
			}(),
			wantErr: true,
		},
		{
			name: "test2: check idc",
			args: func() *ent.Stage {
				params := StageSchemaTag{}
				b, _ := json.Marshal(params)
				s := string(b)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: true,
		},
		{
			name: "test3: request failed",
			args: func() *ent.Stage {
				params := StageSchemaTag{
					IDC: "hbb",
				}
				b, _ := json.Marshal(params)
				s := string(b)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: true,
		},
		{
			name: "test4: success",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/cluster/migration/unmount`),
					httpmock.ResponderFromResponse(httpmock.NewStringResponse(200, `{"code": "00000", "data": "ok"}`)))
			},
			args: func() *ent.Stage {
				params := StageSchemaTag{
					IDC: "hbb",
				}
				b, _ := json.Marshal(params)
				s := string(b)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := UnmountInstance(tt.args)
			if (err != nil) != tt.wantErr {
				t.Errorf("UnmountInstance() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, err)
			}
		})
	}
}
