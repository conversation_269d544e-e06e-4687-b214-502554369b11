package migration

import (
	"encoding/json"
	"fmt"

	"dt-common/ent"
	"dt-common/ent/redis"
	"dt-common/errs"
	"dt-common/mysql"
	"dt-common/omodel"
	"redis-xweb/library/ras"
)

type StageSchemaSlaveOf struct {
	Type string `json:"type"`
}

func SlaveOf(stageData *ent.Stage) error {
	// 参数解析
	var params StageSchemaSlaveOf
	err := json.Unmarshal([]byte(*stageData.Parameter), &params)
	if err != nil {
		omodel.StageAppendErrorLog(stageData.ID, "failed to parse stage parameter, value=%v, error=(%v)", stageData.Parameter, err)
		return err
	}
	// 空值判断
	if params.Type == "" {
		omodel.StageAppendErrorLog(stageData.ID, "expect parameter module but got empty value")
		return fmt.Errorf("module参数错误")
	}

	// redis-cmanager renderer内部实现了
	if params.Type == "docker2docker" {
		return errs.Success
	}

	shardList, err := MigrationSlaveOf(stageData.ID, stageData.ClusterName)
	if err != nil {
		omodel.StageAppendWarnLog(stageData.ID, "%v", err)
		return err
	}

	// xweb使用dt-common中封装好ras slaveof接口的方法
	err = ras.SlaveOf(&ras.SlaveOfParams{
		StageID:   stageData.ID,
		ShardList: shardList,
	})
	if err != nil {
		omodel.StageAppendErrorLog(stageData.ID, "failed to call cmanager to do slaveof, cluster=%s, error=(%v)", stageData.ClusterName, err)
		return err
	}

	return nil
}

// 停止redis slaveof
func StopSlaveOf(stageData *ent.Stage) error {
	// 调用cmanager做slaveof
	err := ras.StopSlaveOf(&ras.BasicParams{
		StageID:     stageData.ID,
		ClusterName: stageData.ClusterName,
	})

	if err != nil {
		omodel.StageAppendErrorLog(stageData.ID, "failed to call cmanager to stop slaveof, cluster=%s, error=(%v)", stageData.ClusterName, err)
		return errs.CodeRequestFailed.Detail(err.Error())
	}

	return nil
}

// ========================================
// 				建立主从关系
// ========================================

// 异步建立容器集群和物理集群的主从关系
func MigrationSlaveOf(stageId int64, clusterName string) ([]*omodel.Shard, error) {
	db, err := mysql.Database()
	if err != nil {
		omodel.StageAppendErrorLog(stageId, "failed to get mysql connection, error=(%v)", err)
		return nil, err
	}

	// 获取容器和物理的主库，按redis name建立主从关系
	ctx, cancel := mysql.ContextWithTimeout()
	redisInstances, err := db.Redis.Query().Where(
		redis.ClusterName(clusterName),
		redis.Or(
			redis.And(redis.Idc(omodel.HBA), redis.Docker(omodel.DEPLOY_ENV_BBC)),
			redis.And(redis.Idc(omodel.HBB), redis.Docker(omodel.DEPLOY_ENV_DOCKER)),
		),
	).All(ctx)
	cancel()
	if err != nil {
		omodel.StageAppendErrorLog(stageId, "failed to query redis instances, cluster=%s, error=(%v)", clusterName, err)
		return nil, err
	}
	if len(redisInstances) == 0 {
		err = fmt.Errorf("there are no redis instances found in cluster %s", clusterName)
		omodel.StageAppendErrorLog(stageId, "%v", err)
		return nil, err
	}

	// 根据redis.name分组，hba bbc为主，hbb docker为从
	shardMap := map[string]*omodel.Shard{}
	for _, instance := range redisInstances {
		if _, exist := shardMap[instance.Name]; !exist {
			shardMap[instance.Name] = &omodel.Shard{Name: instance.Name}
		}

		switch instance.Docker {
		case omodel.DEPLOY_ENV_BBC:
			if shardMap[instance.Name].Master != nil {
				err = fmt.Errorf("shard %s of cluster %s has more than one bbc master", instance.Name, clusterName)
				omodel.StageAppendErrorLog(stageId, "%v", err)
				return nil, err
			}
			shardMap[instance.Name].Master = &omodel.Instance{IP: instance.IP, Port: instance.Port}
			continue
		case omodel.DEPLOY_ENV_DOCKER:
			if len(shardMap[instance.Name].Slaves) != 0 {
				err = fmt.Errorf("shard %s of cluster %s has more than one docker master", instance.Name, clusterName)
				omodel.StageAppendErrorLog(stageId, "%v", err)
				return nil, err
			}
			shardMap[instance.Name].Slaves = []*omodel.Instance{{IP: instance.IP, Port: instance.Port}}
			continue
		}
	}

	shards := []*omodel.Shard{}
	for k, v := range shardMap {
		// 检查是否缺数据
		if v.Master == nil || v.Master.IP == "" || v.Master.Port == 0 {
			err = fmt.Errorf("missing bbc master or field of shard %s, cluster %s", k, clusterName)
			omodel.StageAppendErrorLog(stageId, "%v", err)
			return nil, err
		}
		if len(v.Slaves) != 1 || v.Slaves[0].IP == "" || v.Slaves[0].Port == 0 {
			err = fmt.Errorf("missing docker master or field of shard %s, cluster %s", k, clusterName)
			omodel.StageAppendErrorLog(stageId, "%v", err)
			return nil, err
		}
		omodel.StageAppendInfoLog(stageId, "name=%s, %s:%d slaveof %s:%d", k, v.Slaves[0].IP, v.Slaves[0].Port, v.Master.IP, v.Master.Port)
		shards = append(shards, v)
	}

	return shards, nil
}

// ========================================
// 			 容器从库建立主从关系
//			不设回滚，但需要能停止
// ========================================

// 新扩容的容器从库与集群中的主库建立主从关系。
// - 扩容后的容器redis实例，因为迁移状态的设置，会保持slaveof 127.0.0.1的状态
func DockerSlaveOf(stageId int64, clusterName string) ([]*omodel.Shard, error) {
	db, err := mysql.Database()
	if err != nil {
		omodel.StageAppendErrorLog(stageId, "failed to get mysql connection, error=(%v)", err)
		return nil, err
	}

	// 查询容器redis实例
	ctx, cancel := mysql.ContextWithTimeout()
	redisInstances, err := db.Redis.Query().Where(
		redis.ClusterName(clusterName),
		redis.Docker(omodel.DEPLOY_ENV_DOCKER),
	).All(ctx)
	cancel()
	if err != nil {
		omodel.StageAppendErrorLog(stageId, "failed to query redis instances, cluster=%s, error=(%v)", clusterName, err)
		return nil, err
	}
	if len(redisInstances) == 0 {
		err := fmt.Errorf("no docker redis instances found in cluster %s", clusterName)
		omodel.StageAppendErrorLog(stageId, "%v", err)
		return nil, err
	}

	// 根据redis.name分组
	shardMap := map[string]*omodel.Shard{}
	for _, instance := range redisInstances {
		if _, exist := shardMap[instance.Name]; !exist {
			shardMap[instance.Name] = &omodel.Shard{}
		}

		switch instance.Role {
		case omodel.REDIS_ROLE_MASTER:
			if shardMap[instance.Name].Master != nil {
				err = fmt.Errorf("shard %s of cluster %s has more than one docker master", instance.Name, clusterName)
				omodel.StageAppendErrorLog(stageId, "%v", err)
				return nil, err
			}
			shardMap[instance.Name].Master = &omodel.Instance{IP: instance.IP, Port: instance.Port}
			continue
		case omodel.REDIS_ROLE_SLAVE:
			if len(shardMap[instance.Name].Slaves) != 0 {
				err = fmt.Errorf("shard %s of cluster %s has more than one docker slave", instance.Name, clusterName)
				omodel.StageAppendErrorLog(stageId, "%v", err)
				return nil, err
			}
			shardMap[instance.Name].Slaves = []*omodel.Instance{{IP: instance.IP, Port: instance.Port}}
			continue
		}
	}

	shards := []*omodel.Shard{}
	for k, v := range shardMap {
		// 检查是否缺数据
		if v.Master == nil || v.Master.IP == "" || v.Master.Port == 0 {
			err = fmt.Errorf("missing docker master or field of shard %s, cluster %s", k, clusterName)
			omodel.StageAppendErrorLog(stageId, "%v", err)
			return nil, err
		}
		if len(v.Slaves) != 1 || v.Slaves[0].IP == "" || v.Slaves[0].Port == 0 {
			err = fmt.Errorf("missing docker slave or field of shard %s, cluster %s", k, clusterName)
			omodel.StageAppendErrorLog(stageId, "%v", err)
			return nil, err
		}
		omodel.StageAppendInfoLog(stageId, "name=%s, %s:%d slaveof %s:%d", k, v.Slaves[0].IP, v.Slaves[0].Port, v.Master.IP, v.Master.Port)
		shards = append(shards, v)
	}

	return shards, nil
}
