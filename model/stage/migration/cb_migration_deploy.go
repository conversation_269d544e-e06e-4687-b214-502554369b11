package migration

import (
	"encoding/json"
	"fmt"
	"strings"

	"dt-common/ent"
	"dt-common/ent/cluster"
	"dt-common/ent/stage"
	"dt-common/errs"
	"dt-common/logger"
	"dt-common/mysql"
	"dt-common/omodel"
	"redis-xweb/model/stage/whitelist"
)

type IPs struct {
	BNS string
	HBA []string
	HBB []string
	HBC []string
}

type Module struct {
	Docker *IPs
	BBC    *IPs
	Port   int
}

type ModuleSentinel struct {
	Docker *IPs
	BBC    []*IPs // 兼容支付3改5，物理sentinel可能存在于两个bns里
	Port   int
}

type Cluster struct {
	Proxy    *Module
	Redis    *Module
	Sentinel *ModuleSentinel
}

// 获取proxy的bns和实例
func getProxyInfo(clusterName string, proxyList []*ent.Proxy) (*Module, error) {
	if len(proxyList) == 0 {
		err := fmt.Errorf("there is no proxy record in cluster %s", clusterName)
		logger.Warn(err.Error())
		return nil, err
	}

	proxyInfo := Module{
		Docker: &IPs{HBA: []string{}, HBB: []string{}},
		BBC:    &IPs{HBA: []string{}, HBB: []string{}},
	}
	for _, i := range proxyList {
		switch i.Docker {
		case omodel.DEPLOY_ENV_BBC:
			if i.Idc == "hba" {
				proxyInfo.BBC.HBA = append(proxyInfo.BBC.HBA, i.IP)
			} else if i.Idc == "hbb" {
				proxyInfo.BBC.HBB = append(proxyInfo.BBC.HBB, i.IP)
			} else {
				err := fmt.Errorf("undefined idc of proxy, idc=%s, proxy=%s:%d", i.Idc, i.IP, i.Port)
				logger.Warn(err.Error())
				return nil, err
			}

			if proxyInfo.BBC.BNS == "" {
				proxyInfo.BBC.BNS = i.Bns
			}
			if proxyInfo.BBC.BNS != i.Bns {
				err := fmt.Errorf("there are multiple bbc proxy bns in cluster %s", clusterName)
				logger.Warn(err.Error())
				return nil, err
			}
		case omodel.DEPLOY_ENV_DOCKER:
			if i.Idc == "hba" {
				proxyInfo.Docker.HBA = append(proxyInfo.Docker.HBA, i.IP)
			} else if i.Idc == "hbb" {
				proxyInfo.Docker.HBB = append(proxyInfo.Docker.HBB, i.IP)
			} else {
				err := fmt.Errorf("undefined idc of proxy, idc=%s, proxy=%s:%d", i.Idc, i.IP, i.Port)
				logger.Warn(err.Error())
				return nil, err
			}

			if proxyInfo.Docker.BNS == "" {
				proxyInfo.Docker.BNS = i.Bns
			}
			if proxyInfo.Docker.BNS != i.Bns {
				err := fmt.Errorf("there are multiple docker proxy bns in cluster %s", clusterName)
				logger.Warn(err.Error())
				return nil, err
			}
		default:
			err := fmt.Errorf("undefined env of proxy, docker=%d, proxy=%s:%d", i.Docker, i.IP, i.Port)
			logger.Warn(err.Error())
			return nil, err
		}
	}

	if proxyInfo.BBC.BNS == "" || proxyInfo.Docker.BNS == "" {
		err := fmt.Errorf("there is no bbc or docker proxy bns in cluster %s", clusterName)
		logger.Warn(err.Error())
		return nil, err
	}

	return &proxyInfo, nil
}

// 获取redis的bns和实例
func getRedisInfo(clusterName string, redisList []*ent.Redis) (*Module, error) {
	if len(redisList) == 0 {
		err := fmt.Errorf("there is no redis record in cluster %s", clusterName)
		logger.Warn(err.Error())
		return nil, err
	}

	redisInfo := Module{
		Docker: &IPs{},
		BBC:    &IPs{},
	}
	// 从库关停、主从切换、slaveof都不需要额外参数，只需要bns加白即可
	for _, i := range redisList {
		switch i.Docker {
		case omodel.DEPLOY_ENV_BBC:
			if redisInfo.BBC.BNS == "" {
				redisInfo.BBC.BNS = i.Bns
			}
			if redisInfo.BBC.BNS != i.Bns {
				err := fmt.Errorf("there are multiple bbc redis bns in cluster %s", clusterName)
				logger.Warn(err.Error())
				return nil, err
			}
		case omodel.DEPLOY_ENV_DOCKER:
			if redisInfo.Docker.BNS == "" {
				redisInfo.Docker.BNS = i.Bns
			}
			if redisInfo.Docker.BNS != i.Bns {
				err := fmt.Errorf("there are multiple docker redis bns in cluster %s", clusterName)
				logger.Warn(err.Error())
				return nil, err
			}
		default:
			err := fmt.Errorf("undefined env of redis, docker=%d, redis=%s:%d", i.Docker, i.IP, i.Port)
			logger.Warn(err.Error())
			return nil, err
		}
	}

	if redisInfo.BBC.BNS == "" || redisInfo.Docker.BNS == "" {
		err := fmt.Errorf("there is no bbc or docker redis bns in cluster %s", clusterName)
		logger.Warn(err.Error())
		return nil, err
	}

	return &redisInfo, nil
}

// 获取sentinel的bns和实例
func getSentinelInfo(clusterName string, sentinelList []*ent.Sentinel) (*ModuleSentinel, error) {
	if len(sentinelList) == 0 {
		err := fmt.Errorf("there is no sentinel record in cluster %s", clusterName)
		logger.Warn(err.Error())
		return nil, err
	}

	sentinelInfo := ModuleSentinel{
		Docker: &IPs{HBA: []string{}, HBB: []string{}, HBC: []string{}},
		BBC:    []*IPs{},
	}
	for _, i := range sentinelList {
		switch i.Docker {
		case omodel.DEPLOY_ENV_BBC:
			// 先判断bns
			var idx int
			for idx = 0; idx < len(sentinelInfo.BBC); idx++ {
				if sentinelInfo.BBC[idx].BNS == i.Bns {
					break
				}
			}
			if idx >= len(sentinelInfo.BBC) {
				sentinelInfo.Port = i.Port
				sentinelInfo.BBC = append(sentinelInfo.BBC, &IPs{HBA: []string{}, HBB: []string{}, HBC: []string{}})
				sentinelInfo.BBC[idx].BNS = i.Bns
			}

			switch i.Idc {
			case "hba":
				sentinelInfo.BBC[idx].HBA = append(sentinelInfo.BBC[idx].HBA, i.IP)
			case "hbb":
				sentinelInfo.BBC[idx].HBB = append(sentinelInfo.BBC[idx].HBB, i.IP)
			case "hbc":
				sentinelInfo.BBC[idx].HBC = append(sentinelInfo.BBC[idx].HBC, i.IP)
			default:
				err := fmt.Errorf("undefined idc of sentinel, idc=%s, sentinel=%s:%d", i.Idc, i.IP, i.Port)
				logger.Warn(err.Error())
				return nil, err
			}
		case omodel.DEPLOY_ENV_DOCKER:
			switch i.Idc {
			case "hba":
				sentinelInfo.Docker.HBA = append(sentinelInfo.Docker.HBA, i.IP)
			case "hbb":
				sentinelInfo.Docker.HBB = append(sentinelInfo.Docker.HBB, i.IP)
			case "hbc":
				sentinelInfo.Docker.HBC = append(sentinelInfo.Docker.HBC, i.IP)
			default:
				err := fmt.Errorf("undefined idc of sentinel, idc=%s, sentinel=%s:%d", i.Idc, i.IP, i.Port)
				logger.Warn(err.Error())
				return nil, err
			}

			if sentinelInfo.Docker.BNS == "" {
				sentinelInfo.Docker.BNS = i.Bns
			}
			if sentinelInfo.Docker.BNS != i.Bns {
				err := fmt.Errorf("there are multiple docker sentinel bns in cluster %s", clusterName)
				logger.Warn(err.Error())
				return nil, err
			}
		default:
			err := fmt.Errorf("undefined env of proxy, docker=%d, proxy=%s:%d", i.Docker, i.IP, i.Port)
			logger.Warn(err.Error())
			return nil, err
		}
	}

	if len(sentinelInfo.BBC) == 0 || sentinelInfo.Docker.BNS == "" {
		err := fmt.Errorf("there is no bbc or docker sentinel bns in cluster %s", clusterName)
		logger.Warn(err.Error())
		return nil, err
	}

	// 排序，siod-redis的bns放前面
	if len(sentinelInfo.BBC) == 2 && !strings.HasSuffix(sentinelInfo.BBC[0].BNS, "siod-redis") {
		sentinelInfo.BBC[0], sentinelInfo.BBC[1] = sentinelInfo.BBC[1], sentinelInfo.BBC[0]
	}

	return &sentinelInfo, nil
}

// 以实例维度整理集群数据
func getClusterModuleInfos(clusterName string) (*ent.Cluster, *Cluster, error) {
	db, err := mysql.Database()
	if err != nil {
		logger.Error("failed to get mysql connection, error=(%v)", err)
		return nil, nil, errs.CodeDatabase.Detail("数据库连接获取失败")
	}

	// 获取集群和实例信息
	ctx, cancel := mysql.ContextWithTimeout()
	clusterData, err := db.Cluster.Query().
		Where(cluster.Name(clusterName)).
		WithRedis().WithProxy().WithSentinel().
		Only(ctx)
	cancel()
	if err != nil {
		logger.Error("failed to query Cluster with proxy/redis/sentinel, cluster=%s, error=(%v)", clusterName, err)
		return nil, nil, errs.CodeDatabase.Detail("集群信息查询失败")
	}

	proxyInfo, err := getProxyInfo(clusterName, clusterData.Edges.Proxy)
	if err != nil {
		return nil, nil, err
	}
	redisInfo, err := getRedisInfo(clusterName, clusterData.Edges.Redis)
	if err != nil {
		return nil, nil, err
	}
	sentinelInfo, err := getSentinelInfo(clusterName, clusterData.Edges.Sentinel)
	if err != nil {
		return nil, nil, err
	}

	return clusterData, &Cluster{Proxy: proxyInfo, Redis: redisInfo, Sentinel: sentinelInfo}, nil
}

// 容器集群部署完成后更新stage的参数
// 先将hbb的实例迁进hbb的容器
func CallbackAfterDeployCluster(stageData *ent.Stage) error {
	// 获取所属任务的所有阶段
	db, err := mysql.Database()
	if err != nil {
		logger.Error("failed to get mysql connection, error=(%v)", err)
		return err
	}
	// 查询task & stage
	ctx, cancel := mysql.ContextWithTimeout()
	stages, err := db.Stage.Query().Where(stage.TaskID(stageData.TaskID)).All(ctx)
	cancel()
	if err != nil {
		logger.Error("failed to query Task with Stages, taskId=%d, error=(%v)", stageData.TaskID, err)
		return err
	}

	clusterData, moduleInfo, err := getClusterModuleInfos(stageData.ClusterName)
	if err != nil {
		return err
	}

	logger.Debug("moduleInfo.sentinel.hba", moduleInfo.Sentinel.BBC[0].HBA)
	logger.Debug("moduleInfo.sentinel.hbb", moduleInfo.Sentinel.BBC[0].HBB)

	for i := range stages {
		s := stages[i]
		var params map[string]any
		switch s.Name {
		case "物理实例新增白名单":
			params = map[string]any{
				"type":   whitelist.WhitelistTypeManagement,
				"docker": omodel.DEPLOY_ENV_BBC,
				"action": "add",
				"whitelistBns": []string{
					moduleInfo.Proxy.Docker.BNS,
					moduleInfo.Redis.Docker.BNS,
				},
			}
		case "容器实例新增白名单":
			params = map[string]any{
				"type":   whitelist.WhitelistTypeManagement,
				"docker": omodel.DEPLOY_ENV_DOCKER,
				"action": "add",
				"whitelistBns": []string{
					moduleInfo.Proxy.BBC.BNS,
					moduleInfo.Redis.BBC.BNS,
				},
			}
		case "容器实例清理白名单":
			params = map[string]any{
				"type":   whitelist.WhitelistTypeManagement,
				"docker": omodel.DEPLOY_ENV_DOCKER,
				"action": "del",
				"whiteListBns": []string{
					moduleInfo.Proxy.BBC.BNS,
					moduleInfo.Redis.BBC.BNS,
				},
			}
		case "Smart扩容":
			params = map[string]any{
				"group":         clusterData.Smart,
				"targetAppList": []string{moduleInfo.Proxy.Docker.BNS, moduleInfo.Proxy.BBC.BNS},
				"sourceAppList": []string{moduleInfo.Proxy.BBC.BNS},
			}
		case "Smart缩容":
			params = map[string]any{
				"group":         clusterData.Smart,
				"targetAppList": []string{moduleInfo.Proxy.Docker.BNS},
				"sourceAppList": []string{moduleInfo.Proxy.Docker.BNS, moduleInfo.Proxy.BBC.BNS},
			}
		// proxy接流量
		case "HBB机房流量接入容器":
			params = map[string]any{
				"bns": moduleInfo.Proxy.Docker.BNS,
				"idc": "hbb",
			}
		case "HBA机房流量接入容器":
			params = map[string]any{
				"bns": moduleInfo.Proxy.Docker.BNS,
				"idc": "hba",
			}
		case "业务流量接入容器":
			params = map[string]any{
				"bns": moduleInfo.Proxy.Docker.BNS,
			}
		// 关proxy
		case "HBB机房物理Proxy关停":
			params = map[string]any{
				"module": "proxy",
				"bns":    moduleInfo.Proxy.BBC.BNS,
				"ips":    moduleInfo.Proxy.BBC.HBB,
			}
		case "HBA机房物理Proxy关停":
			params = map[string]any{
				"module": "proxy",
				"bns":    moduleInfo.Proxy.BBC.BNS,
				"ips":    moduleInfo.Proxy.BBC.HBA,
			}
		case "物理Proxy关停": // pay
			params = map[string]any{
				"module": "proxy",
				"bns":    moduleInfo.Proxy.BBC.BNS,
				"ips":    append(moduleInfo.Proxy.BBC.HBB, moduleInfo.Proxy.BBC.HBA...),
			}

		// 关sentinel
		case "HBB机房容器Sentinel关停":
			params = map[string]any{
				"module": "sentinel",
				"bns":    moduleInfo.Sentinel.Docker.BNS,
				"ips":    moduleInfo.Sentinel.Docker.HBB, // 先退hbb，就会先创建hbb
			}
		case "HBB机房容器Sentinel启动":
			params = map[string]any{
				"module": "sentinel",
				"bns":    moduleInfo.Sentinel.Docker.BNS,
				"ips":    moduleInfo.Sentinel.Docker.HBB,
			}
		case "容器Sentinel关停":
			params = map[string]any{
				"module": "sentinel",
				"bns":    moduleInfo.Sentinel.Docker.BNS,
				"ips":    append(moduleInfo.Sentinel.Docker.HBB, moduleInfo.Sentinel.Docker.HBA...),
			}
		case "容器Sentinel启动":
			params = map[string]any{
				"module": "sentinel",
				"bns":    moduleInfo.Sentinel.Docker.BNS,
				"ips":    append(moduleInfo.Sentinel.Docker.HBB, moduleInfo.Sentinel.Docker.HBA...),
			}
		case "HBB机房物理Sentinel关停":
			params = map[string]any{
				"module": "sentinel",
				"bns":    moduleInfo.Sentinel.BBC[0].BNS,
				"ips":    moduleInfo.Sentinel.BBC[0].HBB, // 关1个
			}
		case "HBA机房物理Sentinel关停":
			params = map[string]any{
				"module": "sentinel",
				"bns":    moduleInfo.Sentinel.BBC[0].BNS,
				"ips":    moduleInfo.Sentinel.BBC[0].HBA,
			}
		case "物理Sentinel关停(pay)":
			params = map[string]any{
				"module": "sentinel",
				"bns":    moduleInfo.Sentinel.BBC[1].BNS,
				"ips":    append(moduleInfo.Sentinel.BBC[1].HBB, moduleInfo.Sentinel.BBC[1].HBA...),
			}
		case "2/3物理Sentinel关停(siod-redis)":
			params = map[string]any{
				"module": "sentinel",
				"bns":    moduleInfo.Sentinel.BBC[0].BNS,
				"ips":    append(moduleInfo.Sentinel.BBC[0].HBB, moduleInfo.Sentinel.BBC[0].HBA[0]),
			}
		case "1/3物理Sentinel关停(siod-redis)":
			params = map[string]any{
				"module": "sentinel",
				"bns":    moduleInfo.Sentinel.BBC[0].BNS,
				"ips":    moduleInfo.Sentinel.BBC[0].HBA,
			}
		case "更新SentinelQuorum":
			params = map[string]any{
				"bns":    moduleInfo.Sentinel.BBC[0].BNS,
				"quorum": 2,
			}

		// 从库替换
		case "建立主从关系（物理<-容器）":
			params = map[string]any{
				"type": "docker2bbc",
			}
		case "HBB机房物理从库关停":
			params = map[string]any{
				"bns": moduleInfo.Redis.BBC.BNS,
				"idc": "hbb",
			}
		case "HBA机房物理从库关停":
			params = map[string]any{
				"bns": moduleInfo.Redis.BBC.BNS,
				"idc": "hba", // 再退hba
			}

		case "主从切换":
			params = map[string]any{
				"docker":        omodel.DEPLOY_ENV_BBC, // 将主库从指定环境切走（如果有）
				"maxConcurnecy": 0,                     // 默认按ip计算并发
				"intervalTime":  120,                   // 默认在分片完成切换后等待120s
			}

		// 解挂载
		case "BNS实例缩容(HBB)":
			params = map[string]any{
				"idc": "hbb", // 先退hbb
			}
		case "BNS实例缩容(HBA)":
			params = map[string]any{
				"idc": "hba",
			}

		case "清理Sentinel物理监控":
			params = map[string]any{
				"module": "sentinel",
				"bns":    moduleInfo.Sentinel.BBC[0].BNS,
				"port":   moduleInfo.Sentinel.Port,
			}
		default:
			continue
		}

		paramBytes, err := json.Marshal(params)
		if err != nil {
			err = fmt.Errorf("failed to marshal stage parameter, value=%v", params)
			logger.Error(err.Error())
			return err
		}

		// 保存参数
		ctx, cancel := mysql.ContextWithTimeout()
		_, err = s.Update().SetParameter(string(paramBytes)).Save(ctx)
		cancel()
		if err != nil {
			err = fmt.Errorf("failed to update Stage.Parameter, stageId=%d, error=(%v)", stageData.ID, err)
			logger.Error(err.Error())
			return err
		}
	}

	return nil
}
