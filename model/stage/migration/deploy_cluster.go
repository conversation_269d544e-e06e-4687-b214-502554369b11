package migration

import (
	"dt-common/ent"
	"dt-common/logger"
	"redis-xweb/library/ras"
)

// 扩容HBA实例使容器集群完整
func CompleteCluster(stageData *ent.Stage) error {
	err := ras.ChangeClusterEnabledAZ(&ras.ChangeEnabledAZParams{
		StageID:     stageData.ID,
		ClusterName: stageData.ClusterName,
		EnabledAZ:   []string{"hba", "hbb", "hbc"},
	})
	if err != nil {
		logger.Error("failed to complete container cluster, params=%v, error=(%v)", stageData.ClusterName, err)
		return err
	}

	return nil
}
