package migration

import (
	"context"
	"encoding/json"
	"regexp"
	"testing"

	"github.com/jarcoal/httpmock"

	"dt-common/ent"
	"dt-common/mysql"
	"dt-common/omodel"
	"redis-xweb/env"
)

// 单测：关停从库
func TestStopSlaves(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")
	objC := env.MockCluster("redis_stop_test")
	objT, objS := env.MockTaskWithStage(objC)

	tests := []struct {
		name    string
		before  func()
		args    *ent.Stage
		wantErr bool
		expect  func(*testing.T, error)
	}{
		{
			name: "test1: check params",
			args: func() *ent.Stage {
				params := ""
				objS.Parameter = &params
				return objS
			}(),
			wantErr: true,
		},
		{
			name: "test2: check bns",
			args: func() *ent.Stage {
				params := StageSchemaOperateRedis{
					BNS: "",
				}
				b, _ := json.Marshal(params)
				s := string(b)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: true,
		},
		{
			name: "test3: check idc",
			args: func() *ent.Stage {
				params := StageSchemaOperateRedis{
					BNS: "unit.test",
				}
				b, _ := json.Marshal(params)
				s := string(b)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: true,
		},
		{
			name: "test4: no result",
			args: func() *ent.Stage {
				params := StageSchemaOperateRedis{
					BNS: "unit.test",
					IDC: "hbb",
				}
				b, _ := json.Marshal(params)
				s := string(b)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: true,
		},
		{
			name: "test5: request failed",
			before: func() {
				db, _ := mysql.Database()
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetBns("r3-test-redis.loan-mixed-b").SetIP("************").SetPort(7000).SetIdc("hba").SetName("server1").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).Save(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetBns("r3-test-redis.loan-mixed-b").SetIP("************").SetPort(7000).SetIdc("hbb").SetName("server1").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).Save(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetBns("r3-test-redis.loan-mixed-b").SetIP("************").SetPort(7001).SetIdc("hba").SetName("server2").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).Save(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetBns("r3-test-redis.loan-mixed-b").SetIP("************").SetPort(7001).SetIdc("hbb").SetName("server2").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).Save(context.Background())
			},
			args: func() *ent.Stage {
				obj := StageSchemaOperateRedis{
					BNS: "unit.test",
					IDC: "hbb",
				}
				params, _ := json.Marshal(obj)
				s := string(params)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: true,
		},
		{
			name: "test6: success",
			before: func() {
				httpmock.Activate()
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/process/slave/stop`),
					httpmock.ResponderFromResponse(httpmock.NewStringResponse(200, `{"code": "00000", "data": "ok"}`)))
			},
			args: func() *ent.Stage {
				params := StageSchemaOperateRedis{
					BNS: "r3-test-redis.loan-mixed-b",
					IDC: "hbb",
				}
				b, _ := json.Marshal(params)
				s := string(b)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := StopSlaves(tt.args)
			if (err != nil) != tt.wantErr {
				t.Errorf("StopSlaves() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, err)
			}
		})
	}
	httpmock.Reset()
}

// 单测：启动从库
func TestStartSlaves(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")
	objC := env.MockCluster("slave_start_test")
	objT, objS := env.MockTaskWithStage(objC)

	tests := []struct {
		name    string
		before  func()
		args    *ent.Stage
		wantErr bool
		expect  func(*testing.T, error)
	}{
		{
			name: "test1: check params",
			args: func() *ent.Stage {
				params := ""
				objS.Parameter = &params
				return objS
			}(),
			wantErr: true,
		},
		{
			name: "test2: check bns",
			args: func() *ent.Stage {
				params := StageSchemaOperateRedis{
					BNS: "",
				}
				b, _ := json.Marshal(params)
				s := string(b)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: true,
		},
		{
			name: "test3: check instances",
			args: func() *ent.Stage {
				params := StageSchemaOperateRedis{
					BNS: "unit.test",
				}
				b, _ := json.Marshal(params)
				s := string(b)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: true,
		},
		{
			name: "test4: request failed",
			args: func() *ent.Stage {
				obj := StageSchemaOperateRedis{
					BNS: "unit.test",
					Instances: []*omodel.Instance{
						{IP: "************", Port: 7000, IDC: "hba"},
					},
				}
				params, _ := json.Marshal(obj)
				s := string(params)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: true,
		},
		{
			name: "test5: success",
			before: func() {
				httpmock.Activate()
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/process/slave/start`),
					httpmock.ResponderFromResponse(httpmock.NewStringResponse(200, `{"code": "00000", "data": "ok"}`)))
			},
			args: func() *ent.Stage {
				params := StageSchemaOperateRedis{
					BNS: "r3-test-redis.loan-mixed-b",
					Instances: []*omodel.Instance{
						{IP: "************", Port: 7000, IDC: "hba"},
					},
				}
				b, _ := json.Marshal(params)
				s := string(b)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := StartSlaves(tt.args)
			if (err != nil) != tt.wantErr {
				t.Errorf("StartSlaves() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, err)
			}
		})
	}
	httpmock.Reset()
}
