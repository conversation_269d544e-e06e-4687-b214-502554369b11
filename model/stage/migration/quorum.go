package migration

import (
	"encoding/json"
	"fmt"

	"dt-common/ent"
	"dt-common/errs"
	"dt-common/omodel"
	"redis-xweb/library/ras"
)

type UpdateQuorumParams struct {
	BNS    string `json:"bns"`
	Quorum int    `json:"quorum"`
}

// 更新集群sentinel的quorum投票人数
func UpdateQuorum(stageData *ent.Stage) error {
	// 参数解析
	var params UpdateQuorumParams
	err := json.Unmarshal([]byte(*stageData.Parameter), &params)
	if err != nil {
		omodel.StageAppendErrorLog(stageData.ID, "failed to parse stage parameter, value=%v, error=(%v)", stageData.Parameter, err)
		return errs.CodeUnmarshalError
	}
	// 空值判断
	if params.BNS == "" {
		omodel.StageAppendErrorLog(stageData.ID, "stage parameter bns is empty")
		return fmt.Errorf("bns参数为空")
	}

	// 请求cmanager进行quorum设置
	err = ras.UpdateQuorum(&ras.QuorumParams{
		StageID: stageData.ID,
		BNS:     params.BNS,
		Quorum:  params.Quorum,
	})
	if err != nil {
		omodel.StageAppendErrorLog(stageData.ID, "failed to call manager to update quorum, clusterName=%s, error=(%v)", stageData.ClusterName, err)
		return err
	}

	return nil
}
