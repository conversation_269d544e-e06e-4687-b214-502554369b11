package migration

import (
	"dt-common/ent"
	"dt-common/ent/cluster"
	"dt-common/ent/proxy"
	"dt-common/errs"
	"dt-common/logger"
	"dt-common/mysql"
	"dt-common/omodel"
	"redis-xweb/library/billing"
	"redis-xweb/library/errc"
)

type StageSchemaChangeBill struct {
	Type string `json:"type"`
}

// ===================================================
//			   第一阶段，退还半边的BBC之后
//	   		  容器订单建一半，物理订单缩一半
// ===================================================

// 转换一半的物理套餐到容器套餐。
// 注：如果有调整分片大小的需求，应在启动迁移之前完成，同时更新cluster.storage_size字段
func TransformHalfBill(stageData *ent.Stage) error {
	db, err := mysql.Database()
	if err != nil {
		logger.Error("failed to get mysql connection, error=(%v)", err)
		return errs.CodeDatabase.Detail("数据库连接获取失败")
	}

	// 查询集群信息
	ctx, cancel := mysql.ContextWithTimeout()
	clusterData, err := db.Cluster.Query().Where(cluster.Name(stageData.ClusterName)).Only(ctx)
	cancel()
	if err != nil {
		omodel.StageAppendErrorLog(stageData.ID, "failed to query Cluster, cluster=%s, error=(%v)", stageData.ClusterName, err)
		return errs.CodeDatabase.Detail("集群信息查询失败")
	}
	if clusterData.Docker != omodel.DEPLOY_ENV_MIGRATING {
		omodel.StageAppendErrorLog(stageData.ID, "Cluster.Docker expect %d, but %d", omodel.DEPLOY_ENV_MIGRATING, clusterData.Docker)
		return errc.CodeClusterAbnormal.Detail("集群迁移状态有误，不能转换套餐")
	}
	// 查询容器proxy数量
	ctx, cancel = mysql.ContextWithTimeout()
	proxyNum, err := db.Proxy.Query().Where(proxy.ClusterName(stageData.ClusterName), proxy.Docker(omodel.DEPLOY_ENV_DOCKER)).Count(ctx)
	cancel()
	if err != nil {
		omodel.StageAppendErrorLog(stageData.ID, "failed to query docker proxy num, cluster=%s, error=(%v)", stageData.ClusterName, err)
		return errs.CodeDatabase.Detail("Proxy数量查询失败")
	}
	if proxyNum == 0 {
		omodel.StageAppendErrorLog(stageData.ID, "Cluster has 0 proxy record in db")
		return errc.CodeClusterAbnormal.Detail("未查询到Proxy")
	}

	// 转组合套餐，半边机房
	err = billing.DockerTransform(&billing.CreateOrderParams{
		ClusterName: clusterData.Name,
		ProxyNum:    proxyNum,
		ShardNum:    float64(clusterData.ShardNum) / 2,    // 分片数如果是奇数，按整型计算亏半个分片钱，所以改成float
		StorageSize: float64(clusterData.StorageSize) / 2, // 总内存的一半在物理账单，一半在容器账单
		SentinelNum: 1,                                    // hbb只部署1个sentinel
		InMigration: true,
	})
	if err != nil {
		omodel.StageAppendErrorLog(stageData.ID, "failed to create docker cluster bill, cluster=%s, error=(%v)", stageData.ClusterName, err)
		return errs.CodeBillCenterRequestFailed.Detail("容器订单创建失败")
	}
	omodel.StageAppendInfoLog(stageData.ID, "succeed to create docker billing order, proxy num is %d, storage size is %dGB, shard num is %0.1f", proxyNum, clusterData.StorageSize/2, float64(clusterData.ShardNum)/2)

	// 同步任务返回 errs.Success
	return errs.Success
}

// ===================================================
// 				  第二阶段，迁移完成之后
// 			   容器订单补完整，物理订单全退还
// ===================================================

// 集群账单从物理套餐改为容器套餐
// 1、退还物理账单，2、扩一倍容器账单
func TransformDockerBill(stageData *ent.Stage) error {
	// 更新cluster.Docker -> 1
	db, err := mysql.Database()
	if err != nil {
		logger.Error("failed to get mysql connection, error=(%v)", err)
		return errs.CodeDatabase.Detail("数据库连接获取失败")
	}
	ctx, cancel := mysql.ContextWithTimeout()
	err = db.Cluster.Update().
		SetDocker(omodel.DEPLOY_ENV_DOCKER).SetProductLine("siod-redis").
		Where(cluster.Name(stageData.ClusterName), cluster.Docker(omodel.DEPLOY_ENV_MIGRATING)).
		Exec(ctx)
	cancel()
	if err != nil {
		logger.Error("failed to update cluster %s metadata, docker=%d, productLine=siod-redis, error=(%v)", stageData.ClusterName, omodel.DEPLOY_ENV_DOCKER, err)
		return errs.CodeDatabase.Detail("元数据更新失败")
	}
	omodel.StageAppendInfoLog(stageData.ID, "succeed to update cluster metadata, change productLine to siod-redis and set docker %d", omodel.DEPLOY_ENV_DOCKER)

	// 查询集群信息
	ctx, cancel = mysql.ContextWithTimeout()
	clusterData, err := db.Cluster.Query().Where(cluster.Name(stageData.ClusterName)).Only(ctx)
	cancel()
	if err != nil {
		omodel.StageAppendErrorLog(stageData.ID, "failed to query Cluster, cluster=%s, error=(%v)", stageData.ClusterName, err)
		return errs.CodeDatabase.Detail("集群信息查询失败")
	}
	if clusterData.Docker != omodel.DEPLOY_ENV_DOCKER {
		omodel.StageAppendErrorLog(stageData.ID, "Cluster.Docker expect 1, but %d", clusterData.Docker)
		return errc.CodeClusterAbnormal.Detail("非容器集群，不能转换为容器订单")
	}
	// 查询容器proxy数量
	ctx, cancel = mysql.ContextWithTimeout()
	proxyNum, err := db.Proxy.Query().Where(proxy.ClusterName(stageData.ClusterName), proxy.Docker(omodel.DEPLOY_ENV_DOCKER)).Count(ctx)
	cancel()
	if err != nil {
		omodel.StageAppendErrorLog(stageData.ID, "failed to query docker proxy num, cluster=%s, error=(%v)", stageData.ClusterName, err)
		return errs.CodeDatabase.Detail("Proxy数量查询失败")
	}
	if proxyNum == 0 {
		omodel.StageAppendErrorLog(stageData.ID, "Cluster has 0 proxy record in db")
		return errc.CodeClusterAbnormal.Detail("未查询到Proxy")
	}

	// 容器套餐扩一倍
	err = billing.DockerTransform(&billing.CreateOrderParams{
		ClusterName: clusterData.Name,
		ProxyNum:    proxyNum,
		ShardNum:    float64(clusterData.ShardNum),    // 分片数如果是奇数，按整型计算亏半个分片钱，所以改成float
		StorageSize: float64(clusterData.StorageSize), // 总内存的一半在物理账单，一半在容器账单
		SentinelNum: 3,                                // hbb只部署1个sentinel
		InMigration: false,
	})
	if err != nil {
		omodel.StageAppendErrorLog(stageData.ID, "failed to complete docker bill, cluster=%s, error=(%v)", stageData.ClusterName, err)
		return errs.CodeBillCenterRequestFailed.Detail("容器订单更新失败")
	}
	omodel.StageAppendInfoLog(stageData.ID, "succeed to complete docker billing order, proxy num is %d, storage size is %dGB, shard num is %d", proxyNum, clusterData.StorageSize, clusterData.ShardNum)

	// 同步任务返回 errs.Success
	return errs.Success
}
