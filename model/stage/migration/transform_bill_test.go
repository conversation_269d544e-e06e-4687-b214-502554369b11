package migration

import (
	"context"
	"regexp"
	"testing"

	"github.com/jarcoal/httpmock"

	"dt-common/ent"
	"dt-common/ent/cluster"
	"dt-common/errs"
	"dt-common/mysql"
	"redis-xweb/env"
)

// 单测：物理订单转一半到容器订单
func TestTransformHalfBill(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")
	objC := env.MockCluster("billing_desc_test")
	_, objS := env.MockTaskWithStage(objC)
	db, _ := mysql.Database()

	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	tests := []struct {
		name    string
		before  func()
		args    *ent.Stage
		wantErr bool
		expect  func(*testing.T, error)
	}{
		{
			name: "test1: cluster data has wrong docker field",
			before: func() {
				db.Cluster.Update().SetDocker(1).Where(cluster.ID(objC.ID)).Exec(context.Background())
			},
			args:    objS,
			wantErr: true,
		},
		{
			name: "test2: cluster has no proxy",
			before: func() {
				db.Cluster.Update().SetDocker(2).SetStorageSize(20).SetShardNum(4).SetProxyNum(4).Where(cluster.ID(objC.ID)).Exec(context.Background())
			},
			args:    objS,
			wantErr: true,
		},
		{
			name: "test3: request failed",
			before: func() {
				db.Proxy.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test-router.siod-redis").SetIP("************").SetPort(8001).SetIdc("hba").Save(context.Background())
				db.Proxy.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test-router.siod-redis").SetIP("************").SetPort(8001).SetIdc("hbb").Save(context.Background())
			},
			args:    objS,
			wantErr: true,
		},
		{
			name: "test4: success",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/api/billing/order/info`),
					httpmock.ResponderFromResponse(httpmock.NewStringResponse(200, "{\"errno\":0,\"errmsg\":\"success\",\"data\":[{\"resourceName\":\"REDIS\",\"resourceNameCh\":\"缓存数据库\",\"resourceId\":100049,\"resourceType\":\"\",\"comboName\":\"redis-科技云-单分片\",\"comboId\":21,\"comboCode\":100049985621,\"comboDesc\":\"\",\"comboUnitInfo\":\"\",\"unitPrice\":0,\"departCode\":1000000037,\"department\":\"系统运维部\",\"team\":\"\",\"useType\":0,\"business\":[{\"name\":\"理财\",\"percent\":100}],\"product\":[],\"isAckPercentMonthly\":false,\"percentDesc\":\"\",\"isUseAuthPercent\":false,\"authPercentName\":\"\",\"authPercentCode\":\"\",\"busReview\":null,\"tag\":null,\"cloudName\":\"\",\"cloudLabel\":\"dev\",\"desc\":\"redis: billing_desc_test\",\"RDOwner\":\"\",\"OPOwner\":\"\",\"zoneType\":\"\",\"idc\":\"\",\"useTypeName\":\"普通服务\",\"useForProduct\":\"\",\"staticQuantity\":null,\"id\":\"65f94c5bff1b3ecb5fcb7d0e\",\"instanceId\":\"billing_desc_test\",\"snapInfo\":{\"num\":1},\"costId\":\"\",\"monthPrice\":230,\"orderCode\":\"1000492168276591\",\"owner\":\"jiayiming_dxm\",\"startTime\":\"2024-03-19T16:27:07.653+08:00\",\"endTime\":\"0001-01-01T08:05:43+08:05\",\"createdAt\":\"2024-03-19T16:27:07.684+08:00\",\"updateAt\":\"2024-03-19T16:27:07.684+08:00\",\"status\":1,\"parentCode\":\"\",\"busPercentStatus\":0,\"instanceName\":\"\",\"shortId\":\"redis-57im2gwy4ab\"}]}")))
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/api/billing/order/modify`),
					httpmock.ResponderFromResponse(httpmock.NewStringResponse(200, "{\"errno\":0,\"errmsg\":\"success\",\"data\":{}}")))
			},
			args:    objS,
			wantErr: true,
			expect: func(t *testing.T, err error) {
				if err != errs.Success {
					t.Errorf("unexpected err %v", err)
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := TransformHalfBill(tt.args)
			if (err != nil) != tt.wantErr {
				t.Errorf("TransformHalfBill() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, err)
			}
		})
	}
}

// 单测：物理订单全部转成容器订单
func TestTransformDockerBill(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")
	objC := env.MockCluster("billing_transform_test")
	_, objS := env.MockTaskWithStage(objC)
	db, _ := mysql.Database()

	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	tests := []struct {
		name    string
		before  func()
		args    *ent.Stage
		wantErr bool
		expect  func(*testing.T, error)
	}{
		{
			name: "test1: cluster data has wrong docker field",
			before: func() {
				db.Cluster.Update().SetDocker(0).Where(cluster.ID(objC.ID)).Exec(context.Background())
			},
			args:    objS,
			wantErr: true,
		},
		{
			name: "test2: cluster has no proxy",
			before: func() {
				db.Cluster.Update().SetDocker(1).SetStorageSize(20).SetShardNum(4).SetProxyNum(4).Where(cluster.ID(objC.ID)).Exec(context.Background())
			},
			args:    objS,
			wantErr: true,
		},
		{
			name: "test3: request failed",
			before: func() {
				db.Proxy.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test-router.siod-redis").SetIP("************").SetPort(8001).SetIdc("hba").Save(context.Background())
				db.Proxy.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test-router.siod-redis").SetIP("************").SetPort(8001).SetIdc("hba").Save(context.Background())
				db.Proxy.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test-router.siod-redis").SetIP("************").SetPort(8001).SetIdc("hbb").Save(context.Background())
				db.Proxy.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test-router.siod-redis").SetIP("************").SetPort(8001).SetIdc("hbb").Save(context.Background())
			},
			args:    objS,
			wantErr: true,
		},
		{
			name: "test5: success",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/api/billing/order/info`),
					httpmock.ResponderFromResponse(httpmock.NewStringResponse(200, "{\"errno\":0,\"errmsg\":\"success\",\"data\":[{\"resourceName\":\"REDIS\",\"resourceNameCh\":\"缓存数据库\",\"resourceId\":100049,\"resourceType\":\"\",\"comboName\":\"redis-科技云-单分片\",\"comboId\":21,\"comboCode\":100049985621,\"comboDesc\":\"\",\"comboUnitInfo\":\"\",\"unitPrice\":0,\"departCode\":1000000037,\"department\":\"系统运维部\",\"team\":\"\",\"useType\":0,\"business\":[{\"name\":\"理财\",\"percent\":100}],\"product\":[],\"isAckPercentMonthly\":false,\"percentDesc\":\"\",\"isUseAuthPercent\":false,\"authPercentName\":\"\",\"authPercentCode\":\"\",\"busReview\":null,\"tag\":null,\"cloudName\":\"\",\"cloudLabel\":\"dev\",\"desc\":\"redis: billing_transform_test\",\"RDOwner\":\"\",\"OPOwner\":\"\",\"zoneType\":\"\",\"idc\":\"\",\"useTypeName\":\"普通服务\",\"useForProduct\":\"\",\"staticQuantity\":null,\"id\":\"65f94c5bff1b3ecb5fcb7d0e\",\"instanceId\":\"billing_transform_test\",\"snapInfo\":{\"num\":1},\"costId\":\"\",\"monthPrice\":230,\"orderCode\":\"1000492168276591\",\"owner\":\"jiayiming_dxm\",\"startTime\":\"2024-03-19T16:27:07.653+08:00\",\"endTime\":\"0001-01-01T08:05:43+08:05\",\"createdAt\":\"2024-03-19T16:27:07.684+08:00\",\"updateAt\":\"2024-03-19T16:27:07.684+08:00\",\"status\":1,\"parentCode\":\"\",\"busPercentStatus\":0,\"instanceName\":\"\",\"shortId\":\"redis-57im2gwy4ab\"}]}")))
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/api/billing/order/modify`),
					httpmock.ResponderFromResponse(httpmock.NewStringResponse(200, "{\"errno\":0,\"errmsg\":\"success\",\"data\":{}}")))
			},
			args:    objS,
			wantErr: true,
			expect: func(t *testing.T, err error) {
				if err != errs.Success {
					t.Errorf("TransformDockerBill() expect success but get err %v", err)
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := TransformDockerBill(tt.args)
			if (err != nil) != tt.wantErr {
				t.Errorf("TransformDockerBill() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, err)
			}
		})
	}
}
