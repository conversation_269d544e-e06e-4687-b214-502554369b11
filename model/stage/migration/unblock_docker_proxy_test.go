package migration

import (
	"encoding/json"
	"testing"

	"github.com/jarcoal/httpmock"

	"dt-common/ent"
	"dt-common/errs"
	"dt-common/noah"
	"redis-xweb/env"
)

// 解屏蔽容器proxy实例
func TestUnblockProxyOneByOne(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")
	objC := env.MockCluster("redis_test")
	objT, objS := env.MockTaskWithStage(objC)

	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	tests := []struct {
		name    string
		before  func()
		args    *ent.Stage
		wantErr bool
		expect  func(*testing.T, error)
	}{
		{
			name: "test1: check params",
			args: func() *ent.Stage {
				paramStr := "-1"
				objS.Parameter = &paramStr
				return objS
			}(),
			wantErr: true,
		},
		{
			name: "test2: check bns",
			args: func() *ent.Stage {
				params := StageSchemaBns{}
				b, _ := json.Marshal(params)
				s := string(b)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: true,
		},
		{
			name: "test3: check idc",
			args: func() *ent.Stage {
				params := StageSchemaBns{
					BNS: "r3-test-router.siod-kafka",
				}
				b, _ := json.Marshal(params)
				s := string(b)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: true,
		},
		{
			name: "test4: noah request failed",
			args: func() *ent.Stage {
				params := StageSchemaBns{
					BNS: "r3-test-router.siod-kafka",
					IDC: "hbb",
				}
				b, _ := json.Marshal(params)
				s := string(b)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: true,
		},
		{
			name: "test5: still has proxy unblocked and block failed",
			before: func() {
				// mock flowcenter api
				instances := []noah.Instance{
					{IP: "************", PortInfo: &noah.PortInfo{Main: 8004}, Disable: false},                                        // 没打tag
					{IP: "************", PortInfo: &noah.PortInfo{Main: 8004}, Tags: map[string]string{"idc": "hba"}, Disable: false}, // 非指定机房
					{IP: "************", PortInfo: &noah.PortInfo{Main: 8004}, Tags: map[string]string{"idc": "hbb"}, Disable: false}, // 未屏蔽
					{IP: "************", PortInfo: &noah.PortInfo{Main: 8004}, Tags: map[string]string{"idc": "hbb"}, Disable: true},  // 已屏蔽
				}
				str, _ := json.Marshal(instances)
				httpmock.RegisterResponder("GET", "http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-kafka/apps/r3-test-router/instances?showPage=0",
					httpmock.NewStringResponder(200, "{\"success\": true, \"message\": \"\", \"data\": "+string(str)+" }"))
			},
			args: func() *ent.Stage {
				params := StageSchemaBns{
					BNS: "r3-test-router.siod-kafka",
					IDC: "hbb",
				}
				b, _ := json.Marshal(params)
				s := string(b)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: true,
		},
		{
			name: "test6: unblock success and still has proxy blocked",
			before: func() {
				// mock flowcenter api
				instances := []noah.Instance{
					{IP: "************", PortInfo: &noah.PortInfo{Main: 8004}, Tags: map[string]string{"idc": "hbb"}, Disable: true},
					{IP: "************", PortInfo: &noah.PortInfo{Main: 8004}, Tags: map[string]string{"idc": "hbb"}, Disable: true},
				}
				str, _ := json.Marshal(instances)
				httpmock.RegisterResponder("GET", "http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-kafka/apps/r3-test-router/instances?showPage=0",
					httpmock.NewStringResponder(200, "{\"success\": true, \"message\": \"\", \"data\": "+string(str)+" }"))

			},
			args: func() *ent.Stage {
				params := StageSchemaBns{
					BNS: "r3-test-router.siod-kafka",
					IDC: "hbb",
				}
				b, _ := json.Marshal(params)
				s := string(b)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: true,
		},
		{
			name: "test7: success",
			before: func() {
				// mock flowcenter api
				instances := []noah.Instance{
					{IP: "************", PortInfo: &noah.PortInfo{Main: 8004}, Tags: map[string]string{"idc": "hbb"}, Disable: false},
					{IP: "************", PortInfo: &noah.PortInfo{Main: 8004}, Tags: map[string]string{"idc": "hbb"}, Disable: false},
				}
				str, _ := json.Marshal(instances)
				httpmock.RegisterResponder("GET", "http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-kafka/apps/r3-test-router/instances?showPage=0",
					httpmock.NewStringResponder(200, "{\"success\": true, \"message\": \"\", \"data\": "+string(str)+" }"))
			},
			args: func() *ent.Stage {
				params := StageSchemaBns{
					BNS: "r3-test-router.siod-kafka",
					IDC: "hbb",
				}
				b, _ := json.Marshal(params)
				s := string(b)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: true,
			expect: func(t *testing.T, err error) {
				if err != errs.Success {
					t.Errorf("UnblockProxyOneByOne() expect success but get err %v", err)
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := UnblockProxyOneByOne(tt.args)
			if (err != nil) != tt.wantErr {
				t.Errorf("UnblockProxyOneByOne() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, err)
			}
		})
	}

}

func TestDisableInstances(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")
	objC := env.MockCluster("rollback_unblock_test")
	objT, objS := env.MockTaskWithStage(objC)

	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	tests := []struct {
		name    string
		before  func()
		args    *ent.Stage
		wantErr bool
		expect  func(*testing.T, error)
	}{
		{
			name: "test1: check params",
			args: func() *ent.Stage {
				paramStr := "-1"
				objS.Parameter = &paramStr
				return objS
			}(),
			wantErr: true,
		},
		{
			name: "test2: check bns",
			args: func() *ent.Stage {
				params := StageSchemaBns{}
				b, _ := json.Marshal(params)
				s := string(b)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: true,
		},
		{
			name: "test3: check idc",
			args: func() *ent.Stage {
				params := StageSchemaBns{
					BNS: "r3-test-router.siod-kafka",
				}
				b, _ := json.Marshal(params)
				s := string(b)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: true,
		},
		{
			name: "test4: noah request failed",
			args: func() *ent.Stage {
				params := StageSchemaBns{
					BNS: "r3-test-router.siod-kafka",
					IDC: "hbb",
				}
				b, _ := json.Marshal(params)
				s := string(b)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: true,
		},
		{
			name: "test5: request failed",
			before: func() {
				// mock flowcenter api
				instances := []noah.Instance{
					{IP: "************", PortInfo: &noah.PortInfo{Main: 8004}, Disable: false},                                        // 没打tag
					{IP: "************", PortInfo: &noah.PortInfo{Main: 8004}, Tags: map[string]string{"idc": "hba"}, Disable: false}, // 非指定机房
					{IP: "************", PortInfo: &noah.PortInfo{Main: 8004}, Tags: map[string]string{"idc": "hbb"}, Disable: true},  // 已屏蔽
					{IP: "************", PortInfo: &noah.PortInfo{Main: 8004}, Tags: map[string]string{"idc": "hbb"}, Disable: false}, // 未屏蔽
				}
				str, _ := json.Marshal(instances)
				httpmock.RegisterResponder("GET", "http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-kafka/apps/r3-test-router/instances?showPage=0",
					httpmock.NewStringResponder(200, "{\"success\": true, \"message\": \"\", \"data\": "+string(str)+" }"))
			},
			args: func() *ent.Stage {
				params := StageSchemaBns{
					BNS: "r3-test-router.siod-kafka",
					IDC: "hbb",
				}
				b, _ := json.Marshal(params)
				s := string(b)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: true,
		},
		{
			name: "test6: success",
			before: func() {
				httpmock.RegisterResponder("POST", "http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-kafka/apps/r3-test-router/instances/batchBlock",
					httpmock.NewStringResponder(200, "{\"success\": true, \"message\": \"\", \"data\": \"ok\" }"))
			},
			args: func() *ent.Stage {
				params := StageSchemaBns{
					BNS: "r3-test-router.siod-kafka",
					IDC: "hbb",
				}
				b, _ := json.Marshal(params)
				s := string(b)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: true,
			expect: func(t *testing.T, err error) {
				if err != errs.Success {
					t.Errorf("DisableInstances() expect success but get err %v", err)
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := DisableInstances(tt.args)
			if (err != nil) != tt.wantErr {
				t.Errorf("DisableInstances() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, err)
			}
		})
	}
}
