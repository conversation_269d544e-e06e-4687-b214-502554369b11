package whitelist

import (
	"encoding/json"
	"fmt"
	"strings"

	"dt-common/ent"
	"dt-common/ent/predicate"
	"dt-common/ent/whitelist"
	"dt-common/mysql"
	"dt-common/omodel"
)

// 白名单申请完成后的处理函数
// 1、如果添加白名单，修改白名单状态 adding -> normal
// 2、如果删除白名单，delete白名单 where status = 'deleted'
func CallbackUpdateWhitelistDatabase(stageData *ent.Stage) error {
	// 参数解析
	var params StageSchema
	err := json.Unmarshal([]byte(*stageData.Parameter), &params)
	if err != nil {
		omodel.StageAppendErrorLog(stageData.ID, "[STAGE %d] failed to parse stage parameter, value=%v, error=(%v)", stageData.ID, stageData.Parameter, err)
		return err
	}

	// 管理白名单不需要回调改状态
	if params.Type != WhitelistTypeBusiness {
		return nil
	}

	// 编辑where条件
	conditions := []predicate.Whitelist{}
	for _, item := range params.WhiteListBns {
		tmp := strings.Split(item, " ")
		if len(tmp) != 2 {
			err = fmt.Errorf("whitelist format should be of (xxx rw/r) not (%s)", item)
			omodel.StageAppendErrorLog(stageData.ID, "[STAGE %d] %v", stageData.ID, err)
			return err
		}
		conditions = append(conditions, whitelist.Value(tmp[0]))
	}
	for _, item := range params.WhiteListIp {
		tmp := strings.Split(item, " ")
		if len(tmp) != 2 {
			err = fmt.Errorf("whitelist format should be of (xxx rw/r) not (%s)", item)
			omodel.StageAppendErrorLog(stageData.ID, "[STAGE %d] %v", stageData.ID, err)
			return err
		}
		conditions = append(conditions, whitelist.Value(tmp[0]))
	}

	// 获取数据库连接
	db, err := mysql.Database()
	if err != nil {
		omodel.StageAppendErrorLog(stageData.ID, "[STAGE %d] failed to get mysql connection, error=(%v)", stageData.ID, err)
		return err
	}

	ctx, cancel := mysql.ContextWithTimeout()
	switch params.Action {
	case "add":
		err := db.Whitelist.Update().SetStatus(omodel.WHITELIST_STATUS_NORMAL).Where(
			whitelist.ClusterName(stageData.ClusterName),
			whitelist.Status(omodel.WHITELIST_STATUS_ADDING),
			whitelist.Or(conditions...),
		).Exec(ctx)
		cancel()
		if err != nil {
			omodel.StageAppendErrorLog(stageData.ID, "[STAGE %d] failed to update database, error=(%v)", stageData.ID, err)
			return err
		}
	case "del":
		delNum, err := db.Whitelist.Delete().Where(
			whitelist.ClusterName(stageData.ClusterName),
			whitelist.Status(omodel.WHITELIST_STATUS_DELETED),
			whitelist.Or(conditions...),
		).Exec(ctx)
		cancel()
		if err != nil {
			omodel.StageAppendErrorLog(stageData.ID, "[STAGE %d] failed to delete database, error=(%v)", stageData.ID, err)
			return err
		}
		if delNum != len(conditions) {
			omodel.StageAppendWarnLog(stageData.ID, "[STAGE %d] the number of deleted whitelist incorrect, conditionsNum=%d, delNum=%d", stageData.ID, len(conditions), delNum)
		}
	default:
		err = fmt.Errorf("whitelist stage has undefined action %s, it should be one of add or del", params.Action)
		omodel.StageAppendWarnLog(stageData.ID, "[STAGE %d] %v", stageData.ID, err)
		return err
	}

	return nil
}
