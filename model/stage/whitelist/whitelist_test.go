package whitelist

import (
	"encoding/json"
	"regexp"
	"testing"

	"github.com/jarcoal/httpmock"

	"dt-common/ent"
	"redis-xweb/env"
)

func TestRequest(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")
	objC := env.MockCluster("whitelist_test")
	_, objS := env.MockTaskWithStage(objC)
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		stageId     int64
		clusterName string
		params      *StageSchema
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test1: undefined type",
			args: args{
				stageId:     objS.ID,
				clusterName: objC.Name,
				params: &StageSchema{
					Docker:       1,
					Type:         "undefined",
					Action:       "add",
					WhiteListBns: []string{},
					WhiteListIp:  []string{},
				},
			},
			wantErr: true,
		},
		{
			name: "test2: business type",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/whitelist/updateWhitelist`),
					httpmock.NewStringResponder(200, `{"code": "00000", "data": "mockok"}`))
			},
			args: args{
				stageId:     objS.ID,
				clusterName: objC.Name,
				params: &StageSchema{
					Docker:       1,
					Type:         WhitelistTypeBusiness,
					Action:       "add",
					WhiteListBns: []string{},
					WhiteListIp:  []string{},
				},
			},
			wantErr: false,
		},
		{
			name: "test3: management type",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/whitelist/updateManageWhitelist`),
					httpmock.NewStringResponder(200, `{"code": "00000", "data": "mockok"}`))
			},
			args: args{
				stageId:     objS.ID,
				clusterName: objC.Name,
				params: &StageSchema{
					Docker:       1,
					Type:         WhitelistTypeManagement,
					Action:       "add",
					WhiteListBns: []string{},
					WhiteListIp:  []string{},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := request(tt.args.stageId, tt.args.clusterName, tt.args.params)
			if (err != nil) != tt.wantErr {
				t.Errorf("request() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
	httpmock.Reset()
}

// 单测：更新管理白名单
func TestUpdate(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")
	objC := env.MockCluster("whitelist_test")
	objT, objS := env.MockTaskWithStage(objC)
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	tests := []struct {
		name    string
		before  func()
		args    *ent.Stage
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test1: check params",
			args: func() *ent.Stage {
				params := "1"
				objS.Parameter = &params
				return objS
			}(),
			wantErr: true,
		},
		{
			name: "test2: check action",
			args: func() *ent.Stage {
				params := StageSchema{
					Action:       "udp",
					WhiteListBns: []string{"unit.test"},
					Docker:       0,
				}
				b, _ := json.Marshal(params)
				s := string(b)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: true,
		},
		{
			name: "test3: check empty bns & ip",
			args: func() *ent.Stage {
				params := StageSchema{
					Docker:       0,
					Type:         WhitelistTypeBusiness,
					Action:       "add",
					WhiteListBns: []string{},
					WhiteListIp:  nil,
				}
				b, _ := json.Marshal(params)
				s := string(b)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: true,
		},
		{
			name: "test4: call failed",
			args: func() *ent.Stage {
				params := StageSchema{
					Docker:       0,
					Type:         WhitelistTypeBusiness,
					Action:       "add",
					WhiteListBns: []string{"unit.test rw"},
					WhiteListIp:  nil,
				}
				b, _ := json.Marshal(params)
				s := string(b)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: true,
		},
		{
			name: "test5: success",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/whitelist/updateWhitelist`),
					httpmock.NewStringResponder(200, `{"code": "00000", "data": "mockok"}`))
			},
			args: func() *ent.Stage {
				params := StageSchema{
					Docker:       0,
					Type:         WhitelistTypeBusiness,
					Action:       "add",
					WhiteListBns: []string{"unit.test rw"},
					WhiteListIp:  nil,
				}

				b, _ := json.Marshal(params)
				s := string(b)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			if err := Update(tt.args); (err != nil) != tt.wantErr {
				t.Errorf("Update() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

func TestRollbackManageWhitelist(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")
	objC := env.MockCluster("whitelist_test")
	objT, objS := env.MockTaskWithStage(objC)
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	tests := []struct {
		name    string
		before  func()
		args    *ent.Stage
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test1: check params",
			args: func() *ent.Stage {
				params := "1"
				objS.Parameter = &params
				return objS
			}(),
			wantErr: true,
		},
		{
			name: "test2: check action",
			args: func() *ent.Stage {
				params := StageSchema{
					Action:       "udp",
					WhiteListBns: []string{"unit.test"},
					Docker:       0,
				}
				b, _ := json.Marshal(params)
				s := string(b)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: true,
		},
		{
			name: "test3: check empty bns & ip",
			args: func() *ent.Stage {
				params := StageSchema{
					Docker:       0,
					Type:         WhitelistTypeBusiness,
					Action:       "add",
					WhiteListBns: []string{},
					WhiteListIp:  nil,
				}
				b, _ := json.Marshal(params)
				s := string(b)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: true,
		},
		{
			name: "test4: call failed",
			args: func() *ent.Stage {
				params := StageSchema{
					Docker:       0,
					Type:         WhitelistTypeBusiness,
					Action:       "add",
					WhiteListBns: []string{"unit.test rw"},
					WhiteListIp:  nil,
				}
				b, _ := json.Marshal(params)
				s := string(b)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: true,
		},
		{
			name: "test5: success",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/whitelist/updateWhitelist`),
					httpmock.NewStringResponder(200, `{"code": "00000", "data": "mockok"}`))
			},
			args: func() *ent.Stage {
				params := StageSchema{
					Docker:       0,
					Type:         WhitelistTypeBusiness,
					Action:       "add",
					WhiteListBns: []string{"unit.test rw"},
					WhiteListIp:  nil,
				}

				b, _ := json.Marshal(params)
				s := string(b)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			if err := Rollback(tt.args); (err != nil) != tt.wantErr {
				t.Errorf("Rollback() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}
