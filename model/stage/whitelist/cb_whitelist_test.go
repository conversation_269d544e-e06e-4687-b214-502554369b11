package whitelist

import (
	"context"
	"encoding/json"
	"testing"

	"dt-common/ent"
	"dt-common/ent/whitelist"
	"dt-common/mysql"
	"redis-xweb/env"
)

// 单测：回调 - 更新白名单
func TestCallbackUpdateWhitelistDatabase(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")
	objC := env.MockCluster("whitelist_callback")
	objT, objS := env.MockTaskWithStage(objC)
	db, _ := mysql.Database()
	db.Whitelist.Delete().Where(whitelist.ClusterName(objC.Name)).Exec(context.Background())
	db.Whitelist.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetStatus("normal").SetType("bns").SetValue("blind-kj-broker.siod-kafka").SetPrivilege("r").Save(context.Background())
	db.Whitelist.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetStatus("adding").SetType("bns").SetValue("blind-kj-zk.siod-kafka").SetPrivilege("rw").Save(context.Background())
	db.Whitelist.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetStatus("deleted").SetType("bns").SetValue("unit.test").SetPrivilege("rw").Save(context.Background())
	db.Whitelist.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetStatus("normal").SetType("ip").SetValue("************").SetPrivilege("r").Save(context.Background())
	db.Whitelist.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetStatus("adding").SetType("ip").SetValue("************").SetPrivilege("rw").Save(context.Background())
	db.Whitelist.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetStatus("deleted").SetType("ip").SetValue("************").SetPrivilege("r").Save(context.Background())

	tests := []struct {
		name    string
		before  func()
		args    *ent.Stage
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test: add success",
			args: func() *ent.Stage {
				params := StageSchema{
					Docker:       0,
					Type:         WhitelistTypeBusiness,
					Action:       "add",
					WhiteListBns: []string{"blind-kj-zk.siod-kafka rw"},
					WhiteListIp:  []string{"************ rw"},
				}
				b, _ := json.Marshal(params)
				s := string(b)
				// stage := env.MockStage(objT)
				objS.Parameter = &s
				return objS
			}(),
			wantErr: false,
			expect: func(t *testing.T) {
				list, _ := db.Whitelist.Query().Where(whitelist.ClusterName(objC.Name), whitelist.Status("normal")).All(context.Background())
				if len(list) != 4 {
					t.Errorf("expect 4 normal whitelist, but got %d", len(list))
				}

				list, _ = db.Whitelist.Query().Where(whitelist.ClusterName(objC.Name), whitelist.Status("adding")).All(context.Background())
				if len(list) != 0 {
					t.Errorf("expect 0 normal whitelist, but got %d", len(list))
				}
			},
		},
		{
			name: "test: del success",
			args: func() *ent.Stage {
				params := StageSchema{
					Docker:       0,
					Type:         WhitelistTypeBusiness,
					Action:       "del",
					WhiteListBns: []string{"unit.test rw"},
					WhiteListIp:  []string{"************ rw"},
				}
				b, _ := json.Marshal(params)
				s := string(b)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: false,
			expect: func(t *testing.T) {
				list, _ := db.Whitelist.Query().Where(whitelist.ClusterName(objC.Name), whitelist.Status("normal")).All(context.Background())
				if len(list) != 4 {
					t.Errorf("expect 4 normal whitelist, but got %d", len(list))
				}

				list, _ = db.Whitelist.Query().Where(whitelist.ClusterName(objC.Name), whitelist.Status("deleted")).All(context.Background())
				if len(list) != 0 {
					t.Errorf("expect 0 deleted whitelist, but got %d", len(list))
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := CallbackUpdateWhitelistDatabase(tt.args)
			if (err != nil) != tt.wantErr {
				t.Errorf("CallbackUpdateWhitelistDatabase() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}
