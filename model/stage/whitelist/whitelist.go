package whitelist

import (
	"encoding/json"
	"fmt"

	"dt-common/ent"
	"dt-common/omodel"
	"redis-xweb/library/ras"
)

const (
	WhitelistTypeBusiness   string = "business"
	WhitelistTypeManagement string = "management"
)

type StageSchema struct {
	Docker       int      `json:"docker"`
	Type         string   `json:"type"`
	Action       string   `json:"action"`
	WhiteListBns []string `json:"whitelistBns"`
	WhiteListIp  []string `json:"whitelistIp"`
}

// 异步请求manager & cmanager
func request(stageId int64, clusterName string, params *StageSchema) error {
	requestBody := ras.WhiteListRequest{
		StageID:      stageId,
		ClusterName:  clusterName,
		Action:       params.Action,
		WhiteListBns: params.WhiteListBns,
		WhiteListIp:  params.WhiteListIp,
	}

	switch params.Type {
	case WhitelistTypeBusiness:
		return ras.Deploy(params.Docker).UpdateWhitelist(&requestBody)
	case WhitelistTypeManagement:
		return ras.Deploy(params.Docker).UpdateManageWhitelist(&requestBody)
	default:
		return fmt.Errorf("undefined parameter type %s, it should be one of business or managerment", params.Type)
	}
}

// 更新白名单
// 根据docker参数决定是调用manager还是cmanager的接口
func Update(stageData *ent.Stage) error {
	// 参数解析
	var params StageSchema
	err := json.Unmarshal([]byte(*stageData.Parameter), &params)
	if err != nil {
		omodel.StageAppendErrorLog(stageData.ID, "[STAGE %d] failed to parse stage parameter, value=%v, error=(%v)", stageData.ID, stageData.Parameter, err)
		return err
	}

	// 空值判断
	if params.Action != "add" && params.Action != "del" {
		err = fmt.Errorf("[STAGE %d] undefined action value %s, it should be one of add or del", stageData.ID, params.Action)
		omodel.StageAppendErrorLog(stageData.ID, err.Error())
		return err
	}
	if len(params.WhiteListBns) == 0 && len(params.WhiteListIp) == 0 {
		err = fmt.Errorf("[STAGE %d] expect more than one bns but got 0", stageData.ID)
		omodel.StageAppendErrorLog(stageData.ID, err.Error())
		return err
	}

	err = request(stageData.ID, stageData.ClusterName, &params)
	if err != nil {
		omodel.StageAppendErrorLog(stageData.ID, "[STAGE %d] failed to update whitelist, error=(%v)", stageData.ID, err)
		return err
	}
	omodel.StageAppendInfoLog(stageData.ID, "[STAGE %d] succeed to update whitelist, cluster=%s, type=%s, bnsList=%v, ipList=%v", stageData.ID, stageData.ClusterName, params.Type, params.WhiteListBns, params.WhiteListIp)

	return nil
}

// =======================================================
// 						   回滚
// =======================================================

// 通过覆盖文件可能会出问题，旧stage的回滚会将新stage覆盖
// 所以回滚通过反转action，再做一次变更实现
func Rollback(stageData *ent.Stage) error {
	// 参数解析
	var params StageSchema
	err := json.Unmarshal([]byte(*stageData.Parameter), &params)
	if err != nil {
		omodel.StageAppendErrorLog(stageData.ID, "[STAGE %d] failed to parse stage parameter, value=%v, error=(%v)", stageData.ID, stageData.Parameter, err)
		return err
	}

	// 反转action
	switch params.Action {
	case "add":
		params.Action = "del"
	case "del":
		params.Action = "add"
	default:
		err = fmt.Errorf("[STAGE %d] undefined action value %s, it should be one of add or del", stageData.ID, params.Action)
		omodel.StageAppendErrorLog(stageData.ID, err.Error())
		return err
	}

	err = request(stageData.ID, stageData.ClusterName, &params)
	if err != nil {
		omodel.StageAppendErrorLog(stageData.ID, "[STAGE %d] failed to rollback whitelist, error=(%v)", stageData.ID, err)
		return err
	}
	omodel.StageAppendInfoLog(stageData.ID, "[STAGE %d] succeed to rollback whitelist, cluster=%s, type=%s, bnsList=%v, ipList=%v", stageData.ID, stageData.ClusterName, params.Type, params.WhiteListBns, params.WhiteListIp)

	return nil
}
