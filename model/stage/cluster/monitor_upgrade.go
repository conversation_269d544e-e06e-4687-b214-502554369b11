package cluster

import (
	"fmt"

	"dt-common/ent"
	"dt-common/errs"
	"dt-common/logger"
	"dt-common/omodel"
	"redis-xweb/model/stage/deploy"
)

// 集群监控升级
func MonitorUpgrade(stageData *ent.Stage) error {
	// 获取Apps
	apps, err := deploy.GetClusterBnsApps(stageData.ClusterName, []string{omodel.PROCESS_TYPE_PROXY, omodel.PROCESS_TYPE_REDIS, omodel.PROCESS_TYPE_SENTINEL})
	if err != nil {
		logger.Warn("failed to get cluster app, cluster=%s, error=(%v)", stageData.ClusterName, err)
		return err
	}

	for _, app := range *apps {
		// 过滤掉物理sentinel的监控
		filterSentinelTasks := []string{
			fmt.Sprintf("process#sentinel_%d", app.GetPort()),
			fmt.Sprintf("port#port_%d", app.GetPort()),
			fmt.Sprintf("logCollect#sentinel_%d", app.GetPort()),
			fmt.Sprintf("logCollect#sentinel_whitelist_%d", app.GetPort()),
		}
		filterSentinelPolicies := []string{
			fmt.Sprintf("sentinel_port_%d_dead", app.GetPort()),
			fmt.Sprintf("sentinel_process_%d_dead", app.GetPort()),
			fmt.Sprintf("sentinel_%d_failover_delay", app.GetPort()),
			fmt.Sprintf("sentinel_%d_switch", app.GetPort()),
			fmt.Sprintf("sentinel_%d_odown", app.GetPort()),
			fmt.Sprintf("sentinel_%d_TILT_ON", app.GetPort()),
			fmt.Sprintf("sentinel_%d_TILT_OFF", app.GetPort()),
			fmt.Sprintf("sentinel_%d_sdown", app.GetPort()),
			fmt.Sprintf("sentinel_process_%d_cpu_usage_exceed_35", app.GetPort()),
			fmt.Sprintf("sentinel_process_%d_cpu_usage_exceed_50", app.GetPort()),
			fmt.Sprintf("sentinel_bns_error_%d", app.GetPort()),
			fmt.Sprintf("sentinel_bns_not_exist_%d", app.GetPort()),
			fmt.Sprintf("sentinel_protect_flag_ON_%d", app.GetPort()),
			fmt.Sprintf("sentinel_whitelist_proc_exit_%d", app.GetPort()),
		}

		// 清理任务
		err := app.ClearMonitors(filterSentinelTasks...)
		if err != nil {
			logger.Warn("failed to clear bns monitors, cluster=%s, bns=%s, log=(%s), error=(%v)", stageData.ClusterName, app.BNS(), app.RunMessage(), err)
			return err
		}
		omodel.StageAppendInfoLog(stageData.ID, app.RunMessage())

		// 清理报警
		err = app.ClearAlarms(filterSentinelPolicies...)
		if err != nil {
			logger.Warn("failed to clear bns alarms, cluster=%s, bns=%s, log=(%s), error=(%v)", stageData.ClusterName, app.BNS(), app.RunMessage(), err)
			return err
		}
		omodel.StageAppendInfoLog(stageData.ID, app.RunMessage())

		// 添加任务
		err = app.Monitors("add")
		if err != nil {
			logger.Warn("failed to add bns monitors, cluster=%s, bns=%s, log=(%s), error=(%v)", stageData.ClusterName, app.BNS(), app.RunMessage(), err)
			return err
		}
		omodel.StageAppendInfoLog(stageData.ID, app.RunMessage())

		// 添加报警
		err = app.Alarms("add")
		if err != nil {
			logger.Warn("failed to add bns alarms, cluster=%s, bns=%s, log=(%s), error=(%v)", stageData.ClusterName, app.BNS(), app.RunMessage(), err)
			return err
		}
		omodel.StageAppendInfoLog(stageData.ID, app.RunMessage())
	}

	// 同步任务返回 errs.Success
	return errs.Success
}
