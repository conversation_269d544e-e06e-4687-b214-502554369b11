package cluster

import (
	"context"
	"encoding/json"
	"regexp"
	"testing"

	"github.com/jarcoal/httpmock"

	"dt-common/ent"
	"dt-common/ent/stage"
	"dt-common/mysql"
	"dt-common/omodel"
	"redis-xweb/env"
)

// 单测：获取拓扑结构
func TestGetRedisShards(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")
	objC := env.MockCluster("redis_shard_test")
	_, objS := env.MockTaskWithStage(objC)
	db, _ := mysql.Database()

	type args struct {
		stageId     int64
		clusterName string
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, []*omodel.Shard)
	}{
		{
			name: "test1: query error",
			args: args{
				stageId:     objS.ID,
				clusterName: objS.ClusterName,
			},
			wantErr: true,
		},
		{
			name: "test2: more than one master",
			before: func() {
				db.Redis.Delete().Exec(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test-router.siod-redis").SetName("r3-test_server1").SetIP("************").SetPort(7001).SetIdc("hba").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test-router.siod-redis").SetName("r3-test_server1").SetIP("************").SetPort(7001).SetIdc("hbb").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test-router.siod-redis").SetName("r3-test_server1").SetIP("************").SetPort(7001).SetIdc("hba").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
			},
			args: args{
				stageId:     objS.ID,
				clusterName: objC.Name,
			},
			wantErr: true,
		},
		{
			name: "test3: no master",
			before: func() {
				db.Redis.Delete().Exec(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test-router.siod-redis").SetName("r3-test_server1").SetIP("************").SetPort(7001).SetIdc("hbb").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test-router.siod-redis").SetName("r3-test_server2").SetIP("************").SetPort(7001).SetIdc("hba").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
			},
			args: args{
				stageId:     objS.ID,
				clusterName: objC.Name,
			},
			wantErr: true,
		},
		{
			name: "test4: more than one slave",
			before: func() {
				db.Redis.Delete().Exec(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test-router.siod-redis").SetName("r3-test_server1").SetIP("************").SetPort(7001).SetIdc("hba").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test-router.siod-redis").SetName("r3-test_server1").SetIP("************").SetPort(7001).SetIdc("hbb").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test-router.siod-redis").SetName("r3-test_server1").SetIP("************").SetPort(7001).SetIdc("hba").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
			},
			args: args{
				stageId:     objS.ID,
				clusterName: objC.Name,
			},
			wantErr: true,
		},
		{
			name: "test5: no slave",
			before: func() {
				db.Redis.Delete().Exec(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test-router.siod-redis").SetName("r3-test_server1").SetIP("************").SetPort(7001).SetIdc("hbb").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test-router.siod-redis").SetName("r3-test_server2").SetIP("************").SetPort(7001).SetIdc("hba").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
			},
			args: args{
				stageId:     objS.ID,
				clusterName: objC.Name,
			},
			wantErr: true,
		},
		{
			name: "test6: success",
			before: func() {
				db.Redis.Delete().ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test-router.siod-redis").SetName("r3-test_server1").SetIP("************").SetPort(7000).SetIdc("hba").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test-router.siod-redis").SetName("r3-test_server1").SetIP("************").SetPort(7000).SetIdc("hbb").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test-router.siod-redis").SetName("r3-test_server2").SetIP("************").SetPort(7001).SetIdc("hba").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test-router.siod-redis").SetName("r3-test_server2").SetIP("************").SetPort(7001).SetIdc("hbb").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
			},
			args: args{
				stageId:     objS.ID,
				clusterName: objS.ClusterName,
			},
			wantErr: false,
			expect: func(t *testing.T, shardList []*omodel.Shard) {
				if len(shardList) != 2 {
					t.Errorf("expect len(shardList) = %v, but got %v", 2, len(shardList))
					return
				}

				if shardList[0].Name != "r3-test_server1" {
					t.Errorf("expect r3-test_server1 exist, but it is not")
				}
				if shardList[0].Master.IP != "************" {
					t.Errorf("expect r3-test_server1 Master.IP = ************, but got %v", shardList[0].Master.IP)
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			shards, err := getRedisShards(tt.args.stageId, tt.args.clusterName)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetRedisShards() caseName=%s, error = %v, wantErr %v", tt.name, err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, shards)
			}
		})
	}
}

// 单测：按部署环境维度切换
func TestGetFragmentByDeployEnv(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")

	type args struct {
		shardList []*omodel.Shard
		docker    int
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, []*omodel.Shard)
	}{
		{
			name: "test1: env not match",
			args: func() args {
				return args{
					shardList: []*omodel.Shard{
						{
							Name:   "r3-test_server1",
							Master: &omodel.Instance{IP: "************", Port: 7001, Docker: 0},
							Slaves: []*omodel.Instance{{IP: "************", Port: 7001, Docker: 1}},
						},
						{
							Name:   "r3-test_server2",
							Master: &omodel.Instance{IP: "************", Port: 7001, Docker: 0},
							Slaves: []*omodel.Instance{{IP: "************", Port: 7001, Docker: 0}},
						},
					},
					docker: 1,
				}
			}(),
			wantErr: true,
		},
		{
			name: "test2: success",
			args: func() args {
				return args{
					shardList: []*omodel.Shard{
						{
							Name:     "r3-test_server1",
							Master:   &omodel.Instance{IP: "************", Port: 7001, Docker: 1},
							Slaves:   []*omodel.Instance{{IP: "************", Port: 7001, Docker: 0}},
							Operated: false,
						},
						{
							Name:     "r3-test_server2",
							Master:   &omodel.Instance{IP: "************", Port: 7001, Docker: 1},
							Slaves:   []*omodel.Instance{{IP: "************", Port: 7001, Docker: 0}},
							Operated: false,
						},
					},
					docker: 1,
				}
			}(),
			wantErr: false,
			expect: func(t *testing.T, fragmentList []*omodel.Shard) {
				if len(fragmentList) != 2 {
					t.Errorf("expect 2 fragment, but got %v", len(fragmentList))
				}
				for _, fragment := range fragmentList {
					if fragment.Master.IP != "************" && fragment.Master.IP != "************" {
						t.Errorf("expect master host is ************ or ************, but got %v", fragment.Master.IP)
					}
					if !fragment.Operated {
						t.Errorf("expect Operated true, but got false")
					}
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			fragmentList, err := getFragmentByDeployEnv(tt.args.shardList, tt.args.docker)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetFragmentByDeployEnv() caseName=%s, error = %v, wantErr %v", tt.name, err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, fragmentList)
			}
		})
	}
}

func TestGroupByMasterIP(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")

	type schema struct {
		shardList []*omodel.Shard
	}
	tests := []struct {
		name    string
		before  func()
		args    schema
		wantErr bool
		expect  func(*testing.T, [][]*omodel.Shard)
	}{
		{
			name: "test1: success",
			args: schema{
				shardList: []*omodel.Shard{
					{Name: "redis_public_test-server1", Master: &omodel.Instance{IP: "************", Port: 7000}, Slaves: []*omodel.Instance{{IP: "************", Port: 7000}}},
					{Name: "redis_public_test-server2", Master: &omodel.Instance{IP: "************", Port: 7001}, Slaves: []*omodel.Instance{{IP: "************", Port: 7001}}},
					{Name: "redis_public_test-server3", Master: &omodel.Instance{IP: "************", Port: 7000}, Slaves: []*omodel.Instance{{IP: "************", Port: 7000}}},
					{Name: "redis_public_test-server4", Master: &omodel.Instance{IP: "************", Port: 7001}, Slaves: []*omodel.Instance{{IP: "************", Port: 7001}}},
					{Name: "redis_public_test-server5", Master: &omodel.Instance{IP: "************", Port: 7000}, Slaves: []*omodel.Instance{{IP: "************", Port: 7000}}},
					{Name: "redis_public_test-server6", Master: &omodel.Instance{IP: "************", Port: 7001}, Slaves: []*omodel.Instance{{IP: "************", Port: 7001}}},
					{Name: "redis_public_test-server7", Master: &omodel.Instance{IP: "************", Port: 7000}, Slaves: []*omodel.Instance{{IP: "************", Port: 7000}}},
					{Name: "redis_public_test-server8", Master: &omodel.Instance{IP: "************", Port: 7001}, Slaves: []*omodel.Instance{{IP: "************", Port: 7001}}},
				},
			},
			expect: func(t *testing.T, shardPools [][]*omodel.Shard) {
				if len(shardPools) != 4 {
					t.Errorf("len(shardPools) = %v, want 1", len(shardPools))
				}
				if len(shardPools[0]) != 2 {
					t.Errorf("len(shardPools[0]) = %v, want 2", len(shardPools[0]))
				}
			},
		},
	}
	for _, tt := range tests {
		if tt.before != nil {
			tt.before()
		}
		t.Run(tt.name, func(t *testing.T) {
			shardPools := groupByHostIP(tt.args.shardList)
			// if (err != nil) != tt.wantErr {
			// 	t.Errorf("groupByMasterIP() error = %v, wantErr %v", err, tt.wantErr)
			// }
			if tt.expect != nil {
				tt.expect(t, shardPools)
			}
		})
	}
}

func TestCalMaxConcurnecy(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")

	type schema struct {
		shardList []*omodel.Shard
	}
	tests := []struct {
		name    string
		before  func()
		args    schema
		wantErr bool
		expect  func(*testing.T, int)
	}{
		{
			name: "test1: success",
			args: schema{
				shardList: []*omodel.Shard{
					{Name: "redis_public_test-server1", Master: &omodel.Instance{IP: "************", Port: 7000}, Slaves: []*omodel.Instance{{IP: "************", Port: 7000}}},
					{Name: "redis_public_test-server2", Master: &omodel.Instance{IP: "************", Port: 7001}, Slaves: []*omodel.Instance{{IP: "************", Port: 7001}}},
					{Name: "redis_public_test-server3", Master: &omodel.Instance{IP: "************", Port: 7000}, Slaves: []*omodel.Instance{{IP: "************", Port: 7000}}},
					{Name: "redis_public_test-server4", Master: &omodel.Instance{IP: "************", Port: 7001}, Slaves: []*omodel.Instance{{IP: "************", Port: 7001}}},
					{Name: "redis_public_test-server5", Master: &omodel.Instance{IP: "************", Port: 7000}, Slaves: []*omodel.Instance{{IP: "************", Port: 7000}}},
					{Name: "redis_public_test-server6", Master: &omodel.Instance{IP: "************", Port: 7001}, Slaves: []*omodel.Instance{{IP: "************", Port: 7001}}},
					{Name: "redis_public_test-server7", Master: &omodel.Instance{IP: "************", Port: 7000}, Slaves: []*omodel.Instance{{IP: "************", Port: 7000}}},
					{Name: "redis_public_test-server8", Master: &omodel.Instance{IP: "************", Port: 7001}, Slaves: []*omodel.Instance{{IP: "************", Port: 7001}}},
				},
			},
			expect: func(t *testing.T, con int) {
				if con != 4 {
					t.Errorf("con = %v, want 4", con)
				}
			},
		},
		{
			name: "test2: success",
			args: schema{
				shardList: []*omodel.Shard{
					{Name: "redis_public_test-server1", Master: &omodel.Instance{IP: "*********", Port: 7000}, Slaves: []*omodel.Instance{{IP: "************", Port: 7000}}},
					{Name: "redis_public_test-server2", Master: &omodel.Instance{IP: "*********", Port: 7001}, Slaves: []*omodel.Instance{{IP: "************", Port: 7001}}},
					{Name: "redis_public_test-server3", Master: &omodel.Instance{IP: "*********", Port: 7000}, Slaves: []*omodel.Instance{{IP: "************", Port: 7000}}},
					{Name: "redis_public_test-server4", Master: &omodel.Instance{IP: "*********", Port: 7001}, Slaves: []*omodel.Instance{{IP: "************", Port: 7001}}},
					{Name: "redis_public_test-server5", Master: &omodel.Instance{IP: "*********", Port: 7000}, Slaves: []*omodel.Instance{{IP: "************", Port: 7000}}},
					{Name: "redis_public_test-server6", Master: &omodel.Instance{IP: "*********", Port: 7001}, Slaves: []*omodel.Instance{{IP: "************", Port: 7001}}},
					{Name: "redis_public_test-server7", Master: &omodel.Instance{IP: "*********", Port: 7000}, Slaves: []*omodel.Instance{{IP: "************", Port: 7000}}},
					{Name: "redis_public_test-server8", Master: &omodel.Instance{IP: "*********", Port: 7001}, Slaves: []*omodel.Instance{{IP: "************", Port: 7001}}},
				},
			},
			expect: func(t *testing.T, con int) {
				if con != 2 {
					t.Errorf("con = %v, want 2", con)
				}
			},
		},
	}
	for _, tt := range tests {
		if tt.before != nil {
			tt.before()
		}
		t.Run(tt.name, func(t *testing.T) {
			con := calMaxConcurnecy(tt.args.shardList)
			// if (err != nil) != tt.wantErr {
			// 	t.Errorf("groupByMasterIP() error = %v, wantErr %v", err, tt.wantErr)
			// }
			if tt.expect != nil {
				tt.expect(t, con)
			}
		})
	}
}

// 单测：Failover
func TestFailover(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")
	objC := env.MockCluster("failover_test")
	objT, objS := env.MockTaskWithStage(objC)
	db, _ := mysql.Database()

	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	tests := []struct {
		name    string
		before  func()
		args    *ent.Stage
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test1: check params",
			args: func() *ent.Stage {
				paramStr := "-1"
				objS.Parameter = &paramStr
				return objS
			}(),
			wantErr: true,
		},
		{
			name: "test2: getRedisShardMap failed",
			before: func() {
				db.Redis.Delete().Exec(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test-router.siod-redis").SetName("r3-test_server1").SetIP("************").SetPort(7001).SetIdc("hba").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test-router.siod-redis").SetName("r3-test_server1").SetIP("************").SetPort(7001).SetIdc("hbb").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test-router.siod-redis").SetName("r3-test_server1").SetIP("************").SetPort(7001).SetIdc("hba").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
			},
			args: func() *ent.Stage {
				d := 0
				obj := StageSchemaFailover{
					Docker: &d,
				}
				params, _ := json.Marshal(obj)
				s := string(params)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: true,
		},
		{
			name: "test3: getFragment failed",
			before: func() {
				db.Redis.Delete().ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test-router.siod-redis").SetName("r3-test_server1").SetIP("************").SetPort(7000).SetIdc("hba").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test-router.siod-redis").SetName("r3-test_server1").SetIP("************").SetPort(7000).SetIdc("hbb").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test-router.siod-redis").SetName("r3-test_server2").SetIP("************").SetPort(7001).SetIdc("hba").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test-router.siod-redis").SetName("r3-test_server2").SetIP("************").SetPort(7001).SetIdc("hbb").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
			},
			args: func() *ent.Stage {
				obj := StageSchemaFailover{}
				params, _ := json.Marshal(obj)
				s := string(params)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: true,
		},
		{
			name: "test4: getFragmentByDeployEnv failed",
			args: func() *ent.Stage {
				d := 0
				obj := StageSchemaFailover{
					Docker: &d,
				}
				params, _ := json.Marshal(obj)
				s := string(params)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: true,
		},
		{
			name: "test5: request failed",
			before: func() {
				db.Redis.Delete().ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(0).SetBns("r3-test-router.siod-redis").SetName("r3-test_server1").SetIP("************").SetPort(7000).SetIdc("hba").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test-router.siod-redis").SetName("r3-test_server1").SetIP("************").SetPort(7000).SetIdc("hbb").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(0).SetBns("r3-test-router.siod-redis").SetName("r3-test_server2").SetIP("************").SetPort(7001).SetIdc("hba").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test-router.siod-redis").SetName("r3-test_server2").SetIP("************").SetPort(7001).SetIdc("hbb").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
			},
			args: func() *ent.Stage {
				d := 0
				obj := StageSchemaFailover{
					Docker: &d,
				}
				params, _ := json.Marshal(obj)
				s := string(params)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: true,
		},
		{
			name: "test: success",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/stability/failover`),
					httpmock.ResponderFromResponse(httpmock.NewStringResponse(200, `{"code": "00000", "data": "ok"}`)))
			},
			args: func() *ent.Stage {
				d := 0
				obj := StageSchemaFailover{
					Docker: &d,
				}
				params, _ := json.Marshal(obj)
				s := string(params)
				objS = env.MockStage(objT)
				objS.Parameter = &s
				return objS
			}(),
			wantErr: false,
			expect: func(t *testing.T) {
				s := db.Stage.Query().Where(stage.ID(objS.ID)).OnlyX(context.Background())
				// 参数解析
				var params StageSchemaFailover
				err := json.Unmarshal([]byte(*s.Parameter), &params)
				if err != nil {
					t.Errorf("Failover() params Unmarshal error = %v", err)
					return
				}
				if len(params.ShardList) != 2 {
					t.Errorf("expect len params.FragmentList 2 but got %v", len(params.ShardList))
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := Failover(tt.args)
			if (err != nil) != tt.wantErr {
				t.Errorf("Failover() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

// 单测：RollbackFailover
func TestRollbackFailover(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")
	objC := env.MockCluster("failover_rollback_test")
	objT, objS := env.MockTaskWithStage(objC)

	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	tests := []struct {
		name    string
		before  func()
		args    *ent.Stage
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test1: check params",
			args: func() *ent.Stage {
				paramStr := "-1"
				objS.Parameter = &paramStr
				return objS
			}(),
			wantErr: true,
		},
		{
			name: "test2: FragmentList is nil",
			args: func() *ent.Stage {
				obj := StageSchemaFailover{
					ShardList:     nil,
					MaxConcurnecy: 1,
				}
				params, _ := json.Marshal(obj)
				s := string(params)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: true,
		},
		{
			name: "test3: request failed",
			args: func() *ent.Stage {
				obj := StageSchemaFailover{
					ShardList: []*omodel.Shard{
						{Name: "r3-test_server1", Master: &omodel.Instance{IP: "************", Port: 7000}, Slaves: []*omodel.Instance{{IP: "************", Port: 7000}}, Operated: true},
						{Name: "r3-test_server2", Master: &omodel.Instance{IP: "************", Port: 7001}, Slaves: []*omodel.Instance{{IP: "************", Port: 7001}}, Operated: true},
						{Name: "r3-test_server3", Master: &omodel.Instance{IP: "************", Port: 7002}, Slaves: []*omodel.Instance{{IP: "************", Port: 7002}}, Operated: true},
					},
					MaxConcurnecy: 1,
				}
				params, _ := json.Marshal(obj)
				s := string(params)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: true,
		},
		{
			name: "test4:success",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/stability/failover`),
					httpmock.ResponderFromResponse(httpmock.NewStringResponse(200, `{"code": "00000", "data": "ok"}`)))
			},
			args: func() *ent.Stage {
				obj := StageSchemaFailover{
					ShardList: []*omodel.Shard{
						{Name: "r3-test_server1", Master: &omodel.Instance{IP: "************", Port: 7000}, Slaves: []*omodel.Instance{{IP: "************", Port: 7000}}},
						{Name: "r3-test_server2", Master: &omodel.Instance{IP: "************", Port: 7001}, Slaves: []*omodel.Instance{{IP: "************", Port: 7001}}},
					},
					MaxConcurnecy: 1,
				}
				params, _ := json.Marshal(obj)
				s := string(params)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := RollbackFailover(tt.args)
			if (err != nil) != tt.wantErr {
				t.Errorf("RollbackFailover() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

// 单测：StopFailover
func TestStopFailover(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")
	objC := env.MockCluster("stop_failover_test")
	_, objS := env.MockTaskWithStage(objC)

	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	tests := []struct {
		name    string
		before  func()
		args    *ent.Stage
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name:    "test1: request failed",
			args:    objS,
			wantErr: true,
		},
		{
			name: "test2:success",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/stability/stopFailover`),
					httpmock.ResponderFromResponse(httpmock.NewStringResponse(200, `{"code": "00000", "data": "ok"}`)))
			},
			args:    objS,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := StopFailover(tt.args)
			if (err != nil) != tt.wantErr {
				t.Errorf("StopFailover() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}
