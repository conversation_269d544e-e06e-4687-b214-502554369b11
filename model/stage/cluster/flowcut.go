package cluster

import (
	"encoding/json"
	"fmt"
	"strings"

	"golang.org/x/sync/errgroup"

	"dt-common/ent"
	"dt-common/ent/proxy"
	"dt-common/errs"
	"dt-common/mysql"
	"dt-common/noah"
	"dt-common/omodel"
	"redis-xweb/library/ras"
)

type StageSchemaFlowCut struct {
	IDC string `json:"idc"`
}

// 按机房屏蔽BNS实例
func disableInstancesByIDC(stageID int64, bns string, idc string) error {
	if bns == "" {
		return fmt.Errorf("invalid input parameter bns")
	}
	if idc == "" {
		return fmt.Errorf("invalid input parameter idc")
	}

	tmp := strings.Split(bns, ".")
	if len(tmp) < 2 {
		return fmt.Errorf("invalid input parameter bns")
	}
	product, app := tmp[1], tmp[0]

	// 获取所有实例
	proxyInstances, err := noah.GetInstances(product, app)
	if err != nil {
		return err
	}

	insNames := []string{}
	for _, proxyIns := range proxyInstances {
		// 过滤掉已经屏蔽了的实例
		if proxyIns.Disable {
			continue
		}
		if proxyIns.Tags == nil {
			omodel.StageAppendErrorLog(stageID, "proxy instance %s from bns %s has no tags", proxyIns.Name, bns)
			continue
		}
		if _, exist := proxyIns.Tags["idc"]; !exist {
			omodel.StageAppendErrorLog(stageID, "proxy instance %s from bns %s has no idc tag", proxyIns.Name, bns)
			continue
		}

		if proxyIns.Tags["idc"] == idc {
			insNames = append(insNames, proxyIns.Name)
		}
	}

	if len(insNames) == 0 {
		return nil
	}

	return noah.DisableInstances(product, app, insNames)
}

// 单集群机房切流
func EmergencyFlowCut(stageData *ent.Stage) error {
	// 参数解析
	var params StageSchemaFlowCut
	err := json.Unmarshal([]byte(*stageData.Parameter), &params)
	if err != nil {
		omodel.StageAppendErrorLog(stageData.ID, "failed to parse stage parameter, value=%v, error=(%v)", stageData.Parameter, err)
		return errs.CodeUnmarshalError
	}

	db, err := mysql.Database()
	if err != nil {
		return err
	}

	// 获取集群的proxy、redis实例
	var routerBnsList []string
	var fragmentList []*omodel.Shard
	g := errgroup.Group{}
	g.Go(func() error {
		ctx, cancel := mysql.ContextWithTimeout()
		routerBnsList, err = db.Proxy.Query().Select(proxy.FieldBns).Where(
			proxy.ClusterName(stageData.ClusterName),
			proxy.Idc(params.IDC),
		).GroupBy(proxy.FieldBns).Strings(ctx)
		cancel()
		return err
	})
	g.Go(func() error {
		// 获取所有集群分片信息
		shardList, err := getRedisShards(stageData.ID, stageData.ClusterName)
		if err != nil {
			return err
		}
		// 筛选出主库在指定机房的分片
		fragmentList, err = getFragmentByIDC(shardList, params.IDC)
		return err
	})
	if err = g.Wait(); err != nil {
		return err
	}

	// 屏蔽proxy
	for _, bns := range routerBnsList {
		err = disableInstancesByIDC(stageData.ID, bns, params.IDC)
		if err != nil {
			omodel.StageAppendErrorLog(stageData.ID, "failed to block proxy instance, error=(%v)", err)
			return err
		}
		omodel.StageAppendInfoLog(stageData.ID, "proxy instances in %s from bns %s have been disabled", params.IDC, bns)
	}

	// 全分片主从切换
	if len(fragmentList) != 0 {
		err = ras.Failover(&ras.FailoverParams{
			StageID:       stageData.ID,
			ClusterName:   stageData.ClusterName,
			ShardList:     fragmentList,
			MaxConcurnecy: len(fragmentList), // 一次性切全部
		})
		if err != nil {
			omodel.StageAppendErrorLog(stageData.ID, "failed to call cmanager to failover, cluster=%s, error=(%v)", stageData.ClusterName, err)
			return err
		}
	}

	return nil
}
