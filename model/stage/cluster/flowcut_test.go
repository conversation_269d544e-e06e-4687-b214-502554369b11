package cluster

import (
	"context"
	"encoding/json"
	"testing"

	"github.com/jarcoal/httpmock"

	"dt-common/ent"
	"dt-common/mysql"
	"dt-common/noah"
	"redis-xweb/env"
)

// 单测：获取拓扑结构
func TestDisableInstancesByIDC(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")
	objC := env.MockCluster("disable_test")
	_, objS := env.MockTaskWithStage(objC)

	httpmock.Activate()
	defer httpmock.Deactivate()

	type args struct {
		stageID int64
		bns     string
		idc     string
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test1: check args bns",
			args: args{
				stageID: objS.ID,
				bns:     "",
				idc:     "",
			},
			wantErr: true,
		},
		{
			name: "test2: check args idc",
			args: args{
				stageID: objS.ID,
				bns:     "r3-test-router.siod-redis",
				idc:     "",
			},
			wantErr: true,
		},
		{
			name: "test3: check args bns style",
			args: args{
				stageID: objS.ID,
				bns:     "r3-test-router",
				idc:     "hba",
			},
			wantErr: true,
		},
		{
			name: "test4: get noah instances failed",
			args: args{
				stageID: objS.ID,
				bns:     "r3-test-router.siod-redis",
				idc:     "hba",
			},
			wantErr: true,
		},
		{
			name: "test5: disabled instances failed",
			before: func() {
				// mock flowcenter api
				instances := []noah.Instance{
					{IP: "************", PortInfo: &noah.PortInfo{Main: 8004}, Tags: map[string]string{"idc": "hbb"}, Disable: true},       // 已屏蔽
					{IP: "************", PortInfo: &noah.PortInfo{Main: 8004}, Disable: false},                                             // 没打tag
					{IP: "************", PortInfo: &noah.PortInfo{Main: 8004}, Tags: map[string]string{"service": "hbbs"}, Disable: false}, // 没打tag
					{IP: "************", PortInfo: &noah.PortInfo{Main: 8004}, Tags: map[string]string{"idc": "hba"}, Disable: false},      // 非指定机房
					{IP: "************", PortInfo: &noah.PortInfo{Main: 8004}, Tags: map[string]string{"idc": "hbb"}, Disable: false},      // 未屏蔽
				}
				str, _ := json.Marshal(instances)
				httpmock.RegisterResponder("GET", "http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-redis/apps/r3-test-router/instances?showPage=0",
					httpmock.NewStringResponder(200, "{\"success\": true, \"message\": \"\", \"data\": "+string(str)+" }"))
			},
			args: args{
				stageID: objS.ID,
				bns:     "r3-test-router.siod-redis",
				idc:     "hba",
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := disableInstancesByIDC(tt.args.stageID, tt.args.bns, tt.args.idc)
			if (err != nil) != tt.wantErr {
				t.Errorf("disableInstancesByIDC() caseName=%s, error = %v, wantErr %v", tt.name, err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

// 单测：EmergencyFlowCut
func TestEmergencyFlowCut(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")
	objC := env.MockCluster("cutflow_test")
	objT, objS := env.MockTaskWithStage(objC)
	db, _ := mysql.Database()

	httpmock.Activate()
	defer httpmock.Deactivate()

	tests := []struct {
		name    string
		before  func()
		args    *ent.Stage
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test1: check params",
			args: func() *ent.Stage {
				paramStr := "-1"
				objS.Parameter = &paramStr
				return objS
			}(),
			wantErr: true,
		},
		{
			name: "test2: disable proxy failed",
			before: func() {
				db.Proxy.Delete().Exec(context.Background())
				db.Proxy.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test-router.siod-redis").SetIP("************").SetPort(8001).SetIdc("hba").ExecX(context.Background())
				db.Proxy.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test-router.siod-redis").SetIP("************").SetPort(8001).SetIdc("hbb").ExecX(context.Background())

				db.Redis.Delete().Exec(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test-router.siod-redis").SetName("r3-test_server1").SetIP("************").SetPort(7001).SetIdc("hba").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test-router.siod-redis").SetName("r3-test_server1").SetIP("************").SetPort(7001).SetIdc("hbb").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test-router.siod-redis").SetName("r3-test_server2").SetIP("************").SetPort(7002).SetIdc("hba").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
				db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test-router.siod-redis").SetName("r3-test_server2").SetIP("************").SetPort(7002).SetIdc("hbb").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
			},
			args: func() *ent.Stage {
				obj := StageSchemaFlowCut{
					IDC: "hbb",
				}
				params, _ := json.Marshal(obj)
				s := string(params)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: true,
		},
		{
			name: "test3: ras call failed",
			before: func() {
				// mock flowcenter api
				instances := []noah.Instance{
					{IP: "************", Name: "0.r3-test-router.siod-redis", PortInfo: &noah.PortInfo{Main: 8001}, Tags: map[string]string{"idc": "hba"}, Disable: true},
					{IP: "************", Name: "1.r3-test-router.siod-redis", PortInfo: &noah.PortInfo{Main: 8001}, Tags: map[string]string{"idc": "hbb"}, Disable: false},
				}
				str, _ := json.Marshal(instances)
				httpmock.RegisterResponder("GET", "http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-redis/apps/r3-test-router/instances?showPage=0",
					httpmock.NewStringResponder(200, "{\"success\": true, \"message\": \"\", \"data\": "+string(str)+" }"))
			},
			args: func() *ent.Stage {
				obj := StageSchemaFlowCut{
					IDC: "hba",
				}
				params, _ := json.Marshal(obj)
				s := string(params)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := EmergencyFlowCut(tt.args)
			if (err != nil) != tt.wantErr {
				t.Errorf("EmergencyFlowCut() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}
