package cluster

import (
	"encoding/json"
	"redis-xweb/library/ras"

	"dt-common/ent"
	"dt-common/errs"
	"dt-common/logger"
	"dt-common/omodel"
)

type StageSchemaMode struct {
	Current string `json:"current"`
	Target  string `json:"target"`
}

// 修改容器集群状态 operator托管
// 更新metadata
func ChangeMode(stageData *ent.Stage) error {
	var params StageSchemaMode
	err := json.Unmarshal([]byte(*stageData.Parameter), &params)
	if err != nil {
		omodel.StageAppendErrorLog(stageData.ID, "failed to parse stage parameter, value=%v, error=(%v)", stageData.Parameter, err)
		return err
	}

	err = ras.ChangeMode(&ras.ChangeModeParams{
		StageID:     stageData.ID,
		ClusterName: stageData.ClusterName,
		Target:      params.Target,
	})
	if err != nil {
		logger.Error("failed to change cluster mode to %s, error=(%v)", params.Target, err)
		return errs.CodeRequestFailed.Detail(err.Error())
	}
	omodel.StageAppendInfoLog(stageData.ID, "succeed to change cluster mode")

	return errs.Success
}

// 回滚集群托管状态
func RollbackMode(stageData *ent.Stage) error {
	var params StageSchemaMode
	err := json.Unmarshal([]byte(*stageData.Parameter), &params)
	if err != nil {
		omodel.StageAppendErrorLog(stageData.ID, "failed to parse stage parameter, value=%v, error=(%v)", stageData.Parameter, err)
		return err
	}

	err = ras.ChangeMode(&ras.ChangeModeParams{
		StageID:     stageData.ID,
		ClusterName: stageData.ClusterName,
		Target:      params.Current,
	})
	if err != nil {
		logger.Error("failed to rollback cluster mode to %s, error=(%v)", params.Current, err)
		return errs.CodeRequestFailed.Detail(err.Error())
	}
	omodel.StageAppendInfoLog(stageData.ID, "succeed to rollback cluster mode")

	return errs.Success
}
