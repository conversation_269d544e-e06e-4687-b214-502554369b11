package cluster

import (
	"encoding/json"
	"fmt"
	"strings"

	"dt-common/ent"
	"dt-common/ent/cluster"
	"dt-common/ent/proxy"
	"dt-common/errs"
	"dt-common/mysql"
	"dt-common/noah"
	"dt-common/omodel"
	"redis-xweb/library/billing"
	"redis-xweb/library/ras"
)

// 集群Proxy扩缩容
func ProxyScaling(stageData *ent.Stage) error {
	// 参数解析
	var params ras.ProxyScalingParams
	err := json.Unmarshal([]byte(*stageData.Parameter), &params)
	if err != nil {
		omodel.StageAppendErrorLog(stageData.ID, "failed to parse stage parameter, value=%v, error=(%v)", stageData.Parameter, err)
		return errs.CodeUnmarshalError
	}

	// 调用异步接口更新
	params.BasicParams = &ras.BasicParams{
		StageID:     stageData.ID,
		ClusterName: stageData.ClusterName,
	}
	err = ras.ProxyScaling(&params)
	if err != nil {
		return err
	}

	return nil
}

// 扩缩容完成后将集群状态改为normal，更新账单
func CallbackProxyScaling(stageData *ent.Stage) error {
	// 参数解析
	var params ras.ProxyScalingParams
	err := json.Unmarshal([]byte(*stageData.Parameter), &params)
	if err != nil {
		omodel.StageAppendErrorLog(stageData.ID, "failed to parse stage parameter, value=%v, error=(%v)", stageData.Parameter, err)
		return errs.CodeUnmarshalError
	}

	// 获取数据库连接
	db, err := mysql.Database()
	if err != nil {
		omodel.StageAppendErrorLog(stageData.ID, "failed to get mysql connection, error=(%v)", err)
		return err
	}

	// 更新数量
	ctx, cancel := mysql.ContextWithTimeout()
	err = db.Cluster.Update().SetProxyNum(params.ProxyNum).Where(
		cluster.Name(stageData.ClusterName),
	).Exec(ctx)
	cancel()
	if err != nil {
		omodel.StageAppendErrorLog(stageData.ID, "failed to update cluster proxyNum, cluster=%s, error=(%v)", stageData.ClusterName, err)
		return err
	}

	// 账单
	err = billing.ProxyNumChange(stageData.ClusterName, params.ProxyNum)
	if err != nil {
		omodel.StageAppendErrorLog(stageData.ID, "failed to change cluster status to changing, cluster=%s, error=(%v)", stageData.ClusterName, err)
		return err
	}

	return nil
}

//
//
//

// EnableProxyOneByOne 解屏蔽所有容器Proxy
// - 每次执行解开bns中1个proxy的屏蔽
// - 整个迁移流程中会调用2次本阶段
func EnableProxyOneByOne(stageData *ent.Stage) error {
	db, err := mysql.Database()
	if err != nil {
		omodel.StageAppendErrorLog(stageData.ID, "failed to get mysql connection, error=(%v)", err)
		return err
	}

	ctx, cancel := mysql.ContextWithTimeout()
	proxyBns, err := db.Proxy.Query().Select(proxy.FieldBns).
		Where(proxy.ClusterName(stageData.ClusterName)).
		GroupBy(proxy.FieldBns).String(ctx)
	cancel()
	if err != nil {
		omodel.StageAppendErrorLog(stageData.ID, "failed to get mysql connection, error=(%v)", err)
		return err
	}

	// 通过noah获取实例
	strs := strings.Split(proxyBns, ".")
	if len(strs) < 2 {
		err := fmt.Errorf("parameter bns %s format is incorrect", proxyBns)
		omodel.StageAppendErrorLog(stageData.ID, "%v", err)
		return err
	}
	productLine := strs[1]
	appName := strs[0]
	instances, err := noah.GetInstances(productLine, appName)
	if err != nil {
		return err
	}

	unblockNum := 0
	for _, instance := range instances {
		if len(instance.Tags) == 0 {
			omodel.StageAppendErrorLog(stageData.ID, "proxy %s:%d has no tags", instance.IP, instance.PortInfo.Main)
			continue
		}

		// 跳过已解除屏蔽的proxy
		if !instance.Disable {
			omodel.StageAppendInfoLog(stageData.ID, "proxy %s:%d already unblocked", instance.IP, instance.PortInfo.Main)
			continue
		}
		if unblockNum != 0 {
			omodel.StageAppendErrorLog(stageData.ID, "proxy %s:%d is still blocked", instance.IP, instance.PortInfo.Main)
			return fmt.Errorf("阶段重试继续解屏蔽")
		}

		// 解屏蔽一个proxy
		err = noah.EnableInstances(productLine, appName, []string{instance.Name})
		if err != nil {
			omodel.StageAppendErrorLog(stageData.ID, "failed to unblock proxy %s:%d, error=(%v)", instance.IP, instance.PortInfo.Main, err)
			return err
		}
		omodel.StageAppendInfoLog(stageData.ID, "succeed to unblock proxy %s:%d", instance.IP, instance.PortInfo.Main)
		unblockNum++
	}
	omodel.StageAppendInfoLog(stageData.ID, "all proxy instance unblocked")

	return errs.Success
}

//
//
//

// 移除容器实例
func RemoveTaintedProxy(stageData *ent.Stage) error {
	err := ras.RemoveTaintProxy(&ras.BasicParams{StageID: stageData.ID, ClusterName: stageData.ClusterName})
	if err != nil {
		omodel.StageAppendErrorLog(stageData.ID, "failed to call cmanager to remove proxy, cluster=%s, error=(%v)", stageData.ClusterName, err)
		return err
	}

	return nil
}
