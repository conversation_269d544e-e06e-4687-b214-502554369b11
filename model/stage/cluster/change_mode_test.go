package cluster

import (
	"regexp"
	"testing"

	"github.com/jarcoal/httpmock"

	"dt-common/ent"
	"dt-common/errs"
	"redis-xweb/env"
)

func TestChangeMode(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")
	objC := env.MockCluster("change_mode_test")
	_, objS := env.MockTaskWithStage(objC)

	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	tests := []struct {
		name    string
		before  func()
		args    *ent.Stage
		wantErr bool
		expect  func(*testing.T, error)
	}{
		{
			name:    "test1: request failed",
			args:    objS,
			wantErr: true,
		},
		{
			name: "test2: success",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/migration/changeMode`),
					httpmock.NewStringResponder(200, `{"code": "00000", "data": "ok"}`))
			},
			args:    objS,
			wantErr: true,
			expect: func(t *testing.T, err error) {
				if err != errs.Success {
					t.Errorf("ChangeModeToNormal() error = %v, wantErr %v", err, errs.Success)
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := ChangeMode(tt.args)
			if (err != nil) != tt.wantErr {
				t.Errorf("ChangeMode() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, err)
			}
		})
	}

}

func TestRollbackMode(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")
	objC := env.MockCluster("change_mode_test")
	_, objS := env.MockTaskWithStage(objC)

	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	tests := []struct {
		name    string
		before  func()
		args    *ent.Stage
		wantErr bool
		expect  func(*testing.T, error)
	}{
		{
			name:    "test1: request failed",
			args:    objS,
			wantErr: true,
		},
		{
			name: "test2: success",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/migration/changeMode`),
					httpmock.NewStringResponder(200, `{"code": "00000", "data": "ok"}`))
			},
			args:    objS,
			wantErr: true,
			expect: func(t *testing.T, err error) {
				if err != errs.Success {
					t.Errorf("RollbackMode() error = %v, wantErr %v", err, errs.Success)
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := RollbackMode(tt.args)
			if (err != nil) != tt.wantErr {
				t.Errorf("RollbackMode() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, err)
			}
		})
	}
}
