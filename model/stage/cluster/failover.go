package cluster

import (
	"encoding/json"
	"fmt"
	"sort"

	"dt-common/ent"
	"dt-common/ent/redis"
	"dt-common/errs"
	"dt-common/logger"
	"dt-common/mysql"
	"dt-common/omodel"
	"redis-xweb/library/ras"
)

// 主从切换参数
// 优先级: IDC > IPList > Instances
type StageSchemaFailover struct {
	Docker        *int            `json:"docker,omitempty"` // 按容器维度，将主库从指定环境切走（如果有）
	IDC           string          `json:"idc,omitempty"`    // 按机房维度，将主库从指定IDC切走
	IPList        []string        `json:"ipList,omitempty"` // 按IP维度，将主库从指定IP切走
	ShardList     []*omodel.Shard `json:"shardList"`        // 用于回滚
	MaxConcurnecy int             `json:"maxConcurnecy"`
	IntervalTime  int             `json:"intervalTime"`
}

// 按集群名获取分片拓扑
func getRedisShards(stageID int64, clusterName string) ([]*omodel.Shard, error) {
	db, err := mysql.Database()
	if err != nil {
		omodel.StageAppendErrorLog(stageID, "failed to get mysql connection, error=(%v)", err)
		return nil, err
	}
	// 获取所有redis实例，按redis name梳理关系
	ctx, cancel := mysql.ContextWithTimeout()
	redisInstances, err := db.Redis.Query().Where(
		redis.ClusterName(clusterName),
		redis.Status(omodel.INSTANCE_STATUS_NORMAL),
	).All(ctx)
	cancel()
	if err != nil {
		omodel.StageAppendErrorLog(stageID, "failed to query redis instances, cluster=%s, error=(%v)", clusterName, err)
		return nil, err
	}
	if len(redisInstances) == 0 {
		err := fmt.Errorf("there is no redis instances found in cluster %s", clusterName)
		omodel.StageAppendErrorLog(stageID, "%v", err)
		return nil, err
	}

	// 将主从合到shard里，方便后续操作
	shardMap := make(map[string]*omodel.Shard)
	for _, instance := range redisInstances {
		if _, exist := shardMap[instance.Name]; !exist {
			shardMap[instance.Name] = &omodel.Shard{Name: instance.Name, Operated: false}
		}

		item := &omodel.Instance{IP: instance.IP, Port: instance.Port, Docker: instance.Docker, IDC: instance.Idc}
		if instance.Role == omodel.REDIS_ROLE_MASTER {
			if shardMap[instance.Name].Master != nil {
				err := fmt.Errorf("shard %s of cluster %s has more than one master", instance.Name, clusterName)
				omodel.StageAppendErrorLog(stageID, "%v", err)
				return nil, err
			}
			shardMap[instance.Name].Master = item
			continue
		}

		if instance.Role == omodel.REDIS_ROLE_SLAVE {
			if len(shardMap[instance.Name].Slaves) != 0 {
				err := fmt.Errorf("shard %s of cluster %s has more than one slave", instance.Name, clusterName)
				omodel.StageAppendErrorLog(stageID, "%v", err)
				return nil, err
			}
			shardMap[instance.Name].Slaves = []*omodel.Instance{item}
			continue
		}
	}

	// 检查分片map，有没有缺master，有没有多slave
	shardList := []*omodel.Shard{}
	for k, v := range shardMap {
		if v.Master == nil {
			err := fmt.Errorf("shard %s of cluster %s has no master", k, clusterName)
			omodel.StageAppendErrorLog(stageID, "%v", err)
			return nil, err
		}
		if len(v.Slaves) != 1 {
			err := fmt.Errorf("shard %s of cluster %s has wrong number of slaves, num=%d", k, clusterName, len(v.Slaves))
			omodel.StageAppendErrorLog(stageID, "%v", err)
			return nil, err
		}

		shardList = append(shardList, v)
	}
	// 按分片顺序排序
	sort.Slice(shardList, func(i, j int) bool {
		return shardList[i].Name < shardList[j].Name
	})

	return shardList, nil
}

// ============================================================
// 				     按维度获取FragmentList
// ============================================================

// 按部署环境进行切换，将主库从指定环境中切走
// 条件：主从库需要部署在不同的环境中
func getFragmentByDeployEnv(shardList []*omodel.Shard, docker int) ([]*omodel.Shard, error) {
	for _, shard := range shardList {
		// 主从部署环境一致不符合条件，报错退出
		if shard.Master.Docker == shard.Slaves[0].Docker {
			return nil, fmt.Errorf("redis server of shard %s depoly in same environment, docker=%d", shard.Name, shard.Master.Docker)
		}

		// 主库部署在要切走的环境中
		if shard.Master.Docker == docker {
			shard.Master, shard.Slaves[0] = shard.Slaves[0], shard.Master
			shard.Operated = true
		}
	}

	return shardList, nil
}

// 按机房进行切换，主库从指定机房切走
func getFragmentByIDC(shardList []*omodel.Shard, idc string) ([]*omodel.Shard, error) {
	for _, shard := range shardList {
		// 主从部署在同一个机房不符合条件，报错退出
		if shard.Master.IDC == shard.Slaves[0].IDC {
			return nil, fmt.Errorf("redis server of shard %s depoly in same idc, idc=%s", shard.Name, idc)
		}

		// 主库部署在要切走的机房中
		if shard.Master.IDC == idc {
			shard.Master, shard.Slaves[0] = shard.Slaves[0], shard.Master
			shard.Operated = true
		}
	}

	return shardList, nil
}

// 按IP进行切换，主库从指定IP上切走
func getFragmentByIP(shardList []*omodel.Shard, ipList []string) ([]*omodel.Shard, error) {
	ipFilter := map[string]bool{}
	for _, ip := range ipList {
		ipFilter[ip] = true
	}

	for _, shard := range shardList {
		// 主库部署在要切走的IP上
		if ipFilter[shard.Master.IP] {
			shard.Master, shard.Slaves[0] = shard.Slaves[0], shard.Master
			shard.Operated = true
		}
	}

	return shardList, nil
}

// ============================================================
// 					       调整并发度
// ============================================================

// 按主库ip将分片分组
func groupByHostIP(shardList []*omodel.Shard) [][]*omodel.Shard {
	indexMap := map[string]int{}
	shardLists := [][]*omodel.Shard{}

	ipNum := 0
	for _, shard := range shardList {
		idx, ok := indexMap[shard.Master.IP]
		if !ok {
			shardLists = append(shardLists, []*omodel.Shard{})
			indexMap[shard.Master.IP] = ipNum
			idx = ipNum
			ipNum += 1
		}

		shardLists[idx] = append(shardLists[idx], shard)
	}
	return shardLists
}

// 计算并发度
// 1、一次最多切4个分片
func calMaxConcurnecy(shardList []*omodel.Shard) int {
	con := 0
	shardLists := groupByHostIP(shardList)
	if len(shardLists) != len(shardList) {
		return len(shardLists)
	}

	con = len(shardLists) / 4 // 分4批failover
	if con > 4 {
		con = 4 // 一次最多切4个分片
	}

	return con
}

// ============================================================
// 					     执行/回滚/停止
// ============================================================

// 主从切换
// 每次执行都重新查一遍分片结构，不复用parameters中的fragmentList和concurrentList
func Failover(stageData *ent.Stage) error {
	var params StageSchemaFailover
	err := json.Unmarshal([]byte(*stageData.Parameter), &params)
	if err != nil {
		omodel.StageAppendErrorLog(stageData.ID, "failed to parse stage parameter, value=%v, error=(%v)", stageData.Parameter, err)
		return err
	}

	// 获取集群的redis-server拓扑结构
	shards, err := getRedisShards(stageData.ID, stageData.ClusterName)
	if err != nil {
		return err
	}
	// 按维度获取主从分片关系
	var shardList []*omodel.Shard
	if params.Docker != nil {
		shardList, err = getFragmentByDeployEnv(shards, *params.Docker)
	} else if params.IDC != "" {
		shardList, err = getFragmentByIDC(shards, params.IDC)
	} else if params.IPList != nil {
		shardList, err = getFragmentByIP(shards, params.IPList)
	} else if len(params.ShardList) == 0 {
		omodel.StageAppendErrorLog(stageData.ID, "missing parameter, docker/idc/ipList/shardList should at least have one non empty value")
		return fmt.Errorf("缺少参数, docker/idc/ipList/shardList至少有一个非空值")
	}
	if err != nil {
		return err
	}

	// 计算并发
	con := calMaxConcurnecy(shardList)
	if params.MaxConcurnecy == 0 {
		params.MaxConcurnecy = con
	}

	// 保存到stage.parameter用于回滚
	err = omodel.StageAppendParameter(stageData.ID, map[string]any{
		"shardList":     shardList,
		"maxConcurnecy": params.MaxConcurnecy,
	})
	if err != nil {
		omodel.StageAppendErrorLog(stageData.ID, "failed to save stage parameter, shardList=%v, maxConcurnecy=%v, error=(%v)", shardList, con, err)
		return err
	}

	// 请求cmanager进行主从切换
	err = ras.Failover(&ras.FailoverParams{
		StageID:       stageData.ID,
		ClusterName:   stageData.ClusterName,
		ShardList:     shardList,
		MaxConcurnecy: params.MaxConcurnecy,
		IntervalTime:  params.IntervalTime,
	})
	if err != nil {
		omodel.StageAppendErrorLog(stageData.ID, "failed to call manager to failover, cluster=%s, error=(%v)", stageData.ClusterName, err)
		return err
	}

	return nil
}

// 主从切换回滚
// 从stage.parameter中获取参数，反转数组后调用cmanager接口
func RollbackFailover(stageData *ent.Stage) error {
	// 解析阶段参数
	var params StageSchemaFailover
	err := json.Unmarshal([]byte(*stageData.Parameter), &params)
	if err != nil {
		omodel.StageAppendErrorLog(stageData.ID, "failed to parse stage parameter, value=%v, error=(%v)", stageData.Parameter, err)
		return err
	}
	if len(params.ShardList) == 0 {
		omodel.StageAppendErrorLog(stageData.ID, "missing parameter, shardList and concurrentList can not be empty")
		return fmt.Errorf("shardList和maxConcurnecy不能为空")
	}

	// 过滤operated的分片
	shardList := []*omodel.Shard{}
	for _, shard := range params.ShardList {
		if shard.Operated {
			shardList = append(shardList, shard)
		}
	}
	if len(shardList) == 0 {
		return errs.Success
	}

	if params.MaxConcurnecy == 0 {
		params.MaxConcurnecy = len(shardList)
	}

	// 反转数组，反转主从
	for i, j := 0, len(shardList)-1; i <= j; i, j = i+1, j-1 {
		shardList[i].Master, shardList[i].Slaves[0] = shardList[i].Slaves[0], shardList[i].Master
		if i != j {
			shardList[j].Master, shardList[j].Slaves[0] = shardList[j].Slaves[0], shardList[j].Master
			shardList[i], shardList[j] = shardList[j], shardList[i]
		}
	}
	logger.Debug("failover rollback, shardList=[%+v]", shardList)

	// 执行主从切换
	err = ras.Failover(&ras.FailoverParams{
		StageID:       stageData.ID,
		ClusterName:   stageData.ClusterName,
		ShardList:     shardList,
		MaxConcurnecy: params.MaxConcurnecy,
		IntervalTime:  5, // 回滚时快速切换，不等120s
	})
	if err != nil {
		omodel.StageAppendErrorLog(stageData.ID, "failed to call manager to rollback failover, cluster=%s, error=(%v)", stageData.ClusterName, err)
		return err
	}

	return nil
}

// 停止主从切换
func StopFailover(stageData *ent.Stage) error {
	// 停止主从切换
	err := ras.StopFailover(&ras.BasicParams{
		StageID:     stageData.ID,
		ClusterName: stageData.ClusterName,
	})
	if err != nil {
		omodel.StageAppendErrorLog(stageData.ID, "failed to call manager to stop failover, cluster=%s, error=(%v)", stageData.ClusterName, err)
		return err
	}

	return nil
}
