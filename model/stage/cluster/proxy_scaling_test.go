package cluster

import (
	"context"
	"encoding/json"
	"regexp"
	"testing"

	"github.com/jarcoal/httpmock"

	"dt-common/ent"
	"dt-common/ent/cluster"
	"dt-common/errs"
	"dt-common/mysql"
	"dt-common/noah"
	"redis-xweb/env"
	"redis-xweb/library/ras"
)

// 单测：ProxyScaling
func TestProxyScaling(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")
	objC := env.MockCluster("proxy_scaling_test")
	objT, objS := env.MockTaskWithStage(objC)
	// db, _ := mysql.Database()

	httpmock.Activate()
	defer httpmock.Deactivate()

	tests := []struct {
		name    string
		before  func()
		args    *ent.Stage
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "check params",
			args: func() *ent.Stage {
				paramStr := "-1"
				objS.Parameter = &paramStr
				return objS
			}(),
			wantErr: true,
		},
		{
			name: "success",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/cluster/proxy/scaling`),
					httpmock.ResponderFromResponse(httpmock.NewStringResponse(200, `{"code": "00000", "data": "ok"}`)))
			},
			args: func() *ent.Stage {
				obj := ras.ProxyScalingParams{
					ProxyNum: 4,
				}
				params, _ := json.Marshal(obj)
				s := string(params)
				stage := env.MockStage(objT)
				stage.Parameter = &s
				return stage
			}(),
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := ProxyScaling(tt.args)
			if (err != nil) != tt.wantErr {
				t.Errorf("ProxyScaling() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

func TestCallbackProxyScaling(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")
	objC := env.MockCluster("proxy_scaling_test")
	_, objS := env.MockTaskWithStage(objC)
	db, _ := mysql.Database()

	httpmock.Activate()
	defer httpmock.Deactivate()

	tests := []struct {
		name    string
		before  func()
		args    *ent.Stage
		wantErr bool
		expect  func(*testing.T)
	}{

		{
			name: "success",
			before: func() {
				objC.Update().SetProxyNum(2).Exec(context.Background())
				httpmock.RegisterResponder("POST", "http://10.32.141.8:8800/v2/api/billing/order/info",
					httpmock.ResponderFromResponse(httpmock.NewStringResponse(200, "{\"errno\":0,\"errmsg\":\"success\",\"data\":[{\"resourceName\":\"REDIS\",\"resourceNameCh\":\"缓存数据库\",\"resourceId\":100049,\"resourceType\":\"\",\"comboName\":\"redis-科技云-单分片\",\"comboId\":21,\"comboCode\":100049985621,\"comboDesc\":\"\",\"comboUnitInfo\":\"\",\"unitPrice\":0,\"departCode\":1000000037,\"department\":\"系统运维部\",\"team\":\"\",\"useType\":0,\"business\":[{\"name\":\"理财\",\"percent\":100}],\"product\":[],\"isAckPercentMonthly\":false,\"percentDesc\":\"\",\"isUseAuthPercent\":false,\"authPercentName\":\"\",\"authPercentCode\":\"\",\"busReview\":null,\"tag\":null,\"cloudName\":\"\",\"cloudLabel\":\"dev\",\"desc\":\"redis: billing_desc_test\",\"RDOwner\":\"\",\"OPOwner\":\"\",\"zoneType\":\"\",\"idc\":\"\",\"useTypeName\":\"普通服务\",\"useForProduct\":\"\",\"staticQuantity\":null,\"id\":\"65f94c5bff1b3ecb5fcb7d0e\",\"instanceId\":\"billing_desc_test\",\"snapInfo\":{\"num\":1},\"costId\":\"\",\"monthPrice\":230,\"orderCode\":\"1000492168276591\",\"owner\":\"jiayiming_dxm\",\"startTime\":\"2024-03-19T16:27:07.653+08:00\",\"endTime\":\"0001-01-01T08:05:43+08:05\",\"createdAt\":\"2024-03-19T16:27:07.684+08:00\",\"updateAt\":\"2024-03-19T16:27:07.684+08:00\",\"dynamicComboGroup\": [{\"comboName\": \"\",\"comboId\":0,\"comboCode\":491488382,\"comboDesc\":\"\",\"comboUnitInfo\":\"\",\"unitPrice\":0,\"comboNum\":2},{\"comboName\": \"\",\"comboId\":0,\"comboCode\":491488383,\"comboDesc\":\"\",\"comboUnitInfo\":\"\",\"unitPrice\":0,\"comboNum\":2},{\"comboName\": \"\",\"comboId\":0,\"comboCode\":491488384,\"comboDesc\":\"\",\"comboUnitInfo\":\"\",\"unitPrice\":0,\"comboNum\":10},{\"comboName\": \"\",\"comboId\":0,\"comboCode\":491488385,\"comboDesc\":\"\",\"comboUnitInfo\":\"\",\"unitPrice\":0,\"comboNum\":1}],\"status\":1,\"parentCode\":\"\",\"busPercentStatus\":0,\"instanceName\":\"\",\"shortId\":\"redis-57im2gwy4ab\"}]}")))
				httpmock.RegisterResponder("POST", "http://10.32.141.8:8800/v2/api/billing/order/modify",
					httpmock.ResponderFromResponse(httpmock.NewStringResponse(200, "{\"errno\":0,\"errmsg\":\"success\",\"data\":\"\"}")))
			},
			args: func() *ent.Stage {
				obj := ras.ProxyScalingParams{
					ProxyNum: 4,
				}
				params, _ := json.Marshal(obj)
				s := string(params)
				objS.Parameter = &s
				return objS
			}(),
			wantErr: false,
			expect: func(t *testing.T) {
				c, _ := db.Cluster.Query().Where(cluster.ID(objC.ID)).Only(context.Background())
				if c.ProxyNum != 4 {
					t.Errorf("expect cluster status proxyNum 4, but got %d", c.ProxyNum)
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := CallbackProxyScaling(tt.args)
			if (err != nil) != tt.wantErr {
				t.Errorf("CallbackProxyScaling() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

// 解屏蔽容器proxy实例
func TestEnableProxyOneByOne(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")
	objC := env.MockCluster("enable_proxy")
	_, objS := env.MockTaskWithStage(objC)

	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	tests := []struct {
		name    string
		before  func()
		args    *ent.Stage
		wantErr bool
		expect  func(*testing.T, error)
	}{
		{
			name: "noah request failed",
			before: func() {
				db, _ := mysql.Database()
				db.Proxy.Create().SetCluster(objC).SetClusterName(objC.Name).SetBns("enable-proxy-router.siod-redis").SetIP("************").SetIdc("hba").SetPort(8001).Exec(context.Background())
				db.Proxy.Create().SetCluster(objC).SetClusterName(objC.Name).SetBns("enable-proxy-router.siod-redis").SetIP("************").SetIdc("hba").SetPort(8001).Exec(context.Background())
				db.Proxy.Create().SetCluster(objC).SetClusterName(objC.Name).SetBns("enable-proxy-router.siod-redis").SetIP("************").SetIdc("hbb").SetPort(8001).Exec(context.Background())
				db.Proxy.Create().SetCluster(objC).SetClusterName(objC.Name).SetBns("enable-proxy-router.siod-redis").SetIP("************").SetIdc("hbb").SetPort(8001).Exec(context.Background())
			},
			args:    objS,
			wantErr: true,
		},
		{
			name: "still has proxy unblocked and block failed",
			before: func() {
				// mock flowcenter api
				instances := []noah.Instance{
					{IP: "************", PortInfo: &noah.PortInfo{Main: 8004}, Disable: false},                                        // 没打tag
					{IP: "************", PortInfo: &noah.PortInfo{Main: 8004}, Tags: map[string]string{"idc": "hba"}, Disable: false}, // 非指定机房
					{IP: "************", PortInfo: &noah.PortInfo{Main: 8004}, Tags: map[string]string{"idc": "hbb"}, Disable: false}, // 未屏蔽
					{IP: "************", PortInfo: &noah.PortInfo{Main: 8004}, Tags: map[string]string{"idc": "hbb"}, Disable: true},  // 已屏蔽
				}
				str, _ := json.Marshal(instances)
				httpmock.RegisterResponder("GET", "http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-redis/apps/enable-proxy-router/instances?showPage=0",
					httpmock.NewStringResponder(200, "{\"success\": true, \"message\": \"\", \"data\": "+string(str)+" }"))
			},
			args:    objS,
			wantErr: true,
		},
		{
			name: "test6: unblock success and still has proxy blocked",
			before: func() {
				// mock flowcenter api
				instances := []noah.Instance{
					{IP: "************", PortInfo: &noah.PortInfo{Main: 8004}, Tags: map[string]string{"idc": "hbb"}, Disable: true},
					{IP: "************", PortInfo: &noah.PortInfo{Main: 8004}, Tags: map[string]string{"idc": "hbb"}, Disable: true},
				}
				str, _ := json.Marshal(instances)
				httpmock.RegisterResponder("GET", "http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-redis/apps/enable-test-router/instances?showPage=0",
					httpmock.NewStringResponder(200, "{\"success\": true, \"message\": \"\", \"data\": "+string(str)+" }"))

				httpmock.RegisterResponder("POST", "http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-redis/apps/enable-proxy-router/instances/batchBlock",
					httpmock.NewStringResponder(200, `{"success": true, "message": "", "data": "ok" }`))
			},
			args:    objS,
			wantErr: true,
		},
		{
			name: "test7: success",
			before: func() {
				// mock flowcenter api
				instances := []noah.Instance{
					{IP: "************", PortInfo: &noah.PortInfo{Main: 8004}, Tags: map[string]string{"idc": "hbb"}, Disable: false},
					{IP: "************", PortInfo: &noah.PortInfo{Main: 8004}, Tags: map[string]string{"idc": "hbb"}, Disable: false},
				}
				str, _ := json.Marshal(instances)
				httpmock.RegisterResponder("GET", "http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-redis/apps/enable-test-router/instances?showPage=0",
					httpmock.NewStringResponder(200, "{\"success\": true, \"message\": \"\", \"data\": "+string(str)+" }"))
			},
			args:    objS,
			wantErr: true,
			expect: func(t *testing.T, err error) {
				if err != errs.Success {
					t.Errorf("EnableProxyOneByOne() expect success but get err %v", err)
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := EnableProxyOneByOne(tt.args)
			if (err != nil) != tt.wantErr {
				t.Errorf("EnableProxyOneByOne() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, err)
			}
		})
	}
}
