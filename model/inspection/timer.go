package inspection

import (
	"fmt"
	"strings"

	"github.com/robfig/cron/v3"

	"dt-common/hi"
	"dt-common/logger"
	"dt-common/utils"
)

var c *cron.Cron

// 启动定时巡检任务
func Start(bns string, conf *Config) error {
	ip, err := utils.GetLocalIP()
	if err != nil {
		return err
	}

	// 初始化全局变量
	tmp := strings.Split(bns, ".")
	if len(tmp) < 2 {
		err = fmt.Errorf("[Inspection] manager bns format error, bns=%s", bns)
		hiErr := hi.To().Text("%s managerBns配置格式错误\n%s", localIP, hi.At()).Shutup(600).Send()
		if hiErr != nil {
			logger.Error("[Hi] %v", hiErr)
		}
		return err
	}
	// 默认值赋值
	conf = format(conf)

	localIP = ip
	managerProduct = tmp[1]
	managerApp = tmp[0]
	logger.Info("[Inspection] localIP %s, cmanager bns %s.%s", localIP, managerApp, managerProduct)

	l := &CronLogger{}
	c = cron.New(cron.WithLogger(l), cron.WithSeconds(), cron.WithChain(guard, cron.SkipIfStillRunning(l)))
	defer c.Start()

	// =====================================
	// 		       服务节点探测
	// =====================================
	_, err = c.AddFunc("@every 10s", JobUpdateInstanceNo)
	if err != nil {
		return fmt.Errorf("failed to add timer JobUpdateInstanceNo, error=(%v)", err)
	}

	// =====================================
	// 			   已用内存同步
	// =====================================
	spec := fmt.Sprintf("@every %ds", conf.MemorySync.Interval)
	_, err = c.AddFunc(spec, JobMemorySync)
	if err != nil {
		return fmt.Errorf("failed to add timer JobMemorySync, error=(%v)", err)
	}

	// =====================================
	// 			  Proxy Prober
	// =====================================
	interval = conf.ProxyProber.Interval
	concurrency = conf.ProxyProber.Concurrency
	minEnabledNumPerIDC = conf.ProxyProber.KeepEnabledNum
	// 每 ProxyServiceUpdateInterval 进行一次数据更新
	spec = fmt.Sprintf("@every %ds", conf.ProxyProber.MetaInterval)
	_, err = c.AddFunc(spec, JobUpdateProxyServices)
	if err != nil {
		return fmt.Errorf("failed to add timer JobUpdateProxyServices, error=(%v)", err)
	}
	// 每 ProxyProberInterval 进行一次 proxy 探活
	spec = fmt.Sprintf("@every %ds", conf.ProxyProber.Interval)
	_, err = c.AddFunc(spec, JobProxyProber)
	if err != nil {
		return fmt.Errorf("failed to add timer JobProxyProber, error=(%v)", err)
	}

	// =====================================
	// 			      集群渲染
	// =====================================
	spec = fmt.Sprintf("@every %ds", conf.ClusterReconcile.Interval)
	_, err = c.AddFunc(spec, JobReconcile)
	if err != nil {
		return fmt.Errorf("failed to add timer JobClusterReconcile, error=(%v)", err)
	}

	return nil
}

// 停止定时巡检任务
func Stop() {
	if c != nil {
		ctx := c.Stop()
		<-ctx.Done()
	}
}
