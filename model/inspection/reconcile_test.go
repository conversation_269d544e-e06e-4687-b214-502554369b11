package inspection

import (
	"context"
	"strings"
	"testing"

	"entgo.io/ent/dialect/sql"
	"github.com/jarcoal/httpmock"

	"dt-common/ent"
	"dt-common/ent/deployment"
	"dt-common/mysql"
	"dt-common/omodel"
	"redis-cmanager/env"
	"redis-cmanager/library/renderer"
)

func TestJobReconcile(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")

	type args struct{}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test: no deployment",
			before: func() {
				redisServices = []*ent.Cluster{{Name: "test"}}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			JobReconcile()
			// if (err != nil) != tt.wantErr {
			// 	t.<PERSON>rf("IsProxyAlive() error = %v, wantErr %v", err, tt.wantErr)
			// }
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

func TestClusterReconcile(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")
	objC := env.MockCluster("reconcile")
	db, _ := mysql.Database()

	type args struct {
		clusterName string
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test: no deployment",
			args: args{
				clusterName: objC.Name,
			},
		},
		{
			name: "test: not care",
			before: func() {
				db.Deployment.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).
					SetAlias(strings.ReplaceAll(objC.Name, "_", "-")).SetSpec(`{"clusterWhitelist":["jiayiming.unit"]}`).
					SetInspectionMode(omodel.MODE_NOT_CARE).SetInspectionResult(`{}`).
					SetVersion(1).SetEnabledAz("hba,hbb").
					Exec(context.Background())
			},
			args: args{
				clusterName: objC.Name,
			},
			expect: func(t *testing.T) {
				d, _ := db.Deployment.Query().Where(deployment.ClusterID(objC.ID)).Order(deployment.ByClusterID(sql.OrderDesc())).First(context.Background())
				if d.InspectionResult != "{}" {
					t.Errorf("expect InspectionResult {}, but got %s", d.InspectionResult)
				}
			},
		},
		{
			name: "test: failed 5 times",
			before: func() {
				db.Deployment.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).
					SetAlias(strings.ReplaceAll(objC.Name, "_", "-")).SetSpec(`{"clusterWhitelist":["jiayiming.unit"]}`).
					SetInspectionMode(omodel.MODE_FULL_CARE).SetInspectionResult(`{"state": "TEST", "errMsg": "test", "stepErrTimes":[0,0,0,0,0,0,0,0,2,5,0,0,0,0]}`).
					SetVersion(3).SetEnabledAz("hba,hbb").
					Exec(context.Background())
			},
			args: args{
				clusterName: objC.Name,
			},
			expect: func(t *testing.T) {
				d, _ := db.Deployment.Query().Where(deployment.ClusterID(objC.ID)).Order(deployment.ByClusterID(sql.OrderDesc())).First(context.Background())
				if d.InspectionMode != omodel.MODE_NOT_CARE {
					t.Errorf("expect InspectionMode notCare, but got %s", d.InspectionMode)
				}
			},
		},
		{
			name: "test: success",
			before: func() {
				db.Deployment.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).
					SetAlias(strings.ReplaceAll(objC.Name, "_", "-")).SetSpec(`{"clusterWhitelist":["jiayiming.unit"]}`).
					SetInspectionMode(omodel.MODE_FULL_CARE).SetInspectionResult(`{"state": "TEST"}`).
					SetVersion(4).SetEnabledAz("hba,hbb").
					Exec(context.Background())
			},
			args: args{
				clusterName: objC.Name,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			clusterReconcile(tt.args.clusterName)
			// if (err != nil) != tt.wantErr {
			// 	t.Errorf("IsProxyAlive() error = %v, wantErr %v", err, tt.wantErr)
			// }
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

func TestGetRealtimeProgress(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")

	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		clusterName string
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, *renderer.InspectionResult)
	}{
		{
			name: "test: not running",
			before: func() {
				redisServices = []*ent.Cluster{{Name: "public_test"}}
			},
			args: args{
				clusterName: "public_test",
			},
			wantErr: true,
		},
		{
			name: "test: success",
			before: func() {
				localIP = "************"
				totalInsMap = map[string][]string{
					"hba": {"************"},
					"hbb": {"************"},
				}
				httpmock.RegisterResponder("POST", "http://************/redis-cmanager/inspection/render/progress",
					httpmock.NewStringResponder(200, `{"code": "00000", "message": "OK", "data": {}}`))
			},
			args: args{
				clusterName: "public_test2",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			result, err := GetRealtimeProgress(tt.args.clusterName)
			if (err != nil) != tt.wantErr {
				t.Errorf("IsProxyAlive() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, result)
			}
		})
	}
}
