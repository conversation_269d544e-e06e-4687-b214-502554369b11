package inspection

import (
	"fmt"
	"net/http"
	"sort"

	"dt-common/ent"
	"dt-common/ent/cluster"
	"dt-common/ent/predicate"
	"dt-common/ent/proxy"
	"dt-common/hi"
	"dt-common/logger"
	"dt-common/mysql"
	"dt-common/noah"
	"dt-common/omodel"
)

var (
	localIP        string // 本机IP
	localIDC       string // 本机所属IDC
	managerProduct string // cmanager bns的product
	managerApp     string // cmanager bns的app name

	totalInsMap   map[string][]string = map[string][]string{} // cmanager中的所有实例
	totalNumInAll int                 = 0                     // cmanager所有机房的实例总数
	totalNumInIdc int                 = 0                     // cmanager同机房的实例总数
	localIdxInAll int                 = -1                    // cmanager所有机房实例中当前实例的索引，从0开始
	localIdxInIdc int                 = -1                    // cmanager同机房实例中当前实例的索引，从0开始

	proxyServices []*ProxyService // 当前实例负责的proxy服务列表，同机房cmanager平分
	redisServices []*ent.Cluster  // 当前实例负责的redis服务列表，所有cmanager平分
)

// 按照Name排序，获取当前实例的索引及可用实例数
// 当结果与上次检查不同时，修改 changed = true，否则修改 changed = false
func JobUpdateInstanceNo() {
	// 获取bns下的所有实例
	instances, err := noah.GetInstances(managerProduct, managerApp)
	if err != nil {
		logger.Error("[Inspection] failed to get instances from noah, bns=%s.%s, error=(%v)", managerApp, managerProduct, err)
		return
	}
	// 按照Offset排序
	sort.Slice(instances, func(i, j int) bool {
		return instances[i].Name < instances[j].Name
	})

	// 对每个实例进行探活，计算当前实例的索引
	tmpLocalIDC, tmpTotalMap, tmpNumInAll, tmpIdxInAll, tmpIdxInIdc := "", map[string][]string{}, 0, -1, -1
	for i, instance := range instances {
		// 不启用屏蔽的节点
		if instance.Disable {
			continue
		}
		// 实例没有IDC tag的报警
		idc, exist := instance.Tags["idc"]
		if !exist {
			logger.Error("[Inspection] cmanager %s:%d has no idc tag", instance.IP, instance.PortInfo.Main)
			err := hi.To().Text("[%s.%s] 实例 %s 缺少标签「idc」\n%s", managerApp, managerProduct, instance.Name, hi.At()).Shutup(600).Send()
			if err != nil {
				logger.Error("[Hi] %v", err)
			}
			continue
		}

		// 当前实例不需要进行健康检查
		if instance.IP == localIP {
			tmpLocalIDC = idc                   // 本实例的所属IDC
			tmpIdxInAll = i                     // 当前实例在所有实例中的索引
			tmpIdxInIdc = len(tmpTotalMap[idc]) // 当前实例在同机房实例中的索引
		} else {
			url := fmt.Sprintf("http://%s:%d/healthCheck", instance.IP, instance.PortInfo.Main)
			resp, err := http.Get(url)
			if err != nil {
				logger.Error("[Inspection] cmanager %s:%d healthCheck failed, error=(%v)", instance.IP, instance.PortInfo.Main, err)
				err := hi.To().Text("[%s.%s] 实例 %s 健康检查不通过\n%s", managerApp, managerProduct, instance.Name, hi.At()).Shutup(1800).Send()
				if err != nil {
					logger.Error("[Hi] %v", err)
				}
				continue
			}
			defer resp.Body.Close()
		}

		tmpNumInAll += 1
		tmpTotalMap[idc] = append(tmpTotalMap[idc], fmt.Sprintf("%s:%d", instance.IP, instance.PortInfo.Main))
	}

	// 单边风险报警
	if len(tmpTotalMap) < 2 {
		logger.Error("[Inspection] cmanager deployed only one idc")
		err := hi.To().Text("[%s.%s] 单边机房风险\n%s", managerApp, managerProduct, hi.At()).Shutup(1800).Send()
		if err != nil {
			logger.Error("[Hi] %v", err)
		}
	}

	localIDC = tmpLocalIDC
	totalInsMap = tmpTotalMap

	// 全局数量变化或同机房数量变化会重新分配集群负载
	if totalNumInAll != tmpNumInAll || localIdxInAll != tmpIdxInAll || totalNumInIdc != len(tmpTotalMap[localIDC]) || localIdxInIdc != tmpIdxInIdc {
		totalNumInAll = tmpNumInAll
		totalNumInIdc = len(tmpTotalMap[localIDC])
		localIdxInAll = tmpIdxInAll
		localIdxInIdc = tmpIdxInIdc

		logger.Info("[Inspection] changed! totalInsNum=%d, idxInAll=%d, numInIdc=%d, idxInIdc=%d", totalNumInAll, localIdxInAll, totalNumInIdc, localIdxInIdc)

		// 立即触发一次数据更新
		JobUpdateProxyServices()
		JobUpdateRedisInstances()
	}
}

// ====================================
// 			Proxy自动屏蔽
// ====================================

type ProxyService struct {
	ClusterName string `sql:"cluster_name"`
	BNS         string `sql:"bns"`
	Password    string `sql:"password"`
}

// 结构体打印
func (s *ProxyService) String() string {
	return fmt.Sprintf("{cluster_name=%s, bns=%s, password=%s}", s.ClusterName, s.BNS, s.Password)
}

// 获取proxy bns信息
func JobUpdateProxyServices() {
	// 当前节点未启用
	if localIdxInIdc == -1 {
		proxyServices = []*ProxyService{}
		return
	}

	db, err := mysql.Database()
	if err != nil {
		logger.Error("failed to get mysql connection, error=(%v)", err)
		return
	}
	// 查询所有集群
	ctx, cancel := mysql.ContextWithTimeout()
	clusters, err := db.Cluster.Query().
		Select(cluster.FieldName, cluster.FieldPassword).
		Where(cluster.StatusNEQ(omodel.CLUSTER_STATUS_DELETED)).
		Order(cluster.ByID()).All(ctx)
	cancel()
	if err != nil {
		logger.Error("failed to query cluster, error=(%v)", err)
		return
	}

	// 按同机房实例数取模
	conditions := []predicate.Proxy{}
	passwordMap := map[string]string{}
	names := []string{}
	for i, c := range clusters {
		if i%totalNumInIdc == localIdxInIdc {
			names = append(names, c.Name)
			passwordMap[c.Name] = c.Password
		}
	}
	if totalNumInIdc != 1 {
		conditions = append(conditions, proxy.ClusterNameIn(names...))
	}

	// 按集群获取所有proxy bns
	services := []*ProxyService{}
	ctx, cancel = mysql.ContextWithTimeout()
	err = db.Proxy.Query().Select(proxy.FieldClusterName, proxy.FieldBns).Where(conditions...).
		GroupBy(proxy.FieldClusterName, proxy.FieldBns).Scan(ctx, &services)
	cancel()
	if err != nil {
		return
	}

	// 匹配密码，集群信息不存在时跳过
	for _, service := range services {
		if _, ok := passwordMap[service.ClusterName]; ok {
			service.Password = passwordMap[service.ClusterName]
		}
	}

	proxyServices = services
	logger.Info("[Inspection] proxyServices len %d", len(proxyServices))
}

// 获取当前实例负责的集群列表
func JobUpdateRedisInstances() {
	// 当前节点未启用
	if localIdxInAll == -1 {
		redisServices = []*ent.Cluster{}
		return
	}

	db, err := mysql.Database()
	if err != nil {
		logger.Error("failed to get mysql connection, error=(%v)", err)
		return
	}
	// 查询所有集群
	ctx, cancel := mysql.ContextWithTimeout()
	clusters, err := db.Cluster.Query().
		Select(cluster.FieldName).
		Where(cluster.StatusNEQ(omodel.CLUSTER_STATUS_DELETED)).
		Order(cluster.ByID()).All(ctx)
	cancel()
	if err != nil {
		logger.Error("failed to query cluster, error=(%v)", err)
		return
	}

	// 按行号取模
	conditions := []predicate.Cluster{cluster.StatusNEQ(omodel.CLUSTER_STATUS_DELETED)}
	if totalNumInAll != 1 {
		names := []string{}
		for i, c := range clusters {
			if i%totalNumInAll == localIdxInAll {
				names = append(names, c.Name)
			}
		}
		conditions = append(conditions, cluster.NameIn(names...))
	}

	// 按集群获取所有redis实例，主+从，物理+容器
	ctx, cancel = mysql.ContextWithTimeout()
	redisServices, err = db.Cluster.Query().Where(conditions...).WithRedis().All(ctx)
	cancel()
	if err != nil {
		logger.Error("failed to query cluster with redis instances, error=(%v)", err)
		return
	}

	logger.Info("[Inspection] redisServices len %d", len(redisServices))
}
