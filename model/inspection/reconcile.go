package inspection

import (
	"encoding/json"
	"fmt"
	"strings"

	"dt-common/errs"
	"dt-common/logger"
	"dt-common/omodel"
	"redis-cmanager/library/renderer"
)

var stepRetryTimes = 5

// 集群渲染 30s/次
func JobReconcile() {
	// 需要更新docker字段
	JobUpdateRedisInstances()
	for _, redisService := range redisServices {
		// 物理集群不检查
		if redisService.Docker == omodel.DEPLOY_ENV_BBC {
			continue
		}

		go clusterReconcile(redisService.Name)
	}
}

// 集群渲染
func clusterReconcile(clusterName string) {
	// 获取集群deployment
	deployment, err := renderer.GetDeploymentFromDB(clusterName)
	if err != nil {
		logger.Error("[Reconcile] failed to get deployment from db, cluster=%s, error=(%v)", clusterName, err)
		return
	}
	// 判断是否托管
	if deployment.InspectionMode == omodel.MODE_NOT_CARE {
		logger.Debug("[Reconcile] cluster is set to notCare, skip, cluster=%s", clusterName)
		return
	}

	// 启动渲染
	err = renderer.Render(deployment)
	if err != nil {
		logger.Warn("[Reconcile] end up with exception, cluster=%s, error=(%v)", clusterName, err)
		// 检查 stepErrTimes 数组中有没有大于等于 X 的阶段，如果有暂停巡检人工介入
		for _, times := range deployment.InspectionResult.StepErrTimes {
			if times < stepRetryTimes {
				continue
			}
			err := renderer.UpdateDeploymentMode(clusterName, omodel.MODE_NOT_CARE)
			if err != nil {
				logger.Error("[Reconcile] failed to change inspection mode to notCare, cluster=%s, error=(%v)", clusterName, err)
			}
			logger.Error("[Reconcile] manual intervention is required, cluster=%s", clusterName)
		}
	}
}

// 获取集群渲染实时进度
func GetRealtimeProgress(clusterName string) (*renderer.InspectionResult, error) {
	// 先检查集群是否是在本实例上渲染的
	local := false
	for _, redisService := range redisServices {
		if redisService.Name == clusterName {
			local = true
			break
		}
	}

	if local {
		if result, err := renderer.GetInspectionProgress(clusterName); err == nil {
			return result, nil
		}
		logger.Info("[RealtimeProgress] cluster %s not found in memory", clusterName)
	} else {
		// 不在本实例渲染，调用其他cmanager获取
		params := map[string]any{"clusterName": clusterName}
		for _, hosts := range totalInsMap {
			for _, host := range hosts {
				if strings.HasPrefix(host, localIP+":") {
					continue
				}

				url := fmt.Sprintf("http://%s/redis-cmanager/inspection/render/progress", host)
				resp, err := post(url, params)
				if err != nil {
					logger.Error("[RealtimeProgress] failed to call cmanager realtime progress, host=%s, error=(%v)", host, err)
					continue
				}
				// 调用失败跳过，预期错误：rendering of cluster is not running
				if resp.Code != string(errs.Success) {
					logger.Info("[RealtimeProgress] %s, host=%s", resp.Message, host)
					continue
				}

				// 结果格式化失败报错返回
				var result renderer.InspectionResult
				err = json.Unmarshal(resp.Data, &result)
				if err != nil {
					logger.Error("[RealtimeProgress] wrong format of value %s", host, resp.Data)
					return nil, err
				}

				// 调用成功返回结果
				return &result, nil
			}
		}
	}

	// 内存里没查到，返回数据库结果
	deployment, err := renderer.GetDeploymentFromDB(clusterName)
	if err != nil {
		return nil, err
	}
	return deployment.InspectionResult, nil
}
