package inspection

import (
	"encoding/json"
	"fmt"
	"regexp"
	"sync"
	"testing"
	"time"

	"github.com/jarcoal/httpmock"

	"dt-common/redisc"
	"redis-cmanager/env"
)

func TestGetLastProberResult(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")

	type args struct {
		ip   string
		port int
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, bool)
	}{
		{
			name: "test1: ************:8001 alive",
			before: func() {
				recentResults.Store("************:8001", ProberResult{IsAlive: true, UpdatedAt: time.Now()})
			},
			args: args{
				ip:   "************",
				port: 8001,
			},
			wantErr: false,
			expect: func(t *testing.T, alive bool) {
				if !alive {
					t.<PERSON><PERSON><PERSON>("************:8001 alive expect true, but got false")
				}
			},
		},
		{
			name: "test2: ************:8001 expired",
			before: func() {
				recentResults.Store("************:8001", ProberResult{IsAlive: true, UpdatedAt: time.Now().Add(-30 * time.Second)})
			},
			args: args{
				ip:   "************",
				port: 8001,
			},
			wantErr: true,
		},
		{
			name: "test3: ************:8001 not exist",
			args: args{
				ip:   "************",
				port: 8001,
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			alive, err := GetLastProberResult(tt.args.ip, tt.args.port)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetLastProberResult() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, alive)
			}
		})
	}
}

func TestIsProxyAlive(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		ip       string
		port     int
		password string
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, bool)
	}{
		{
			name: "test1: ************:8001 ping ok",
			before: func() {
				redisc.Mock().ExpectPing().SetVal("PONG")
			},
			args: args{
				ip:       "************",
				port:     8001,
				password: "123456",
			},
			wantErr: false,
			expect: func(t *testing.T, alive bool) {
				if !alive {
					t.Errorf("************:8001 alive expect true, but got false")
				}
			},
		},
		{
			name: "test2: ************:8001 noauth",
			before: func() {
				redisc.Mock().ExpectPing().SetErr(fmt.Errorf("NOAUTH Authentication required"))
			},
			args: args{
				ip:   "************",
				port: 8001,
			},
			wantErr: false,
			expect: func(t *testing.T, alive bool) {
				if !alive {
					t.Errorf("************:8001 alive expect true, but got false")
				}
			},
		},
		{
			name: "test3: ************:8001 ping notOK, but alive",
			before: func() {
				redisc.Mock().ExpectPing().SetErr(fmt.Errorf("Connection refused"))
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/proxy/status\?port=8001`),
					httpmock.NewStringResponder(200, `{"code": "00000", "data": true}`))
			},
			args: args{
				ip:       "************",
				port:     8001,
				password: "123456",
			},
			wantErr: false,
			expect: func(t *testing.T, alive bool) {
				if !alive {
					t.Errorf("************:8001 alive expect true, but got false")
				}
			},
		},
		{
			name: "test4: ************:8001 ping notOK, not alive",
			before: func() {
				redisc.Mock().ExpectPing().SetErr(fmt.Errorf("Connection refused"))
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/proxy/status\?port=8001`),
					httpmock.NewStringResponder(200, `{"code": "00000", "data": false}`))
			},
			args: args{
				ip:       "************",
				port:     8001,
				password: "123456",
			},
			wantErr: false,
			expect: func(t *testing.T, alive bool) {
				if alive {
					t.Errorf("************:8001 alive expect false, but got true")
				}
			},
		},
		{
			name: "test5: ************:8001 ping notOK, agent notOK",
			before: func() {
				redisc.Mock().ExpectPing().SetErr(fmt.Errorf("Connection refused"))
				httpmock.Reset()
			},
			args: args{
				ip:   "************",
				port: 8003,
			},
			wantErr: false,
			expect: func(t *testing.T, alive bool) {
				if alive {
					t.Errorf("************:8001 alive expect false, but got true")
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			alive := isProxyAlive(tt.args.ip, tt.args.port, tt.args.password)
			// if (err != nil) != tt.wantErr {
			// 	t.Errorf("IsProxyAlive() error = %v, wantErr %v", err, tt.wantErr)
			// }
			if tt.expect != nil {
				tt.expect(t, alive)
			}
		})
	}
}

func TestPost(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		url    string
		params map[string]any
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, *response)
	}{
		{
			name: "test1: ************:8001 ping ok",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/redis-cmanager/inspection/proxy/prober`),
					httpmock.NewStringResponder(200, `{"code": "00000", "data": true}`))
			},
			args: args{
				url:    "http://************:8811/redis-cmanager/inspection/proxy/prober",
				params: map[string]any{"key": "value"},
			},
			wantErr: false,
			expect: func(t *testing.T, resp *response) {
				var remoteAlive bool
				err := json.Unmarshal(resp.Data, &remoteAlive)
				if err != nil {
					t.Errorf("unexpect unmarshal error, error=(%v)", err)
					return
				}

				if !remoteAlive {
					t.Errorf("expect resp.Data is true, but got false")
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			resp, err := post(tt.args.url, tt.args.params)
			if (err != nil) != tt.wantErr {
				t.Errorf("post() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, resp)
			}
		})
	}
}

func TestShouldProxyDisabled(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		ip       string
		port     int
		password string
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, bool)
	}{
		{
			name: "test1: ************:8001 ping ok",
			before: func() {
				redisc.Mock().ExpectPing().SetVal("PONG")
			},
			args: args{
				ip:       "************",
				port:     8001,
				password: "123123",
			},
			wantErr: false,
			expect: func(t *testing.T, shouldDisable bool) {
				if shouldDisable {
					t.Errorf("************:8001 shoud not disable, but got true")
				}
			},
		},
		{
			name: "test2: ************:8001 ping not ok, 1 cmanager instance",
			before: func() {
				redisc.Mock().ExpectPing().SetErr(fmt.Errorf("Connection refused"))
				localIDC = "hba"
				totalInsMap = map[string][]string{
					"hba": {"************:8811"},
				}
			},
			args: args{
				ip:       "************",
				port:     8001,
				password: "123123",
			},
			wantErr: false,
			expect: func(t *testing.T, shouldDisable bool) {
				if shouldDisable {
					t.Errorf("************:8001 shoud not disable, but got true")
				}
			},
		},
		{
			name: "test3: ************:8001 ping not ok, but other ok",
			before: func() {
				redisc.Mock().ExpectPing().SetErr(fmt.Errorf("Connection refused"))
				localIdxInIdc = 0
				localIDC = "hba"
				totalInsMap = map[string][]string{
					"hba": {"************:8811"},
					"hbb": {"************:8811"},
				}

				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/redis-cmanager/inspection/proxy/prober`),
					httpmock.NewStringResponder(200, `{"code": "00000", "data": true}`))
			},
			args: args{
				ip:       "************",
				port:     8001,
				password: "123123",
			},
			wantErr: false,
			expect: func(t *testing.T, shouldDisable bool) {
				if shouldDisable {
					t.Errorf("************:8001 shoud not disable, but got true")
				}
			},
		},
		{
			name: "test4: ************:8001 ping not ok, other not ok",
			before: func() {
				redisc.Mock().ExpectPing().SetErr(fmt.Errorf("Connection refused"))
				localIdxInIdc = 0
				localIDC = "hba"
				totalInsMap = map[string][]string{
					"hba": {"************:8811"},
					"hbb": {"************:8811"},
				}

				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/redis-cmanager/inspection/proxy/prober`),
					httpmock.NewStringResponder(200, `{"code": "00000", "data": false}`))
			},
			args: args{
				ip:       "************",
				port:     8001,
				password: "123123",
			},
			wantErr: false,
			expect: func(t *testing.T, shouldDisable bool) {
				if !shouldDisable {
					t.Errorf("************:8001 shoud disable, but got false")
				}
			},
		},
		{
			name: "test5: ************:8001 ping not ok, other cannot connect",
			before: func() {
				redisc.Mock().ExpectPing().SetErr(fmt.Errorf("Connection refused"))
				localIdxInIdc = 0
				localIDC = "hba"
				totalInsMap = map[string][]string{
					"hba": {"************:8811"},
					"hbb": {"************:8811"},
				}

				httpmock.Reset()
			},
			args: args{
				ip:       "************",
				port:     8001,
				password: "123123",
			},
			wantErr: false,
			expect: func(t *testing.T, shouldDisable bool) {
				if shouldDisable {
					t.Errorf("************:8001 shoud not disable, but got true")
				}
			},
		},
		{
			name: "test6: ************:8001 ping not ok, 4 cmanager",
			before: func() {
				redisc.Mock().ExpectPing().SetErr(fmt.Errorf("Connection refused"))
				localIdxInIdc = 0
				totalNumInIdc = 2
				localIDC = "hba"
				totalInsMap = map[string][]string{
					"hba": {"************:8811", "************:8811"},
					"hbb": {"************:8811", "************:8811"},
				}

				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/redis-cmanager/inspection/proxy/prober`),
					httpmock.NewStringResponder(200, `{"code": "00000", "data": false}`))
			},
			args: args{
				ip:       "************",
				port:     8001,
				password: "123123",
			},
			wantErr: false,
			expect: func(t *testing.T, shouldDisable bool) {
				if !shouldDisable {
					t.Errorf("************:8001 shoud disable, but got false")
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			shouldDisable := shouldProxyDisabled(tt.args.ip, tt.args.port, tt.args.password)
			// if (err != nil) != tt.wantErr {
			// 	t.Errorf("ProxyJudger() error = %v, wantErr %v", err, tt.wantErr)
			// }
			if tt.expect != nil {
				tt.expect(t, shouldDisable)
			}
		})
	}
}

func TestPrintBnsStatus(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		productLine       string
		appName           string
		disabledHostNames string
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test1: success",
			before: func() {
				httpmock.RegisterResponder("GET", "http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-kafka/apps/r3-test-router/instances?showPage=0",
					httpmock.NewStringResponder(200, `{"success": true, "message": "OK", "data": [
						{"name": "0.r3-test-router.siod-kafka","hostName": "kafka0000.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8001},"deployInfo": {"deployPath": "/home/<USER>"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hba"},"disable": false,"instanceType": "host","podId": null,"containerList": []},
						{"name": "1.r3-test-router.siod-kafka","hostName": "kafka0002.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8002},"deployInfo": {"deployPath": "/home/<USER>"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hba"},"disable": true,"instanceType": "host","podId": null,"containerList": []},
						{"name": "2.r3-test-router.siod-kafka","hostName": "kafka0051.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8003},"deployInfo": {"deployPath": "/home/<USER>"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hbb"},"disable": false,"instanceType": "host","podId": null,"containerList": []},
						{"name": "3.r3-test-router.siod-kafka","hostName": "kafka0004.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8004},"deployInfo": {"deployPath": "/home/<USER>"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hbb"},"disable": true,"instanceType": "host","podId": null,"containerList": []},
						{"name": "4.r3-test-router.siod-kafka","hostName": "kafka0006.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8005},"deployInfo": {"deployPath": "/home/<USER>"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hbb"},"disable": true,"instanceType": "host","podId": null,"containerList": []}
					]}`),
				)
			},
			args: args{
				productLine:       "siod-kafka",
				appName:           "r3-test-router",
				disabledHostNames: "2.r3-qa-test-sec-1-router.siod-kafka",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			printBnsStatus(tt.args.productLine, tt.args.appName, tt.args.disabledHostNames)
			// if (err != nil) != tt.wantErr {
			// 	t.Errorf("ProxyJudger() error = %v, wantErr %v", err, tt.wantErr)
			// }
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

// TEST：
func TestExecDisableInstances(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	httpmock.RegisterResponder("POST", "http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-kafka/apps/r3-test-router/instances/batchBlock",
		httpmock.NewStringResponder(200, `{"success": true, "message": "OK", "data": {}}`),
	)

	type args struct {
		product            string
		app                string
		totalHostsNum      map[string]float64
		alreadyDisabledNum map[string]float64
		needDisableHosts   map[string][]string
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test1: no instance need to disable",
			before: func() {
				minEnabledNumPerIDC = 0.5
			},
			args: args{
				product:            "siod-kafka",
				app:                "r3-test-router",
				totalHostsNum:      map[string]float64{"hba": 2, "hbb": 2},
				alreadyDisabledNum: map[string]float64{"hba": 0, "hbb": 1},
				needDisableHosts:   map[string][]string{},
			},
			expect: func(t *testing.T) {
				m := httpmock.GetCallCountInfo()
				if m["POST http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-kafka/apps/r3-test-router/instances/batchBlock"] != 0 {
					t.Errorf("expect call 0 but got %d", m["POST http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-kafka/apps/r3-test-router/instances/batchBlock"])
				}
			},
		},
		{
			name: "test2: disable no matter what",
			before: func() {
				minEnabledNumPerIDC = 0
			},
			args: args{
				product:            "siod-kafka",
				app:                "r3-test-router",
				totalHostsNum:      map[string]float64{"hba": 2, "hbb": 2},
				alreadyDisabledNum: map[string]float64{"hba": 0, "hbb": 1},
				needDisableHosts: map[string][]string{
					"hbb": {"2.r3-test-router.siod-kafka"},
				},
			},
			expect: func(t *testing.T) {
				m := httpmock.GetCallCountInfo()
				if m["POST http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-kafka/apps/r3-test-router/instances/batchBlock"] != 1 {
					t.Errorf("expect call 1 but got %d", m["POST http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-kafka/apps/r3-test-router/instances/batchBlock"])
				}
			},
		},
		{
			name: "test3: disable greater than 50%",
			before: func() {
				minEnabledNumPerIDC = 0.5
			},
			args: args{
				product:            "siod-kafka",
				app:                "r3-test-router",
				totalHostsNum:      map[string]float64{"hba": 2, "hbb": 2},
				alreadyDisabledNum: map[string]float64{"hba": 0, "hbb": 1},
				needDisableHosts: map[string][]string{
					"hbb": {"2.r3-test-router.siod-kafka"},
				},
			},
			expect: func(t *testing.T) {
				m := httpmock.GetCallCountInfo()
				if m["POST http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-kafka/apps/r3-test-router/instances/batchBlock"] != 1 {
					t.Errorf("expect call 1 but got %d", m["POST http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-kafka/apps/r3-test-router/instances/batchBlock"])
				}
			},
		},
		{
			name: "test4: disable less than 50%",
			before: func() {
				minEnabledNumPerIDC = 0.5
			},
			args: args{
				product:            "siod-kafka",
				app:                "r3-test-router",
				totalHostsNum:      map[string]float64{"hba": 2, "hbb": 2},
				alreadyDisabledNum: map[string]float64{"hba": 0, "hbb": 1},
				needDisableHosts: map[string][]string{
					"hba": {"0.r3-test-router.siod-kafka"},
				},
			},
			expect: func(t *testing.T) {
				m := httpmock.GetCallCountInfo()
				if m["POST http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-kafka/apps/r3-test-router/instances/batchBlock"] != 2 {
					t.Errorf("expect call 2 but got %d", m["POST http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-kafka/apps/r3-test-router/instances/batchBlock"])
				}
			},
		},
		{
			name: "test5: disable less than 2",
			before: func() {
				minEnabledNumPerIDC = 2
			},
			args: args{
				product:            "siod-kafka",
				app:                "r3-test-router",
				totalHostsNum:      map[string]float64{"hba": 2, "hbb": 2},
				alreadyDisabledNum: map[string]float64{"hba": 0, "hbb": 1},
				needDisableHosts: map[string][]string{
					"hba": {"0.r3-test-router.siod-kafka"},
				},
			},
			expect: func(t *testing.T) {
				m := httpmock.GetCallCountInfo()
				if m["POST http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-kafka/apps/r3-test-router/instances/batchBlock"] != 2 {
					t.Errorf("expect call 2 but got %d", m["POST http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-kafka/apps/r3-test-router/instances/batchBlock"])
				}
			},
		},
		{
			name: "test5: disable less than 2",
			before: func() {
				minEnabledNumPerIDC = 1
			},
			args: args{
				product:            "siod-kafka",
				app:                "r3-test-router",
				totalHostsNum:      map[string]float64{"hba": 2, "hbb": 2},
				alreadyDisabledNum: map[string]float64{"hba": 0, "hbb": 1},
				needDisableHosts: map[string][]string{
					"hba": {"0.r3-test-router.siod-kafka", "1.r3-test-router.siod-kafka"},
				},
			},
			expect: func(t *testing.T) {
				m := httpmock.GetCallCountInfo()
				if m["POST http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-kafka/apps/r3-test-router/instances/batchBlock"] != 3 {
					t.Errorf("expect call 3 but got %d", m["POST http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-kafka/apps/r3-test-router/instances/batchBlock"])
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			execDisableInstances(tt.args.product, tt.args.app, tt.args.totalHostsNum, tt.args.alreadyDisabledNum, tt.args.needDisableHosts)
			// if (err != nil) != tt.wantErr {
			// 	t.Errorf("ClusterProber() error = %v, wantErr %v", err, tt.wantErr)
			// }
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

// TEST：
func TestClusterProber(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	wg := &sync.WaitGroup{}
	ch := make(chan int, 4)

	type args struct {
		wg          *sync.WaitGroup
		concurrency chan int
		bns         string
		password    string
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test1: ************:8001 not ok",
			before: func() {
				wg.Add(1)
				localIdxInIdc = 0
				localIDC = "hba"
				totalInsMap = map[string][]string{
					"hba": {"************:8811"},
					"hbb": {"************:8811"},
				}

				httpmock.RegisterResponder("GET", "http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-kafka/apps/r3-test-router/instances?showPage=0",
					httpmock.NewStringResponder(200, `{"success": true, "message": "OK", "data": [
						{"name": "0.r3-test-router.siod-kafka","hostName": "kafka0000.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8001},"deployInfo": {"deployPath": "/home/<USER>"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {},"disable": false,"instanceType": "host","podId": null,"containerList": []},
						{"name": "1.r3-test-router.siod-kafka","hostName": "kafka0002.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8002},"deployInfo": {"deployPath": "/home/<USER>"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hba"},"disable": true,"instanceType": "host","podId": null,"containerList": []},
						{"name": "2.r3-test-router.siod-kafka","hostName": "kafka0051.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8003},"deployInfo": {"deployPath": "/home/<USER>"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hbb"},"disable": false,"instanceType": "host","podId": null,"containerList": []},
						{"name": "3.r3-test-router.siod-kafka","hostName": "kafka0001.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8004},"deployInfo": {"deployPath": "/home/<USER>"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hbb"},"disable": false,"instanceType": "host","podId": null,"containerList": []}
					]}`),
				)

				redisc.Mock().ExpectPing().SetErr(fmt.Errorf("Connection refused"))
				redisc.Mock().ExpectPing().SetErr(fmt.Errorf("Connection refused"))
				redisc.Mock().ExpectPing().SetErr(fmt.Errorf("Connection refused"))
				redisc.Mock().ExpectPing().SetErr(fmt.Errorf("Connection refused"))

				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/proxy/status\?port=8001`),
					httpmock.NewStringResponder(200, `{"code": "00000", "data": false}`))
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/proxy/status\?port=8002`),
					httpmock.NewStringResponder(200, `{"code": "00000", "data": true}`))
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/proxy/status\?port=8003`),
					httpmock.NewStringResponder(200, `{"code": "00000", "data": true}`))
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/proxy/status\?port=8004`),
					httpmock.NewStringResponder(200, `{"code": "00000", "data": true}`))
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/redis-cmanager/inspection/proxy/prober`),
					httpmock.NewStringResponder(200, `{"code": "00000", "data": false}`))
			},
			args: args{
				wg:          wg,
				concurrency: ch,
				bns:         "r3-test-router.siod-kafka",
				password:    "123123",
			},
		},
		{
			name: "test2: hbb not ok",
			before: func() {
				wg.Add(1)
				localIdxInIdc = 0
				localIDC = "hba"
				totalInsMap = map[string][]string{
					"hba": {"************:8811"},
					"hbb": {"************:8811"},
				}

				httpmock.RegisterResponder("GET", "http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-kafka/apps/r3-test-router/instances?showPage=0",
					httpmock.NewStringResponder(200, `{"success": true, "message": "OK", "data": [
						{"name": "0.r3-test-router.siod-kafka","hostName": "kafka0000.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8001},"deployInfo": {"deployPath": "/home/<USER>"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hba"},"disable": false,"instanceType": "host","podId": null,"containerList": []},
						{"name": "1.r3-test-router.siod-kafka","hostName": "kafka0002.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8002},"deployInfo": {"deployPath": "/home/<USER>"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hba"},"disable": false,"instanceType": "host","podId": null,"containerList": []},
						{"name": "2.r3-test-router.siod-kafka","hostName": "kafka0051.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8003},"deployInfo": {"deployPath": "/home/<USER>"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hbb"},"disable": false,"instanceType": "host","podId": null,"containerList": []},
						{"name": "3.r3-test-router.siod-kafka","hostName": "kafka0001.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8004},"deployInfo": {"deployPath": "/home/<USER>"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hbb"},"disable": false,"instanceType": "host","podId": null,"containerList": []}
					]}`),
				)

				redisc.Mock().ExpectPing().SetErr(fmt.Errorf("Connection refused"))
				redisc.Mock().ExpectPing().SetErr(fmt.Errorf("Connection refused"))
				redisc.Mock().ExpectPing().SetErr(fmt.Errorf("Connection refused"))
				redisc.Mock().ExpectPing().SetErr(fmt.Errorf("Connection refused"))

				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/proxy/status\?port=8001`),
					httpmock.NewStringResponder(200, `{"code": "00000", "data": true}`))
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/proxy/status\?port=8002`),
					httpmock.NewStringResponder(200, `{"code": "00000", "data": true}`))
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/proxy/status\?port=8003`),
					httpmock.NewStringResponder(200, `{"code": "00000", "data": false}`))
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/proxy/status\?port=8004`),
					httpmock.NewStringResponder(200, `{"code": "00000", "data": false}`))
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/redis-cmanager/inspection/proxy/prober`),
					httpmock.NewStringResponder(200, `{"code": "00000", "data": false}`))
			},
			args: args{
				wg:          wg,
				concurrency: ch,
				bns:         "r3-test-router.siod-kafka",
				password:    "123123",
			},
		},
		{
			name: "test2: 50% limit",
			before: func() {
				wg.Add(1)
				localIdxInIdc = 0
				localIDC = "hba"
				totalInsMap = map[string][]string{
					"hba": {"************:8811"},
					"hbb": {"************:8811"},
				}

				httpmock.RegisterResponder("GET", "http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-kafka/apps/r3-test-router/instances?showPage=0",
					httpmock.NewStringResponder(200, `{"success": true, "message": "OK", "data": [
						{"name": "0.r3-test-router.siod-kafka","hostName": "kafka0000.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8001},"deployInfo": {"deployPath": "/home/<USER>"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hba"},"disable": false,"instanceType": "host","podId": null,"containerList": []},
						{"name": "1.r3-test-router.siod-kafka","hostName": "kafka0002.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8002},"deployInfo": {"deployPath": "/home/<USER>"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hba"},"disable": false,"instanceType": "host","podId": null,"containerList": []},
						{"name": "2.r3-test-router.siod-kafka","hostName": "kafka0051.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8003},"deployInfo": {"deployPath": "/home/<USER>"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hbb"},"disable": false,"instanceType": "host","podId": null,"containerList": []},
						{"name": "3.r3-test-router.siod-kafka","hostName": "kafka0004.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8004},"deployInfo": {"deployPath": "/home/<USER>"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hbb"},"disable": false,"instanceType": "host","podId": null,"containerList": []},
						{"name": "4.r3-test-router.siod-kafka","hostName": "kafka0006.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8005},"deployInfo": {"deployPath": "/home/<USER>"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hbb"},"disable": true,"instanceType": "host","podId": null,"containerList": []}
					]}`),
				)

				redisc.Mock().ExpectPing().SetErr(fmt.Errorf("Connection refused"))
				redisc.Mock().ExpectPing().SetErr(fmt.Errorf("Connection refused"))
				redisc.Mock().ExpectPing().SetErr(fmt.Errorf("Connection refused"))
				redisc.Mock().ExpectPing().SetErr(fmt.Errorf("Connection refused"))

				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/proxy/status\?port=8001`), httpmock.NewStringResponder(200, `{"code": "00000", "data": false}`))
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/proxy/status\?port=8002`), httpmock.NewStringResponder(200, `{"code": "00000", "data": true}`))
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/proxy/status\?port=8003`), httpmock.NewStringResponder(200, `{"code": "00000", "data": false}`))
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/proxy/status\?port=8004`), httpmock.NewStringResponder(200, `{"code": "00000", "data": false}`))

				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/redis-cmanager/inspection/proxy/prober`), httpmock.NewStringResponder(200, `{"code": "00000", "data": false}`))
			},
			args: args{
				wg:          wg,
				concurrency: ch,
				bns:         "r3-test-router.siod-kafka",
				password:    "123123",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			clusterProber(tt.args.wg, tt.args.concurrency, tt.args.bns, tt.args.password)
			// if (err != nil) != tt.wantErr {
			// 	t.Errorf("ClusterProber() error = %v, wantErr %v", err, tt.wantErr)
			// }
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

func TestJobProxyProber(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct{}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test1: nil",
			before: func() {
				proxyServices = []*ProxyService{
					{ClusterName: "r3_test", BNS: "r3-test-router.siod-redis", Password: "1231231"},
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			JobProxyProber()
			// if (err != nil) != tt.wantErr {
			// 	t.Errorf("ProxyJudger() error = %v, wantErr %v", err, tt.wantErr)
			// }
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}
