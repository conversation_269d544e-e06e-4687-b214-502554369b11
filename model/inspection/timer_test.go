package inspection

import (
	"testing"

	"redis-cmanager/env"
)

func TestStart(t *testing.T) {
	env.<PERSON><PERSON>(t, "../../config/config.yaml")

	type args struct {
		bns  string
		conf *Config
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name:   "test1: bns fotmat error",
			before: func() {},
			args: args{
				bns: "redis-cmanager",
				conf: &Config{
					ProxyProber: &ProxyProber{
						MetaInterval:   0,
						Interval:       10,
						Concurrency:    300,
						KeepEnabledNum: 0.5,
					},
				},
			},
			wantErr: true,
			expect:  func(t *testing.T) {},
		},
		{
			name:   "test2: success",
			before: func() {},
			args: args{
				bns: "redis-cmanager.siod-redis",
				conf: &Config{
					MemorySync: &MemorySync{
						Interval: 10,
					},
					ProxyProber: &ProxyProber{
						MetaInterval:   10,
						Interval:       10,
						Concurrency:    300,
						KeepEnabledNum: 2,
					},
					ClusterReconcile: &ClusterReconcile{
						Interval:       10,
						Concurrency:    10,
						StepRetryTimes: 5,
					},
				},
			},
			wantErr: false,
			expect: func(t *testing.T) {
				Stop()
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := Start(tt.args.bns, tt.args.conf)
			if (err != nil) != tt.wantErr {
				t.Errorf("Start() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}
