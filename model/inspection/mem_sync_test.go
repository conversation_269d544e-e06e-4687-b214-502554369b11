package inspection

import (
	"context"
	"testing"

	"dt-common/mysql"
	"dt-common/redisc"
	"redis-cmanager/env"
)

func TestJobMemorySync(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")

	// mock集群数据
	objC := env.MockCluster("mem_sync_test")
	db, _ := mysql.Database()
	db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(0).SetBns("sync-test-router.siod-redis").SetName("sync_test_server1").SetIP("************").SetPort(7000).SetIdc("hba").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())

	type args struct{}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test1: redis info failed, errCount++",
			expect: func(t *testing.T) {

			},
		},
		{
			name: "test2: maxmemory string to int failed, errCount++",
			before: func() {
				redisc.Mock().ExpectInfo().SetVal("maxmemory:abc")
			},
		},
		{
			name: "test2: used_memory string to int failed, errCount++",
			before: func() {
				redisc.Mock().ExpectInfo().SetVal("maxmemory:123\nused_memory:abc")
			},
		},
		{
			name: "test3: success",
			before: func() {
				redisc.Mock().ExpectInfo().SetVal("maxmemory:123\nused_memory:123\nrole:slave")
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			JobMemorySync()
			// if (err != nil) != tt.wantErr {
			// 	t.Errorf("syncRedis() error = %v, wantErr %v", err, tt.wantErr)
			// }
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}
