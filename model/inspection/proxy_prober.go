package inspection

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"sort"
	"strings"
	"sync"
	"time"

	"dt-common/errs"
	"dt-common/hi"
	"dt-common/logger"
	"dt-common/noah"
	"dt-common/redisc"
	"redis-cmanager/library/ragent"
)

type ProberResult struct {
	IsAlive   bool
	UpdatedAt time.Time
}

var (
	// 上次的proxy探活结果，key为ip:port，value为ProberResult
	recentResults sync.Map
	// 探活间隔，单位为秒
	interval time.Duration = 10
	// 探活并发数
	concurrency int = 100
	// 最少生效实例数
	minEnabledNumPerIDC float64 = 0.5
)

// 获取最近一次的Proxy探活结果
func GetLastProberResult(ip string, port int) (bool, error) {
	key := fmt.Sprintf("%s:%d", ip, port)
	value, ok := recentResults.Load(key)
	if !ok {
		return false, fmt.Errorf("%s:%d prober result not found", ip, port)
	}

	ret := value.(ProberResult)
	// 更新时间大于2个周期，数据不可信
	if time.Since(ret.UpdatedAt) > (2 * interval * time.Second) {
		recentResults.Delete(key)
		return false, fmt.Errorf("%s:%d prober result expired", ip, port)
	}

	return ret.IsAlive, nil
}

// 判断指定的proxy是否存活
// 1、通过ping proxy探测，如果ping通，返回TRUE，如果ping不通，转2
// 2、通过redis-agent检查进程存活状态，proxy存活，返回TRUE，否则返回FALSE，agent调不通转3
// 3、立刻重试，3次都没正常返回，认为proxy不可用，返回FALSE
// ping proxy 超时设置1s，ping agent超时设置2s
func isProxyAlive(ip string, port int, password string) bool {
	options := redisc.ConnOptions{}
	if password != "" {
		options.Password = &password
	}

	// 如果能确定proxy的状态立即返回，不能确定就重试，2次重试后仍不能确定则返回false
	for i := 0; i < 2; i++ {
		// step1. 尝试ping proxy，需要密码
		client, err := redisc.Client(ip, port, options)
		if err != nil {
			logger.Error("[ProxyProber] failed to create proxy client, proxy=%s:%d, retryTime=%d, error=(%v)", ip, port, err)
			continue
		}
		defer client.Close()
		ctx, cancel := redisc.ReadTimeout()
		ret, err := client.Ping(ctx).Result()
		cancel()
		if err == nil && ret == "PONG" {
			return true
		}
		// 权限问题另行纠正，但Proxy确定是活着的
		if strings.Contains(err.Error(), "NOAUTH") || strings.Contains(err.Error(), "no password") {
			logger.Warn("[ProxyProber] database got wrong password for proxy %s:%d, password=%s, error=(%v)", ip, port, password, err)
			err := hi.To().Text("[Proxy Prober] Proxy %s:%d密码有误，请检查数据库并及时更正! \n%s", ip, port, hi.At()).Shutup(600).Send()
			if err != nil {
				logger.Error("[Hi] %v", err)
			}
			return true
		}
		logger.Warn("[ProxyProber] failed to ping proxy %s:%d, retryTime=%d, error=(%v)", ip, port, i, err)

		// step2. 尝试请求agent获取proxy的状态
		alive, err := ragent.ProxyStatus(ip, port)
		if err == nil {
			logger.Debug("[ProxyProber] proxy %s:%d status is %v", ip, port, alive)
			return alive
		}
		// redis-agent调用失败报错（agent探活用的netstat，异常概率极小，调用失败最大可能是网络或agent挂了）
		logger.Warn("[ProxyProber] failed to call redis-agent on %s, error=(%v)", ip, err)
	}

	return false
}

// proxy探活接口返回值结构
type response struct {
	Code    string          `json:"code"`
	Message string          `json:"msg,omitempty"`
	Data    json.RawMessage `json:"data,omitempty"`
}

// 请求cmanger proxy prober接口
func post(url string, params interface{}) (*response, error) {
	// 请求接口获取探活结果
	body, err := json.Marshal(params)
	if err != nil {
		return nil, err
	}

	var resp *http.Response
	for i := 0; i < 3; i++ {
		resp, err = http.Post(url, "application/json", strings.NewReader(string(body)))
		if err != nil {
			time.Sleep(500 * time.Millisecond)
			continue
		}
		break
	}
	if resp == nil {
		return nil, err
	}

	resBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	result := response{}
	err = json.Unmarshal(resBody, &result)
	if err != nil {
		return nil, err
	}

	return &result, nil
}

// 多点决策是否屏蔽一个proxy
// return true -> proxy应该被屏蔽
// 每个机房都有1个cmanager节点负责某个proxy的探活
//   - 只有全部机房都有节点认为proxy该屏蔽时才能屏蔽
func shouldProxyDisabled(ip string, port int, password string) bool {
	// step1. 本地探测&记录结果
	alive := isProxyAlive(ip, port, password)
	recentResults.Store(fmt.Sprintf("%s:%d", ip, port), ProberResult{IsAlive: alive, UpdatedAt: time.Now()})
	if alive {
		logger.Debug("[ProxyProber] proxy %s:%d alived", ip, port)
		return false
	}
	logger.Info("[ProxyProber] proxy %s:%d seems to be dead, start voting", ip, port)

	// step2. 获取其他节点探测结果
	if len(totalInsMap) < 2 {
		return false
	}

	votes := 1
	params := map[string]any{"ip": ip, "port": port}
	for idc, hosts := range totalInsMap {
		if idc == localIDC {
			continue
		}

		// 单机房节点数与本机房一致时，只看同索引的即可
		if len(hosts) == totalNumInIdc {
			hosts = hosts[localIdxInIdc : localIdxInIdc+1]
		}

		for _, host := range hosts {
			url := fmt.Sprintf("http://%s/redis-cmanager/inspection/proxy/prober", host)
			result, err := post(url, params)
			if err != nil {
				logger.Error("[ProxyProber] failed to call cmanager proxy prober, host=%s, error=(%v)", host, err)
				continue
			}

			// 调用失败不屏蔽
			if result.Code != string(errs.Success) {
				logger.Info("[ProxyProber] cmanager %s abstain from voting, %s", host, result.Message)
				break
			}

			// 返回值格式化失败不屏蔽
			var remoteAlive bool
			err = json.Unmarshal(result.Data, &remoteAlive)
			if err != nil {
				logger.Error("[ProxyProber] cmanager %s abstain from voting, wrong format of value %s", host, result.Data)
				break
			}

			// 只要有一个节点认为proxy是活着的，就不能屏蔽
			if remoteAlive {
				logger.Info("[ProxyProber] cmanager %s vote for proxy %s:%d alive", host, ip, port)
				break
			}
			votes += 1
			logger.Info("[ProxyProber] cmanager %s vote for proxy %s:%d dead", host, ip, port)
		}
	}

	return len(totalInsMap) == votes
}

// 操作通告
// 【Proxy Prober】
// 【BNS】：r3-qa-test-sec-1-router.siod-kafka
// 【屏蔽实例】：2.r3-qa-test-sec-1-router.siod-kafka
// 【hba机房】：总节点 2，生效中 2
// 【hbb机房】：总节点 2，生效中 1
func printBnsStatus(productLine string, appName string, disabledHostNames string) {
	messages := []string{"【Proxy Prober】",
		fmt.Sprintf("【BNS】：%s.%s", appName, productLine),
		fmt.Sprintf("【屏蔽实例】：%s", disabledHostNames),
	}

	defer func() {
		logger.Info("[ProxyProber] succeed to disable instances %s from bns %s.%s", disabledHostNames, appName, productLine)
		err := hi.To().Text("%s\n%s", strings.Join(messages, "\n"), hi.At()).Send()
		if err != nil {
			logger.Error("[Hi] %v", err)
		}
	}()

	instances, err := noah.GetInstances(productLine, appName)
	if err != nil {
		logger.Error("[ProxyProber] failed to get instances from noah bns, bns=%s.%s, error=(%v)", appName, productLine, err)
		return
	}
	totalHostsNum := map[string]int{}
	disabledHostsNum := map[string]int{}
	for _, instance := range instances {
		idc := instance.Tags["idc"]
		if idc == "" {
			continue
		}

		totalHostsNum[idc] += 1
		if instance.Disable {
			disabledHostsNum[idc] += 1
		}
	}
	for idc, num := range totalHostsNum {
		messages = append(messages, fmt.Sprintf("【%s机房】：总节点 %d，生效中 %d", idc, num, num-disabledHostsNum[idc]))
	}
}

// 按ProxyProberKeepEnabledNum条件屏蔽Proxy实例
// ProxyProberKeepEnabledNum: 0, 不保留实例，该屏蔽就屏蔽
// ProxyProberKeepEnabledNum: 0~1, 按百分比计算
// ProxyProberKeepEnabledNum: >1, 按整数计算
func execDisableInstances(product, app string, totalHostsNum, alreadyDisabledNum map[string]float64, needDisableHosts map[string][]string) {
	// 没有要屏蔽的实例直接返回
	if len(needDisableHosts) == 0 {
		return
	}

	// 计算每个机房的可屏蔽实例数量
	var disableHosts []string
	var maxDisableNum int
	for idc, hosts := range needDisableHosts {
		switch true {
		// 不保留实例，该屏蔽就屏蔽
		case minEnabledNumPerIDC == 0:
			maxDisableNum = len(hosts)
		// 按百分比计算, maxDisableNum=最大可屏蔽的实例数量-已经屏蔽的实例数量
		case minEnabledNumPerIDC > 0 && minEnabledNumPerIDC < 1:
			maxDisableNum = int(totalHostsNum[idc]*(1-minEnabledNumPerIDC) - alreadyDisabledNum[idc])
		// 按整数计算
		case minEnabledNumPerIDC >= 1:
			maxDisableNum = int(totalHostsNum[idc]-alreadyDisabledNum[idc]) - int(minEnabledNumPerIDC)
		}

		// 可屏蔽实例数不足1，不能再屏蔽了
		if maxDisableNum < 1 {
			logger.Warn("[ProxyProber] there is no more instance in bns %s.%s of idc %s can be disable, skip hosts=%s", product, app, idc, hosts)
			err := hi.To().Text("[Proxy Prober] BNS %s.%s 触发单机房最大可屏蔽实例规则，跳过自动屏蔽！\n%s", app, product, hi.At()).Shutup(600).Send()
			if err != nil {
				logger.Error("[Hi] %v", err)
			}
			continue
		}
		// 从后向前数，超出可屏蔽数量的部分不屏蔽
		if len(hosts) > maxDisableNum {
			hosts = hosts[len(hosts)-maxDisableNum:]
			logger.Warn("[ProxyProber] the maximum number of disable hosts has been reached, bns=%s.%s, idc=%s, skipHosts=%s", product, app, idc, hosts[0:len(hosts)-maxDisableNum])
			err := hi.To().Text("[Proxy Prober] BNS %s.%s 触发单机房最大可屏蔽实例规则，跳过实例 %s \n%s", app, product, hosts[0:len(hosts)-maxDisableNum], hi.At()).Shutup(600).Send()
			if err != nil {
				logger.Error("[Hi] %v", err)
			}
		}
		disableHosts = append(disableHosts, hosts...)
	}
	// 屏蔽实例
	if len(disableHosts) != 0 {
		names := strings.Join(disableHosts, ",")
		err := noah.DisableInstances(product, app, disableHosts)
		if err != nil {
			logger.Error("[ProxyProber] failed to disable instances (%s), error=(%v)", names, err)
			err = hi.To().Text("[Proxy Prober] BNS %s.%s 节点 %s 屏蔽失败！\n%s", app, product, names, hi.At()).Send()
			if err != nil {
				logger.Error("[Hi] %v", err)
			}
			return
		}

		// TODO: 给容器实例打上污点，使render服务快速调度一个新实例出来，避免容量问题

		// 操作结果通告
		printBnsStatus(product, app, names)
	}
}

// 单集群proxy探测+自动屏蔽
func clusterProber(wg *sync.WaitGroup, conCh chan int, bns string, password string) {
	defer wg.Done()
	logger.Debug("[ProxyProber] start bns %s proxy prober", bns)

	// 调用noah接口获取proxy实例列表
	tmp := strings.Split(bns, ".")
	product, app := tmp[1], tmp[0]
	instances, err := noah.GetInstances(product, app)
	if err != nil {
		logger.Error("[ProxyProber] failed to get instances from noah bns, bns=%s, error=(%v)", bns, err)
		return
	}
	// 按照Name排序
	sort.Slice(instances, func(i, j int) bool {
		return instances[i].Name < instances[j].Name
	})

	// 遍历实例列表，探测proxy是否存活
	totalHostsNum := map[string]float64{}
	alreadyDisabledNum := map[string]float64{}
	needDisableHosts := map[string][]string{}
	cwg := sync.WaitGroup{}
	for _, instance := range instances {
		ip, port, name, idc := instance.IP, instance.PortInfo.Main, instance.Name, instance.Tags["idc"]
		// 不处理缺少IDC标签的情况，报警跳过
		if idc == "" {
			err := hi.To().Text("[Proxy Prober] BNS %s 节点 %s:%d 缺少标签「idc」\n%s", bns, ip, port, hi.At()).Shutup(600).Send()
			if err != nil {
				logger.Error("[Hi] %v", err)
			}
			continue
		}

		// 统计各IDC中实例的总数，已屏蔽实例额外计入已屏蔽实例数
		totalHostsNum[idc] += 1
		if instance.Disable {
			alreadyDisabledNum[idc] += 1
			continue
		}

		conCh <- 1
		cwg.Add(1)
		go func() {
			defer cwg.Done()
			// 多节点决策是否要屏蔽proxy，将需要屏蔽的proxy追加到disableHostsMap中
			if shouldProxyDisabled(ip, port, password) {
				needDisableHosts[idc] = append(needDisableHosts[idc], name)
			}
			<-conCh
		}()
	}
	cwg.Wait()

	// 按机房决策是否屏蔽实例
	execDisableInstances(product, app, totalHostsNum, alreadyDisabledNum, needDisableHosts)
	logger.Debug("[ProxyProber] end bns %s proxy prober", bns)
}

// proxy自动屏蔽，10s一次
// 因redis变更不频繁（当前规模下），ProxyServices列表由一个较长定时任务刷新
func JobProxyProber() {
	var ch = make(chan int, concurrency)
	defer close(ch)

	// 以bns为单位，异步探测proxy
	wg := sync.WaitGroup{}
	for _, proxyService := range proxyServices {
		wg.Add(1)
		go clusterProber(&wg, ch, proxyService.BNS, proxyService.Password)
	}

	wg.Wait()
}
