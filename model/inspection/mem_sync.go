package inspection

import (
	"strconv"

	"dt-common/ent/redis"
	"dt-common/logger"
	"dt-common/mysql"
	"dt-common/redisc"
)

// 更新所有集群的实例数据，每5分钟被noah调用一次
// 同时仅能有一个更新任务在执行，若上次任务还没完成则跳过下一次任务
func JobMemorySync() {
	db, err := mysql.Database()
	if err != nil {
		logger.Error("[MemorySync] failed to get mysql connection, error=(%v)", err)
	}

	// 按集群更新redis数据
	for _, clusterData := range redisServices {
		for _, instance := range clusterData.Edges.Redis {
			infos, err := redisc.Info(instance.IP, instance.Port)
			if err != nil {
				logger.Warn("[MemorySync] failed to get redis %s:%d info, error=(%v)", instance.IP, instance.Port, err)
				continue
			}

			// 最大内存
			maxmemory, err := strconv.Atoi(infos["maxmemory"])
			if err != nil {
				logger.Warn("[MemorySync] failed to convert string to int, infos[maxmemory]=%s, instance=%s:%d", infos["maxmemory"], instance.IP, instance.Port)
				continue
			}
			// 已用内存
			usedMemory, err := strconv.Atoi(infos["used_memory"])
			if err != nil {
				logger.Warn("[MemorySync] failed to convert string to int, infos[used_memory]=%s, instance=%s:%d", infos["used_memory"], instance.IP, instance.Port)
				continue
			}

			// 避免on duplicate使自增id无限放大，改为直接update
			ctx, cancel := mysql.ContextWithTimeout()
			err = db.Redis.Update().SetMaxmemory(maxmemory).SetUsedMemory(usedMemory).SetRole(infos["role"]).
				Where(redis.IP(instance.IP), redis.Port(instance.Port)).
				Exec(ctx)
			cancel()
			if err != nil {
				logger.Warn("[MemorySync] failed to update redis, cluster=%s, error=(%v)", clusterData.Name, err)
				continue
			}
			logger.Debug("[MemorySync] succeed to update %s:%d maxmemory=%d, usedMemory=%d, role=%s", instance.IP, instance.Port, maxmemory, usedMemory, infos["role"])
		}

		logger.Info("[MemorySync] succeed to sync redis, cluster=%s", clusterData.Name)
	}
}
