package inspection

import (
	"context"
	"regexp"
	"testing"

	"github.com/jarcoal/httpmock"

	"dt-common/mysql"
	"redis-cmanager/env"
)

func TestJobUpdateInstanceNo(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`//[\w\W]+/healthCheck`),
		httpmock.NewStringResponder(200, `{"success": "00000", "message": "OK", "data": {"status": 0,"msg": ""}}`))
	httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`//[\w\W]+/message/sendMessage`),
		httpmock.NewStringResponder(200, `{"errno": 0, "data": {"status": 0,"msg": ""}}`))

	type args struct{}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test1: hba:hbb 1:1",
			before: func() {
				managerProduct = "siod-kafka"
				managerApp = "redis-cmanager"
				localIP, localIDC = "************", ""
				totalInsMap = make(map[string][]string)
				totalNumInAll, localIdxInAll, totalNumInIdc, localIdxInIdc = 0, 0, -1, -1

				httpmock.RegisterResponder("GET", "http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-kafka/apps/redis-cmanager/instances?showPage=0",
					httpmock.NewStringResponder(200, `{"success": true, "message": "OK", "data": [
						{"name": "0.redis-cmanager.siod-kafka","hostName": "kafka0002.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hba"},"disable": false,"instanceType": "host","podId": null,"containerList": []},
						{"name": "2.redis-cmanager.siod-kafka","hostName": "kafka0051.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hbb"},"disable": false,"instanceType": "host","podId": null,"containerList": []}
					]}`),
				)
			},
			wantErr: false,
			expect: func(t *testing.T) {
				if localIDC != "hba" {
					t.Errorf("localIDC = %v, want hba", localIDC)
				}
				if totalNumInAll != 2 {
					t.Errorf("totalNumInAll = %v, want 2", totalNumInAll)
				}
				if localIdxInAll != 0 {
					t.Errorf("localIdxInAll = %v, want 0", localIdxInAll)
				}

				if totalNumInIdc != 1 {
					t.Errorf("totalNumInIdc = %v, want 1", totalNumInIdc)
				}
				if localIdxInIdc != 0 {
					t.Errorf("localIdxInIdc = %v, want 0", localIdxInIdc)
				}
			},
		},
		{
			name: "test2: hba:hbb 2:2",
			before: func() {
				managerProduct = "siod-kafka"
				managerApp = "redis-cmanager"
				localIP, localIDC = "************", ""
				totalInsMap = make(map[string][]string)
				totalNumInAll, localIdxInAll, totalNumInIdc, localIdxInIdc = 0, 0, -1, -1

				httpmock.RegisterResponder("GET", "http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-kafka/apps/redis-cmanager/instances?showPage=0",
					httpmock.NewStringResponder(200, `{"success": true, "message": "OK", "data": [
						{"name": "0.redis-cmanager.siod-kafka","hostName": "kafka0002.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hba"},"disable": false,"instanceType": "host","podId": null,"containerList": []},
						{"name": "1.redis-cmanager.siod-kafka","hostName": "kafka0000.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hbb"},"disable": false,"instanceType": "host","podId": null,"containerList": []},
						{"name": "2.redis-cmanager.siod-kafka","hostName": "kafka0051.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hba"},"disable": false,"instanceType": "host","podId": null,"containerList": []},
						{"name": "3.redis-cmanager.siod-kafka","hostName": "kafka0052.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hbb"},"disable": false,"instanceType": "host","podId": null,"containerList": []}
					]}`),
				)
			},
			wantErr: false,
			expect: func(t *testing.T) {
				if localIDC != "hbb" {
					t.Errorf("localIDC = %v, want hbb", localIDC)
				}
				if totalNumInAll != 4 {
					t.Errorf("totalNumInAll = %v, want 4", totalNumInAll)
				}
				if localIdxInAll != 1 {
					t.Errorf("localIdxInAll = %v, want 1", localIdxInAll)
				}

				if totalNumInIdc != 2 {
					t.Errorf("totalNumInIdc = %v, want 2", totalNumInIdc)
				}
				if localIdxInIdc != 0 {
					t.Errorf("localIdxInIdc = %v, want 0", localIdxInIdc)
				}
			},
		},
		{
			name: "test3: hba:hbb 2:1",
			before: func() {
				managerProduct = "siod-kafka"
				managerApp = "redis-cmanager"
				localIP, localIDC = "************", ""
				totalInsMap = make(map[string][]string)
				totalNumInAll, localIdxInAll, totalNumInIdc, localIdxInIdc = 0, 0, -1, -1

				httpmock.RegisterResponder("GET", "http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-kafka/apps/redis-cmanager/instances?showPage=0",
					httpmock.NewStringResponder(200, `{"success": true, "message": "OK", "data": [
						{"name": "0.redis-cmanager.siod-kafka","hostName": "kafka0002.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hba"},"disable": false,"instanceType": "host","podId": null,"containerList": []},
						{"name": "1.redis-cmanager.siod-kafka","hostName": "kafka0000.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hba"},"disable": false,"instanceType": "host","podId": null,"containerList": []},
						{"name": "2.redis-cmanager.siod-kafka","hostName": "kafka0051.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hbb"},"disable": false,"instanceType": "host","podId": null,"containerList": []}
					]}`),
				)
			},
			wantErr: false,
			expect: func(t *testing.T) {
				if localIDC != "hba" {
					t.Errorf("localIDC = %v, want hba", localIDC)
				}
				if totalNumInAll != 3 {
					t.Errorf("totalNumInAll = %v, want 3", totalNumInAll)
				}
				if localIdxInAll != 1 {
					t.Errorf("localIdxInAll = %v, want 1", localIdxInAll)
				}

				if totalNumInIdc != 2 {
					t.Errorf("totalNumInIdc = %v, want 2", totalNumInIdc)
				}
				if localIdxInIdc != 1 {
					t.Errorf("localIdxInIdc = %v, want 1", localIdxInIdc)
				}
			},
		},
		{
			name: "test4: hba:hbb 2:0",
			before: func() {
				managerProduct = "siod-kafka"
				managerApp = "redis-cmanager"
				localIP, localIDC = "************", ""
				totalInsMap = make(map[string][]string)
				totalNumInAll, localIdxInAll, totalNumInIdc, localIdxInIdc = 0, 0, -1, -1

				httpmock.RegisterResponder("GET", "http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-kafka/apps/redis-cmanager/instances?showPage=0",
					httpmock.NewStringResponder(200, `{"success": true, "message": "OK", "data": [
						{"name": "0.redis-cmanager.siod-kafka","hostName": "kafka0002.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hba"},"disable": false,"instanceType": "host","podId": null,"containerList": []},
						{"name": "1.redis-cmanager.siod-kafka","hostName": "kafka0000.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hba"},"disable": false,"instanceType": "host","podId": null,"containerList": []},
						{"name": "2.redis-cmanager.siod-kafka","hostName": "kafka0051.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": null,"disable": false,"instanceType": "host","podId": null,"containerList": []}
					]}`),
				)
			},
			wantErr: false,
			expect: func(t *testing.T) {
				if localIDC != "hba" {
					t.Errorf("localIDC = %v, want hba", localIDC)
				}
				if totalNumInAll != 2 {
					t.Errorf("totalNumInAll = %v, want 2", totalNumInAll)
				}
				if localIdxInAll != 1 {
					t.Errorf("localIdxInAll = %v, want 1", localIdxInAll)
				}

				if totalNumInIdc != 2 {
					t.Errorf("totalNumInIdc = %v, want 2", totalNumInIdc)
				}
				if localIdxInIdc != 1 {
					t.Errorf("localIdxInIdc = %v, want 1", localIdxInIdc)
				}
			},
		},
		{
			name: "test5: local disable",
			before: func() {
				managerProduct = "siod-kafka"
				managerApp = "redis-cmanager"
				localIP, localIDC = "************", "hba"
				totalInsMap = make(map[string][]string)
				totalNumInAll, localIdxInAll, totalNumInIdc, localIdxInIdc = 0, 0, -1, -1

				httpmock.RegisterResponder("GET", "http://noah.duxiaoman-int.com/apptreeNew/v1/products/siod-kafka/apps/redis-cmanager/instances?showPage=0",
					httpmock.NewStringResponder(200, `{"success": true, "message": "OK", "data": [
						{"name": "0.redis-cmanager.siod-kafka","hostName": "kafka0002.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hba"},"disable": false,"instanceType": "host","podId": null,"containerList": []},
						{"name": "1.redis-cmanager.siod-kafka","hostName": "kafka0000.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hba"},"disable": true,"instanceType": "host","podId": null,"containerList": []},
						{"name": "2.redis-cmanager.siod-kafka","hostName": "kafka0051.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hbb"},"disable": false,"instanceType": "host","podId": null,"containerList": []},
						{"name": "3.redis-cmanager.siod-kafka","hostName": "kafka0051.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hbb"},"disable": false,"instanceType": "host","podId": null,"containerList": []}
					]}`),
				)
			},
			wantErr: false,
			expect: func(t *testing.T) {
				if localIDC != "" {
					t.Errorf("localIDC = %v, want \"\"", localIDC)
				}
				if totalNumInAll != 3 {
					t.Errorf("totalNumInAll = %v, want 3", totalNumInAll)
				}
				if totalNumInIdc != 0 {
					t.Errorf("totalNumInIdc = %v, want 1", totalNumInIdc)
				}
				if localIdxInAll != -1 {
					t.Errorf("localIdxInAll = %v, want -1", localIdxInAll)
				}
				if localIdxInIdc != -1 {
					t.Errorf("localIdxInIdc = %v, want -1", localIdxInIdc)
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			JobUpdateInstanceNo()
			// if (err != nil) != tt.wantErr {
			// 	t.Errorf("JobUpdateInstanceNo() error = %v, wantErr %v", err, tt.wantErr)
			// }
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
	httpmock.Deactivate()
}

func TestJobUpdateProxyServices(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")

	db, _ := mysql.Database()
	objC := env.MockCluster("test1")
	db.Proxy.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetBns("redis-test1-router.siod-redis").SetIP("************").SetPort(8001).SetIdc("hba").Save(context.Background())
	db.Proxy.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetBns("redis-test1-router.siod-redis").SetIP("************").SetPort(8001).SetIdc("hbb").Save(context.Background())

	objC = env.MockCluster("test2")
	db.Proxy.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetBns("redis-test2-router.siod-redis").SetIP("************").SetPort(8002).SetIdc("hba").Save(context.Background())
	db.Proxy.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetBns("redis-test2-router.siod-redis").SetIP("************").SetPort(8002).SetIdc("hbb").Save(context.Background())

	objC = env.MockCluster("test3")
	db.Proxy.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetBns("redis-test3-router.siod-redis").SetIP("************").SetPort(8003).SetIdc("hba").Save(context.Background())
	db.Proxy.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetBns("redis-test3-router.siod-redis").SetIP("************").SetPort(8003).SetIdc("hbb").Save(context.Background())

	objC = env.MockCluster("test4")
	db.Proxy.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetBns("redis-test4-router.siod-redis").SetIP("************").SetPort(8004).SetIdc("hba").Save(context.Background())
	db.Proxy.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetBns("redis-test4-router.siod-redis").SetIP("************").SetPort(8004).SetIdc("hbb").Save(context.Background())

	type args struct{}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test1: 1 instance",
			before: func() {
				localIdxInIdc = 0
				totalNumInIdc = 1
			},
			wantErr: false,
			expect: func(t *testing.T) {
				if len(proxyServices) != 4 {
					t.Errorf("len of proxy services expect 4, but got %v", len(proxyServices))
				}
				if proxyServices[0].Password != "123" {
					t.Errorf("services[0].Password expect 123, but got %v", proxyServices[0].Password)
				}
			},
		},
		{
			name: "test2: 2 instance, no 1",
			before: func() {
				localIdxInIdc = 1
				totalNumInIdc = 2
			},
			wantErr: false,
			expect: func(t *testing.T) {
				if len(proxyServices) != 2 {
					t.Errorf("len of proxy services expect 4, but got %v", len(proxyServices))
				}
				if proxyServices[0].ClusterName != "test2" {
					t.Errorf("services[0].ClusterName expect test2, but got %v", proxyServices[0].ClusterName)
				}
			},
		},
		{
			name: "test2: 2 instance, no 1",
			before: func() {
				localIdxInIdc = -1
				totalNumInIdc = 2
			},
			wantErr: false,
			expect: func(t *testing.T) {
				if len(proxyServices) != 0 {
					t.Errorf("len of proxy services expect 4, but got %v", len(proxyServices))
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			JobUpdateProxyServices()
			// if (err != nil) != tt.wantErr {
			// 	t.Errorf("JobUpdateProxyServices() error = %v, wantErr %v", err, tt.wantErr)
			// }
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

func TestJobUpdateRedisInstances(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")

	db, _ := mysql.Database()
	objC := env.MockCluster("test1")
	db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(0).SetBns("r3-test1-router.siod-redis").SetName("r3-test1_server1").SetIP("************").SetPort(7001).SetIdc("hba").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
	db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(0).SetBns("r3-test1-router.siod-redis").SetName("r3-test1_server1").SetIP("************").SetPort(7001).SetIdc("hbb").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())

	objC = env.MockCluster("test2")
	db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test2-router.siod-redis").SetName("r3-test2_server1").SetIP("************").SetPort(7002).SetIdc("hba").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
	db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test2-router.siod-redis").SetName("r3-test2_server1").SetIP("************").SetPort(7002).SetIdc("hbb").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())

	objC = env.MockCluster("test3")
	db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test3-router.siod-redis").SetName("r3-test3_server1").SetIP("************").SetPort(7003).SetIdc("hba").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
	db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test3-router.siod-redis").SetName("r3-test3_server1").SetIP("************").SetPort(7003).SetIdc("hbb").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())

	objC = env.MockCluster("test4")
	db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test4-router.siod-redis").SetName("r3-test4_server1").SetIP("************").SetPort(7004).SetIdc("hba").SetRole("master").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())
	db.Redis.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("r3-test4-router.siod-redis").SetName("r3-test4_server1").SetIP("************").SetPort(7004).SetIdc("hbb").SetRole("slave").SetMaxmemory(1234).SetUsedMemory(1).ExecX(context.Background())

	type args struct{}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test1: 2 instance, no 1",
			before: func() {
				localIdxInAll = 1
				totalNumInAll = 2
			},
			wantErr: false,
			expect: func(t *testing.T) {
				if len(redisServices) != 2 {
					t.Errorf("len of redisServices expect 2, but got %v", len(redisServices))
				}
				if redisServices[0].Name != "test2" {
					t.Errorf("redisServices[0].ClusterName expect test2, but got %v", redisServices[0].Name)
				}
			},
		},
		{
			name: "test2: 4 instance, no 3",
			before: func() {
				localIdxInAll = 3
				totalNumInAll = 4
			},
			wantErr: false,
			expect: func(t *testing.T) {
				if len(redisServices) != 1 {
					t.Errorf("len of redisServices expect 1, but got %v", len(redisServices))
				}
				if redisServices[0].Name != "test4" {
					t.Errorf("redisServices[0].ClusterName expect test4, but got %v", redisServices[0].Name)
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			JobUpdateRedisInstances()
			// if (err != nil) != tt.wantErr {
			// 	t.Errorf("JobUpdateRedisInstances() error = %v, wantErr %v", err, tt.wantErr)
			// }
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}

}
