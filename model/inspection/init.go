package inspection

import (
	"fmt"
	"runtime"
	"time"

	"github.com/robfig/cron/v3"

	"dt-common/logger"
)

type CronLogger struct{}

// Info logs routine messages about cron's operation. do nothing
func (l *CronLogger) Info(msg string, keysAndValues ...interface{}) {}

// Error logs an error condition.
func (l *CronLogger) Error(err error, msg string, keysAndValues ...interface{}) {
	logger.Error("[Cron] error=(%v), %s, %+v", err, msg, keysAndValues)
}

// 对于所有job的panic，统一进行错误处理，发到钉钉报警
func guard(job cron.Job) cron.Job {
	return cron.FuncJob(func() {
		defer func() {
			if r := recover(); r != nil {
				const size = 64 << 10
				buf := make([]byte, size)
				buf = buf[:runtime.Stack(buf, false)]
				err, ok := r.(error)
				if !ok {
					err = fmt.Errorf("%v", r)
				}
				logger.Error("[Timer Panic Occured] %v", err)
				logger.Error("[Recovery From Panic] %v", string(buf))
				// dbot.Ding(dbot.RobotUSKidTech, "定时任务报错")
			}
		}()
		job.Run()
	})
}

// =====================================
//   			Configs
// =====================================

type MemorySync struct {
	Interval time.Duration `yaml:"interval"` // 内存同步周期
}

type ProxyProber struct {
	MetaInterval   time.Duration `yaml:"meta_interval"`    // 元数据更新周期
	Interval       time.Duration `yaml:"interval"`         // 探活周期
	Concurrency    int           `yaml:"concurrency"`      // 探活并发数
	KeepEnabledNum float64       `yaml:"keep_enabled_num"` // 最小保留实例数
}

type ClusterReconcile struct {
	Interval       time.Duration `yaml:"interval"`         // 集群渲染周期
	Concurrency    int           `yaml:"concurrency"`      // 集群渲染并发数
	StepRetryTimes int           `yaml:"step_retry_times"` // 集群渲染重试次数
}

type Config struct {
	MemorySync       *MemorySync       `yaml:"memory_sync"`
	ProxyProber      *ProxyProber      `yaml:"proxy_prober"`
	ClusterReconcile *ClusterReconcile `yaml:"cluster_reconcile"`
}

func format(conf *Config) *Config {
	// =====================================
	// 			  Memory Sync
	// =====================================
	// 内存更新默认10分钟一次
	if conf.MemorySync.Interval == 0 {
		conf.MemorySync.Interval = 600 // 10min
	}

	// =====================================
	// 			  Proxy Prober
	// =====================================
	// 元数据更新默认10分钟一次
	if conf.ProxyProber.MetaInterval == 0 {
		conf.ProxyProber.MetaInterval = 600 // 10min
	}
	// 探活默认10秒一次
	if conf.ProxyProber.Interval == 0 {
		conf.ProxyProber.Interval = 10
	}
	// 探活并发数默认100
	if conf.ProxyProber.Concurrency == 0 {
		conf.ProxyProber.Concurrency = 100
	}
	// 最少生效实例数默认50%
	if conf.ProxyProber.KeepEnabledNum < 0 {
		conf.ProxyProber.KeepEnabledNum = 0.5
	}

	// =====================================
	// 			 Cluster Reconcile
	// =====================================
	// 默认30s一次
	if conf.ClusterReconcile.Interval <= 0 {
		conf.ClusterReconcile.Interval = 30
	}
	// 默认20个集群并发
	if conf.ClusterReconcile.Concurrency <= 0 {
		conf.ClusterReconcile.Concurrency = 20
	}
	// 默认5次重试
	if conf.ClusterReconcile.StepRetryTimes <= 0 {
		conf.ClusterReconcile.StepRetryTimes = 5
	}

	return conf
}
