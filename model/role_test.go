package model

import (
	"dt-common/user-center/authc"
	"regexp"
	"testing"

	"redis-xweb/env"

	"github.com/jarcoal/httpmock"
)

func TestIsRedisSuperAdmin(t *testing.T) {
	// 初始化配置
	env.Mock(t, "../config/config.yaml")

	type args struct {
		username string
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, bool)
	}{
		{
			name: "test1: request failed",
			args: args{
				username: "jiayiming_dxm",
			},
			wantErr: true,
		},
		{
			name: "test2: success",
			args: args{
				username: "jiayiming_dxm",
			},
			before: func() {
				httpmock.Activate()
				// mock authcenter
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/auth/GetUsersByRole`),
					httpmock.NewStringResponder(200, "{\"errno\":0,\"errmsg\":\"\",\"data\":\"jiayiming_dxm\"}"))
			},
			wantErr: false,
			expect: func(t *testing.T, isAdmin bool) {
				if !isAdmin {
					t.Errorf("IsRedisSuperAdmin() jiayiming_dxm is redis-manager but got false")
				}
			},
		},
		{
			name: "test3: not admin",
			args: args{
				username: "notadmin_dxm",
			},
			wantErr: false,
			expect: func(t *testing.T, isAdmin bool) {
				if isAdmin {
					t.Errorf("IsRedisSuperAdmin() notadmin_dxm is not redis-manger but got true")
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			isAdmin, err := IsRedisSuperAdmin(tt.args.username)
			if (err != nil) != tt.wantErr {
				t.Errorf("IsRedisSuperAdmin() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, isAdmin)
			}
		})
	}
	httpmock.DeactivateAndReset()
}

func TestHasReadPermission(t *testing.T) {
	// 初始化配置
	env.Mock(t, "../config/config.yaml")

	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		user        *authc.User
		clusterName string
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, bool)
	}{
		{
			name: "test1: request failed",
			args: args{
				user: &authc.User{
					ID:   1,
					Name: "jiayiming_dxm",
				},
				clusterName: "test_cluster",
			},
			wantErr: true,
		},
		{
			name: "test2: success, is admin",
			args: args{
				user: &authc.User{
					ID:   1,
					Name: "jiayiming_dxm",
				},
				clusterName: "test_cluster",
			},
			before: func() {
				// mock authcenter
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/auth/GetUsersByRole`),
					httpmock.NewStringResponder(200, "{\"errno\":0,\"errmsg\":\"\",\"data\":\"jiayiming_dxm\"}"))
			},
			wantErr: false,
			expect: func(t *testing.T, isAdmin bool) {
				if !isAdmin {
					t.Errorf("HasReadPermission() jiayiming_dxm has read permission but got false")
				}
			},
		},
		{
			name: "test3: success, not admin",
			args: args{
				user: &authc.User{
					ID:   1,
					Name: "huzhaoyun_dxm",
				},
				clusterName: "test_cluster",
			},
			before: func() {
				// mock authcenter
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/auth/GetRoleByUserID`),
					httpmock.NewStringResponder(200, "[{\"Id\":\"1\",\"Name\":\"test_cluster__rd-engineer\",\"Alias\":\"test_cluster__rd-engineer\",\"Type\":\"0\",\"Comment\":\"\"}]"))
			},
			wantErr: false,
			expect: func(t *testing.T, hasPermission bool) {
				if !hasPermission {
					t.Errorf("HasReadPermission() huzhaoyun_dxm has read permission but got false")
				}
			},
		},
		{
			name: "test3: not admin",
			before: func() {
				// mock authcenter
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/auth/GetRoleByUserID`),
					httpmock.NewStringResponder(200, "[{\"Id\":\"1\",\"Name\":\"test_cluster1__rd-engineer\",\"Alias\":\"test_cluster1__rd-engineer\",\"Type\":\"0\",\"Comment\":\"\"}]"))
			},
			args: args{
				user: &authc.User{
					ID:   1,
					Name: "daiqi_dxm",
				},
				clusterName: "test_cluster",
			},
			wantErr: false,
			expect: func(t *testing.T, hasPermission bool) {
				if hasPermission {
					t.Errorf("HasReadPermission() daiqi_dxm has no read permission but got true")
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			hasPermission, err := HasReadPermission(tt.args.user, tt.args.clusterName)
			if (err != nil) != tt.wantErr {
				t.Errorf("HasReadPermission() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, hasPermission)
			}
		})
	}
}

func TestHasWritePermission(t *testing.T) {
	// 初始化配置
	env.Mock(t, "../config/config.yaml")

	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		user        *authc.User
		clusterName string
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, bool)
	}{
		{
			name: "test1: request failed",
			args: args{
				user: &authc.User{
					ID:   1,
					Name: "jiayiming_dxm",
				},
				clusterName: "test_cluster",
			},
			wantErr: true,
		},
		{
			name: "test2: success, is admin",
			args: args{
				user: &authc.User{
					ID:   1,
					Name: "jiayiming_dxm",
				},
				clusterName: "test_cluster",
			},
			before: func() {
				// mock authcenter
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/auth/GetUsersByRole`),
					httpmock.NewStringResponder(200, "{\"errno\":0,\"errmsg\":\"\",\"data\":\"jiayiming_dxm\"}"))
			},
			wantErr: false,
			expect: func(t *testing.T, isAdmin bool) {
				if !isAdmin {
					t.Errorf("HasWritePermission() jiayiming_dxm has read permission but got false")
				}
			},
		},
		{
			name: "test3: success, not admin",
			args: args{
				user: &authc.User{
					ID:   1,
					Name: "huzhaoyun_dxm",
				},
				clusterName: "test_cluster",
			},
			before: func() {
				// mock authcenter
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/auth/GetRoleByUserID`),
					httpmock.NewStringResponder(200, "[{\"Id\":\"1\",\"Name\":\"test_cluster__rd-manager\",\"Alias\":\"test_cluster__rd-manager\",\"Type\":\"0\",\"Comment\":\"\"}]"))
			},
			wantErr: false,
			expect: func(t *testing.T, hasPermission bool) {
				if !hasPermission {
					t.Errorf("HasWritePermission() huzhaoyun_dxm has write permission but got false")
				}
			},
		},
		{
			name: "test3: not admin",
			before: func() {
				// mock authcenter
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/auth/GetRoleByUserID`),
					httpmock.NewStringResponder(200, "[{\"Id\":\"1\",\"Name\":\"test_cluster__rd-engineer\",\"Alias\":\"test_cluster__rd-engineer\",\"Type\":\"0\",\"Comment\":\"\"}]"))
			},
			args: args{
				user: &authc.User{
					ID:   1,
					Name: "daiqi_dxm",
				},
				clusterName: "test_cluster",
			},
			wantErr: false,
			expect: func(t *testing.T, hasPermission bool) {
				if hasPermission {
					t.Errorf("HasWritePermission() daiqi_dxm has no write permission but got true")
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			hasPermission, err := HasWritePermission(tt.args.user, tt.args.clusterName)
			if (err != nil) != tt.wantErr {
				t.Errorf("HasWritePermission() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, hasPermission)
			}
		})
	}
}
