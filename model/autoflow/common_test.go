package autoflow

import (
	"context"
	"regexp"
	"testing"
	"time"

	"github.com/jarcoal/httpmock"

	"dt-common/ent"
	"dt-common/ent/cluster"
	"dt-common/mysql"
	"dt-common/omodel"
	"redis-xweb/env"
)

func TestGetStagesByFlowNode(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")
	objC := env.MockCluster("flow_test")
	db, _ := mysql.Database()

	type args struct {
		flowInstanceId uint32
		nodeInstanceId uint32
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, []*ent.Stage)
	}{
		{
			name: "test: success",
			args: args{
				flowInstanceId: 45908,
				nodeInstanceId: 120171,
			},
			wantErr: false,
		},
		{
			name: "test: already exist",
			before: func() {
				db.Task.Create().
					SetFlowInstanceID(45908).SetNodeInstanceID(120171).
					SetClusterID(objC.ID).SetClusterName(objC.Name).
					SetName("白名单申请").SetType(omodel.TASK_TYPE_WHITELIST).
					SetApplicant("jiayiming_dxm").SetDescription("23").
					SaveX(context.Background())
			},
			args: args{
				flowInstanceId: 45908,
				nodeInstanceId: 120171,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			stageList, err := GetStagesByFlowNode(tt.args.flowInstanceId, tt.args.nodeInstanceId)
			if (err != nil) != tt.wantErr {
				t.Errorf("UpdateWhitelist() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, stageList)
			}
		})
	}
}

func TestCreateTaskAndStages(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")

	tests := []struct {
		name    string
		before  func()
		args    *TaskArgs
		wantErr bool
		expect  func(*testing.T, []*ent.Stage)
	}{
		{
			name: "单集群任务",
			args: &TaskArgs{
				FlowArgs: &FlowArgs{
					FlowInstanceID: 1,
					NodeInstanceID: 10,
					Applicant:      "jiayiming_dxm",
					Description:    "Proxy扩容",
					Operator:       "jiayiming_dxm",
					ClusterID:      1,
					ClusterName:    "huoke_brain",
				},
				Type: omodel.TASK_TYPE_PROXY_SCALING,
				Name: "Proxy扩容",
				Stages: []*StageArgs{
					{Name: "配置更新", Type: omodel.STAGE_TYPE_PROXY_SCALING, Parameter: map[string]int{"proxyNum": 6}, Automate: true},
					{Name: "Proxy解屏蔽", Type: omodel.STAGE_TYPE_PROXY_ENABLE, Automate: false},
				},
			},
			wantErr: false,
			expect: func(t *testing.T, stages []*ent.Stage) {
				if len(stages) != 2 {
					t.Errorf("expect length of stages 2, but got %d", len(stages))
				}
			},
		},
		{
			name: "多集群任务",
			args: &TaskArgs{
				FlowArgs: &FlowArgs{
					FlowInstanceID: 100,
					NodeInstanceID: 1000,
					Applicant:      "jiayiming_dxm",
					Description:    "机房切流",
					Operator:       "jiayiming_dxm",
					ClusterID:      0,
					ClusterName:    "",
				},
				Type: "emergencyCut",
				Name: "机房紧急切流",
				Stages: []*StageArgs{
					{Name: "huoke_brain", Type: "emergencyCut", Parameter: map[string]string{"idc": "hbb"}, Automate: false},
					{Name: "huoke_yxcoin", Type: "emergencyCut", Parameter: map[string]string{"idc": "hbb"}, Automate: false},
					{Name: "huoke_yxrain", Type: "emergencyCut", Parameter: map[string]string{"idc": "hbb"}, Automate: false},
					{Name: "huoke_wccs", Type: "emergencyCut", Parameter: map[string]string{"idc": "hbb"}, Automate: false},
				},
			},
			wantErr: false,
			expect: func(t *testing.T, stages []*ent.Stage) {
				if len(stages) != 4 {
					t.Errorf("expect length of stages 4, but got %d", len(stages))
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			stages, err := CreateTaskAndStages(tt.args)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateTaskAndStages() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, stages)
			}
		})
	}
}

func TestRun(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")
	objC := env.MockCluster("huoke_brain")

	type args struct {
		flowInstanceId    uint32
		nodeInstanceId    uint32
		listId            int64
		listType          string
		nodeSubmitHistory map[string]string
		nodeReturnHistory map[string]string
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "ProxyScaling",
			args: args{
				flowInstanceId: 45908,
				nodeInstanceId: 120171,
				listId:         17,
				listType:       "",
				nodeSubmitHistory: map[string]string{
					"redisReview": `[{"operator": "hanshengzhao_dxm","role": ["redis-manager","redis-engineer"],"action": "pass","submitInfo": {"desc": "无"}}]`,
				},
				nodeReturnHistory: nil,
			},
			before: func() {
				db, _ := mysql.Database()
				db.Flow.Create().SetID(17).SetFlowInstanceID(45908).SetClusterID(objC.ID).SetClusterName(objC.Name).
					SetDepartmentID(1).SetDepartment("信贷系统平台部门").SetApplicant("jiayiming_dxm").SetDescription("测试").
					SetType("proxyScaling").SetParameter(`{"proxyNum":6}`).Exec(context.Background())

				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/cluster/proxy/scaling`),
					httpmock.ResponderFromResponse(httpmock.NewStringResponse(200, `{"code": "00000", "data": "ok"}`)))
			},
			wantErr: false,
			expect: func(t *testing.T) {
				time.Sleep(3 * time.Second)
				db, _ := mysql.Database()
				c, _ := db.Cluster.Query().Where(cluster.Name(objC.Name)).Only(context.Background())
				if c.Status != string(omodel.CLUSTER_STATUS_CHANGING) {
					t.Errorf("expect cluster status changing, but got %v", c.Status)
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			Run(tt.args.flowInstanceId, tt.args.nodeInstanceId, tt.args.listId, tt.args.listType, tt.args.nodeSubmitHistory, tt.args.nodeReturnHistory)
			// if (err != nil) != tt.wantErr {
			// 	t.Errorf("Run() error = %v, wantErr %v", err, tt.wantErr)
			// }
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}
