package autoflow

import (
	"fmt"

	"dt-common/ent/flowapplycluster"
	"dt-common/errs"
	"dt-common/logger"
	"dt-common/mysql"
	"dt-common/user-center/authc"
	"redis-xweb/library/userc"
	"redis-xweb/model"
)

// 为用户绑定指定角色
func AddUserToRole(flowInstanceId, nodeInstanceId uint32, listId int64, listType string, nodeSubmitHistory map[string]string, nodeReturnHistory map[string]string) {
	var err error
	finishFlow := true
	defer userc.SetFlowResultIfNeeded(flowInstanceId, nodeInstanceId, &finishFlow, &err)

	// step1.获取工单详情
	db, err := mysql.Database()
	if err != nil {
		logger.Error("failed to get mysql connection, error=(%v)", err)
		err = errs.CodeDatabase.Detail("数据库连接获取失败")
		return
	}

	ctx, cancel := mysql.ContextWithTimeout()
	flowDetail, err := db.FlowApplyCluster.Query().Where(flowapplycluster.IDEQ(listId)).WithCluster().Only(ctx)
	cancel()
	if err != nil {
		logger.Warn("failed to query FlowApplyCluster, id=%d, error=(%v)", listId, err)
		err = errs.CodeDatabase.Detail("获取工单详情失败")
		return
	}

	// 获取角色详情
	switch flowDetail.Role {
	case "user":
		flowDetail.Role = model.ROLE_USER
	case "manager":
		flowDetail.Role = model.ROLE_MANAGER
	default:
		err = fmt.Errorf("undefined role %s", flowDetail.Role)
		logger.Warn(err.Error())
		return 
	}
	roleName := fmt.Sprintf("%s__%s", flowDetail.Edges.Cluster.Name, flowDetail.Role)
	role, err := authc.GetRoleByName(roleName, model.ROLE_TYPE_REDIS)
	if err != nil {
		logger.Error("failed to get role %s, error=(%v)", roleName, err)
		err = errs.CodeAuthCenterRequestFailed.Detail("未找到角色")
		return
	}

	// 绑定角色
	err = authc.BindRoleToUserV2(flowDetail.Applicant, model.ROLE_TYPE_REDIS, &[]int{role.ID})
	if err != nil {
		logger.Error("failed to bind role to user, user=%s, roleId=%d, error=(%v)", flowDetail.Applicant, role.ID, err)
		err = errs.CodeAuthCenterRequestFailed.Detail("角色绑定失败")
		return
	}

	// 更新记录状态，失败也不影响流程结束
	ctx, cancel = mysql.ContextWithTimeout()
	_, _ = flowDetail.Update().SetStatus(model.FLOW_STATUS_DONE).Save(ctx)
	cancel()
	if err != nil {
		logger.Error("failed to update FlowApplyCluster.status, id=%d, error=(%v)", listId, err)
	}

	// 同步任务，将err赋值errs.Success结束阶段
	err = errs.Success
}
