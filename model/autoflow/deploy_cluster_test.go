package autoflow

import (
	"context"
	"regexp"
	"testing"

	"github.com/jarcoal/httpmock"

	"dt-common/ent"
	"dt-common/ent/stage"
	"dt-common/mysql"
	"dt-common/omodel"
	"redis-xweb/env"
)

// 单测：Flow自动执行 - 初始化权限
func Test_getStagesRawData(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")
	objC := env.MockCluster("new_deploy")

	type args struct {
		clusterData *ent.Cluster
		bussiness   map[string]float64
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, []*omodel.Stage)
	}{
		{
			name: "success",
			args: args{
				clusterData: objC,
				bussiness:   map[string]float64{"信贷": 100},
			},
			wantErr: true,
			expect: func(t *testing.T, s []*omodel.Stage) {
				if len(s) != len(StageList) {
					t.Errorf("length of stages is not equal to StageList")
					return
				}
				if s[2].Parameter["group"] != "smart.group.new-deploy-router.siod-redis" {
					t.Errorf("expect group smart.group.new-deploy-router.siod-redis, but got %s", s[2].Parameter["group"])
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			stages := getStagesRawData(tt.args.clusterData, tt.args.bussiness)
			// if (err != nil) != tt.wantErr {
			// 	t.Errorf("getStagesRawData() error = %v, wantErr %v", err, tt.wantErr)
			// }
			if tt.expect != nil {
				tt.expect(t, stages)
			}
		})
	}
}

func Test_createTaskAndStages(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")
	objC := env.MockCluster("new_deploy")

	type args struct {
		flowInstanceId uint32
		nodeInstanceId uint32
		clusterId      int64
		clusterName    string
		stageRawDatas  []*omodel.Stage
		applicant      string
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, []*ent.Stage)
	}{
		{
			name: "success",
			args: args{
				flowInstanceId: 10,
				nodeInstanceId: 10,
				clusterId:      objC.ID,
				clusterName:    objC.Name,
				stageRawDatas:  getStagesRawData(objC, map[string]float64{"信贷": 100}),
				applicant:      objC.Owner,
			},
			wantErr: false,
			expect: func(t *testing.T, s []*ent.Stage) {
				if len(s) != len(StageList) {
					t.Errorf("length of stages is not equal to StageList")
					return
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			stages, err := createTaskAndStages(tt.args.flowInstanceId, tt.args.nodeInstanceId, tt.args.clusterId, tt.args.clusterName, tt.args.stageRawDatas, tt.args.applicant)
			if (err != nil) != tt.wantErr {
				t.Errorf("createTaskAndStages() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, stages)
			}
		})
	}
}

func Test_insertCluster(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")
	db, _ := mysql.Database()
	objFC := db.FlowCluster.Create().SetFlowInstanceID(85610).SetName("insert_test").SetType("enterprise").
		SetArea("public").SetRedisVersion("4.0").SetStorageSize(4).SetShardNum(2).SetShardSize(2).
		SetMaxmemoryPolicy("allkeys-lfu").SetProxyMap("{\"hba\":2,\"hbb\":2}").
		SetDepartmentID(1000000038).SetDepartment("信贷系统平台部").SetApplicant("lizhangwei_dxm").
		SetLevel(1).SetBusiness("{\"信贷\":100}").SetDescription("缓存扶摇策略配置，例如账户信息等").
		SetWhitelist("[\"fuyao-api.user-growth\",\"fuyao.user-growth\"]").SetStatus("normal").SaveX(context.Background())

	type args struct {
		flowDetail *ent.FlowCluster
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, *ent.Cluster)
	}{
		{
			name: "success",
			args: args{
				flowDetail: objFC,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			objC, err := insertCluster(tt.args.flowDetail)
			if (err != nil) != tt.wantErr {
				t.Errorf("insertCluster() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, objC)
			}
		})
	}
}

// 单测：Flow自动执行 - 初始化权限
func Test_createRoleAndBindtoUser(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		clusterName string
		applicant   string
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test: CreateRoleV2 failed",
			args: args{
				clusterName: "r3_test",
				applicant:   "jiayiming_dxm",
			},
			wantErr: true,
		},
		{
			name: "test: BindRoleToUserV2 failed",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/auth/AddRole`),
					httpmock.NewStringResponder(200, `{"errno": 0, "errmsg": "", "data": 1}`))
			},
			args: args{
				clusterName: "r3_test",
				applicant:   "jiayiming_dxm",
			},
			wantErr: true,
		},
		{
			name: "test: success",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/auth/BindRoleToUser`),
					httpmock.NewStringResponder(200, `{"errno": 0, "errmsg": "", "data": "ok"}`))
			},
			args: args{
				clusterName: "r3_test",
				applicant:   "jiayiming_dxm",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			err := createRoleAndBindtoUser(tt.args.clusterName, tt.args.applicant)
			if (err != nil) != tt.wantErr {
				t.Errorf("createRoleAndBindtoUser() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

// 单测：Flow自动执行 - 更新白名单
func TestDeployCluster(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")
	db, _ := mysql.Database()
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		flowInstanceId    uint32
		nodeInstanceId    uint32
		listId            int64
		listType          string
		nodeSubmitHistory map[string]string
		nodeReturnHistory map[string]string
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test: flow not found",
			args: args{
				flowInstanceId: 45908,
				nodeInstanceId: 120171,
				listId:         17,
				listType:       "",
				nodeSubmitHistory: map[string]string{
					"redisReview": `[{"operator": "hanshengzhao_dxm","role": ["redis-manager","redis-engineer"],"action": "pass","submitInfo": {"desc": "无"}}]`,
				},
				nodeReturnHistory: nil,
			},
		},
		{
			name: "test: start deploy",
			before: func() {
				db.FlowCluster.Create().SetFlowInstanceID(45908).SetName("r3_test").SetStorageSize(10).SetShardNum(2).SetProxyMap("{\"hba\":2,\"hbb\":2}").SetLevel(3).SetMaxmemoryPolicy("novication").SetShardSize(5).SetPassword("").SetBusiness("{\"金科\":100}").SetWhitelist("[\"fuyao-api.user-growth\",\"fuyao.user-growth\"]").SetDepartmentID(1000000037).SetDepartment("系统运维部").SetApplicant("jiayiming_dxm").SetArea("public").SetDescription("123").SaveX(context.Background())

				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/auth/AddRole`),
					httpmock.NewStringResponder(200, `{"errno": 0, "errmsg": "", "data": 1}`))
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/auth/BindRoleToUser`),
					httpmock.NewStringResponder(200, `{"errno": 0, "errmsg": "", "data": "ok"}`))
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/cluster/deploy`),
					httpmock.NewStringResponder(200, `{"code": "00000", "message": "", "data": "ok"}`))
			},
			args: args{
				flowInstanceId: 45908,
				nodeInstanceId: 120171,
				listId:         1,
				listType:       "",
				nodeSubmitHistory: map[string]string{
					"redisReview": `[{"operator": "hanshengzhao_dxm","role": ["redis-manager","redis-engineer"],"action": "pass","submitInfo": {"desc": "无"}}]`,
				},
				nodeReturnHistory: nil,
			},
			expect: func(t *testing.T) {
				s := db.Stage.Query().Where(stage.ClusterName("r3_test"), stage.Type("deploy")).OnlyX(context.Background())
				// 正常情况因为调不通接口，status会被设置为error，但因为是异步，所以不一定能走到update
				// 为了避免出现不确定结果，增加cmanager的mock，保证status一定是running
				if s.Status != string(omodel.STAGE_STATUS_RUNNING) {
					t.Errorf("expect deploy stage status running, but got %s", s.Status)
				}
			},
		},
		{
			name: "test: retry, skip deploy",
			before: func() {
				db.Stage.Update().SetStatus(string(omodel.STAGE_STATUS_DONE)).
					Where(stage.ClusterName("r3_test"), stage.Type("deploy")).ExecX(context.Background())
			},
			args: args{
				flowInstanceId: 45908,
				nodeInstanceId: 120171,
				listId:         1,
				listType:       "",
				nodeSubmitHistory: map[string]string{
					"redisReview": `[{"operator": "hanshengzhao_dxm","role": ["redis-manager","redis-engineer"],"action": "pass","submitInfo": {"desc": "无"}}]`,
				},
				nodeReturnHistory: nil,
			},
			expect: func(t *testing.T) {
				s := db.Stage.Query().Where(stage.ClusterName("r3_test"), stage.Type("addMonitor")).OnlyX(context.Background())
				// 第一个status为done，第二是normal，重试会启动第二个
				if s.Status != string(omodel.STAGE_STATUS_RUNNING) {
					t.Errorf("expect deploy stage status running, but got %s", s.Status)
				}
			},
		},
		{
			name: "test: all done",
			before: func() {
				db, _ := mysql.Database()
				db.Stage.Update().SetStatus(string(omodel.STAGE_STATUS_DONE)).
					Where(stage.ClusterName("r3_test")).ExecX(context.Background())
			},
			args: args{
				flowInstanceId: 45908,
				nodeInstanceId: 120171,
				listId:         1,
				listType:       "",
				nodeSubmitHistory: map[string]string{
					"redisReview": `[{"operator": "hanshengzhao_dxm","role": ["redis-manager","redis-engineer"],"action": "pass","submitInfo": {"desc": "无"}}]`,
				},
				nodeReturnHistory: nil,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			DeployCluster(tt.args.flowInstanceId, tt.args.nodeInstanceId, tt.args.listId, tt.args.listType, tt.args.nodeSubmitHistory, tt.args.nodeReturnHistory)
			// if (err != nil) != tt.wantErr {
			// 	t.Errorf("DeployCluster() error = %v, wantErr %v", err, tt.wantErr)
			// }
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}
