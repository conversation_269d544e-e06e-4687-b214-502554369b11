package autoflow

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"dt-common/ent"
	"dt-common/ent/cluster"
	"dt-common/ent/flowwhitelist"
	"dt-common/ent/predicate"
	"dt-common/ent/whitelist"
	"dt-common/logger"
	"dt-common/mysql"
	"dt-common/omodel"
	"redis-xweb/library/userc"
	whitelistStage "redis-xweb/model/stage/whitelist"
	"redis-xweb/model/workflow"
)

type WhitelistTask struct {
	FlowInstanceID uint32
	NodeInstanceID uint32
	ClusterId      int64
	ClusterName    string
	Applicant      string
	Description    string
	Operator       string
	Stages         [2]*whitelistStage.StageSchema
}

// 创建白名单任务和阶段
func CreateWhitelistStages(args *WhitelistTask) ([]*ent.Stage, error) {
	// step1.获取工单&集群详情
	db, err := mysql.Database()
	if err != nil {
		logger.Error("failed to get mysql connection, error=(%v)", err)
		return nil, err
	}

	stages := []*ent.Stage{}
	err = db.Transaction(context.Background(), func(tx *ent.Tx) error {
		// create task
		ctx, cancel := mysql.ContextWithTimeout()
		taskData, err := tx.Task.Create().
			SetFlowInstanceID(args.FlowInstanceID).SetNodeInstanceID(args.NodeInstanceID).
			SetClusterID(args.ClusterId).SetClusterName(args.ClusterName).
			SetName("白名单申请").SetType(omodel.TASK_TYPE_WHITELIST).
			SetApplicant(args.Applicant).SetDescription(args.Description).
			Save(ctx)
		cancel()
		if err != nil {
			logger.Error("failed to create whitelist task, flowInstanceId=%d, error=(%v) ", args.FlowInstanceID, err)
			return err
		}

		// 批量创建stage
		bulkCreate := []*ent.StageCreate{}
		for i, params := range args.Stages {
			if params == nil {
				continue
			}

			b, err := json.Marshal(params)
			if err != nil {
				logger.Warn("failed to unmarshal whitelist stage.Parameter, parameter=%v", params)
				return err
			}

			name := "Manager更新白名单"
			if params.Docker == omodel.DEPLOY_ENV_DOCKER {
				name = "CManager更新白名单"
			}

			bulkCreate = append(bulkCreate, tx.Stage.Create().
				SetTaskID(taskData.ID).SetClusterName(args.ClusterName).
				SetName(name).SetType(omodel.STAGE_TYPE_UPDATE_WHITELIST).
				SetSequence(i+1).SetOperator(args.Operator).SetLog("").
				SetParameter(string(b)),
			)
		}

		ctx, cancel = mysql.ContextWithTimeout()
		stages, err = tx.Stage.CreateBulk(bulkCreate...).Save(ctx)
		cancel()
		if err != nil {
			logger.Error("failed to bulk create whitelist stage, flowInstanceId=%d, error=(%v) ", args.FlowInstanceID, err)
		}
		return nil
	})

	return stages, err
}

// 组合bns、ip和权限参数
func formatWhitelistParams(bnsList, ipList []string, privilege string) ([]string, []string) {
	bnsWhitelist, ipWhitelist := []string{}, []string{}
	for _, v := range bnsList {
		if v != "" {
			bnsWhitelist = append(bnsWhitelist, v+" "+privilege)
		}
	}
	for _, v := range ipList {
		if v != "" {
			ipWhitelist = append(ipWhitelist, v+" "+privilege)
		}
	}
	return bnsWhitelist, ipWhitelist
}

// 以 adding 状态落库
func insertIntoWhitelist(clusterID int64, clusterName string, bnsList []string, ipList []string, privilege string, status string) error {
	db, err := mysql.Database()
	if err != nil {
		return err
	}

	bulkCreate := []*ent.WhitelistCreate{}
	for _, bns := range bnsList {
		if bns != "" {
			bulkCreate = append(bulkCreate, db.Whitelist.Create().
				SetClusterID(clusterID).SetClusterName(clusterName).
				SetType("bns").SetValue(bns).SetPrivilege(privilege).
				SetStatus(status),
			)
		}
	}
	for _, ip := range ipList {
		if ip != "" {
			bulkCreate = append(bulkCreate, db.Whitelist.Create().
				SetClusterID(clusterID).SetClusterName(clusterName).
				SetType("ip").SetValue(ip).SetPrivilege(privilege).
				SetStatus(status),
			)
		}
	}
	if len(bulkCreate) != 0 {
		ctx, cancel := mysql.ContextWithTimeout()
		err = db.Whitelist.CreateBulk(bulkCreate...).OnConflict().UpdatePrivilege().Exec(ctx)
		cancel()
		if err != nil {
			return err
		}
	}

	return nil
}

// 避免巡检将白名单结果覆盖，预删除白名单
func deleteFromWhitelist(clusterName string, bnsList []string, ipList []string) error {
	db, err := mysql.Database()
	if err != nil {
		return err
	}

	conditions := []predicate.Whitelist{}
	for _, bns := range bnsList {
		if bns != "" {
			conditions = append(conditions, whitelist.Value(bns))
		}
	}
	for _, ip := range ipList {
		if ip != "" {
			conditions = append(conditions, whitelist.Value(ip))
		}
	}
	if len(conditions) != 0 {
		ctx, cancel := mysql.ContextWithTimeout()
		err = db.Whitelist.Update().SetStatus(omodel.WHITELIST_STATUS_DELETED).Where(
			whitelist.ClusterName(clusterName),
			whitelist.Or(conditions...),
		).Exec(ctx)
		cancel()
		if err != nil {
			return err
		}
	}

	return nil
}

// 更新白名单, 异步方法，需要等待回调返回结果
func UpdateWhitelist(flowInstanceId, nodeInstanceId uint32, listId int64, listType string, nodeSubmitHistory map[string]string, nodeReturnHistory map[string]string) {
	var err error
	finishFlow := true
	defer userc.SetFlowResultIfNeeded(flowInstanceId, nodeInstanceId, &finishFlow, &err)

	// 从审批流中解析出审批人
	var nodeHistory []ReviewNode
	err = json.Unmarshal([]byte(nodeSubmitHistory["redisReview"]), &nodeHistory)
	if err != nil {
		logger.Warn("failed to unmarshal nodeSubmitHistory['redisReview'], redisReview=%v", nodeSubmitHistory["redisReview"])
		return
	}

	// step1.获取工单&集群详情
	db, err := mysql.Database()
	if err != nil {
		logger.Error("failed to get mysql connection, error=(%v)", err)
		return
	}
	ctx, cancel := mysql.ContextWithTimeout()
	flowDetail, err := db.FlowWhitelist.Query().Where(flowwhitelist.IDEQ(listId)).Only(ctx)
	cancel()
	if err != nil {
		logger.Error("failed to query FlowWhitelist, flowInstanceId=%d, error=(%v)", flowInstanceId, err)
		return
	}
	// 查询集群信息，联查proxy
	ctx, cancel = mysql.ContextWithTimeout()
	clusterData, err := db.Cluster.Query().Where(cluster.IDEQ(flowDetail.ClusterID)).WithProxy().Only(ctx)
	cancel()
	if err != nil {
		logger.Error("failed to query Cluster, flowInstanceId=%d, error=(%v)", flowInstanceId, err)
		return
	}
	if len(clusterData.Edges.Proxy) == 0 {
		logger.Error("cluster %s has no proxy", clusterData.Name)
		return
	}

	// step2.整理白名单 & 落库
	tmpBnsList := strings.Split(flowDetail.Bns, ",")
	tmpIpList := strings.Split(flowDetail.IP, ",")
	if len(tmpBnsList) == 0 && len(tmpIpList) == 0 {
		logger.Error("bns and ip cannot be both empty, flowInstanceId=%d, error=(%v)", flowInstanceId, err)
		return
	}

	switch flowDetail.Type {
	case "add":
		err = insertIntoWhitelist(flowDetail.ClusterID, clusterData.Name, tmpBnsList, tmpIpList, flowDetail.Privilege, omodel.WHITELIST_STATUS_ADDING)
	case "del":
		err = deleteFromWhitelist(clusterData.Name, tmpBnsList, tmpIpList)
	default:
		err = fmt.Errorf("whitelist flow has undefined type %s, it should be one of add or del", flowDetail.Type)
	}
	if err != nil {
		logger.Warn(err.Error())
		return
	}

	// step3.根据集群状态创建阶段
	stages, err := GetStagesByFlowNode(flowInstanceId, nodeInstanceId)
	if err != nil {
		logger.Error("failed to get stages, flowInstanceId=(%d), error=(%v)", flowInstanceId, err)
		return
	}
	if stages == nil {
		bnsWhitelist, ipWhitelist := formatWhitelistParams(tmpBnsList, tmpIpList, flowDetail.Privilege)

		taskParams := WhitelistTask{
			FlowInstanceID: flowInstanceId,
			NodeInstanceID: nodeInstanceId,
			ClusterId:      flowDetail.ClusterID,
			ClusterName:    clusterData.Name,
			Applicant:      flowDetail.Applicant,
			Description:    flowDetail.Description,
			Operator:       nodeHistory[0].Operator,
			Stages:         [2]*whitelistStage.StageSchema{},
		}

		// 通过proxy表有没有docker或bbc实例来判断应该创建几个stage
		for _, proxy := range clusterData.Edges.Proxy {
			if taskParams.Stages[proxy.Docker] == nil {
				taskParams.Stages[proxy.Docker] = &whitelistStage.StageSchema{
					Docker:       proxy.Docker,
					Type:         whitelistStage.WhitelistTypeBusiness,
					Action:       flowDetail.Type,
					WhiteListBns: bnsWhitelist,
					WhiteListIp:  ipWhitelist,
				}
			}
		}

		stages, err = CreateWhitelistStages(&taskParams)
		if err != nil {
			logger.Error("failed to create whitelist stages, flowInstanceId=(%d), error=(%v)", flowInstanceId, err)
			return
		}
	}

	// step4.发送请求给控制层执行
	for _, s := range stages {
		err := workflow.Exec(s.ID)
		if err != nil {
			err = fmt.Errorf("failed to %s whitelist, flowInstanceId=%d, stageId=%d, error=(%v)", flowDetail.Type, flowInstanceId, s.ID, err)
			logger.Warn(err.Error())
			return
		}
		logger.Info("succeed to %s whitelist, flowInstanceId=%d", flowDetail.Type, flowInstanceId)
	}
}
