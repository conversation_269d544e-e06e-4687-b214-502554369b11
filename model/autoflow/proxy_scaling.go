/*
 * proxy扩缩容需要区分场景
 * 1、扩容 - 配置更新（前置检查）、按序解屏蔽
 * 2、缩容 - 配置更新（前置检查、屏蔽&污点多余Proxy实例）、删除污点实例
 */
package autoflow

import (
	"encoding/json"
	"fmt"

	"dt-common/ent/cluster"
	"dt-common/logger"
	"dt-common/mysql"
	"dt-common/omodel"
	"redis-xweb/library/ras"
)

// 扩缩容流程表单参数
type ProxyScalingSchema struct {
	ProxyNum int `json:"proxyNum"`
}

// Proxy扩缩容流程
func ProxyScaling(clusterName string, paramStr string) (*TaskArgs, error) {
	// step1. 解析参数
	var params ProxyScalingSchema
	err := json.Unmarshal([]byte(paramStr), &params)
	if err != nil {
		logger.Warn("failed to unmarshal flow parameter, cluster=%s, parameter=%s, error=(%v)", clusterName, paramStr, err)
		return nil, err
	}

	// step2.并发变更检查
	db, err := mysql.Database()
	if err != nil {
		logger.Error("failed to get mysql connection, error=(%v)", err)
		return nil, err
	}
	ctx, cancel := mysql.ContextWithTimeout()
	clusterData, err := db.Cluster.Query().Where(cluster.Name(clusterName)).Only(ctx)
	cancel()
	if err != nil {
		logger.Error("failed to query cluster, cluster=%s, error=(%v)", clusterName, err)
		return nil, err
	}
	if params.ProxyNum%2 != 0 {
		logger.Error("number of proxies must be an even number, cluster=%s, error=(%v)", clusterName, err)
		return nil, fmt.Errorf("number of proxies must be an even number")
	}
	// 数量一致的不用再改了
	if clusterData.ProxyNum == params.ProxyNum {
		logger.Error("no change in number of proxy, cluster=%s, error=(%v)", clusterName, err)
		return nil, fmt.Errorf("no change in number of proxy")
	}

	// step3.组装参数
	taskArgs := TaskArgs{
		Type: omodel.TASK_TYPE_PROXY_SCALING,
		Stages: []*StageArgs{
			{
				Name:      "配置更新",
				Type:      omodel.STAGE_TYPE_PROXY_SCALING,
				Parameter: ras.ProxyScalingParams{ProxyNum: params.ProxyNum},
				Automate:  true,
			},
		},
	}

	// 扩 & 缩容
	if clusterData.ProxyNum < params.ProxyNum {
		taskArgs.Name = "Proxy扩容"
		taskArgs.Stages = append(taskArgs.Stages, &StageArgs{
			Name:     "Proxy解屏蔽",
			Type:     omodel.STAGE_TYPE_PROXY_ENABLE,
			Automate: false,
		})
	} else {
		taskArgs.Name = "Proxy缩容"
		taskArgs.Stages = append(taskArgs.Stages, &StageArgs{
			Name:     "污点Proxy退还",
			Type:     omodel.STAGE_TYPE_PROXY_REMOVE,
			Automate: false,
		})
	}

	return &taskArgs, nil
}
