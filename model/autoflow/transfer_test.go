package autoflow

import (
	"context"
	"regexp"
	"testing"

	"github.com/jarcoal/httpmock"

	"dt-common/ent/cluster"
	"dt-common/mysql"
	"redis-xweb/env"
)

// 单测：Flow自动执行 - 转交
func TestTransfer(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")

	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	//
	db, _ := mysql.Database()
	objC := env.MockCluster("transfer_test")
	db.FlowTransfer.Create().
		SetFlowInstanceID(45908).SetClusterID(objC.ID).SetClusterName(objC.Name).
		SetDepartmentID(1000000037).SetDepartment("系统运维部").SetApplicant("jiayiming_dxm").
		SetReceiver("huzhaoyun_dxm").SetDescription("test").
		SaveX(context.Background())

	type args struct {
		flowInstanceId    uint32
		nodeInstanceId    uint32
		listId            int64
		listType          string
		nodeSubmitHistory map[string]string
		nodeReturnHistory map[string]string
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test: success",
			before: func() {
				// 获取订单
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/api/billing/order/info`),
					httpmock.ResponderFromResponse(httpmock.NewStringResponse(200, "{\"errno\":0,\"errmsg\":\"success\",\"data\":[{\"resourceName\":\"REDIS\",\"resourceNameCh\":\"缓存数据库\",\"resourceId\":100049,\"resourceType\":\"\",\"comboName\":\"redis-科技云-单分片\",\"comboId\":21,\"comboCode\":100049985621,\"comboDesc\":\"\",\"comboUnitInfo\":\"\",\"unitPrice\":0,\"departCode\":1000000037,\"department\":\"系统运维部\",\"team\":\"\",\"useType\":0,\"business\":[{\"name\":\"理财\",\"percent\":100}],\"product\":[],\"isAckPercentMonthly\":false,\"percentDesc\":\"\",\"isUseAuthPercent\":false,\"authPercentName\":\"\",\"authPercentCode\":\"\",\"busReview\":null,\"tag\":null,\"cloudName\":\"\",\"cloudLabel\":\"dev\",\"desc\":\"redis: billing_desc_test\",\"RDOwner\":\"\",\"OPOwner\":\"\",\"zoneType\":\"\",\"idc\":\"\",\"useTypeName\":\"普通服务\",\"useForProduct\":\"\",\"staticQuantity\":null,\"id\":\"65f94c5bff1b3ecb5fcb7d0e\",\"instanceId\":\"billing_desc_test\",\"snapInfo\":{\"num\":1},\"costId\":\"\",\"monthPrice\":230,\"orderCode\":\"1000492168276591\",\"owner\":\"jiayiming_dxm\",\"startTime\":\"2024-03-19T16:27:07.653+08:00\",\"endTime\":\"0001-01-01T08:05:43+08:05\",\"createdAt\":\"2024-03-19T16:27:07.684+08:00\",\"updateAt\":\"2024-03-19T16:27:07.684+08:00\",\"status\":1,\"parentCode\":\"\",\"busPercentStatus\":0,\"instanceName\":\"\",\"shortId\":\"redis-57im2gwy4ab\"}]}")))

				// 更新订单
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/api/billing/order/modify`),
					httpmock.ResponderFromResponse(httpmock.NewStringResponse(200, "{\"errno\":0,\"errmsg\":\"success\",\"data\":{}}")))

				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/auth/GetRoleByCondition`),
					httpmock.NewStringResponder(200, "[{\"Id\":\"1\",\"Name\":\"test_cluster1__rd-engineer\",\"Alias\":\"test_cluster1__rd-engineer\",\"Type\":\"0\",\"Comment\":\"\"}]"))

				// 绑定角色
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/auth/BindRoleToUser`),
					httpmock.NewStringResponder(200, `{"errno": 0, "errmsg": "", "data": "ok"}`))
			},
			args: args{
				flowInstanceId: 45908,
				nodeInstanceId: 120171,
				listId:         1,
				listType:       "",
				nodeSubmitHistory: map[string]string{
					"redisReview": `[{"operator": "hanshengzhao_dxm","role": ["redis-manager","redis-engineer"],"action": "pass","submitInfo": {"desc": "无"}}]`,
				},
				nodeReturnHistory: nil,
			},
			expect: func(t *testing.T) {
				cc, _ := db.Cluster.Query().Where(cluster.ID(objC.ID)).Only(context.Background())
				if cc.Owner != "huzhaoyun_dxm" {
					t.Errorf("Transfer() error = %v, wantErr %v", cc.Owner, "huzhaoyun_dxm")
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			Transfer(tt.args.flowInstanceId, tt.args.nodeInstanceId, tt.args.listId, tt.args.listType, tt.args.nodeSubmitHistory, tt.args.nodeReturnHistory)
			// if (err != nil) != tt.wantErr {
			// 	t.Errorf("Transfer() error = %v, wantErr %v", err, tt.wantErr)
			// }
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}
