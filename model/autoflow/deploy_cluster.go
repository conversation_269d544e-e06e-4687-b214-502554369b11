package autoflow

import (
	"context"
	"encoding/json"
	"fmt"
	"slices"
	"strings"

	"dt-common/ent"
	"dt-common/ent/flowcluster"
	"dt-common/errs"
	"dt-common/logger"
	"dt-common/mysql"
	"dt-common/omodel"
	"dt-common/user-center/authc"
	"redis-xweb/library/userc"
	"redis-xweb/model"
	"redis-xweb/model/workflow"
)

const (
	defaultBnsGroupOwner = "jiayiming_dxm"
)

var (
	StageList = []*omodel.Stage{
		{Name: "实例部署", Type: omodel.STAGE_TYPE_DEPLOY},
		{Name: "添加监控", Type: omodel.STAGE_TYPE_ADD_MONITOR},
		{Name: "创建SMART", Type: omodel.STAGE_TYPE_CREATE_GROUP},
		{Name: "生成账单", Type: omodel.STAGE_TYPE_GENERATE_BILL},
	}
)

// 根据现有集群信息组装各阶段参数
// 后续阶段的绝大部分参数需要在容器集群部署完成之后才能得到
// 参数规则：object， value可以是简单类型、数组或object，但不能是 object 的数组
func getStagesRawData(clusterData *ent.Cluster, bussiness map[string]float64) []*omodel.Stage {
	stages := slices.Clone(StageList)
	for _, s := range stages {
		switch s.Type {
		case omodel.STAGE_TYPE_DEPLOY:
			s.Parameter = map[string]any{
				"clusterId":        clusterData.ID,
				"name":             clusterData.Name,
				"proxyPassword":    clusterData.Password,
				"proxyPort":        clusterData.Port,
				"proxyReplicas":    map[string]int{"hba": clusterData.ProxyNum / 2, "hbb": clusterData.ProxyNum / 2},
				"shardNum":         clusterData.ShardNum,
				"shardMem":         clusterData.StorageSize / clusterData.ShardNum,
				"shardPort":        7000,
				"shardReplicas":    map[string]int{"hba": 1, "hbb": 1},
				"maxmemoryPolicy":  clusterData.MaxmemoryPolicy,
				"sentinelPort":     9001,
				"sentinelReplicas": map[string]int{"hba": 1, "hbb": 1, "hbc": 1},
				"enabledAZ":        []string{"hba", "hbb", "hbc"},
			}
		case omodel.STAGE_TYPE_ADD_MONITOR:
		case omodel.STAGE_TYPE_CREATE_GROUP:
			s.Parameter = map[string]any{
				"group":         clusterData.Smart,
				"owner":         defaultBnsGroupOwner,
				"targetAppList": []string{fmt.Sprintf("%s-router.%s", clusterData.Subsystem, clusterData.ProductLine)},
			}
		case omodel.STAGE_TYPE_GENERATE_BILL:
			s.Parameter = map[string]any{
				"business": bussiness,
				"products": map[string]float64{},
			}
		}
	}

	return stages
}

// 创建任务和阶段，返回任务ID供前端跳转
func createTaskAndStages(flowInstanceId, nodeInstanceId uint32, clusterId int64, clusterName string, stageRawDatas []*omodel.Stage, applicant string) ([]*ent.Stage, error) {
	db, err := mysql.Database()
	if err != nil {
		logger.Error("failed to get mysql connection, error=(%v)", err)
		return nil, err
	}

	// 开启事务，创建task和stages
	stages := []*ent.Stage{}
	err = db.Transaction(context.Background(), func(tx *ent.Tx) error {
		// 创建Task
		ctx, cancel := mysql.ContextWithTimeout()
		taskData, err := tx.Task.Create().
			SetFlowInstanceID(flowInstanceId).SetNodeInstanceID(nodeInstanceId).
			SetClusterID(clusterId).SetClusterName(clusterName).SetApplicant(applicant).
			SetType(omodel.TASK_TYPE_DEPLOY).SetName("新实例部署").SetDescription("新实例部署").
			Save(ctx)
		cancel()
		if err != nil {
			logger.Error("failed to create deploy task, cluster=%s, error=(%v)", clusterName, err)
			return err
		}

		// 创建Stages
		bulkArray := make([]*ent.StageCreate, len(stageRawDatas))
		emptyParams := map[string]any{}
		for i, rawData := range stageRawDatas {
			if rawData.Parameter == nil {
				rawData.Parameter = emptyParams
			}
			params, err := json.Marshal(rawData.Parameter)
			if err != nil {
				logger.Warn("failed to marshal Stage.Parameter, parameter=%+v, error=(%v)", rawData.Parameter, err)
				return err
			}

			bulkArray[i] = tx.Stage.Create().
				SetTask(taskData).SetClusterName(clusterName).
				SetName(rawData.Name).SetType(rawData.Type).
				SetSequence(i + 1).SetParameter(string(params)).SetAutomate(true)
		}
		ctx, cancel = mysql.ContextWithTimeout()
		stages, err = tx.Stage.CreateBulk(bulkArray...).Save(ctx)
		cancel()
		if err != nil {
			logger.Error("failed to bulk create stages, cluster=%s, error=(%v)", clusterName, err)
			return err
		}
		return nil
	})
	if err != nil {
		logger.Error("failed to create task, cluster=%s, error=(%v)", clusterName, err)
		return nil, err
	}

	return stages, nil
}

// 创建一条cluster记录
func insertCluster(flowDetail *ent.FlowCluster) (*ent.Cluster, error) {
	productLine := "siod-redis"
	subsystem := strings.ReplaceAll(flowDetail.Name, "_", "-")
	// 计算proxy数量
	proxyMap := make(map[string]int)
	err := json.Unmarshal([]byte(flowDetail.ProxyMap), &proxyMap)
	if err != nil {
		return nil, err
	}
	proxyNum := 0
	for _, num := range proxyMap {
		proxyNum += num
	}
	// smart入口地址
	smart := fmt.Sprintf("smart.group.%s-router.%s", subsystem, productLine)

	db, err := mysql.Database()
	if err != nil {
		logger.Error("failed to get mysql connection, error=(%v)", err)
		return nil, err
	}

	clusterData, err := db.Cluster.Create().
		SetName(flowDetail.Name).SetAlias(flowDetail.Name).SetType(flowDetail.Type).
		SetProductLine(productLine).SetSubsystem(subsystem).SetArea(flowDetail.Area).
		SetStorageSize(flowDetail.StorageSize).SetMaxmemoryPolicy(flowDetail.MaxmemoryPolicy).
		SetShardNum(flowDetail.ShardNum).SetDocker(omodel.DEPLOY_ENV_DOCKER).
		SetSmart(smart).SetProxyNum(proxyNum).SetPort(8001).SetPassword(flowDetail.Password).
		SetProxyVersion("a326").SetRedisVersion(flowDetail.RedisVersion).
		SetDepartmentID(flowDetail.DepartmentID).SetDepartment(flowDetail.Department).
		SetOwner(flowDetail.Applicant).SetLevel(flowDetail.Level).SetStatus(omodel.CLUSTER_STATUS_DEPLOYING).
		Save(context.Background())
	if err != nil {
		logger.Warn("failed to insert cluster, cluster=%s, error=(%v)", flowDetail.Name, err)
		return nil, err
	}

	return clusterData, nil
}

// 初始化集群的权限系统
func createRoleAndBindtoUser(clusterName, applicant string) error {
	// 创建新角色：Name__rd-engineer / Name__rd-manager
	userRole := fmt.Sprintf("%s__%s", clusterName, model.ROLE_USER)
	managerRole := fmt.Sprintf("%s__%s", clusterName, model.ROLE_MANAGER)

	userRoles := map[string]int{userRole: 0, managerRole: 0}
	for roleName := range userRoles {
		roleId, err := authc.CreateRoleV2(&authc.Role{
			Name:   roleName,
			Type:   model.ROLE_TYPE_REDIS,
			Alias:  roleName,
			Params: map[string]string{},
		})
		if err != nil && !strings.Contains(err.Error(), "Duplicate") {
			logger.Error("failed to create role, cluster=%, roleName=%s, error=(%v)", clusterName, roleName, err)
			return err
		}
		userRoles[roleName] = roleId
	}

	// 将申请人添加为管理员
	managerRoleID := userRoles[managerRole]
	err := authc.BindRoleToUserV2(applicant, model.ROLE_TYPE_REDIS, &[]int{managerRoleID})
	if err != nil {
		logger.Warn("failed to bind role %s to user %s, roleId=%d, error=(%v)", managerRole, applicant, managerRoleID, err)
		return err
	}

	return nil
}

// 启动集群部署流程
func DeployCluster(flowInstanceId, nodeInstanceId uint32, listId int64, listType string, nodeSubmitHistory map[string]string, nodeReturnHistory map[string]string) {
	var err error
	finishFlow := true
	defer userc.SetFlowResultIfNeeded(flowInstanceId, nodeInstanceId, &finishFlow, &err)

	// step1.获取工单详情
	db, err := mysql.Database()
	if err != nil {
		logger.Error("failed to get mysql connection, error=(%v)", err)
		return
	}
	ctx, cancel := mysql.ContextWithTimeout()
	flowDetail, err := db.FlowCluster.Query().Where(flowcluster.IDEQ(listId)).Only(ctx)
	cancel()
	if err != nil {
		logger.Warn("failed to query FlowCluster, flowInstanceId=%d, listId=%d, error=(%v)", flowInstanceId, listId, err)
		return
	}
	bussiness := map[string]float64{}
	err = json.Unmarshal([]byte(flowDetail.Business), &bussiness)
	if err != nil {
		logger.Warn("failed to unmarshal business to map[string]float64, flowInstanceId=%d, listId=%d, error=(%v)", flowInstanceId, listId, err)
		return
	}

	// step2.重入判断
	stages, err := GetStagesByFlowNode(flowInstanceId, nodeInstanceId)
	if err != nil {
		logger.Error("failed to get stages, flowInstanceId=%d, error=(%v)", flowInstanceId, err)
		return
	}
	if stages == nil {
		// step2.1.创建集群
		clusterData, err := insertCluster(flowDetail)
		if err != nil {
			return
		}
		// step2.2.创建角色绑定Owner
		err = createRoleAndBindtoUser(clusterData.Name, clusterData.Owner)
		if err != nil {
			return
		}
		// step2.3.创建白名单
		bnsWhitelist := []string{}
		json.Unmarshal([]byte(flowDetail.Whitelist), &bnsWhitelist)
		err = insertIntoWhitelist(clusterData.ID, clusterData.Name, bnsWhitelist, []string{}, "rw", omodel.WHITELIST_STATUS_NORMAL)
		if err != nil {
			return
		}
		// step2.4.组装Stages元数据
		rawStages := getStagesRawData(clusterData, bussiness)
		// step2.5.创建task和stages
		stages, err = createTaskAndStages(flowInstanceId, nodeInstanceId, clusterData.ID, clusterData.Name, rawStages, clusterData.Owner)
		if err != nil {
			return
		}
	}

	// step3. 工单页面点击「通过」或「重试」时，按顺序自动执行未完成的阶段
	for _, stage := range stages {
		// 跳过执行成功的阶段
		if stage.Status == string(omodel.STAGE_STATUS_DONE) {
			continue
		}

		// 未执行或执行失败了启动执行
		if stage.Status == string(omodel.STAGE_STATUS_NORMAL) || stage.Status == string(omodel.STAGE_STATUS_ERROR) {
			err = workflow.Exec(stage.ID)
			if err != nil {
				logger.Warn("failed to exec stage, cluster=%s, flowInstanceId=%d, stageId=%d, stageName=%s, error=(%v)", flowDetail.Name, flowInstanceId, stage.ID, stage.Name, err)
				return
			}
			logger.Info("succeed to exec stage, cluster=%s, flowInstanceId=%d, stageId=%d, stageName=%s", flowDetail.Name, flowInstanceId, stage.ID, stage.Name)
			return
		}

		// 非预期状态，结束工单
		err = fmt.Errorf("unexpected status %s of stage %d", stage.Status, stage.ID)
		return
	}

	// 全部阶段执行完毕，结束工单（一般也走不到这
	err = errs.Success
	logger.Info("succeed to deploy, cluster=%s, flowInstanceId=%d", flowDetail.Name, flowInstanceId)
}
