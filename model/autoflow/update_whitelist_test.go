package autoflow

import (
	"context"
	"strings"
	"testing"

	"github.com/jarcoal/httpmock"

	"dt-common/ent"
	"dt-common/ent/cluster"
	"dt-common/ent/stage"
	"dt-common/ent/whitelist"
	"dt-common/mysql"
	"dt-common/omodel"
	"redis-xweb/env"
	whitelistStage "redis-xweb/model/stage/whitelist"
)

// 单测：Flow自动执行 - 更新白名单
func TestCreateWhitelistStages(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")
	objC := env.MockCluster("flow_test")

	type args struct {
		args WhitelistTask
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, []*ent.Stage)
	}{
		{
			name: "test: success",
			args: args{
				args: WhitelistTask{
					FlowInstanceID: 1,
					NodeInstanceID: 1,
					ClusterId:      objC.ID,
					ClusterName:    objC.Name,
					Applicant:      "jiayiming_dxm",
					Description:    "123",
					Operator:       "jiayiming_dxm",
					Stages: [2]*whitelistStage.StageSchema{
						{
							Docker:       0,
							Type:         whitelistStage.WhitelistTypeBusiness,
							Action:       "add",
							WhiteListBns: []string{"unit.test", "unit1.test"},
							WhiteListIp:  []string{"************", "************"},
						},
						{
							Docker:       1,
							Type:         whitelistStage.WhitelistTypeBusiness,
							Action:       "add",
							WhiteListBns: []string{"unit.test", "unit1.test"},
							WhiteListIp:  []string{"************", "************"},
						},
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			stageList, err := CreateWhitelistStages(&tt.args.args)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateWhitelistStages() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, stageList)
			}
		})
	}
}

func TestFormatWhitelistParams(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")

	type args struct {
		bnsList   []string
		ipList    []string
		privilege string
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, []string, []string)
	}{
		{
			name: "test: empty whitelist",
			args: args{
				bnsList:   []string{},
				ipList:    nil,
				privilege: "rw",
			},
			wantErr: true,
		},
		{
			name: "test: success",
			args: args{
				bnsList:   []string{"unit.test", "unit2.test"},
				ipList:    []string{"************", "************"},
				privilege: "rw",
			},
			wantErr: false,
			expect: func(t *testing.T, s1, s2 []string) {
				if len(s1) != 2 {
					t.Errorf("expect bns list len 2, but %d", len(s1))
				}
				if len(s2) != 2 {
					t.Errorf("expect ip list len 2, but %d", len(s1))
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			bnsList, ipList := formatWhitelistParams(tt.args.bnsList, tt.args.ipList, tt.args.privilege)
			// if (err != nil) != tt.wantErr {
			// 	t.Errorf("formatWhitelistParams() error = %v, wantErr %v", err, tt.wantErr)
			// }
			if tt.expect != nil {
				tt.expect(t, bnsList, ipList)
			}
		})
	}
}

// 单测：更新业务白名单
func TestInsertIntoWhitelist(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")
	objC := env.MockCluster("r3_test")
	db, _ := mysql.Database()

	type args struct {
		clusterID   int64
		clusterName string
		bnsList     []string
		ipList      []string
		privilege   string
		status      string
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test1: success added",
			before: func() {
				db.Whitelist.Delete().Exec(context.Background())
			},
			args: args{
				clusterID:   objC.ID,
				clusterName: objC.Name,
				bnsList:     []string{"blind-kj-zk.siod-kafka", "blind-kj-broker.siod-kafka"},
				ipList:      []string{"************"},
				privilege:   "rw",
				status:      omodel.WHITELIST_STATUS_ADDING,
			},
			wantErr: false,
			expect: func(t *testing.T) {
				list, _ := db.Whitelist.Query().Where(whitelist.ClusterName(objC.Name)).All(context.Background())
				if len(list) != 3 {
					t.Errorf("expect 2 whitelist, but got %d", len(list))
				}
				if list[0].Status != omodel.WHITELIST_STATUS_ADDING {
					t.Errorf("expect status equal adding, but got %s", list[0].Status)
				}
				if list[2].Privilege != "rw" {
					t.Errorf("expect privilege equal rw, but got %s", list[2].Privilege)
				}
			},
		},
	}
	for _, tt := range tests {
		if tt.before != nil {
			tt.before()
		}
		t.Run(tt.name, func(t *testing.T) {
			err := insertIntoWhitelist(tt.args.clusterID, tt.args.clusterName, tt.args.bnsList, tt.args.ipList, tt.args.privilege, tt.args.status)
			if (err != nil) != tt.wantErr {
				t.Errorf("insertIntoWhitelist() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

// 单测：更新业务白名单
func TestDeleteFromWhitelist(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")
	objC := env.MockCluster("r3_test")
	db, _ := mysql.Database()
	db.Whitelist.Delete().Where(whitelist.ClusterName(objC.Name)).Exec(context.Background())
	db.Whitelist.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetType("bns").SetValue("blind-kj-zk.siod-kafka").SetPrivilege("rw").Save(context.Background())
	db.Whitelist.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetType("bns").SetValue("blind-kj-broker.siod-kafka").SetPrivilege("r").Save(context.Background())
	db.Whitelist.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetType("ip").SetValue("************").SetPrivilege("rw").Save(context.Background())
	db.Whitelist.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetType("ip").SetValue("************").SetPrivilege("r").Save(context.Background())

	type args struct {
		clusterName string
		bnsList     []string
		ipList      []string
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test1: success",
			args: args{
				clusterName: objC.Name,
				bnsList:     []string{"blind-kj-zk.siod-kafka"},
				ipList:      []string{"************"},
			},
			wantErr: false,
			expect: func(t *testing.T) {
				whitelists, _ := db.Whitelist.Query().Where(whitelist.ClusterName(objC.Name)).All(context.Background())
				if len(whitelists) != 4 {
					t.Errorf("expect 4 whitelists, but got %d", len(whitelists))
				}
				if whitelists[0].Status != omodel.WHITELIST_STATUS_DELETED {
					t.Errorf("expect status equal deleted, but got %s", whitelists[0].Status)
				}
				if whitelists[1].Status == omodel.WHITELIST_STATUS_DELETED {
					t.Errorf("expect status not equal deleted, but got %s", whitelists[1].Status)
				}
			},
		},
	}
	for _, tt := range tests {
		if tt.before != nil {
			tt.before()
		}
		t.Run(tt.name, func(t *testing.T) {
			err := deleteFromWhitelist(tt.args.clusterName, tt.args.bnsList, tt.args.ipList)
			if (err != nil) != tt.wantErr {
				t.Errorf("deleteFromWhitelist() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

// 单测：Flow自动执行 - 更新白名单
func TestUpdateWhitelist(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")
	objC := env.MockCluster("flow_test")
	db, _ := mysql.Database()
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		flowInstanceId    uint32
		nodeInstanceId    uint32
		listId            int64
		listType          string
		nodeSubmitHistory map[string]string
		nodeReturnHistory map[string]string
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test: unmarshal reviewer failed",
			args: args{
				flowInstanceId: 45908,
				nodeInstanceId: 120171,
				listId:         17,
				listType:       "",
				nodeSubmitHistory: map[string]string{
					"redisReview": `["operator": "hanshengzhao_dxm","role": ["redis-manager","redis-engineer"],"action": "pass","submitInfo": {"desc": "无"}}]`,
				},
				nodeReturnHistory: nil,
			},
		},
		{
			name: "test: flow not found",
			args: args{
				flowInstanceId: 45908,
				nodeInstanceId: 120171,
				listId:         17,
				listType:       "",
				nodeSubmitHistory: map[string]string{
					"redisReview": `[{"operator": "hanshengzhao_dxm","role": ["redis-manager","redis-engineer"],"action": "pass","submitInfo": {"desc": "无"}}]`,
				},
				nodeReturnHistory: nil,
			},
		},
		{
			name: "test: flow type error",
			before: func() {
				db.Task.Delete().ExecX(context.Background())
				db.Stage.Delete().ExecX(context.Background())
				db.Proxy.Delete().ExecX(context.Background())
				db.FlowWhitelist.Delete().ExecX(context.Background())

				db.FlowWhitelist.Create().SetFlowInstanceID(45908).SetCluster(objC).SetDepartmentID(1000000037).SetDepartment("系统运维部").SetApplicant("jiayiming_dxm").SetType("add1").SetBns("unit.test").SetPrivilege("rw").SetIP("").SetDescription("123").SaveX(context.Background())
			},
			args: args{
				flowInstanceId: 45908,
				nodeInstanceId: 120171,
				listId:         1,
				listType:       "",
				nodeSubmitHistory: map[string]string{
					"redisReview": `[{"operator": "hanshengzhao_dxm","role": ["redis-manager","redis-engineer"],"action": "pass","submitInfo": {"desc": "无"}}]`,
				},
				nodeReturnHistory: nil,
			},
		},
		{
			name: "test: formatWhitelistParams failed",
			before: func() {
				db.Task.Delete().ExecX(context.Background())
				db.Stage.Delete().ExecX(context.Background())
				db.Proxy.Delete().ExecX(context.Background())
				db.FlowWhitelist.Delete().ExecX(context.Background())

				db.FlowWhitelist.Create().SetFlowInstanceID(45909).SetCluster(objC).SetDepartmentID(1000000037).SetDepartment("系统运维部").SetApplicant("jiayiming_dxm").SetType("add").SetBns("").SetPrivilege("rw").SetIP("").SetDescription("123").SaveX(context.Background())
			},
			args: args{
				flowInstanceId: 45909,
				nodeInstanceId: 120171,
				listId:         2,
				listType:       "",
				nodeSubmitHistory: map[string]string{
					"redisReview": `[{"operator": "hanshengzhao_dxm","role": ["redis-manager","redis-engineer"],"action": "pass","submitInfo": {"desc": "无"}}]`,
				},
				nodeReturnHistory: nil,
			},
		},
		{
			name: "test: call failed",
			before: func() {
				db.Cluster.Update().SetDocker(2).Where(cluster.ID(objC.ID)).ExecX(context.Background())
				db.FlowWhitelist.Create().SetFlowInstanceID(45909).SetCluster(objC).SetDepartmentID(1000000037).SetDepartment("系统运维部").SetApplicant("jiayiming_dxm").SetType("add").SetBns("unit.test").SetPrivilege("rw").SetIP("************").SetDescription("123").SaveX(context.Background())
			},
			args: args{
				flowInstanceId: 45909,
				nodeInstanceId: 120171,
				listId:         3,
				listType:       "",
				nodeSubmitHistory: map[string]string{
					"redisReview": `[{"operator": "hanshengzhao_dxm","role": ["redis-manager","redis-engineer"],"action": "pass","submitInfo": {"desc": "无"}}]`,
				},
				nodeReturnHistory: nil,
			},
		},
		{
			name: "test: create bbc stage",
			before: func() {
				db.Task.Delete().ExecX(context.Background())
				db.Stage.Delete().ExecX(context.Background())
				db.Proxy.Delete().ExecX(context.Background())
				db.FlowWhitelist.Delete().ExecX(context.Background())

				db.FlowWhitelist.Create().SetFlowInstanceID(45909).SetCluster(objC).SetDepartmentID(1000000037).SetDepartment("系统运维部").SetApplicant("jiayiming_dxm").SetType("add").SetBns("unit.tet").SetPrivilege("rw").SetIP("").SetDescription("123").SaveX(context.Background())
				db.Proxy.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(0).SetBns("flow-test-router.siod-redis").SetIP("************").SetPort(8001).SetIdc("hba").Save(context.Background())
			},
			args: args{
				flowInstanceId: 45909,
				nodeInstanceId: 120171,
				listId:         4,
				listType:       "",
				nodeSubmitHistory: map[string]string{
					"redisReview": `[{"operator": "hanshengzhao_dxm","role": ["redis-manager","redis-engineer"],"action": "pass","submitInfo": {"desc": "无"}}]`,
				},
				nodeReturnHistory: nil,
			},
			expect: func(t *testing.T) {
				stages, err := db.Stage.Query().Where(stage.ClusterName(objC.Name), stage.Type("updateWhitelist")).All(context.Background())
				if err != nil {
					t.Errorf("expect nil error but got %v", err)
				}
				if len(stages) != 1 {
					t.Errorf("expect 1 stage but got %d", len(stages))
				}
				if stages[0].Name != "Manager更新白名单" {
					t.Errorf("expect Manager更新白名单 but got %s", stages[0].Name)
				}
				if !strings.Contains(*stages[0].Parameter, "unit.tet") {
					t.Errorf("expect unit.tet in s.Parameter but got nothing")
				}
			},
		},
		{
			name: "test: create docker stage",
			before: func() {
				db.Task.Delete().ExecX(context.Background())
				db.Stage.Delete().ExecX(context.Background())
				db.Proxy.Delete().ExecX(context.Background())
				db.FlowWhitelist.Delete().ExecX(context.Background())

				db.FlowWhitelist.Create().SetFlowInstanceID(45909).SetCluster(objC).SetDepartmentID(1000000037).SetDepartment("系统运维部").SetApplicant("jiayiming_dxm").SetType("add").SetBns("unit.tet").SetPrivilege("rw").SetIP("").SetDescription("123").SaveX(context.Background())
				db.Proxy.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("flow-test-router.siod-redis").SetIP("************").SetPort(8001).SetIdc("hbb").Save(context.Background())
			},
			args: args{
				flowInstanceId: 45909,
				nodeInstanceId: 120171,
				listId:         5,
				listType:       "",
				nodeSubmitHistory: map[string]string{
					"redisReview": `[{"operator": "hanshengzhao_dxm","role": ["redis-manager","redis-engineer"],"action": "pass","submitInfo": {"desc": "无"}}]`,
				},
				nodeReturnHistory: nil,
			},
			expect: func(t *testing.T) {
				stages, err := db.Stage.Query().Where(stage.ClusterName(objC.Name), stage.Type("updateWhitelist")).All(context.Background())
				if err != nil {
					t.Errorf("expect nil error but got %v", err)
				}
				if len(stages) != 1 {
					t.Errorf("expect 1 stage but got %d", len(stages))
				}
				if stages[0].Name != "CManager更新白名单" {
					t.Errorf("expect CManager更新白名单 but got %s", stages[0].Name)
				}
				if !strings.Contains(*stages[0].Parameter, "unit.tet") {
					t.Errorf("expect unit.tet in s.Parameter but got nothing")
				}
			},
		},
		{
			name: "test: create 2 stages",
			before: func() {
				db.Task.Delete().ExecX(context.Background())
				db.Stage.Delete().ExecX(context.Background())
				db.Proxy.Delete().ExecX(context.Background())
				db.FlowWhitelist.Delete().ExecX(context.Background())

				db.FlowWhitelist.Create().SetFlowInstanceID(45909).SetCluster(objC).SetDepartmentID(1000000037).SetDepartment("系统运维部").SetApplicant("jiayiming_dxm").SetType("add").SetBns("unit.tet").SetPrivilege("rw").SetIP("").SetDescription("123").SaveX(context.Background())
				db.Proxy.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(0).SetBns("flow-test-router.siod-redis").SetIP("************").SetPort(8001).SetIdc("hba").Save(context.Background())
				db.Proxy.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).SetDocker(1).SetBns("flow-test-router.siod-redis").SetIP("************").SetPort(8001).SetIdc("hbb").Save(context.Background())
			},
			args: args{
				flowInstanceId: 45909,
				nodeInstanceId: 120171,
				listId:         6,
				listType:       "",
				nodeSubmitHistory: map[string]string{
					"redisReview": `[{"operator": "hanshengzhao_dxm","role": ["redis-manager","redis-engineer"],"action": "pass","submitInfo": {"desc": "无"}}]`,
				},
				nodeReturnHistory: nil,
			},
			expect: func(t *testing.T) {
				stages, err := db.Stage.Query().Where(stage.ClusterName(objC.Name), stage.Type("updateWhitelist")).All(context.Background())
				if err != nil {
					t.Errorf("expect nil error but got %v", err)
				}
				if len(stages) != 2 {
					t.Errorf("expect 2 stage but got %d", len(stages))
				}
				if stages[0].Name != "Manager更新白名单" {
					t.Errorf("expect Manager更新白名单 but got %s", stages[0].Name)
				}
				if stages[1].Name != "CManager更新白名单" {
					t.Errorf("expect CManager更新白名单 but got %s", stages[0].Name)
				}
				if !strings.Contains(*stages[0].Parameter, "unit.tet") {
					t.Errorf("expect unit.tet in s.Parameter but got nothing")
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			UpdateWhitelist(tt.args.flowInstanceId, tt.args.nodeInstanceId, tt.args.listId, tt.args.listType, tt.args.nodeSubmitHistory, tt.args.nodeReturnHistory)
			// if (err != nil) != tt.wantErr {
			// 	t.Errorf("UpdateWhitelist() error = %v, wantErr %v", err, tt.wantErr)
			// }
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}
