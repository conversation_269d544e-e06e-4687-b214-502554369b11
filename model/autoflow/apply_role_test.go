package autoflow

import (
	"context"
	"regexp"
	"testing"

	"github.com/jarcoal/httpmock"

	"dt-common/mysql"
	"redis-xweb/env"
)

// 单测：Flow自动执行 - 更新白名单
func TestAddUserToRole(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")
	objC := env.MockCluster("flow_test")
	db, _ := mysql.Database()
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		flowInstanceId    uint32
		nodeInstanceId    uint32
		listId            int64
		listType          string
		nodeSubmitHistory map[string]string
		nodeReturnHistory map[string]string
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test: flow not found",
			args: args{
				flowInstanceId: 45908,
				nodeInstanceId: 120171,
				listId:         17,
				listType:       "",
				nodeSubmitHistory: map[string]string{
					"redisReview": `[{"operator": "hanshengzhao_dxm","role": ["redis-manager","redis-engineer"],"action": "pass","submitInfo": {"desc": "无"}}]`,
				},
				nodeReturnHistory: nil,
			},
		},
		{
			name: "test: undefined role",
			before: func() {
				db.FlowApplyCluster.Create().SetFlowInstanceID(908).SetCluster(objC).SetDepartmentID(1000000037).SetDepartment("系统运维部").SetApplicant("jiayiming_dxm").SetRole("boss").SetDescription("123").SaveX(context.Background())
			},
			args: args{
				flowInstanceId: 45908,
				nodeInstanceId: 120171,
				listId:         1,
				listType:       "",
				nodeSubmitHistory: map[string]string{
					"redisReview": `[{"operator": "hanshengzhao_dxm","role": ["redis-manager","redis-engineer"],"action": "pass","submitInfo": {"desc": "无"}}]`,
				},
				nodeReturnHistory: nil,
			},
		},
		{
			name: "test: GetRoleByName failed",
			before: func() {
				db.FlowApplyCluster.Create().SetFlowInstanceID(908).SetCluster(objC).SetDepartmentID(1000000037).SetDepartment("系统运维部").SetApplicant("jiayiming_dxm").SetRole("user").SetDescription("123").SaveX(context.Background())
			},
			args: args{
				flowInstanceId: 45909,
				nodeInstanceId: 120171,
				listId:         2,
				listType:       "",
				nodeSubmitHistory: map[string]string{
					"redisReview": `[{"operator": "hanshengzhao_dxm","role": ["redis-manager","redis-engineer"],"action": "pass","submitInfo": {"desc": "无"}}]`,
				},
				nodeReturnHistory: nil,
			},
		},
		{
			name: "test: bind role failed",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/auth/GetRoleByCondition`),
					httpmock.NewStringResponder(200, "[{\"Id\":\"1\",\"Name\":\"test_cluster1__rd-engineer\",\"Alias\":\"test_cluster1__rd-engineer\",\"Type\":\"0\",\"Comment\":\"\"}]"))
			},
			args: args{
				flowInstanceId: 45909,
				nodeInstanceId: 120171,
				listId:         2,
				listType:       "",
				nodeSubmitHistory: map[string]string{
					"redisReview": `[{"operator": "hanshengzhao_dxm","role": ["redis-manager","redis-engineer"],"action": "pass","submitInfo": {"desc": "无"}}]`,
				},
				nodeReturnHistory: nil,
			},
		},
		{
			name: "test: success",
			before: func() {
				httpmock.RegisterRegexpResponder("POST", regexp.MustCompile(`://[\w\W]+/auth/BindRoleToUser`),
					httpmock.NewStringResponder(200, `{"errno": 0, "errmsg": "", "data": "ok"}`))
			},
			args: args{
				flowInstanceId: 45909,
				nodeInstanceId: 120171,
				listId:         2,
				listType:       "",
				nodeSubmitHistory: map[string]string{
					"redisReview": `[{"operator": "hanshengzhao_dxm","role": ["redis-manager","redis-engineer"],"action": "pass","submitInfo": {"desc": "无"}}]`,
				},
				nodeReturnHistory: nil,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			AddUserToRole(tt.args.flowInstanceId, tt.args.nodeInstanceId, tt.args.listId, tt.args.listType, tt.args.nodeSubmitHistory, tt.args.nodeReturnHistory)
			// if (err != nil) != tt.wantErr {
			// 	t.Errorf("AddUserToRole() error = %v, wantErr %v", err, tt.wantErr)
			// }
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}
