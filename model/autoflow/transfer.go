package autoflow

import (
	"fmt"

	"dt-common/ent/cluster"
	"dt-common/ent/flowtransfer"
	"dt-common/errs"
	"dt-common/hi"
	"dt-common/logger"
	"dt-common/mysql"
	"dt-common/user-center/authc"
	"redis-xweb/library/billing"
	"redis-xweb/library/userc"
	"redis-xweb/model"
)

// 集群转让，变更owner记录的同时还会重置成本中心订单
func Transfer(flowInstanceId, nodeInstanceId uint32, listId int64, listType string, nodeSubmitHistory map[string]string, nodeReturnHistory map[string]string) {
	var err error
	finishFlow := true
	defer userc.SetFlowResultIfNeeded(flowInstanceId, nodeInstanceId, &finishFlow, &err)

	// step1.获取工单&集群详情
	db, err := mysql.Database()
	if err != nil {
		logger.Error("failed to get mysql connection, error=(%v)", err)
		return
	}
	// 工单详情
	ctx, cancel := mysql.ContextWithTimeout()
	flowDetail, err := db.FlowTransfer.Query().WithCluster().Where(flowtransfer.IDEQ(listId)).Only(ctx)
	cancel()
	if err != nil {
		logger.Error("failed to query FlowTransfer, flowInstanceId=%d, error=(%v)", flowInstanceId, err)
		return
	}

	// 账单转让, 征信的两个混部集群没有账单
	if flowDetail.ClusterName != "rim_ric_isolate" && flowDetail.ClusterName != "zx_di_predictor" {
		err = billing.Transfer(flowDetail.ClusterName, flowDetail.Receiver, flowDetail.DepartmentID, flowDetail.Department)
		if err != nil {
			logger.Error("failed to update billing order, error=(%v)", err)
			return
		}
	}

	// 绑定角色
	managerRole := fmt.Sprintf("%s__%s", flowDetail.ClusterName, model.ROLE_MANAGER)
	roleData, err := authc.GetRoleByName(managerRole, model.ROLE_TYPE_REDIS)
	if err != nil {
		logger.Warn("failed to get role detail, roleName=%s, error=(%v)", managerRole, err)
		return
	}
	err = authc.BindRoleToUserV2(flowDetail.Receiver, model.ROLE_TYPE_REDIS, &[]int{roleData.ID})
	if err != nil {
		logger.Error("failed to bind role to user, user=%s, role=%s, roleId=%d, error=(%v)", flowDetail.Receiver, managerRole, roleData.ID, err)
		return
	}

	// 修改数据库记录
	ctx, cancel = mysql.ContextWithTimeout()
	_, err = db.Cluster.Update().SetOwner(flowDetail.Receiver).Where(cluster.IDEQ(flowDetail.ClusterID)).Save(ctx)
	cancel()
	if err != nil {
		logger.Error("failed to update owner, clusterId=%d, error=(%v)", flowDetail.ClusterID, err)
		return
	}
	// HI通知到群，如果有
	if flowDetail.Edges.Cluster.FeishuID != "" {
		err = hi.To(flowDetail.Edges.Cluster.FeishuID).Text("集群负责人变更：%s -> %s", flowDetail.Edges.Cluster.Owner, flowDetail.Receiver).Send()
		if err != nil {
			logger.Error("[Hi] %v", err)
			return
		}
	}

	// 同步任务，将err赋值errs.Success结束阶段
	err = errs.Success
}
