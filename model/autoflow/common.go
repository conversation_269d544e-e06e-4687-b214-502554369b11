package autoflow

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"dt-common/ent"
	"dt-common/ent/flow"
	"dt-common/ent/task"
	"dt-common/errs"
	"dt-common/logger"
	"dt-common/mysql"
	"dt-common/omodel"
	"redis-xweb/library/userc"
	"redis-xweb/model/workflow"
)

// ======================================================
// 				     通用Flow任务注册
// ======================================================

type Job func(clusterName string, paramStr string) (*TaskArgs, error)

var (
	JobMap = map[string]Job{
		"proxyScaling": ProxyScaling,
	}
)

// ======================================================
// 				    创建Task & Stages
// ======================================================

// 根据flowInstanceId获取task&stages
func GetStagesByFlowNode(flowInstanceId, nodeInstanceId uint32) ([]*ent.Stage, error) {
	db, err := mysql.Database()
	if err != nil {
		logger.Error("failed to get mysql connection, error=(%v)", err)
		return nil, err
	}

	ctx, cancel := mysql.ContextWithTimeout()
	taskData, err := db.Task.Query().Where(
		task.FlowInstanceID(flowInstanceId),
		task.NodeInstanceID(nodeInstanceId),
	).WithStages().Only(ctx)
	cancel()
	if err != nil && !strings.Contains(err.Error(), "task not found") {
		logger.Warn("failed to query Task, flowInstanceId=%d, nodeInstanceId=%d, error=(%v)", flowInstanceId, nodeInstanceId, err)
		return nil, err
	}

	if taskData != nil {
		return taskData.Edges.Stages, nil
	}

	return nil, nil
}

type FlowArgs struct {
	FlowInstanceID uint32
	NodeInstanceID uint32
	Applicant      string
	Description    string
	Operator       string
	ClusterID      int64
	ClusterName    string
}
type StageArgs struct {
	Name      string
	Type      string
	Parameter any
	Automate  bool
}
type TaskArgs struct {
	*FlowArgs
	Type   string
	Name   string
	Stages []*StageArgs
}

// 创建任务和阶段，返回任务ID供前端跳转
func CreateTaskAndStages(args *TaskArgs) ([]*ent.Stage, error) {
	db, err := mysql.Database()
	if err != nil {
		logger.Error("failed to get mysql connection, error=(%v)", err)
		return nil, err
	}

	// 开启事务，创建task和stages
	stages := []*ent.Stage{}
	err = db.Transaction(context.Background(), func(tx *ent.Tx) error {
		// 创建Task
		ctx, cancel := mysql.ContextWithTimeout()
		taskData, err := tx.Task.Create().
			SetFlowInstanceID(args.FlowInstanceID).SetNodeInstanceID(args.NodeInstanceID).
			SetClusterID(args.ClusterID).SetClusterName(args.ClusterName).SetApplicant(args.Applicant).
			SetType(args.Type).SetName(args.Name).SetDescription(args.Description).
			Save(ctx)
		cancel()
		if err != nil {
			logger.Error("failed to create deploy task, cluster=%s, error=(%v)", args.ClusterName, err)
			return err
		}

		// 创建Stages
		bulkArray := make([]*ent.StageCreate, len(args.Stages))
		emptyParams := map[string]any{}
		for i, rawData := range args.Stages {
			if rawData.Parameter == nil {
				rawData.Parameter = emptyParams
			}
			bytesParam, err := json.Marshal(rawData.Parameter)
			if err != nil {
				logger.Warn("failed to marshal Stage.Parameter, parameter=%+v, error=(%v)", rawData.Parameter, err)
				return err
			}

			bulkArray[i] = tx.Stage.Create().
				SetTask(taskData).SetClusterName(args.ClusterName).
				SetName(rawData.Name).SetType(rawData.Type).
				SetSequence(i + 1).SetParameter(string(bytesParam)).SetAutomate(rawData.Automate)
		}
		ctx, cancel = mysql.ContextWithTimeout()
		stages, err = tx.Stage.CreateBulk(bulkArray...).Save(ctx)
		cancel()
		if err != nil {
			logger.Error("failed to bulk create stages, cluster=%s, error=(%v)", args.ClusterName, err)
			return err
		}
		return nil
	})
	if err != nil {
		logger.Error("failed to create task, cluster=%s, error=(%v)", args.ClusterName, err)
		return nil, err
	}

	return stages, nil
}

// ======================================================
// 				     通用Flow执行入口
// ======================================================

// 需要从nodeSubmitHistory中解析出操作人
type ReviewNode struct {
	Operator string `json:"operator"`
}

// 通用自动流程执行入口
func Run(flowInstanceId, nodeInstanceId uint32, listId int64, listType string, nodeSubmitHistory map[string]string, nodeReturnHistory map[string]string) {
	var err error
	finishFlow := true
	defer userc.SetFlowResultIfNeeded(flowInstanceId, nodeInstanceId, &finishFlow, &err)

	// 从审批流中解析出审批人
	var nodeHistory []ReviewNode
	err = json.Unmarshal([]byte(nodeSubmitHistory["redisReview"]), &nodeHistory)
	if err != nil {
		logger.Warn("failed to unmarshal nodeSubmitHistory['redisReview'], redisReview=%v", nodeSubmitHistory["redisReview"])
		return
	}

	// step1.获取工单详情
	db, err := mysql.Database()
	if err != nil {
		logger.Error("failed to get mysql connection, error=(%v)", err)
		return
	}
	ctx, cancel := mysql.ContextWithTimeout()
	flowDetail, err := db.Flow.Query().Where(flow.IDEQ(listId)).Only(ctx)
	cancel()
	if err != nil {
		logger.Warn("failed to query Flow, flowInstanceId=%d, listId=%d, error=(%v)", flowInstanceId, listId, err)
		return
	}

	// step2.重入判断
	stages, err := GetStagesByFlowNode(flowInstanceId, nodeInstanceId)
	if err != nil {
		logger.Error("failed to get stages, flowInstanceId=%d, error=(%v)", flowInstanceId, err)
		return
	}
	if stages == nil {
		// step2.1.检查流程类型
		if _, ok := JobMap[flowDetail.Type]; !ok {
			err = fmt.Errorf("unexpected flow type %s", flowDetail.Type)
			return
		}
		// step2.2.获取Task&Stages参数
		var taskArgs *TaskArgs
		taskArgs, err = JobMap[flowDetail.Type](flowDetail.ClusterName, flowDetail.Parameter)
		if err != nil {
			return
		}
		// step2.3.创建Task&Stages
		taskArgs.FlowArgs = &FlowArgs{
			FlowInstanceID: flowInstanceId,
			NodeInstanceID: nodeInstanceId,
			Applicant:      flowDetail.Applicant,
			Description:    flowDetail.Description,
			Operator:       nodeHistory[0].Operator,
			ClusterID:      flowDetail.ClusterID,
			ClusterName:    flowDetail.ClusterName,
		}

		stages, err = CreateTaskAndStages(taskArgs)
		if err != nil {
			return
		}
	}

	// step3. 工单页面点击「通过」或「重试」时，按顺序自动执行未完成的第一个阶段
	for _, stage := range stages {
		// 跳过执行成功的阶段
		if stage.Status == string(omodel.STAGE_STATUS_DONE) {
			continue
		}

		// 未执行或执行失败了启动执行
		if stage.Status == string(omodel.STAGE_STATUS_NORMAL) || stage.Status == string(omodel.STAGE_STATUS_ERROR) {
			err = workflow.Exec(stage.ID)
			if err != nil {
				logger.Warn("failed to exec stage, cluster=%s, flowInstanceId=%d, stageId=%d, stageName=%s, error=(%v)", flowDetail.ClusterName, flowInstanceId, stage.ID, stage.Name, err)
				return
			}
			logger.Info("succeed to exec stage, cluster=%s, flowInstanceId=%d, stageId=%d, stageName=%s", flowDetail.ClusterName, flowInstanceId, stage.ID, stage.Name)
			return
		}

		// 非预期状态，结束工单
		err = fmt.Errorf("unexpected status %s of stage %d", stage.Status, stage.ID)
		return
	}

	// 全部阶段执行完毕，结束工单（一般也走不到这
	err = errs.Success
	logger.Info("succeed to deploy, cluster=%s, flowInstanceId=%d", flowDetail.ClusterName, flowInstanceId)
}
