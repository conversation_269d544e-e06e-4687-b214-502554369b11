package autoflow

import (
	"testing"

	"github.com/jarcoal/httpmock"

	"dt-common/omodel"
	"redis-xweb/env"
)

// 单测：Flow自动执行 - 代理扩缩容
func TestProxyScaling(t *testing.T) {
	env.Mock(t, "../../config/config.yaml")
	objC := env.MockCluster("proxy_scaling")

	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		clusterName string
		paramStr    string
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, *TaskArgs)
	}{
		{
			name: "unmarshal error",
			args: args{
				clusterName: objC.Name,
				paramStr:    "",
			},
			wantErr: true,
		},
		{
			name: "same proxy num",
			args: args{
				clusterName: objC.Name,
				paramStr:    `{"proxyNum": 4}`,
			},
			wantErr: true,
		},
		{
			name: "success",
			args: args{
				clusterName: objC.Name,
				paramStr:    `{"proxyNum": 2}`,
			},
			wantErr: false,
			expect: func(t *testing.T, args *TaskArgs) {
				if args.Name != "Proxy缩容" {
					t.Errorf("expect name Proxy缩容, but got %v", args.Name)
				}
				if args.Stages[1].Type != omodel.STAGE_TYPE_PROXY_REMOVE {
					t.Errorf("expect stage1.type %s, but got %v", omodel.STAGE_TYPE_PROXY_REMOVE, args.Stages[1].Type)
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			args, err := ProxyScaling(tt.args.clusterName, tt.args.paramStr)
			if (err != nil) != tt.wantErr {
				t.Errorf("ProxyScaling() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, args)
			}
		})
	}
}
