package whitelist

import (
	"fmt"
	"strings"

	"dt-common/noah"
	"dt-common/omodel"
	"redis-cmanager/library/renderer"
)

// 检查业务BNS白名单的格式、BNS合法性、权限是否错误
// 格式：xxxxx.xxxx r/rw
func checkSpecBNS(action string, bnsList []string) error {
	for _, bns := range bnsList {
		if strings.ContainsAny(bns, " ") {
			return fmt.Errorf("wrong format of spec bns %s", bns)
		}

		if action == "add" {
			_, err := noah.GetInstancesByBns(bns)
			if err != nil {
				return fmt.Errorf("failed to check spec bns %s, error=(%v)", bns, err)
			}
		}
	}
	return nil
}

// 合并spec白名单数组
func mergeWhitelist(action string, oldList, newList []string) ([]string, error) {
	bnsMap := make(map[string]int)
	for _, bns := range oldList {
		bnsMap[bns] = 1
	}

	for _, bns := range newList {
		switch action {
		case "add":
			if _, exist := bnsMap[bns]; !exist {
				bnsMap[bns] = 1
			}
		case "del":
			delete(bnsMap, bns)
		default:
			return nil, fmt.Errorf("undefined action, it should be one of add or del")
		}
	}

	whitelists := make([]string, len(bnsMap))
	i := 0
	for k := range bnsMap {
		whitelists[i] = k
		i += 1
	}

	return whitelists, nil
}

// 更新集群组件白名单
// 用于场景：实例互信
func UpdateSpecWhitelist(stageId int64, clusterName string, action string, bnsList []string) error {
	// 检查BNS白名单正确性
	err := checkSpecBNS(action, bnsList)
	if err != nil {
		omodel.StageAppendErrorLog(stageId, "%v", err)
		return err
	}

	// 查库
	deployment, err := renderer.GetDeploymentFromDB(clusterName)
	if err != nil {
		omodel.StageAppendErrorLog(stageId, "%v", err)
		return err
	}

	// 合并
	whitelists, err := mergeWhitelist(action, deployment.Spec.ClusterWhitelist, bnsList)
	if err != nil {
		omodel.StageAppendErrorLog(stageId, "%v", err)
		return err
	}
	deployment.Spec.ClusterWhitelist = whitelists

	// 写库
	err = renderer.SaveNewDeployment(deployment)
	if err != nil {
		omodel.StageAppendErrorLog(stageId, "%v", err)
		return err
	}

	// 主动更新3个组件
	err = renderer.EnsureWhitelists(deployment)
	if err != nil {
		omodel.StageAppendErrorLog(stageId, "%v", err)
		return err
	}
	omodel.StageAppendInfoLog(stageId, "spec whitelist has been updated")

	return nil
}
