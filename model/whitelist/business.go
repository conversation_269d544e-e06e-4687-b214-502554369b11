package whitelist

import (
	"fmt"
	"net"
	"strings"

	"dt-common/noah"
	"dt-common/omodel"
	"redis-cmanager/library/ragent"
	"redis-cmanager/library/renderer"
	"redis-cmanager/library/renderer/common"
	"redis-cmanager/library/renderer/lib"
)

// 检查业务BNS白名单的格式、BNS合法性、权限是否错误
// 格式：xxxxx.xxxx r/rw
func checkBNSWhiteList(action string, input []string) error {
	for i := 0; i < len(input); i++ {
		parts := strings.Split(input[i], " ")
		if len(parts) != 2 {
			return fmt.Errorf("bns whitelist format error=(%v)", input[i])
		}
		if action == "add" {
			_, err := noah.GetInstancesByBns(parts[0])
			if err != nil {
				return fmt.Errorf("bns whitelist address error=(%v)", input[i])
			}
		}
		if parts[1] != "r" && parts[1] != "rw" {
			return fmt.Errorf("bns whitelist permission error=(%v)", input[i])
		}
	}
	return nil
}

// 检查业务IP白名单的格式、IP合法性、权限是否错误
// 格式：xx.xx.xx.xxx r/rw
func checkIPWhiteList(input []string) error {
	result := make(map[string]string)
	for i := 0; i < len(input); i++ {
		parts := strings.Split(input[i], " ")
		if len(parts) != 2 {
			return fmt.Errorf("ip whitelist format error=(%v)", input[i])
		}
		if net.ParseIP(parts[0]) == nil {
			return fmt.Errorf("ip whitelist address error=(%v)", input[i])
		}
		if parts[1] != "r" && parts[1] != "rw" {
			return fmt.Errorf("ip whitelist permission error=(%v)", input[i])
		}
		result[parts[0]] = parts[1]
	}
	return nil
}

// 更新业务白名单，仅对proxy变更
func UpdateProxyWhitelist(stageId int64, clusterName string, action string, bnsList []string, ipList []string) error {
	// 检查BNS白名单正确性
	err := checkBNSWhiteList(action, bnsList)
	if err != nil {
		omodel.StageAppendErrorLog(stageId, "failed to check bns whitelist, error=(%v)", err)
		return err
	}

	// 检查IP白名单的格式
	err = checkIPWhiteList(ipList)
	if err != nil {
		omodel.StageAppendErrorLog(stageId, "failed to check ip whitelist, error=(%v)", err)
		return err
	}

	// 获取集群proxy port
	deployment, err := renderer.GetDeploymentFromDB(clusterName)
	if err != nil {
		omodel.StageAppendErrorLog(stageId, "failed to get cluster %s deployment, error=(%v)", clusterName, err)
		return err
	}

	// 获取集群proxy pod列表，污点pod也更新上，避免出现解除污点后丢失白名单的情况
	podList, err := lib.ListPods(clusterName, common.COMPONENT_ROUTER, true)
	if err != nil {
		omodel.StageAppendErrorLog(stageId, "failed to list cluster %s's all proxy pods, error=(%v)", clusterName, err)
		return err
	}
	omodel.StageAppendInfoLog(stageId, "succeed to query proxy pods, proceed to call redis-agent to update whitelist")

	// 遍历proxy实例更新白名单
	for _, item := range podList {
		err := ragent.UpdateWhitelist(item.PodIp, &ragent.UpdateParams{
			Port:    deployment.Spec.Router.Port,
			Action:  action,
			BnsList: bnsList,
			IpList:  ipList,
		})
		if err != nil {
			omodel.StageAppendWarnLog(stageId, "failed to call redis-agent to %s proxy %s:%d whitelist, error=(%v)", action, item.PodIp, deployment.Spec.Router.Port, err)
			return err
		}
		omodel.StageAppendInfoLog(stageId, "succeed to %s proxy %s:%d whitelist,", action, item.PodIp, deployment.Spec.Router.Port)
	}
	omodel.StageAppendInfoLog(stageId, "all proxies are updated")

	return nil
}
