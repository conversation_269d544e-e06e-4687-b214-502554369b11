package whitelist

import (
	"context"
	"regexp"
	"strings"
	"testing"

	"entgo.io/ent/dialect/sql"
	"github.com/jarcoal/httpmock"

	"dt-common/ent/deployment"
	"dt-common/mysql"
	"dt-common/omodel"
	"redis-cmanager/env"
)

// 单侧：bns白名单校验
func Test_checkSpecBNS(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		action       string
		bnsWhitelist []string
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test1: wrong format",
			args: args{
				action: "del",
				bnsWhitelist: []string{
					"blind-kj-zk.siod-kafka rw",
				},
			},
			wantErr: true,
		},
		{
			name: "test3: get instances failed",
			args: args{
				action: "add",
				bnsWhitelist: []string{
					"blind-kj-broker.siod-daiqi",
				},
			},
			wantErr: true,
		},
		{
			name: "test4: success",
			before: func() {

				httpmock.RegisterResponder("GET", "http://localhost:1793/apptreeNew/v1/products/siod-kafka/apps/blind-kj-broker/instances?pageNo=1&pageSize=200&disable=0",
					// httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/apptreeNew/v1/products/siod-kafka/apps/blind-kj-broker/instances?pageNo=1&pageSize=200&disable=0`),
					httpmock.NewStringResponder(200, `{"success": true, "message": "OK", "page": {"totalCount":1}, "data": [
						{"name": "0.redis-cmanager.siod-kafka","hostName": "kafka0002.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hba"},"disable": false,"instanceType": "host","podId": null,"containerList": []}, 
						{"name": "1.redis-cmanager.siod-kafka","hostName": "kafka0000.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hbb"},"disable": false,"instanceType": "host","podId": null,"containerList": []}, 
						{"name": "2.redis-cmanager.siod-kafka","hostName": "kafka0051.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hba"},"disable": false,"instanceType": "host","podId": null,"containerList": []},
						{"name": "3.redis-cmanager.siod-kafka","hostName": "kafka0052.kj01.bddx.dxm-int.com","ip": "************","status": 0,"statusTime": "2024-05-15 15:49:38","portInfo": {"main": 8811},"deployInfo": {"deployPath": "/home/<USER>/redis-cmanager"},"runUser": "redis","service": null,"saveOrUpdateSyncTag": null,"tags": {"idc": "hbb"},"disable": false,"instanceType": "host","podId": null,"containerList": []}
					]}`),
				)
			},
			args: args{
				action: "add",
				bnsWhitelist: []string{
					"blind-kj-broker.siod-kafka",
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			if err := checkSpecBNS(tt.args.action, tt.args.bnsWhitelist); (err != nil) != tt.wantErr {
				t.Errorf("checkSpecBNS() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

func Test_mergeWhitelist(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")
	httpmock.Activate()
	defer httpmock.DeactivateAndReset()

	type args struct {
		action  string
		oldList []string
		newList []string
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T, []string)
	}{
		{
			name: "test1: wrong action",
			args: args{
				action:  "sss",
				oldList: []string{"blind-kj-zk.siod-kafka"},
				newList: []string{"blind-kj-zk.siod-kafka"},
			},
			wantErr: true,
		},
		{
			name: "test2: add",
			args: args{
				action:  "add",
				oldList: []string{"blind-kj-zk.siod-kafka"},
				newList: []string{"blind-kj-zk.siod-kafka", "blind-kj-broker.siod-kafka"},
			},
			wantErr: false,
			expect: func(t *testing.T, whitelists []string) {
				if len(whitelists) != 2 {
					t.Errorf("expect 2 whitelists, but got %d", len(whitelists))
				}
			},
		},
		{
			name: "test3: del",
			args: args{
				action:  "del",
				oldList: []string{"blind-kj-zk.siod-kafka", "jiayiming.unit"},
				newList: []string{"blind-kj-zk.siod-kafka", "blind-kj-broker.siod-kafka"},
			},
			wantErr: false,
			expect: func(t *testing.T, whitelists []string) {
				if len(whitelists) != 1 {
					t.Errorf("expect 1 whitelists, but got %d", len(whitelists))
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}
			whitelists, err := mergeWhitelist(tt.args.action, tt.args.oldList, tt.args.newList)
			if (err != nil) != tt.wantErr {
				t.Errorf("mergeWhitelist() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, whitelists)
			}
		})
	}
}

// 单测：更新业务白名单
func TestUpdateSpecWhitelist(t *testing.T) {
	env.Mock(t, "../../../config/config.yaml")

	objC := env.MockCluster("spec_test")
	_, objS := env.MockTaskWithStage(objC)

	type args struct {
		stageId     int64
		clusterName string
		action      string
		bnsList     []string
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(*testing.T)
	}{
		{
			name: "test1: bns check failed",
			args: args{
				stageId:     objS.ID,
				clusterName: objS.ClusterName,
				action:      "add",
				bnsList:     []string{"jiayiming.bns rw"},
			},
			wantErr: true,
		},
		{
			name: "test2: get deployment from db failed",
			before: func() {
				httpmock.RegisterRegexpResponder("GET", regexp.MustCompile(`://[\w\W]+/apptreeNew/[\w\W]+`),
					httpmock.NewStringResponder(200, "{\"success\": true, \"message\": \"\", \"page\":{\"totalCount\":0}, \"data\": []}"))
			},
			args: args{
				stageId:     objS.ID,
				clusterName: objS.ClusterName,
				action:      "add",
				bnsList:     []string{"jiayiming.bns"},
			},
			wantErr: true,
		},
		{
			name: "test3: merge failed",
			before: func() {
				db, _ := mysql.Database()
				db.Deployment.Create().SetClusterID(objC.ID).SetClusterName(objC.Name).
					SetAlias(strings.ReplaceAll(objC.Name, "_", "-")).SetSpec(`{"clusterWhitelist":["jiayiming.unit"]}`).
					SetInspectionMode(omodel.MODE_FULL_CARE).SetInspectionResult(`{}`).
					SetVersion(1).SetEnabledAz("hba,hbb").
					Exec(context.Background())
			},
			args: args{
				stageId:     objS.ID,
				clusterName: objS.ClusterName,
				action:      "sss",
				bnsList:     []string{"jiayiming.bns"},
			},
			wantErr: true,
		},
		{
			name: "test4: succeed to save, failed to exec",
			args: args{
				stageId:     objS.ID,
				clusterName: objS.ClusterName,
				action:      "add",
				bnsList:     []string{"jiayiming.bns"},
			},
			wantErr: true,
			expect: func(t *testing.T) {
				db, _ := mysql.Database()
				d, _ := db.Deployment.Query().Where(deployment.ClusterID(objC.ID)).Order(deployment.ByVersion(sql.OrderDesc())).First(context.Background())
				if !strings.Contains(d.Spec, "jiayiming.bns") {
					t.Errorf("expect spec contains jiayiming.bns, but got %s", d.Spec)
				}
			},
		},
	}
	for _, tt := range tests {
		if tt.before != nil {
			tt.before()
		}
		t.Run(tt.name, func(t *testing.T) {
			err := UpdateSpecWhitelist(tt.args.stageId, tt.args.clusterName, tt.args.action, tt.args.bnsList)
			if (err != nil) != tt.wantErr {
				t.Errorf("UpdateSpecWhitelist() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}
