package model

import (
	"fmt"
	"strings"

	"dt-common/user-center/authc"
)

const (
	// 集群权限
	ROLE_USER    string = "rd-engineer" // 集群普通用户
	ROLE_MANAGER string = "rd-manager"  // 集群管理员
	ROLE_OWNER   string = "owner"       // 集群负责人

	// 审批流
	ROLE_FLOW_ENGINEER string = "redis-engineer" // Redis管理员
	ROLE_FLOW_MANAGER  string = "redis-manager"  // Redis经理

	// 页面可见性
	ROLE_LOCAL_MANAGER string = "redis-manager"
)

const (
	APPLY_STATUS_AUTHORIZED   string = "authorized"
	APPLY_STATUS_UNAUTHORIZED string = "unauthorized"
)

// 认证中心Redis角色类型
const (
	ROLE_TYPE_REDIS string = "redis"
	ROLE_TYPE_FLOW  string = "flow"
	ROLE_TYPE_LOCAL string = "local"
)

// 检查用户是否为Redis管理员.
// Redis管理员以local类型下 redis-manager 内的用户为准
func IsRedisSuperAdmin(username string) (bool, error) {
	users, err := authc.GetUsersByRole(ROLE_LOCAL_MANAGER, ROLE_TYPE_LOCAL)
	if err != nil {
		return false, err
	}
	for _, user := range users {
		if user == username {
			return true, nil
		}
	}
	return false, nil
}

// 检查用户有无集群读权限，有与集群相关的任一角色即可读
func HasReadPermission(user *authc.User, clusterName string) (bool, error) {
	// 先判断是不是REDIS的管理员
	isAdmin, err := IsRedisSuperAdmin(user.Name)
	if err != nil {
		return false, err
	}
	if isAdmin {
		return true, nil
	}

	// 再判断是不是集群用户
	roles, err := authc.GetRoleByUserID(user.ID, ROLE_TYPE_REDIS)
	if err != nil {
		return false, err
	}
	rolePrefix := fmt.Sprintf("%s__", clusterName)
	for _, role := range *roles {
		if strings.HasPrefix(role.Name, rolePrefix) {
			return true, nil
		}
	}

	return false, nil
}

// 检查用户有无集群写权限
// Admin, Manager
func HasWritePermission(user *authc.User, clusterName string) (bool, error) {
	// 先判断是不是REDIS的管理员
	isAdmin, err := IsRedisSuperAdmin(user.Name)
	if err != nil {
		return false, err
	}
	if isAdmin {
		return true, nil
	}

	roles, err := authc.GetRoleByUserID(user.ID, ROLE_TYPE_REDIS)
	if err != nil {
		return false, err
	}
	managerRole := fmt.Sprintf("%s__%s", clusterName, ROLE_MANAGER)
	for _, role := range *roles {
		if role.Name == managerRole {
			return true, nil
		}
	}

	return false, nil
}
