package model

import (
	"fmt"

	"dt-common/logger"
	"redis-xweb/config"
)

var (
	areaMap map[string]string
)

type Area struct {
	Name  string `json:"name"`
	<PERSON><PERSON> string `json:"alias"`
}

// 初始化区域配置
func initAreaMap() error {
	err := config.Get("area", &areaMap)
	if err != nil {
		logger.Warn("area map init failed")
		return fmt.Errorf("area map init failed")
	}
	return nil
}

// 获取部署区域
func GetAreaList() (*[]Area, error) {
	if areaMap == nil {
		err := initAreaMap()
		if err != nil {
			return nil, err
		}
	}

	result := []Area{}
	for name := range areaMap {
		result = append(result, Area{
			Name:  name,
			Alias: areaMap[name],
		})
	}

	return &result, nil
}

// 获取区域别名
func GetAreaName(name string) (alias string, err error) {
	if areaMap == nil {
		err := initAreaMap()
		if err != nil {
			return "", err
		}
	}

	var exists bool
	for i := 0; i < 2; i++ {
		if alias, exists = areaMap[name]; exists {
			break
		}

		if i == 0 {
			err = initAreaMap()
			if err != nil {
				break
			}
		} else {
			err = fmt.Errorf("can't find area named %s", name)
		}
	}

	return
}

// 判断区域是否存在
func IsAreaExists(name string) bool {
	if areaMap == nil {
		err := initAreaMap()
		if err != nil {
			return false
		}
	}

	_, exists := areaMap[name]
	return exists
}
