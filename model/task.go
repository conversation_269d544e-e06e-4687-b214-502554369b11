package model

const (
	TASK_TYPE_DEPLOY           string = "deploy"
	TASK_TYPE_WHITELIST        string = "whitelist"
	TASK_TYPE_DOCKER_MIGRATION string = "dockerMigration"
)

const (
	STAGE_TYPE_DEPLOY       string = "deploy"
	STAGE_TYPE_INIT_MONITOR string = "initMonitor"
	STAGE_TYPE_WHITELIST    string = "whitelist"
)

type StageStatus string

const (
	STAGE_STATUS_NORMAL   StageStatus = "normal"   // 初始状态
	STAGE_STATUS_ROLLBACK StageStatus = "rollback" // 已回滚
	STAGE_STATUS_RUNNING  StageStatus = "running"  // 运行中
	STAGE_STATUS_ERROR    StageStatus = "error"    // 执行失败
	STAGE_STATUS_DONE     StageStatus = "done"     // 执行结束
)

// 用于格式化参数
type Stage struct {
	Type      string
	Name      string
	Parameter map[string]any
}
