package worker

import (
	"fmt"
	"os"
	"strconv"
	"testing"

	"dt-common/utils"
	"redis-agent/env"
)

// Fake ExecCommand
func TestExecCommandHelper(t *testing.T) {
	if os.Getenv("GO_WANT_HELPER_PROCESS") != "1" {
		return
	}

	// println("Mocked stdout:", os.Getenv("STDOUT"))
	fmt.Fprint(os.Stdout, os.Getenv("STDOUT"))
	i, _ := strconv.Atoi(os.Getenv("EXIT_STATUS"))
	os.Exit(i)
}

func TestIsProxyAlive(t *testing.T) {
	env.Init("../../mock/config/config.yaml")

	utils.ActivateExecMock()
	defer utils.DeactivateExecMock()

	type args struct {
		port int
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(t *testing.T, alive bool)
	}{
		{
			name: "test1",
			before: func() {
				utils.ExpectExec(0, "")
			},
			args: args{
				port: 8001,
			},
			wantErr: false,
			expect: func(t *testing.T, alive bool) {
				if !alive {
					t.Errorf("IsProxyAlive() expect 8001 alive but got %v", alive)
				}
			},
		},
	}
	for _, tt := range tests {
		if tt.before != nil {
			tt.before()
		}
		t.Run(tt.name, func(t *testing.T) {
			alive := IsProxyAlive(tt.args.port)
			// if (err != nil) != tt.wantErr {
			// 	t.Errorf("IsProxyAlive() error = %v, wantErr %v", err, tt.wantErr)
			// }
			if tt.expect != nil {
				tt.expect(t, alive)
			}
		})
	}
}

func TestIsRedisAlive(t *testing.T) {
	env.Init("../../mock/config/config.yaml")

	utils.ActivateExecMock()
	defer utils.DeactivateExecMock()

	type args struct {
		port int
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(t *testing.T, alive bool)
	}{
		{
			name: "test1",
			before: func() {
				utils.ExpectExec(0, "")
			},
			args: args{
				port: 7000,
			},
			wantErr: false,
			expect: func(t *testing.T, alive bool) {
				if !alive {
					t.Errorf("IsRedisAlive() expect 7000 alive but got %v", alive)
				}
			},
		},
	}
	for _, tt := range tests {
		if tt.before != nil {
			tt.before()
		}
		t.Run(tt.name, func(t *testing.T) {
			alive := IsRedisAlive(tt.args.port)
			// if (err != nil) != tt.wantErr {
			// 	t.Errorf("IsRedisAlive() error = %v, wantErr %v", err, tt.wantErr)
			// }
			if tt.expect != nil {
				tt.expect(t, alive)
			}
		})
	}
}

func TestIsSentinelAlive(t *testing.T) {
	env.Init("../../mock/config/config.yaml")

	utils.ActivateExecMock()
	defer utils.DeactivateExecMock()

	type args struct {
		port int
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(t *testing.T, alive bool)
	}{
		{
			name: "test1",
			before: func() {
				utils.ExpectExec(0, "")
			},
			args: args{
				port: 9001,
			},
			wantErr: false,
			expect: func(t *testing.T, alive bool) {
				if !alive {
					t.Errorf("IsSentinelAlive() expect 9001 alive but got %v", alive)
				}
			},
		},
	}
	for _, tt := range tests {
		if tt.before != nil {
			tt.before()
		}
		t.Run(tt.name, func(t *testing.T) {
			alive := IsSentinelAlive(tt.args.port)
			// if (err != nil) != tt.wantErr {
			// 	t.Errorf("IsSentinelAlive() error = %v, wantErr %v", err, tt.wantErr)
			// }
			if tt.expect != nil {
				tt.expect(t, alive)
			}
		})
	}
}

// =============================================
//   			  控制函数单测
// =============================================

func TestProxyControl(t *testing.T) {
	env.Init("../../mock/config/config.yaml")

	utils.ActivateExecMock()
	defer utils.DeactivateExecMock()

	type args struct {
		port int
		opt  string
	}
	tests := []struct {
		name   string
		before func()
		args
		wantErr bool
		expect  func(t *testing.T)
	}{
		{
			name: "test1",
			before: func() {
				utils.ExpectExec(-1, "")
			},
			args: args{
				port: 8001,
				opt:  "stop",
			},
			wantErr: true,
		},
		{
			name: "test2",
			before: func() {
				utils.ExpectExec(0, "")
				utils.ExpectExec(0, "")
			},
			args: args{
				port: 8001,
				opt:  "start",
			},
			wantErr: false,
		},
		{
			name: "test3",
			before: func() {
				utils.ExpectExec(0, "")
				utils.ExpectExec(-1, "")
			},
			args: args{
				port: 8002,
				opt:  "stop",
			},
			wantErr: false,
		},
		{
			name: "test4",
			before: func() {
				utils.ExpectExec(-1, "")
			},
			args: args{
				port: 8002,
				opt:  "start",
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		if tt.before != nil {
			tt.before()
		}
		t.Run(tt.name, func(t *testing.T) {
			err := ProxyControl(tt.args.port, tt.args.opt)
			if (err != nil) != tt.wantErr {
				t.Errorf("ProxyControl() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

func TestRedisControl(t *testing.T) {
	env.Init("../../mock/config/config.yaml")

	utils.ActivateExecMock()
	defer utils.DeactivateExecMock()

	type args struct {
		port int
		opt  string
	}
	tests := []struct {
		name   string
		before func()
		args
		wantErr bool
		expect  func(t *testing.T)
	}{
		{
			name: "test1",
			before: func() {
				utils.ExpectExec(-1, "")
			},
			args: args{
				port: 7001,
				opt:  "start",
			},
			wantErr: true,
		},
		{
			name: "test2",
			before: func() {
				utils.ExpectExec(0, "")
				utils.ExpectExec(0, "")
			},
			args: args{
				port: 7000,
				opt:  "start",
			},
			wantErr: false,
		},
		{
			name: "test3",
			before: func() {
				utils.ExpectExec(-1, "")
			},
			args: args{
				port: 7000,
				opt:  "stop",
			},
			wantErr: true,
		},
		{
			name: "test1",
			before: func() {
				utils.ExpectExec(0, "")
				utils.ExpectExec(-1, "")
			},
			args: args{
				port: 7001,
				opt:  "stop",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		if tt.before != nil {
			tt.before()
		}
		t.Run(tt.name, func(t *testing.T) {
			err := RedisControl(tt.args.port, tt.args.opt)
			if (err != nil) != tt.wantErr {
				t.Errorf("RedisControl() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}

func TestSentinelControl(t *testing.T) {
	env.Init("../../mock/config/config.yaml")

	utils.ActivateExecMock()
	defer utils.DeactivateExecMock()

	type args struct {
		port int
		opt  string
	}
	tests := []struct {
		name   string
		before func()
		args
		wantErr bool
		expect  func(t *testing.T)
	}{
		{
			name: "test1",
			before: func() {
				utils.ExpectExec(-1, "")
			},
			args: args{
				port: 9001,
				opt:  "stop",
			},
			wantErr: true,
		},
		{
			name: "test2",
			before: func() {
				utils.ExpectExec(0, "")
				utils.ExpectExec(0, "")
			},
			args: args{
				port: 9001,
				opt:  "start",
			},
			wantErr: false,
		},
		{
			name: "test3",
			before: func() {
				utils.ExpectExec(0, "")
				utils.ExpectExec(-1, "")
			},
			args: args{
				port: 9002,
				opt:  "stop",
			},
			wantErr: false,
		},
		{
			name: "test4",
			before: func() {
				utils.ExpectExec(0, "")
				utils.ExpectExec(-1, "")
			},
			args: args{
				port: 9002,
				opt:  "start",
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		if tt.before != nil {
			tt.before()
		}
		t.Run(tt.name, func(t *testing.T) {
			err := SentinelControl(tt.args.port, tt.args.opt)
			if (err != nil) != tt.wantErr {
				t.Errorf("SentinelControl() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t)
			}
		})
	}
}
