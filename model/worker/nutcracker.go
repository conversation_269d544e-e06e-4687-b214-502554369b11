package worker

import (
	"fmt"
	"os"
	"strings"

	"gopkg.in/yaml.v2"

	"redis-agent/config"
)

type Nutcracker struct {
	Listen                 string   `yaml:"listen"`
	Hash                   string   `yaml:"hash"`
	Distribution           string   `yaml:"distribution"`
	Preconnect             bool     `yaml:"preconnect"`
	AutoEjectHosts         bool     `yaml:"auto_eject_hosts"`
	Redis                  bool     `yaml:"redis"`
	LogAutoRemove          bool     `yaml:"log_auto_remove"`
	LogRemovePeriod        int      `yaml:"log_remove_period"`
	LogPreservedDay        int      `yaml:"log_preserved_day"`
	Backlog                int      `yaml:"backlog"`
	ClientConnections      int      `yaml:"client_connections"`
	ServerConnections      int      `yaml:"server_connections"`
	ServerRetryTimeout     int      `yaml:"server_retry_timeout"`
	ServerFailureLimit     int      `yaml:"server_failure_limit"`
	ClientKeepalive        int      `yaml:"client_keepalive"`
	SlowlogLogSlowerThan   int      `yaml:"slowlog_log_slower_than"`
	PrintIntervalEveryCmds int      `yaml:"print_interval_every_cmds"`
	ClientAuth             string   `yaml:"client_auth"`
	Servers                []string `yaml:"servers"`
}

// 读取nutcracker.yaml文件
func readNutcrackerYaml(path string) (string, *Nutcracker, error) {
	// 读取yaml配置文件
	content, err := os.ReadFile(path)
	if err != nil {
		return "", nil, err
	}

	// 反序列化
	pools := make(map[string]*Nutcracker)
	err = yaml.Unmarshal(content, &pools)
	if err != nil {
		return "", nil, err
	}

	if len(pools) != 1 {
		return "", nil, fmt.Errorf("proxy has more than one Redis pool")
	}

	var poolName string
	var configs *Nutcracker
	for k, v := range pools {
		poolName = k
		configs = v
		break
	}

	return poolName, configs, nil
}

// 获取proxy密码, 若不是proxy返回空字符串
func GetPassword(port int) (string, error) {
	// 8000 - 9000 为 proxy的端口范围，非proxy不需要密码
	if port < 8000 || port >= 9000 {
		return "", nil
	}

	// proxy配置文件路径
	root, err := config.GetString("env/proxy_root")
	if err != nil {
		return "", err
	}
	root = strings.ReplaceAll(root, "${port}", fmt.Sprintf("_%d", port))
	confPath := fmt.Sprintf("%s/conf/nutcracker.yml", root)

	_, cfgs, err := readNutcrackerYaml(confPath)
	if err != nil {
		return "", err
	}

	return cfgs.ClientAuth, nil
}

// 获取Proxy配置文件中的poolname
func GetPoolName(port int) (string, error) {
	// 8000 - 9000 为 proxy的端口范围
	if port < 8000 || port >= 9000 {
		return "", fmt.Errorf("port %d is not a proxy", port)
	}

	// proxy配置文件路径
	root, err := config.GetString("env/proxy_root")
	if err != nil {
		return "", err
	}
	root = strings.ReplaceAll(root, "${port}", fmt.Sprintf("_%d", port))
	confPath := fmt.Sprintf("%s/conf/nutcracker.yml", root)

	name, _, err := readNutcrackerYaml(confPath)
	if err != nil {
		return "", err
	}

	return name, nil
}

// 获取proxy拓扑
func GetProxyTopo(port int) ([]string, error) {
	// 8000 - 9000 为 proxy的端口范围
	if port < 8000 || port >= 9000 {
		return nil, fmt.Errorf("port %d is not a proxy", port)
	}

	// proxy配置文件路径
	root, err := config.GetString("env/proxy_root")
	if err != nil {
		return nil, err
	}
	root = strings.ReplaceAll(root, "${port}", fmt.Sprintf("_%d", port))
	confPath := fmt.Sprintf("%s/conf/nutcracker.yml", root)

	_, cfgs, err := readNutcrackerYaml(confPath)
	if err != nil {
		return nil, err
	}

	result := make([]string, len(cfgs.Servers))
	for i, v := range cfgs.Servers {
		s := strings.IndexAny(v, " ") + 1
		e := strings.LastIndex(v, ":")

		if e < s {
			return nil, fmt.Errorf("proxy %d got wrong server %s", port, v)
		}
		result[i] = v[s:e]
	}

	return result, nil
}
