package worker

import (
	"testing"

	"redis-agent/env"
)

func TestGetPassword(t *testing.T) {
	env.Init("../../mock/config/config.yaml")

	type args struct {
		port int
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(t *testing.T, password string)
	}{
		{
			name:   "test1",
			before: func() {},
			args: args{
				port: 6379,
			},
			wantErr: false,
			expect: func(t *testing.T, password string) {
				if password != "" {
					t.<PERSON>rro<PERSON>("GetPassword() port 6379 should get nil password but got %s", password)
				}
			},
		},
		{
			name:   "test2",
			before: func() {},
			args: args{
				port: 9191,
			},
			wantErr: false,
			expect: func(t *testing.T, password string) {
				if password != "" {
					t.Errorf("GetPassword() port 9191 should get nil password but got %s", password)
				}
			},
		},
		{
			name:   "test3",
			before: func() {},
			args: args{
				port: 8001,
			},
			wantErr: false,
			expect: func(t *testing.T, password string) {
				if password != "@$^*t0test" {
					t.<PERSON><PERSON>("GetPassword() port 8001 should get nil password but got %s", password)
				}
			},
		},
		{
			name:   "test4",
			before: func() {},
			args: args{
				port: 8002,
			},
			wantErr: false,
			expect: func(t *testing.T, password string) {
				if password != "" {
					t.Errorf("GetPassword() port 8002 should get nil password but got %s", password)
				}
			},
		},
	}
	for _, tt := range tests {
		if tt.before != nil {
			tt.before()
		}
		t.Run(tt.name, func(t *testing.T) {
			pwd, err := GetPassword(tt.args.port)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetPassword() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, pwd)
			}
		})
	}
}

func TestGetPoolName(t *testing.T) {
	env.Init("../../mock/config/config.yaml")

	type args struct {
		port int
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(t *testing.T, poolname string)
	}{
		{
			name:   "test1",
			before: func() {},
			args: args{
				port: 6379,
			},
			wantErr: true,
			expect:  func(t *testing.T, poolname string) {},
		},
		{
			name:   "test2",
			before: func() {},
			args: args{
				port: 9191,
			},
			wantErr: true,
			expect:  func(t *testing.T, poolname string) {},
		},
		{
			name:   "test3",
			before: func() {},
			args: args{
				port: 8001,
			},
			wantErr: false,
			expect: func(t *testing.T, poolname string) {
				if poolname != "r3_test_3" {
					t.Errorf("GetPoolName() port 8001 should get pool name r3_test_3 but got %s", poolname)
				}
			},
		},
	}
	for _, tt := range tests {
		if tt.before != nil {
			tt.before()
		}
		t.Run(tt.name, func(t *testing.T) {
			pwd, err := GetPoolName(tt.args.port)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetPoolName() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, pwd)
			}
		})
	}
}

func TestGetProxyTopo(t *testing.T) {
	env.Init("../../mock/config/config.yaml")

	type args struct {
		port int
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(t *testing.T, servers []string)
	}{
		{
			name:   "test1",
			before: func() {},
			args: args{
				port: 6379,
			},
			wantErr: true,
		},
		{
			name:   "test2",
			before: func() {},
			args: args{
				port: 9191,
			},
			wantErr: true,
		},
		{
			name:   "test3",
			before: func() {},
			args: args{
				port: 8001,
			},
			wantErr: false,
			expect: func(t *testing.T, servers []string) {
				if servers[0] != "10.32.162.81:7000" {
					t.Errorf("GetProxyTopo() port 8001 should get server1 10.32.162.81:7000 but got %s", servers[0])
				}
				if servers[1] != "10.32.162.81:7001" {
					t.Errorf("GetProxyTopo() port 8001 should get server2 10.32.162.81:7001 but got %s", servers[1])
				}
			},
		},
	}
	for _, tt := range tests {
		if tt.before != nil {
			tt.before()
		}
		t.Run(tt.name, func(t *testing.T) {
			servers, err := GetProxyTopo(tt.args.port)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetProxyTopo() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, servers)
			}
		})
	}
}
