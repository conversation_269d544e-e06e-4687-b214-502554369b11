package worker

import (
	"strings"
	"testing"

	"redis-agent/env"
)

func TestGetRoot(t *testing.T) {
	env.Init("../../mock/config/config.yaml")

	type args struct {
		port int
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(t *testing.T, bnsW string)
	}{
		{
			name:   "test1",
			before: func() {},
			args: args{
				port: 6379,
			},
			wantErr: true,
		},
		{
			name:   "test2",
			before: func() {},
			args: args{
				port: 7000,
			},
			wantErr: false,
		},
		{
			name:   "test3",
			before: func() {},
			args: args{
				port: 7001,
			},
			wantErr: true,
		},
		{
			name:   "test4",
			before: func() {},
			args: args{
				port: 8001,
			},
			wantErr: false,
		},
		{
			name:   "9001",
			before: func() {},
			args: args{
				port: 9001,
			},
			wantErr: false,
		},
		{
			name:   "10032",
			before: func() {},
			args: args{
				port: 10032,
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		if tt.before != nil {
			tt.before()
		}
		t.Run(tt.name, func(t *testing.T) {
			root, err := GetRoot(tt.args.port)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetRoot() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, root)
			}
		})
	}
}

func TestGetWhitelistPath(t *testing.T) {
	env.Init("../../mock/config/config.yaml")

	type args struct {
		port int
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(t *testing.T, bnsW, ipW string)
	}{
		{
			name:   "test1",
			before: func() {},
			args: args{
				port: 6379,
			},
			wantErr: true,
		},
		{
			name:   "test2",
			before: func() {},
			args: args{
				port: 7000,
			},
			wantErr: false,
			expect: func(t *testing.T, bnsW, ipW string) {
				if !strings.Contains(bnsW, "redis_7000") {
					t.Errorf("GetWhitelistPath() port 7000 got %s", bnsW)
				}
			},
		},
		{
			name:   "test3",
			before: func() {},
			args: args{
				port: 8010,
			},
			wantErr: true,
		},
		{
			name:   "test4",
			before: func() {},
			args: args{
				port: 9001,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		if tt.before != nil {
			tt.before()
		}
		t.Run(tt.name, func(t *testing.T) {
			bnsW, ipW, err := GetWhitelistPath(tt.args.port)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetWhitelistPath() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, bnsW, ipW)
			}
		})
	}
}
