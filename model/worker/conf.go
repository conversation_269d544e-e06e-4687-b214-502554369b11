package worker

import (
	"fmt"
	"os"
	"strings"

	"dt-common/logger"
	"redis-agent/config"
)

// 获取实例根路径
func GetRoot(port int) (root string, err error) {
	switch true {
	// redis根目录
	case port >= 7000 && port <= 7999:
		root, err = config.GetString("env/redis_root")
		if err != nil {
			return "", err
		}
	// proxy根目录
	case port >= 8000 && port <= 8999:
		root, err = config.GetString("env/proxy_root")
		if err != nil {
			return "", err
		}
	// sentinel根目录
	case port >= 9000 && port <= 9999:
		root, err = config.GetString("env/sentinel_root")
		if err != nil {
			return "", err
		}
	default:
		return "", fmt.Errorf("can not get instance root, illegal port %d", port)
	}

	root = strings.ReplaceAll(root, "${port}", fmt.Sprintf("_%d", port))
	// 检查目录是否存在
	_, err = os.Stat(root)
	if err != nil && os.IsNotExist(err) {
		return "", fmt.Errorf("can not get instance root, %d not found", port)
	}

	return
}

// 获取白名单文件路径
func GetWhitelistPath(port int) (whitelistBns string, whitelistIp string, err error) {
	var folderPath string
	switch true {
	// redis根目录
	case port >= 7000 && port <= 7999:
		folderPath, err = config.GetString("env/redis_whitelist")
		if err != nil {
			return "", "", err
		}
	// proxy根目录
	case port >= 8000 && port <= 8999:
		folderPath, err = config.GetString("env/proxy_whitelist")
		if err != nil {
			return "", "", err
		}
	// sentinel根目录
	case port >= 9000 && port <= 9999:
		folderPath, err = config.GetString("env/sentinel_whitelist")
		if err != nil {
			return "", "", err
		}
	default:
		return "", "", fmt.Errorf("can not get whitelist path, illegal port %d", port)
	}

	folderPath = strings.ReplaceAll(folderPath, "${port}", fmt.Sprintf("_%d", port))
	// 检查目录是否存在
	_, err = os.Stat(folderPath)
	if err != nil && os.IsNotExist(err) {
		logger.Debug("os.stat error=(%v)", err)
		return "", "", fmt.Errorf("can not get whitelist path, %d not found", port)
	}

	whitelistBns = fmt.Sprintf("%s/whitelist.bns", folderPath)
	whitelistIp = fmt.Sprintf("%s/whitelist.ip", folderPath)

	return
}
