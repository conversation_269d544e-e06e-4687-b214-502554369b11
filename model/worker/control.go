package worker

import (
	"fmt"
	"strings"

	"dt-common/logger"
	"dt-common/utils"
	"redis-agent/config"
)

const (
	OperationStart   string = "start"
	OperationStop    string = "stop"
	OperationRestart string = "restart"
)

// 判断proxy是否存活
func IsProxyAlive(port int) bool {
	// 检查端口是否被占用、进程名
	cmd := fmt.Sprintf("netstat -nltp | grep nutcracke | grep 0.0.0.0:%d", port)
	_, err := utils.ExecCommand(&utils.Shell{
		Command: "bash",
		Args:    []string{"-c", cmd},
	})
	if err != nil {
		logger.Warn("failed to exec netstat cmd. port=%d, cmd=%s, error=(%v)", port, cmd, err)
		return false
	}

	return true
}

// Proxy控制函数
func ProxyControl(port int, opt string) error {
	// 从配置文件获取执行脚本路径
	script, err := config.GetString("env/proxy_script")
	if err != nil {
		logger.Error("missing config env/proxy_script")
		return err
	}
	// proxy脚本名中带着端口，需要再format一下
	script = strings.ReplaceAll(script, "${port}", fmt.Sprintf("_%d", port))

	// 执行脚本
	_, err = utils.ExecCommand(&utils.Shell{
		Command: script,
		Args:    []string{opt},
	})
	if err != nil {
		logger.Error("failed to %s proxy, port=%d, error=%v", opt, port, err)
		return err
	}

	// 判断是否操作成功
	alive := IsProxyAlive(port)
	if opt == OperationStop && alive {
		return fmt.Errorf("failed to stop proxy, port=%d", port)
	}
	if opt != OperationStop && !alive {
		return fmt.Errorf("failed to %s proxy, port=%d", opt, port)
	}
	return nil
}

// 判断redis是否存活
func IsRedisAlive(port int) bool {
	// 检查端口是否被占用、进程名
	cmd := fmt.Sprintf("netstat -nltp | grep redis-ser | grep 0.0.0.0:%d", port)
	_, err := utils.ExecCommand(&utils.Shell{
		Command: "bash",
		Args:    []string{"-c", cmd},
	})
	if err != nil {
		logger.Warn("failed to exec netstat cmd. port=%d, cmd=%s, error=(%v)", port, cmd, err)
		return false
	}

	return true
}

// redis控制函数
func RedisControl(port int, opt string) error {
	// 从配置文件获取执行脚本路径
	script, err := config.GetString("env/redis_script")
	if err != nil {
		logger.Error("missing config env/redis_script")
		return err
	}

	// 执行脚本
	_, err = utils.ExecCommand(&utils.Shell{
		Command: script,
		Args:    []string{opt, fmt.Sprintf("%d", port)},
	})
	if err != nil {
		logger.Error("failed to %s redis, port=%d, error=%v", opt, port, err)
		return err
	}

	// 判断是否操作成功
	alive := IsRedisAlive(port)
	if opt == OperationStop && alive {
		return fmt.Errorf("failed to stop redis, port=%d", port)
	}
	if opt != OperationStop && !alive {
		return fmt.Errorf("failed to %s redis, port=%d", opt, port)
	}
	return nil
}

// 判断sentinel是否存活
func IsSentinelAlive(port int) bool {
	// 检查端口是否被占用、进程名
	cmd := fmt.Sprintf("netstat -nltp | grep redis-sen | grep 0.0.0.0:%d", port)
	_, err := utils.ExecCommand(&utils.Shell{
		Command: "bash",
		Args:    []string{"-c", cmd},
	})
	if err != nil {
		logger.Warn("failed to exec netstat cmd. port=%d, cmd=%s, error=(%v)", port, cmd, err)
		return false
	}

	return true
}

// sentinel控制函数
func SentinelControl(port int, opt string) error {
	// 从配置文件获取执行脚本路径
	script, err := config.GetString("env/sentinel_script")
	if err != nil {
		logger.Error("missing config env/sentinel_script")
		return err
	}

	// 执行脚本
	_, err = utils.ExecCommand(&utils.Shell{
		Command: script,
		Args:    []string{opt, fmt.Sprintf("%d", port)},
	})
	if err != nil {
		logger.Error("failed to %s sentinel, port=%d, error=%v", opt, port, err)
		return err
	}

	// 判断是否操作成功
	alive := IsSentinelAlive(port)
	if opt == OperationStop && alive {
		return fmt.Errorf("failed to stop sentinel, port=%d", port)
	}
	if opt != OperationStop && !alive {
		return fmt.Errorf("failed to %s sentinel, port=%d", opt, port)
	}
	return nil
}
