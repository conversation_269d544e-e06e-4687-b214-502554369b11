package worker

import (
	"encoding/json"
	"fmt"

	"dt-common/logger"
	"dt-common/utils"
	"redis-agent/config"
)

type RedisServerMonitor struct {
	ServerEof         int `json:"server_eof"`         // "server_eof": 0,
	ServerErr         int `json:"server_err"`         // "server_err": 2,
	ServerTimedout    int `json:"server_timedout"`    // "server_timedout": 0,
	ServerConnections int `json:"server_connections"` // "server_connections": 1,
	Requests          int `json:"requests"`           // "requests": 1184363862,
	RequestBytes      int `json:"request_bytes"`      // "request_bytes": 218635379183,
	Responses         int `json:"responses"`          // "responses": 1184363845,
	ResponseBytes     int `json:"response_bytes"`     // "response_bytes": 7049772743,
	InQueue           int `json:"in_queue"`           // "in_queue": 0,
	InQueueBytes      int `json:"in_queue_bytes"`     // "in_queue_bytes": 0,
	OutQueue          int `json:"out_queue"`          // "out_queue": 0,
	OutQueueBytes     int `json:"out_queue_bytes"`    // "out_queue_bytes": 0,
	SlowCmdCount      int `json:"slow_cmd_count"`     // "slow_cmd_count": 1184363861
}

type PoolMonitor struct {
	ClientEOF                  int `json:"client_eof"`                    // "client_eof": 13434498
	ClientErr                  int `json:"client_err"`                    // "client_err": 10975110
	ClientConn                 int `json:"client_connections"`            // "client_connections": 81
	TotalQPS                   int `json:"total_qps"`                     // "total_qps": 56
	RealReadQPS                int `json:"real_read_qps"`                 // "real_read_qps": 0
	RealWriteQPS               int `json:"real_write_qps"`                // "real_write_qps": 56
	UnknownCmdQPS              int `json:"unknown_cmd_qps"`               // "unknown_cmd_qps": 0
	DelCmdQPS                  int `json:"del_cmd_qps"`                   // "del_cmd_qps": 0
	ExistsCmdQPS               int `json:"exists_cmd_qps"`                // "exists_cmd_qps": 0
	ExpireCmdQPS               int `json:"expire_cmd_qps"`                // "expire_cmd_qps": 0
	ExpireatCmdQPS             int `json:"expireat_cmd_qps"`              // "expireat_cmd_qps":0
	PexpireCmdQPS              int `json:"pexpire_cmd_qps"`               // "pexpire_cmd_qps":0
	PexpireatCmdQPS            int `json:"pexpireat_cmd_qps"`             // "pexpireat_cmd":0
	PersistCmdQPS              int `json:"persist_cmd_qps"`               // "persist_cmd_qps":0
	PttlCmdQPS                 int `json:"pttl_cmd_qps"`                  // "pttl_cmd_qps":0
	TtlCmdQPS                  int `json:"ttl_cmd_qps"`                   // "ttl_cmd_qps":0
	TypeCmdQPS                 int `json:"type_cmd_qps"`                  // "type_cmd_qps":0
	AppendCmdQPS               int `json:"append_cmd_qps"`                // "append_cmd_qps":0
	BitcountCmdQPS             int `json:"bitcount_cmd_qps"`              // "bitcount_cmd_qps":0
	DecrCmdQps                 int `json:"decr_cmd_qps"`                  // "decr_cmd_qps": 0,
	DecrbyCmdQps               int `json:"decrby_cmd_qps"`                // "decrby_cmd_qps": 0,
	DumpCmdQps                 int `json:"dump_cmd_qps"`                  // "dump_cmd_qps": 0,
	GetCmdQps                  int `json:"get_cmd_qps"`                   // "get_cmd_qps": 0,
	GetbitCmdQps               int `json:"getbit_cmd_qps"`                // "getbit_cmd_qps": 0,
	GetrangeCmdQps             int `json:"getrange_cmd_qps"`              // "getrange_cmd_qps": 0,
	GetsetCmdQps               int `json:"getset_cmd_qps"`                // "getset_cmd_qps": 0,
	IncrCmdQps                 int `json:"incr_cmd_qps"`                  // "incr_cmd_qps": 0,
	IncrbyCmdQps               int `json:"incrby_cmd_qps"`                // "incrby_cmd_qps": 0,
	IncrbyfloatCmdQps          int `json:"incrbyfloat_cmd_qps"`           // "incrbyfloat_cmd_qps": 0,
	MgetCmdQps                 int `json:"mget_cmd_qps"`                  // "mget_cmd_qps": 0,
	PsetexCmdQps               int `json:"psetex_cmd_qps"`                // "psetex_cmd_qps": 0,
	RestoreCmdQps              int `json:"restore_cmd_qps"`               // "restore_cmd_qps": 0,
	SetCmdQps                  int `json:"set_cmd_qps"`                   // "set_cmd_qps": 24,
	SetbitCmdQps               int `json:"setbit_cmd_qps"`                // "setbit_cmd_qps": 0,
	SetexCmdQps                int `json:"setex_cmd_qps"`                 // "setex_cmd_qps": 0,
	SetnxCmdQps                int `json:"setnx_cmd_qps"`                 // "setnx_cmd_qps": 0,
	SetrangeCmdQps             int `json:"setrange_cmd_qps"`              // "setrange_cmd_qps": 0,
	StrlenCmdQps               int `json:"strlen_cmd_qps"`                // "strlen_cmd_qps": 0,
	HdelCmdQps                 int `json:"hdel_cmd_qps"`                  // "hdel_cmd_qps": 0,
	HexistsCmdQps              int `json:"hexists_cmd_qps"`               // "hexists_cmd_qps": 0,
	HgetCmdQps                 int `json:"hget_cmd_qps"`                  // "hget_cmd_qps": 0,
	HgetallCmdQps              int `json:"hgetall_cmd_qps"`               // "hgetall_cmd_qps": 0,
	HincrbyCmdQps              int `json:"hincrby_cmd_qps"`               // "hincrby_cmd_qps": 0,
	HincrbyfloatCmdQps         int `json:"hincrbyfloat_cmd_qps"`          // "hincrbyfloat_cmd_qps": 0,
	HkeysCmdQps                int `json:"hkeys_cmd_qps"`                 // "hkeys_cmd_qps": 0,
	HlenCmdQps                 int `json:"hlen_cmd_qps"`                  // "hlen_cmd_qps": 0,
	HmgetCmdQps                int `json:"hmget_cmd_qps"`                 // "hmget_cmd_qps": 0,
	HmsetCmdQps                int `json:"hmset_cmd_qps"`                 // "hmset_cmd_qps": 0,
	HsetCmdQps                 int `json:"hset_cmd_qps"`                  // "hset_cmd_qps": 0,
	HsetnxCmdQps               int `json:"hsetnx_cmd_qps"`                // "hsetnx_cmd_qps": 0,
	HvalsCmdQps                int `json:"hvals_cmd_qps"`                 // "hvals_cmd_qps": 0,
	LindexCmdQps               int `json:"lindex_cmd_qps"`                // "lindex_cmd_qps": 0,
	LinsertCmdQps              int `json:"linsert_cmd_qps"`               // "linsert_cmd_qps": 0,
	LlenCmdQps                 int `json:"llen_cmd_qps"`                  // "llen_cmd_qps": 0,
	LpopCmdQps                 int `json:"lpop_cmd_qps"`                  // "lpop_cmd_qps": 0,
	LpushCmdQps                int `json:"lpush_cmd_qps"`                 // "lpush_cmd_qps": 0,
	LpushxCmdQps               int `json:"lpushx_cmd_qps"`                // "lpushx_cmd_qps": 0,
	LrangeCmdQps               int `json:"lrange_cmd_qps"`                // "lrange_cmd_qps": 0,
	LremCmdQps                 int `json:"lrem_cmd_qps"`                  // "lrem_cmd_qps": 0,
	LsetCmdQps                 int `json:"lset_cmd_qps"`                  // "lset_cmd_qps": 0,
	LtrimQps                   int `json:"ltrim_qps"`                     // "ltrim_qps": 0,
	RpopCmdQps                 int `json:"rpop_cmd_qps"`                  // "rpop_cmd_qps": 0,
	RpoplpushCmdQps            int `json:"rpoplpush_cmd_qps"`             // "rpoplpush_cmd_qps": 0,
	RpushCmdQps                int `json:"rpush_cmd_qps"`                 // "rpush_cmd_qps": 0,
	RpushxCmdQps               int `json:"rpushx_cmd_qps"`                // "rpushx_cmd_qps": 0,
	SaddCmdQps                 int `json:"sadd_cmd_qps"`                  // "sadd_cmd_qps": 0,
	ScardCmdQps                int `json:"scard_cmd_qps"`                 // "scard_cmd_qps": 0,
	SdiffCmdQps                int `json:"sdiff_cmd_qps"`                 // "sdiff_cmd_qps": 0,
	SdiffstoreCmdQps           int `json:"sdiffstore_cmd_qps"`            // "sdiffstore_cmd_qps": 0,
	SinterCmdQps               int `json:"sinter_cmd_qps"`                // "sinter_cmd_qps": 0,
	SinterstoreCmdQps          int `json:"sinterstore_cmd_qps"`           // "sinterstore_cmd_qps": 0,
	SismemberCmdQps            int `json:"sismember_cmd_qps"`             // "sismember_cmd_qps": 0,
	SmembersCmdQps             int `json:"smembers_cmd_qps"`              // "smembers_cmd_qps": 0,
	SmoveCmdQps                int `json:"smove_cmd_qps"`                 // "smove_cmd_qps": 0,
	SpopCmdQps                 int `json:"spop_cmd_qps"`                  // "spop_cmd_qps": 0,
	SrandmemberCmdQps          int `json:"srandmember_cmd_qps"`           // "srandmember_cmd_qps": 0,
	SremCmdQps                 int `json:"srem_cmd_qps"`                  // "srem_cmd_qps": 0,
	SunionCmdQps               int `json:"sunion_cmd_qps"`                // "sunion_cmd_qps": 0,
	SunionstoreCmdQps          int `json:"sunionstore_cmd_qps"`           // "sunionstore_cmd_qps": 0,
	ZaddCmdQps                 int `json:"zadd_cmd_qps"`                  // "zadd_cmd_qps": 0,
	ZcardCmdQps                int `json:"zcard_cmd_qps"`                 // "zcard_cmd_qps": 0,
	ZcountCmdQps               int `json:"zcount_cmd_qps"`                // "zcount_cmd_qps": 0,
	ZincrbyCmdQps              int `json:"zincrby_cmd_qps"`               // "zincrby_cmd_qps": 0,
	ZinterstoreCmdQps          int `json:"zinterstore_cmd_qps"`           // "zinterstore_cmd_qps": 0,
	ZrangeCmdQps               int `json:"zrange_cmd_qps"`                // "zrange_cmd_qps": 0,
	ZrangebyscoreCmdQps        int `json:"zrangebyscore_cmd_qps"`         // "zrangebyscore_cmd_qps": 0,
	ZrankCmdQps                int `json:"zrank_cmd_qps"`                 // "zrank_cmd_qps": 0,
	ZremCmdQps                 int `json:"zrem_cmd_qps"`                  // "zrem_cmd_qps": 0,
	ZremrangebyrankCmdQps      int `json:"zremrangebyrank_cmd_qps"`       // "zremrangebyrank_cmd_qps": 0,
	ZremrangebyscoreCmdQps     int `json:"zremrangebyscore_cmd_qps"`      // "zremrangebyscore_cmd_qps": 0,
	ZrevrangeCmdQps            int `json:"zrevrange_cmd_qps"`             // "zrevrange_cmd_qps": 0,
	ZrevrangebyscoreCmdQps     int `json:"zrevrangebyscore_cmd_qps"`      // "zrevrangebyscore_cmd_qps": 0,
	ZrevrankCmdQps             int `json:"zrevrank_cmd_qps"`              // "zrevrank_cmd_qps": 0,
	ZscoreCmdQps               int `json:"zscore_cmd_qps"`                // "zscore_cmd_qps": 0,
	ZunionstoreCmdQps          int `json:"zunionstore_cmd_qps"`           // "zunionstore_cmd_qps": 0,
	EvalCmdQps                 int `json:"eval_cmd_qps"`                  // "eval_cmd_qps": 30,
	EvalshaCmdQps              int `json:"evalsha_cmd_qps"`               // "evalsha_cmd_qps": 0,
	MsetCmdQps                 int `json:"mset_cmd_qps"`                  // "mset_cmd_qps": 0,
	PingCmdQps                 int `json:"ping_cmd_qps"`                  // "ping_cmd_qps": 0,
	QuitCmdQps                 int `json:"quit_cmd_qps"`                  // "quit_cmd_qps": 0,
	SortCmdQps                 int `json:"sort_cmd_qps"`                  // "sort_cmd_qps": 0,
	PfcountCmdQps              int `json:"pfcount_cmd_qps"`               // "pfcount_cmd_qps": 0,
	ZlexcountCmdQps            int `json:"zlexcount_cmd_qps"`             // "zlexcount_cmd_qps": 0,
	ZremrangebylexCmdQps       int `json:"zremrangebylex_cmd_qps"`        // "zremrangebylex_cmd_qps": 0,
	HscanCmdQps                int `json:"hscan_cmd_qps"`                 // "hscan_cmd_qps": 0,
	SscanCmdQps                int `json:"sscan_cmd_qps"`                 // "sscan_cmd_qps": 0,
	PfaddCmdQps                int `json:"pfadd_cmd_qps"`                 // "pfadd_cmd_qps": 0,
	PfmergeCmdQps              int `json:"pfmerge_cmd_qps"`               // "pfmerge_cmd_qps": 0,
	ZrangebylexCmdQps          int `json:"zrangebylex_cmd_qps"`           // "zrangebylex_cmd_qps": 0,
	ZscanCmdQps                int `json:"zscan_cmd_qps"`                 // "zscan_cmd_qps": 0,
	FlushdbCmdQps              int `json:"flushdb_cmd_qps"`               // "flushdb_cmd_qps": 0,
	FlushallCmdQps             int `json:"flushall_cmd_qps"`              // "flushall_cmd_qps": 0,
	TotalRspTime               int `json:"total_rsp_time"`                // "total_rsp_time": 3310030271716,
	RealRspTime                int `json:"real_rsp_time"`                 // "real_rsp_time": 62,
	UnknownCmdRspTime          int `json:"unknown_cmd_rsp_time"`          // "unknown_cmd_rsp_time": 0,
	DelCmdRspTime              int `json:"del_cmd_rsp_time"`              // "del_cmd_rsp_time": 0,
	ExistsCmdRspTime           int `json:"exists_cmd_rsp_time"`           // "exists_cmd_rsp_time": 0,
	ExpireCmdRspTime           int `json:"expire_cmd_rsp_time"`           // "expire_cmd_rsp_time": 0,
	ExpireatCmdRspTime         int `json:"expireat_cmd_rsp_time"`         // "expireat_cmd_rsp_time": 0,
	PexpireCmdRspTime          int `json:"pexpire_cmd_rsp_time"`          // "pexpire_cmd_rsp_time": 0,
	PexpireatCmdRspTime        int `json:"pexpireat_cmd_rsp_time"`        // "pexpireat_cmd_rsp_time": 0,
	PersistCmdRspTime          int `json:"persist_cmd_rsp_time"`          // "persist_cmd_rsp_time": 0,
	PttlCmdRspTime             int `json:"pttl_cmd_rsp_time"`             // "pttl_cmd_rsp_time": 0,
	TtlCmdRspTime              int `json:"ttl_cmd_rsp_time"`              // "ttl_cmd_rsp_time": 0,
	TypeCmdRspTime             int `json:"type_cmd_rsp_time"`             // "type_cmd_rsp_time": 0,
	AppendCmdRspTime           int `json:"append_cmd_rsp_time"`           // "append_cmd_rsp_time": 0,
	BitcountCmdRspTime         int `json:"bitcount_cmd_rsp_time"`         // "bitcount_cmd_rsp_time": 0,
	DecrCmdRspTime             int `json:"decr_cmd_rsp_time"`             // "decr_cmd_rsp_time": 0,
	DecrbyCmdRspTime           int `json:"decrby_cmd_rsp_time"`           // "decrby_cmd_rsp_time": 0,
	DumpCmdRspTime             int `json:"dump_cmd_rsp_time"`             // "dump_cmd_rsp_time": 0,
	GetCmdRspTime              int `json:"get_cmd_rsp_time"`              // "get_cmd_rsp_time": 0,
	GetbitCmdRspTime           int `json:"getbit_cmd_rsp_time"`           // "getbit_cmd_rsp_time": 0,
	GetrangeCmdRspTime         int `json:"getrange_cmd_rsp_time"`         // "getrange_cmd_rsp_time": 0,
	GetsetCmdRspTime           int `json:"getset_cmd_rsp_time"`           // "getset_cmd_rsp_time": 0,
	IncrCmdRspTime             int `json:"incr_cmd_rsp_time"`             // "incr_cmd_rsp_time": 0,
	IncrbyCmdRspTime           int `json:"incrby_cmd_rsp_time"`           // "incrby_cmd_rsp_time": 0,
	IncrbyfloatCmdRspTime      int `json:"incrbyfloat_cmd_rsp_time"`      // "incrbyfloat_cmd_rsp_time": 0,
	MgetCmdRspTime             int `json:"mget_cmd_rsp_time"`             // "mget_cmd_rsp_time": 0,
	PsetexCmdRspTime           int `json:"psetex_cmd_rsp_time"`           // "psetex_cmd_rsp_time": 0,
	RestoreCmdRspTime          int `json:"restore_cmd_rsp_time"`          // "restore_cmd_rsp_time": 0,
	SetCmdRspTime              int `json:"set_cmd_rsp_time"`              // "set_cmd_rsp_time": 69,
	SetbitCmdRspTime           int `json:"setbit_cmd_rsp_time"`           // "setbit_cmd_rsp_time": 0,
	SetexCmdRspTime            int `json:"setex_cmd_rsp_time"`            // "setex_cmd_rsp_time": 0,
	SetnxCmdRspTime            int `json:"setnx_cmd_rsp_time"`            // "setnx_cmd_rsp_time": 0,
	SetrangeCmdRspTime         int `json:"setrange_cmd_rsp_time"`         // "setrange_cmd_rsp_time": 0,
	StrlenCmdRspTime           int `json:"strlen_cmd_rsp_time"`           // "strlen_cmd_rsp_time": 0,
	HdelCmdRspTime             int `json:"hdel_cmd_rsp_time"`             // "hdel_cmd_rsp_time": 0,
	HexistsCmdRspTime          int `json:"hexists_cmd_rsp_time"`          // "hexists_cmd_rsp_time": 0,
	HgetCmdRspTime             int `json:"hget_cmd_rsp_time"`             // "hget_cmd_rsp_time": 0,
	HgetallCmdRspTime          int `json:"hgetall_cmd_rsp_time"`          // "hgetall_cmd_rsp_time": 0,
	HincrbyCmdRspTime          int `json:"hincrby_cmd_rsp_time"`          // "hincrby_cmd_rsp_time": 0,
	HincrbyfloatCmdRspTime     int `json:"hincrbyfloat_cmd_rsp_time"`     // "hincrbyfloat_cmd_rsp_time": 0,
	HkeysCmdRspTime            int `json:"hkeys_cmd_rsp_time"`            // "hkeys_cmd_rsp_time": 0,
	HlenCmdRspTime             int `json:"hlen_cmd_rsp_time"`             // "hlen_cmd_rsp_time": 0,
	HmgetCmdRspTime            int `json:"hmget_cmd_rsp_time"`            // "hmget_cmd_rsp_time": 0,
	HmsetCmdRspTime            int `json:"hmset_cmd_rsp_time"`            // "hmset_cmd_rsp_time": 0,
	HsetCmdRspTime             int `json:"hset_cmd_rsp_time"`             // "hset_cmd_rsp_time": 0,
	HsetnxCmdRspTime           int `json:"hsetnx_cmd_rsp_time"`           // "hsetnx_cmd_rsp_time": 0,
	HvalsCmdRspTime            int `json:"hvals_cmd_rsp_time"`            // "hvals_cmd_rsp_time": 0,
	LindexCmdRspTime           int `json:"lindex_cmd_rsp_time"`           // "lindex_cmd_rsp_time": 0,
	LinsertCmdRspTime          int `json:"linsert_cmd_rsp_time"`          // "linsert_cmd_rsp_time": 0,
	LlenCmdRspTime             int `json:"llen_cmd_rsp_time"`             // "llen_cmd_rsp_time": 0,
	LpopCmdRspTime             int `json:"lpop_cmd_rsp_time"`             // "lpop_cmd_rsp_time": 0,
	LpushCmdRspTime            int `json:"lpush_cmd_rsp_time"`            // "lpush_cmd_rsp_time": 0,
	LpushxCmdRspTime           int `json:"lpushx_cmd_rsp_time"`           // "lpushx_cmd_rsp_time": 0,
	LrangeCmdRspTime           int `json:"lrange_cmd_rsp_time"`           // "lrange_cmd_rsp_time": 0,
	LremCmdRspTime             int `json:"lrem_cmd_rsp_time"`             // "lrem_cmd_rsp_time": 0,
	LsetCmdRspTime             int `json:"lset_cmd_rsp_time"`             // "lset_cmd_rsp_time": 0,
	LtrimRspTime               int `json:"ltrim_rsp_time"`                // "ltrim_rsp_time": 0,
	RpopCmdRspTime             int `json:"rpop_cmd_rsp_time"`             // "rpop_cmd_rsp_time": 0,
	RpoplpushCmdRspTime        int `json:"rpoplpush_cmd_rsp_time"`        // "rpoplpush_cmd_rsp_time": 0,
	RpushCmdRspTime            int `json:"rpush_cmd_rsp_time"`            // "rpush_cmd_rsp_time": 0,
	RpushxCmdRspTime           int `json:"rpushx_cmd_rsp_time"`           // "rpushx_cmd_rsp_time": 0,
	SaddCmdRspTime             int `json:"sadd_cmd_rsp_time"`             // "sadd_cmd_rsp_time": 0,
	ScardCmdRspTime            int `json:"scard_cmd_rsp_time"`            // "scard_cmd_rsp_time": 0,
	SdiffCmdRspTime            int `json:"sdiff_cmd_rsp_time"`            // "sdiff_cmd_rsp_time": 0,
	SdiffstoreCmdRspTime       int `json:"sdiffstore_cmd_rsp_time"`       // "sdiffstore_cmd_rsp_time": 0,
	SinterCmdRspTime           int `json:"sinter_cmd_rsp_time"`           // "sinter_cmd_rsp_time": 0,
	SinterstoreCmdRspTime      int `json:"sinterstore_cmd_rsp_time"`      // "sinterstore_cmd_rsp_time": 0,
	SismemberCmdRspTime        int `json:"sismember_cmd_rsp_time"`        // "sismember_cmd_rsp_time": 0,
	SmembersCmdRspTime         int `json:"smembers_cmd_rsp_time"`         // "smembers_cmd_rsp_time": 0,
	SmoveCmdRspTime            int `json:"smove_cmd_rsp_time"`            // "smove_cmd_rsp_time": 0,
	SpopCmdRspTime             int `json:"spop_cmd_rsp_time"`             // "spop_cmd_rsp_time": 0,
	SrandmemberCmdRspTime      int `json:"srandmember_cmd_rsp_time"`      // "srandmember_cmd_rsp_time": 0,
	SremCmdRspTime             int `json:"srem_cmd_rsp_time"`             // "srem_cmd_rsp_time": 0,
	SunionCmdRspTime           int `json:"sunion_cmd_rsp_time"`           // "sunion_cmd_rsp_time": 0,
	SunionstoreCmdRspTime      int `json:"sunionstore_cmd_rsp_time"`      // "sunionstore_cmd_rsp_time": 0,
	ZaddCmdRspTime             int `json:"zadd_cmd_rsp_time"`             // "zadd_cmd_rsp_time": 0,
	ZcardCmdRspTime            int `json:"zcard_cmd_rsp_time"`            // "zcard_cmd_rsp_time": 0,
	ZcountCmdRspTime           int `json:"zcount_cmd_rsp_time"`           // "zcount_cmd_rsp_time": 0,
	ZincrbyCmdRspTime          int `json:"zincrby_cmd_rsp_time"`          // "zincrby_cmd_rsp_time": 0,
	ZinterstoreCmdRspTime      int `json:"zinterstore_cmd_rsp_time"`      // "zinterstore_cmd_rsp_time": 0,
	ZrangeCmdRspTime           int `json:"zrange_cmd_rsp_time"`           // "zrange_cmd_rsp_time": 0,
	ZrangebyscoreCmdRspTime    int `json:"zrangebyscore_cmd_rsp_time"`    // "zrangebyscore_cmd_rsp_time": 0,
	ZrankCmdRspTime            int `json:"zrank_cmd_rsp_time"`            // "zrank_cmd_rsp_time": 0,
	ZremCmdRspTime             int `json:"zrem_cmd_rsp_time"`             // "zrem_cmd_rsp_time": 0,
	ZremrangebyrankCmdRspTime  int `json:"zremrangebyrank_cmd_rsp_time"`  // "zremrangebyrank_cmd_rsp_time": 0,
	ZremrangebyscoreCmdRspTime int `json:"zremrangebyscore_cmd_rsp_time"` // "zremrangebyscore_cmd_rsp_time": 0,
	ZrevrangeCmdRspTime        int `json:"zrevrange_cmd_rsp_time"`        // "zrevrange_cmd_rsp_time": 0,
	ZrevrangebyscoreCmdRspTime int `json:"zrevrangebyscore_cmd_rsp_time"` // "zrevrangebyscore_cmd_rsp_time": 0,
	ZrevrankCmdRspTime         int `json:"zrevrank_cmd_rsp_time"`         // "zrevrank_cmd_rsp_time": 0,
	ZscoreCmdRspTime           int `json:"zscore_cmd_rsp_time"`           // "zscore_cmd_rsp_time": 0,
	ZunionstoreCmdRspTime      int `json:"zunionstore_cmd_rsp_time"`      // "zunionstore_cmd_rsp_time": 0,
	EvalCmdRspTime             int `json:"eval_cmd_rsp_time"`             // "eval_cmd_rsp_time": 57,
	EvalshaCmdRspTime          int `json:"evalsha_cmd_rsp_time"`          // "evalsha_cmd_rsp_time": 0,
	MsetCmdRspTime             int `json:"mset_cmd_rsp_time"`             // "mset_cmd_rsp_time": 0,
	PingCmdRspTime             int `json:"ping_cmd_rsp_time"`             // "ping_cmd_rsp_time": 0,
	QuitCmdRspTime             int `json:"quit_cmd_rsp_time"`             // "quit_cmd_rsp_time": 0,
	SortCmdRspTime             int `json:"sort_cmd_rsp_time"`             // "sort_cmd_rsp_time": 0,
	PfcountCmdRspTime          int `json:"pfcount_cmd_rsp_time"`          // "pfcount_cmd_rsp_time": 0,
	ZlexcountCmdRspTime        int `json:"zlexcount_cmd_rsp_time"`        // "zlexcount_cmd_rsp_time": 0,
	ZremrangebylexCmdRspTime   int `json:"zremrangebylex_cmd_rsp_time"`   // "zremrangebylex_cmd_rsp_time": 0,
	HscanCmdRspTime            int `json:"hscan_cmd_rsp_time"`            // "hscan_cmd_rsp_time": 0,
	SscanCmdRspTime            int `json:"sscan_cmd_rsp_time"`            // "sscan_cmd_rsp_time": 0,
	PfaddCmdRspTime            int `json:"pfadd_cmd_rsp_time"`            // "pfadd_cmd_rsp_time": 0,
	PfmergeCmdRspTime          int `json:"pfmerge_cmd_rsp_time"`          // "pfmerge_cmd_rsp_time": 0,
	ZrangebylexCmdRspTime      int `json:"zrangebylex_cmd_rsp_time"`      // "zrangebylex_cmd_rsp_time": 0,
	ZscanCmdRspTime            int `json:"zscan_cmd_rsp_time"`            // "zscan_cmd_rsp_time": 0,
	FlushdbCmdRspTime          int `json:"flushdb_cmd_rsp_time"`          // "flushdb_cmd_rsp_time": 0,
	FlushallCmdRspTime         int `json:"flushall_cmd_rsp_time"`         // "flushall_cmd_rsp_time": 0,
	ServerEjects               int `json:"server_ejects"`                 // "server_ejects": 0,
	ForwardError               int `json:"forward_error"`                 // "forward_error": 2,
	Fragments                  int `json:"fragments"`                     // "fragments": 0,
	SlowlogCnt                 int `json:"slowlog_cnt"`                   // "slowlog_cnt": 1589,
	Servers                    []*RedisServerMonitor
}

type ProxyMonitor struct {
	Service       string `json:"service"`         // "dxm-nutcracker"
	Source        string `json:"source"`          // "kafka0003.kj01.bddx.dxm-int.com"
	Version       string `json:"version"`         // "2.0.0.2"
	UpTime        int    `json:"uptime"`          // 22980840
	Timestamp     int    `json:"timestamp"`       // 1708669455
	ProxyQPS      int    `json:"proxy_qps"`       // 56
	ProxyReadQPS  int    `json:"proxy_read_qps"`  // 0
	ProxyWriteQPS int    `json:"proxy_write_qps"` // 56
	Pool          *PoolMonitor
}

// 解析各个server的监控数据
func getServerMonitor(poolBody json.RawMessage) ([]*RedisServerMonitor, error) {
	var poolMetricMap map[string]json.RawMessage
	err := json.Unmarshal(poolBody, &poolMetricMap)
	if err != nil {
		return nil, err
	}

	servers := []*RedisServerMonitor{}

	serverIndex := 1
	for {
		serverRaw, exist := poolMetricMap[fmt.Sprintf("server%d", serverIndex)]
		if !exist {
			break
		}

		var server RedisServerMonitor
		err = json.Unmarshal(serverRaw, &server)
		if err != nil {
			return nil, err
		}
		servers = append(servers, &server)
		serverIndex += 1
	}

	return servers, nil
}

// 解析pool的监控数据
func getPoolMonitor(port int, resBody json.RawMessage) (*PoolMonitor, error) {
	var metricMap map[string]json.RawMessage
	err := json.Unmarshal(resBody, &metricMap)
	if err != nil {
		return nil, err
	}

	// 解析pool监控内容，需要知道poolname
	poolname, err := GetPoolName(port)
	if err != nil {
		return nil, err
	}

	var result PoolMonitor
	err = json.Unmarshal(metricMap[poolname], &result)
	if err != nil {
		return nil, err
	}

	// server1/2/3...需要再unmarshal一次
	result.Servers, err = getServerMonitor(metricMap[poolname])
	if err != nil {
		return nil, err
	}

	return &result, nil
}

// 获取proxy的状态监控
func GetProxyMonitor(port int) (*ProxyMonitor, error) {
	if port < 8000 || port >= 9000 {
		return nil, fmt.Errorf("not a proxy")
	}

	localIP, err := config.GetString("env/local_ip")
	if err != nil {
		localIP = "127.0.0.1"
	}

	// go不支持http0.9，通过curl实现
	url := fmt.Sprintf("http://%s:%d/", localIP, port+1000)
	logger.Debug("monitor url: %s", url)
	output, err := utils.ExecCommand(&utils.Shell{
		Command: "curl",
		Args:    []string{"-s", url},
	})

	// 规避 exit status 56 问题，暂不清楚原因
	var result ProxyMonitor
	var unmarshalErr error
	if output != "" {
		unmarshalErr = json.Unmarshal([]byte(output), &result)
		if unmarshalErr != nil {
			logger.Warn("failed to unmarshal json string, output=(%s), curlError=%v", output, err)
			return nil, err
		}
	} else {
		logger.Warn("failed to exec curl cmd, url=%s, output=%s, error=%v", url, output, err)
		return nil, err
	}

	// pool监控需要再unmarshal一次
	result.Pool, err = getPoolMonitor(port, []byte(output))
	if err != nil {
		return nil, err
	}

	return &result, nil
}
