package worker

import (
	"testing"

	"redis-agent/env"
)

func TestGetConnections(t *testing.T) {
	env.Init("../../mock/config/config.yaml")

	type args struct {
		port int
	}
	tests := []struct {
		name    string
		before  func()
		args    args
		wantErr bool
		expect  func(t *testing.T, conns []string)
	}{
		{
			name:   "test1",
			before: func() {},
			args: args{
				port: 8001,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		if tt.before != nil {
			tt.before()
		}
		t.Run(tt.name, func(t *testing.T) {
			conns, err := GetConnections(tt.args.port)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetConnections() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.expect != nil {
				tt.expect(t, conns)
			}
		})
	}
}
