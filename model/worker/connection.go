package worker

import (
	"fmt"
	"strings"

	"dt-common/logger"
	"dt-common/utils"
)

// 获取Proxy的连接数
func GetConnections(port int) ([]string, error) {
	localIP, err := utils.GetLocalIP()
	if err != nil {
		return nil, err
	}

	// 获取连接
	cmd := fmt.Sprintf("netstat -anlt | grep ESTABLISHED | grep '%s:%d' | grep -Ev '^$' | awk '{print $5}' | awk -F: '{print $1}' | sort | uniq", localIP, port)
	output, err := utils.ExecCommand(&utils.Shell{
		Command: "bash",
		Args:    []string{"-c", cmd},
	})
	if err != nil {
		logger.Warn("failed to exec netstat cmd. port=%d, cmd=%s, error=(%v)", port, cmd, err)
		return nil, err
	}
	// 按照回车切割
	ipList := strings.TrimSpace(string(output))
	res := strings.Split(ipList, "\n")
	if res[0] == "" {
		return nil, nil
	}

	return res, nil
}
