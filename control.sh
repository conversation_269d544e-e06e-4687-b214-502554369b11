#!/bin/bash
#file :service.pid is used to store pid

#set -x

dir=$(dirname $0)
cd ${dir}
cur=$(pwd)

SERVICE_ROOT="$cur"

BIN_NAME=redis-xweb
CMD_LINE=${SERVICE_ROOT}/cmd/${BIN_NAME}
LOG_DIR=$SERVICE_ROOT/log

#use nohup
PID_FILE=${SERVICE_ROOT}/var/service.pid
START_STOP_LOG=$LOG_DIR/nohup_$BIN_NAME.log

#make sure PID is belong to $CMD_LINE
#avert operate the other process
status() {
    PID=$1
    process_num=$(cat /proc/$PID/cmdline 2>/dev/null | grep $CMD_LINE | wc -l)
    if [ $process_num -gt 0 ]; then
        echo 1
    else
        echo 0
    fi
}

check() {
    PID=$(serverPidByNohup)
    if [ $PID -eq 0 ]; then
        echo "service $CMD_LINE is not running"
    else
        echo "service $CMD_LINE is running. PID:$PID"
    fi
    exit 0
}

init() {
    if [ ! -f $LOG_DIR ]; then
        mkdir -p $LOG_DIR
    fi
}

############################################################################
# use nohup
############################################################################

#get servier pid
serverPidByNohup() {
    if [[ ! -f $PID_FILE ]]; then
        echo "0"
        return
    fi
    PID=$(head $PID_FILE)
    if [ $PID -ne 0 ]; then
        $(ls /proc/$PID >/dev/null 2>/dev/null)
        if [ $? -ne 0 ]; then
            echo "0"
        else
            echo $PID
        fi
    else
        echo "0"
    fi
}

startByNohup() {
    echo "Ready to start service $CMD_LINE: "
    PID=$(serverPidByNohup)
    if [ $PID -ne 0 ]; then
        echo "server $CMD_LINE is running. Not allow to start."
        exit 1
    fi

    nohup $CMD_LINE >$START_STOP_LOG 2>&1 &
    if [ $? -ne 0 ]; then
        echo "start service $CMD_LINE failed\n"
        exit $?
    else
        PID=$!
        echo service pid $PID
        sleep 1
        ret=$(status $PID)
        if [ $ret -eq "1" ]; then
            echo $PID >$PID_FILE
            echo $PID
            echo "start service $CMD_LINE success"
            exit 0
        else
            echo "start service $CMD_LINE failed"
            exit 1
        fi
    fi
}

stopByNohup() {
    echo "Ready to stop $BIN_NAME By nohup"
    PID=$(serverPidByNohup)
    if [ $PID -eq 0 ]; then
        echo "server $CMD_LINE is not running"
        exit 0
    fi

    echo "Ready to stop $CMD_LINE: $PID"
    ret=$(status $PID)
    if [ $ret -gt 0 ]; then
        kill -9 $PID
        ret=$?
    else
        echo "server $CMD_LINE is not running"
        return
    fi
    sleep 5
    ret=$(status $PID)
    if [ $ret -gt 0 ]; then
        echo 1
    else
        echo 0
    fi
}

############################################################################
#main procss

init

case "$1" in
start)
    startByNohup
    ;;

stop)
    stopByNohup
    ;;

restart)
    stopByNohup
    startByNohup
    ;;

check)
    check
    ;;

*)
    echo "Usage: $0 {start|stop|restart|check}"
    exit 1
    ;;
esac

############################################################################
