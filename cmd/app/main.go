package main

import (
	"fmt"
	"log"
	"runtime"

	"dt-common/logger"
	"redis-cmanager/config"
	"redis-cmanager/env"
	"redis-cmanager/model/inspection"
	"redis-cmanager/router"
)

func main() {
	//控制main使用cpu的总数,只用一颗cpu
	runtime.GOMAXPROCS(1)

	cfg := env.Init()
	logger.Info("env initialization successfully")

	// 初始化巡检配置
	var inspectionCfg inspection.Config
	err := config.Get("inspection", &inspectionCfg)
	if err != nil {
		log.Panicf("failed to init inspection timer, error=(%v)", err)
	}
	err = inspection.Start(cfg.BNS, &inspectionCfg)
	if err != nil {
		inspection.Stop()
		log.Panicf("failed to start inspection, error=(%v)", err)
	}
	logger.Info("inspection started successfully, server starting...")

	// 启动server
	app := router.Init(cfg.Mode, cfg.Token)
	app.Run(fmt.Sprintf(":%d", cfg.Port))
}
