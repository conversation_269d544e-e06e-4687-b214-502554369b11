package main

import (
	"fmt"
	"log"
	"runtime"

	"dt-common/logger"
	"redis-xweb/config"
	"redis-xweb/env"
	"redis-xweb/router"
)

func main() {
	//控制main使用cpu的总数,只用一颗cpu
	runtime.GOMAXPROCS(1)

	env.Init()
	logger.Info("env is initialized successfully, server start")

	// 获取app config
	var cfg router.Config
	err := config.Get("application", &cfg)
	if err != nil {
		log.Panicf("failed to get application config, error=(%v)", err)
	}

	app := router.Init(cfg.Mode)
	app.Run(fmt.Sprintf(":%d", cfg.Port))
}
