package ui

import (
	"github.com/gin-gonic/gin"

	"dt-common/gintool"
	"redis-xweb/controller/apply"
	"redis-xweb/controller/cluster"
	"redis-xweb/controller/migration"
	"redis-xweb/controller/task"
)

// 集群接口组，各类数据查询
func Routes(r *gin.Engine) {
	// 实例路由组
	ui := r.Group("/redis-xweb/ui")
	{
		// 前端鉴权
		ui.Use(gintool.AuthFilter())

		// 流程发单用接口
		applyRoutes := ui.Group("/apply")
		{
			// 部署申请，仅发单
			applyRoutes.POST("/deploy", apply.Deploy)
			applyRoutes.POST("/resubDeploy", apply.ReDeploy)

			// 权限申请，仅发单
			applyRoutes.POST("/role", apply.Role)
			applyRoutes.POST("/resubRole", apply.ReRole)

			// 白名单申请，仅发单
			applyRoutes.POST("/whitelist", apply.Whitelist)
			applyRoutes.POST("/resubWitelist", apply.ReWhitelist)

			// 转让，仅发单
			applyRoutes.POST("/transfer", apply.Transfer)
			applyRoutes.POST("/resubTransfer", apply.ReTransfer)
		}

		// 集群路由组
		clusterRoutes := ui.Group("/cluster")
		{
			// 集群可选部署区域
			clusterRoutes.GET("/areas", cluster.GetAreas)
			// 获取所有业务属性
			clusterRoutes.GET("/businesses", cluster.GetAllBusinesses)

			// 获取用户可见集群列表
			clusterRoutes.GET("/list", cluster.GetClusterList)
			// 获取用户可见集群下拉列表
			clusterRoutes.GET("/selectList", cluster.GetClusterSelectList)
			// 获取所有实例列表（用于申请角色）
			clusterRoutes.GET("/listAll", cluster.GetClusterListForApply)

			// 获取集群详情
			clusterRoutes.GET("/detail", cluster.GetClusterDetail)
			// 获取集群账单
			clusterRoutes.GET("/bill", cluster.GetClusterBill)
			// 获取拓扑结构
			clusterRoutes.GET("/topo", cluster.GetTopology)
			// 主从切换部分redis分片
			// clusterRoutes.POST("/failoverDesignatedFragment", cluster.FailoverDesignatedFragment)

			// 获取白名单列表
			clusterRoutes.GET("/whitelist", cluster.GetWhitelist)

			// 集群转让
			clusterRoutes.POST("/transfer", cluster.Transfer)
		}

		// 变更管理
		taskRoutes := ui.Group("/task")
		{
			// 获取任务列表
			taskRoutes.GET("/list", task.GetTaskList)
			// 获取任务详情（阶段列表）
			taskRoutes.GET("/detail", task.GetTaskDetail)

			// 获取阶段详情
			taskRoutes.GET("/stage/detail", task.GetStageDetail)

			// 阶段运行(执行/回滚)
			taskRoutes.POST("/stage/run", task.StageRun)
			// 阶段中止运行（主从切换）
			taskRoutes.POST("/stage/cancel", task.StageStop)
		}

		// 迁移接口
		migrationRoutes := ui.Group("/migration")
		{
			// 创建迁移任务
			migrationRoutes.POST("/start", migration.Start)
		}
	}
}
