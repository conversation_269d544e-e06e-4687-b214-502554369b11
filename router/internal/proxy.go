package internal

import (
	"github.com/gin-gonic/gin"

	"redis-agent/controller/proxy"
)

// proxy管理
func ProxyRoutes(r *gin.Engine) {
	group := r.Group("/proxy")
	{
		// 执行proxy启动
		group.POST("/start", proxy.Start)

		// 执行proxy从库关停
		group.POST("/stop", proxy.Stop)

		// 检查proxy存活状态
		group.GET("/status", proxy.Status)

		// 检查proxy拓扑
		group.GET("/topo", proxy.Topo)

		// 检查qps和长连接
		group.GET("/conns", proxy.Connections)
	}
}
