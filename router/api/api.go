package api

import (
	"github.com/gin-gonic/gin"

	"dt-common/errs"
	"dt-common/gintool"
	"redis-xweb/config"
	"redis-xweb/controller/api"
	"redis-xweb/controller/cluster"
	"redis-xweb/controller/flow"
	"redis-xweb/controller/operation"
	"redis-xweb/controller/task"
)

// 适用于gin的鉴权中间件，简单鉴权，只验证Token
func AuthFilter(token string) gin.HandlerFunc {
	return func(c *gin.Context) {
		t := c.Request.Header.Get("Authorization")
		if t == "" || t != token {
			gintool.JSON(c, nil, errs.New(errs.CodePermissionDenied))
			return
		}
	}
}

// 集群接口组，各类数据查询
func Routes(r *gin.Engine) {
	// 实例路由组
	apiGroup := r.Group("/redis-xweb/api")
	{
		operationGroup := apiGroup.Group("/operation")
		{
			// 监控升级
			operationGroup.POST("/monitorUpgrade", operation.MonitorUpgrade)
			// 机房紧急切流
			operationGroup.POST("/emergencyFlowCut", operation.EmergencyFlowCut)

			// Proxy扩缩容
			operationGroup.POST("/proxyScaling", api.ProxyScaling)
		}

		// 流程中心回调交互接口
		flowGroup := apiGroup.Group("/flow")
		{
			// 接入流程中心必须实现的接口
			flowGroup.POST("/GetListDetail", flow.GetListDetail)
			flowGroup.POST("/FuncSyncCaller", flow.FuncSyncCaller)
			flowGroup.POST("/FuncAsyncCaller", flow.FuncAsyncCaller)
			flowGroup.POST("/FlowFuncSyncCaller", flow.FlowFuncSyncCaller)
			flowGroup.POST("/FlowFuncAsyncCaller", flow.FlowFuncAsyncCaller)

			// 自定义接口：获取集群管理员列表，无鉴权
			flowGroup.GET("/GetClusterManagers", cluster.GetClusterManagers)
		}

		// 离职流程，获取个人负责的集群
		demandGroup := apiGroup.Group("/demand")
		{
			demandGroup.GET("/queryUserRes", api.GetUserResponsibleClusters)
			demandGroup.POST("/queryUserRes", api.GetUserResponsibleClusters)
		}

		// task路由组
		taskRoutes := apiGroup.Group("/task")
		{
			token, err := config.GetString("application/token")
			if err != nil {
				panic("missing config application/token")
			}

			// 异步任务结果回调
			taskRoutes.POST("/stage/callback", AuthFilter(token), task.AsyncCallback)
		}

		// 运维接口：与认证中心交互
		authGroup := apiGroup.Group("/auth")
		{
			authGroup.POST("/createRole", api.CreateRole)
			authGroup.POST("/addUserToRole", api.AddUserToRole)
		}

		billingGroup := apiGroup.Group("/billing")
		{
			billingGroup.GET("/getBillingInstance", api.GetOrderInfo)
			billingGroup.POST("/memorySizeChange", api.MemorySizeChange)
			billingGroup.POST("/proxyNumChange", api.ProxyNumChange)

			billingGroup.POST("/getBillingInstances", api.GetBillingCluster)
		}
	}
}
