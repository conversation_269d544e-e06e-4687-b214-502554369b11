package router

import (
	"github.com/gin-gonic/gin"

	"dt-common/errs"
	"dt-common/gintool"
	"redis-cmanager/controller/api"
	"redis-cmanager/controller/cluster"
	"redis-cmanager/controller/component"
	"redis-cmanager/controller/migration"
	"redis-cmanager/controller/monitor"
	"redis-cmanager/controller/whitelist"
)

// 适用于gin的鉴权中间件，简单鉴权，只验证Token
func AuthFilter(token string) gin.HandlerFunc {
	return func(c *gin.Context) {
		t := c.Request.Header.Get("Authorization")
		if t == "" || t != token {
			gintool.JSON(c, nil, errs.New(errs.CodeAuthenticationFailed))
			return
		}
	}
}

// Routes ras接口
func Routes(r *gin.Engine, token string) {
	// 监控路由
	g := r.Group("/redis-cmanager")
	{
		// 运维接口(无页面，手动调)
		apiGroup := g.Group("/api", AuthFilter(token))
		{
			apiGroup.POST("/pod/taint", api.TaintPods)     // 污点POD
			apiGroup.POST("/pod/untaint", api.UnTaintPods) // 取消污点POD
			apiGroup.POST("/pod/delete", api.DeletePods)   // 删除POD
		}

		// 集群配置变更、查询
		clusterGroup := g.Group("/cluster", AuthFilter(token))
		{
			// POD启动时获取资源
			clusterGroup.GET("/pod/agentPackage", cluster.GetAgentPackage)
			clusterGroup.GET("/pod/agentConfig", cluster.GetAgentConfig)
			clusterGroup.GET("/pod/redisMonitorPackage", cluster.GetRedisMonitorPackage)

			/** 变更 */
			clusterGroup.POST("/deploy", cluster.Deploy)                         // 新集群部署
			clusterGroup.GET("/render/progress", cluster.RealtimeRenderProgress) // 获取实时渲染进度

			/** 代理扩缩容 */
			clusterGroup.POST("/proxy/scaling", cluster.ProxyScaling) // Proxy扩缩容
			clusterGroup.POST("/proxy/remove", component.RemoveProxy) // 删除污点代理

			/** 内存扩缩容 */
			// clusterGroup.POST("/redis/scaling", cluster.MemoryScaling) // 内存扩缩容
			// clusterGroup.POST("/redis/remove", component.RemoveRedis) // 删除污点从库

			/** 主从切换 */
			clusterGroup.POST("/redis/failover", component.Failover)         // 主从切换
			clusterGroup.POST("/redis/stopFailover", component.StopFailover) // 取消：主从切换

			/** 白名单管理 */
			clusterGroup.POST("/whitelist/update", whitelist.UpdateProxyWhitelist)   // 更新白名单（Proxy）
			clusterGroup.POST("/whitelist/manager", whitelist.UpdateManageWhitelist) // 更新管理白名单（Proxy、Redis、Sentinel）
		}

		// 巡检
		inspectionGroup := g.Group("/inspection")
		{
			inspectionGroup.POST("/render/progress", monitor.RenderProgress) // ClusterRenderProgress
			inspectionGroup.POST("/proxy/prober", monitor.ProxyProber)       // Proxy探活
		}

		// ================================================
		//  TODO：兼容manager单独设置/process段，迁移完成可删除
		// ================================================

		// 更新白名单
		whitelistGroup := g.Group("/whitelist", AuthFilter(token))
		{
			whitelistGroup.POST("/updateWhitelist", whitelist.UpdateProxyWhitelist)        // 更新白名单（Proxy）
			whitelistGroup.POST("/updateManageWhitelist", whitelist.UpdateManageWhitelist) // 更新管理白名单（Proxy、Redis、Sentinel）
		}

		// 进程路由(容器实例后面直接退还，不需要开关)
		processGroup := g.Group("/process", AuthFilter(token))
		{
			processGroup.POST("/proxy/start", migration.StartProxy)       // 启动proxy
			processGroup.POST("/proxy/stop", migration.StopProxy)         // 关停proxy
			processGroup.POST("/sentinel/start", migration.StartSentinel) // 启动sentinel
			processGroup.POST("/sentinel/stop", migration.StopSentinel)   // 关停sentinel
		}

		// 迁移用变更方法
		migrationGroup := g.Group("/migration", AuthFilter(token))
		{
			migrationGroup.POST("/slaveOf", migration.SlaveOf)         // SlaveOf
			migrationGroup.POST("/stopSlaveOf", migration.StopSlaveOf) // 取消SlaveOf

			migrationGroup.POST("/updateQuorum", migration.SentinelQuorum) // 更新quorum，支持支付3改5
			migrationGroup.POST("/unmount", migration.UnmountInstance)     // 解挂载实例

			migrationGroup.POST("/changeEnabledAZ", migration.ChangeEnabledAZ) // HBA、HBC扩容
			migrationGroup.POST("/changeMode", migration.ChangeMode)           // 改变托管状态
		}
	}
}
