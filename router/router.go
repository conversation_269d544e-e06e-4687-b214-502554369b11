package router

import (
	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"

	"dt-common/errs"
	"dt-common/gintool"
	"redis-xweb/router/api"
	"redis-xweb/router/ui"
)

type Config struct {
	Mode string `yaml:"mode"`
	Port int    `yaml:"port"`
}

// 404处理
func HandlerNotFound(c *gin.Context) {
	gintool.JSON2FE(c, nil, errs.CodeNotFound.Detail())
}

func HealthCheck(c *gin.Context) {
	gintool.JSON(c, "ok", nil)
}

// 初始化路由
func Init(mode string) *gin.Engine {
	gin.SetMode(mode)

	// init engine
	r := gin.New()
	r.Use(cors.Default())
	r.Use(gintool.Logger)
	r.Use(gintool.Recovery)

	// 404
	r.NoMethod(HandlerNotFound)
	r.NoRoute(HandlerNotFound)

	// HealthCheck
	r.GET("/healthCheck", HealthCheck)
	ui.Routes(r)
	api.Routes(r)

	return r
}
